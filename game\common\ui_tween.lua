require("game/common/ui_tween_const")

UITween = UITween or {}

function UITween.ShowFadeUp(self)
	local MOVE_TIME = 0.3
	local MOVE_DISTANCE = 25
	local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 0
	self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, -MOVE_DISTANCE, 0)

	local tween = self.root_node_transform.transform:DOAnchorPosY(0, MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.Linear)

	local on_tween_updata = function ()
		canvas_group.alpha = canvas_group.alpha + UnityEngine.Time.deltaTime / MOVE_TIME
	end

	local on_tween_complete = function ()
		canvas_group.alpha = 1
		self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
	end

	return tween, on_tween_updata, on_tween_complete
end

-- 打开界面（让界面从某个位置打开）
function UITween.ShowFadeUpToOpenPos(self, aim_root)
	if not aim_root then
		return
	end

	local MOVE_TIME = 0.4
	local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 0.1

	-- self.root_node_transform.transform.position = u3dpool.vec3(aim_root.transform.position.x, aim_root.transform.position.y, 0)
	-- self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(self.root_node_transform.transform.anchoredPosition.x,
	-- 														self.root_node_transform.transform.anchoredPosition.y, 0)
	self.root_node_transform.transform.localScale = u3dpool.vec3(0.1, 0.1, 0.1)
	local tween = self.root_node_transform.transform:DOAnchorPos(u3dpool.vec3(0, 0, 0), MOVE_TIME)
	-- local tween_scale = self.root_node_transform.transform:DOScale(1, MOVE_TIME)

	-- tween_scale:OnComplete(function ()
	-- 	self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
	-- end)

	tween:SetEase(DG.Tweening.Ease.Linear)

	local on_tween_updata = function ()
		if not IsNil(canvas_group) then
			canvas_group.alpha = canvas_group.alpha + UnityEngine.Time.deltaTime / MOVE_TIME
		end
	end

	local on_tween_complete = function ()
		if not IsNil(canvas_group) then
			canvas_group.alpha = 1
		end
		if not IsNil(self.root_node_transform) then
			self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
			self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
		end
	end

	return tween, on_tween_updata, on_tween_complete
end

function UITween.HideFadeUp(self)
	local MOVE_TIME = 0.3
	local MOVE_DISTANCE = 25
	local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 1
	self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)

	local tween = self.root_node_transform.transform:DOAnchorPosY(MOVE_DISTANCE, MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.Linear)

	local on_tween_updata = function ()
		if not IsNil(canvas_group) then
			canvas_group.alpha = canvas_group.alpha - UnityEngine.Time.deltaTime / MOVE_TIME
		end
	end

	local on_tween_complete = function ()
		if not IsNil(canvas_group) then
			canvas_group.alpha = 1
		end
		self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
	end

	return tween, on_tween_updata, on_tween_complete
end

-- 关闭界面（让界面到达某个位置关闭掉）
function UITween.HideFadeUpToOpenPos(self, aim_root)
	if not aim_root or IsNil(aim_root.transform) then
		return
	end

	local MOVE_TIME = 0.6
	local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 1

	self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
	self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
	local tween = self.root_node_transform.transform:DOMove(aim_root.transform.position, MOVE_TIME)
	local tween_scale = self.root_node_transform.transform:DOScale(0.1, MOVE_TIME)

	tween_scale:OnComplete(function ()
		if not IsNil(self.root_node_transform) then
			self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
		end
	end)

	tween:SetEase(DG.Tweening.Ease.Linear)

	local on_tween_updata = function ()
		if not IsNil(canvas_group) then
			canvas_group.alpha = canvas_group.alpha - UnityEngine.Time.deltaTime / MOVE_TIME
		end
	end

	local on_tween_complete = function ()
		if not IsNil(canvas_group) then
			canvas_group.alpha = 1
		end
		if not IsNil(self.root_node_transform) then
			self.root_node_transform.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
			self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
		end
	end

	return tween, on_tween_updata, on_tween_complete
end

--从小到大逐渐显示(SafeBaseView)
function UITween.ShowFadeScale(self)
	local MOVE_TIME = 0.3
	local SCALE = 0.9
	local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 0
	self.root_node_transform.transform.localScale = u3dpool.vec3(SCALE, SCALE, SCALE)

	local tween = self.root_node_transform.transform:DOScale(1, MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.OutCubic)

	local on_tween_updata = function ()
		canvas_group.alpha = canvas_group.alpha + UnityEngine.Time.deltaTime / MOVE_TIME
	end

	local on_tween_complete = function ()
		canvas_group.alpha = 1
		self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
	end

	return tween, on_tween_updata, on_tween_complete
end

--从小到大逐渐隐藏(SafeBaseView)
function UITween.HideFadeScale(self)
	local MOVE_TIME = 0.3
	local SCALE = 1
	local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 1
	self.root_node_transform.transform.localScale = u3dpool.vec3(SCALE, SCALE, SCALE)

	local tween = self.root_node_transform.transform:DOScale(0.9, MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.OutCubic)

	local on_tween_updata = function ()
		canvas_group.alpha = canvas_group.alpha - UnityEngine.Time.deltaTime / MOVE_TIME
	end

	local on_tween_complete = function ()
		canvas_group.alpha = 0
		self.root_node_transform.transform.localScale = u3dpool.vec3(1, 1, 1)
	end

	return tween, on_tween_updata, on_tween_complete
end

local Tween_MoveTab = {}
function UITween.MoveShowPanel(gameObject, start_pos, tween_time, show_type)
	if Tween_MoveTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local x, y = gameObject.transform.anchoredPosition.x, gameObject.transform.anchoredPosition.y
	gameObject.transform.anchoredPosition = start_pos
	local tween = gameObject.transform:DOAnchorPos(u3dpool.vec3(x, y, 0), TWEEN_TIME)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnComplete(function ()
		Tween_MoveTab[gameObject] = nil
	end)
	Tween_MoveTab[gameObject] = tween
end

local Tween_Model_MoveTab = {}
function UITween.MoveShowModel(gameObject, start_pos, tween_time, show_type)
	if Tween_Model_MoveTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local x, y, z = gameObject.transform.localPosition.x, gameObject.transform.localPosition.y, gameObject.transform.localPosition.z
	start_pos.z = z
	gameObject.transform.localPosition = start_pos
	local tween = gameObject.transform:DOLocalMove(u3dpool.vec3(x, y, z), TWEEN_TIME)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnComplete(function ()
		Tween_Model_MoveTab[gameObject] = nil
	end)
	Tween_Model_MoveTab[gameObject] = tween
end

local Sequence_MoveLoop = {}
local Sequence_childlist = {}
function UITween.MoveLoop(gameObj, start_pos, end_pos, tween_time, show_type)
	if Sequence_MoveLoop[gameObj] then
		return
	end
	Sequence_childlist[gameObj] = {}
	local tween_update = function ()
		if not gameObj.gameObject.activeInHierarchy then
			return
		end
		if Sequence_childlist[gameObj] then
			for k,v in pairs(Sequence_childlist[gameObj]) do
				if nil ~= v and not IsNil(v.gameObject) and v.gameObject.activeInHierarchy then
					v.rect.anchoredPosition3D = gameObj.rect.anchoredPosition3D
				end
				if IsNil(v.gameObject) then
					Sequence_childlist[gameObj][k] = nil
				end
			end
		end
	end
	local TWEEN_TIME = tween_time or 0.5
	gameObj.transform.anchoredPosition = start_pos
	Sequence_MoveLoop[gameObj] = DG.Tweening.DOTween.Sequence()
	local tween_up = gameObj.transform:DOAnchorPos(end_pos, TWEEN_TIME)		-- 到终点
	local tween_down = gameObj.transform:DOAnchorPos(start_pos, TWEEN_TIME)	-- 到起点

	tween_up:SetEase(show_type or DG.Tweening.Ease.InOutSine)
	tween_down:SetEase(show_type or DG.Tweening.Ease.InOutSine)
	Sequence_MoveLoop[gameObj]:OnUpdate(tween_update)
	Sequence_MoveLoop[gameObj]:Append(tween_up)
	Sequence_MoveLoop[gameObj]:Append(tween_down)
	Sequence_MoveLoop[gameObj]:OnComplete(function (tween_update)
		Sequence_MoveLoop[gameObj]:Restart()
	end)

end

function UITween.AddChildMoveLoop(child_obj , copy_obj)
	if nil ~= Sequence_childlist[copy_obj] then
		Sequence_childlist[copy_obj][child_obj] = child_obj
	end
end
function UITween.ReduceChildMoveLoop(child_obj , copy_obj)
	if nil ~= Sequence_childlist[copy_obj] then
		Sequence_childlist[copy_obj][child_obj] = nil
	end
end
function UITween.KillMoveLoop(gameObject)
	if Sequence_MoveLoop[gameObject] then
		Sequence_MoveLoop[gameObject]:Kill()
		Sequence_MoveLoop[gameObject] = nil
		Sequence_childlist[gameObject] = {}
	end
end

local Tween_ScaleTab = {}
function UITween.ScaleShowPanel(gameObject, scale, tween_time, show_type, callback, start_scale)
	if Tween_ScaleTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local x, y = gameObject.transform.localScale.x, gameObject.transform.localScale.y
	gameObject.transform.localScale = scale or u3dpool.vec3(0, 0, 0)
	if start_scale ~= nil then
		x = start_scale.x
		y = start_scale.y
	end

	local tween = gameObject.transform:DOScale(Vector3(x, y, 1), TWEEN_TIME)
	tween:SetEase(show_type or DG.Tweening.Ease.InOutBack)
	tween:OnComplete(function ()
		Tween_ScaleTab[gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_ScaleTab[gameObject] = tween
end

function UITween.KillScaleShow(gameObject)
	if Tween_ScaleTab[gameObject] then
		Tween_ScaleTab[gameObject]:Kill()
		Tween_ScaleTab[gameObject] = nil
		Tween_ScaleTab[gameObject] = {}
	end
end

local Tween_SizeTab = {}
function UITween.SizeShowPanel(gameObject, size, tween_time, show_type, callback)
	if Tween_SizeTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local x, y = gameObject.rect.sizeDelta.x, gameObject.rect.sizeDelta.y
	gameObject.rect.sizeDelta = size or u3dpool.vec2(0, 0)
	local tween = gameObject.transform:DOSizeDelta(u3dpool.vec2(x, y), TWEEN_TIME)
	tween:SetEase(show_type or DG.Tweening.Ease.InOutBack)
	tween:OnComplete(function ()
		Tween_SizeTab[gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_SizeTab[gameObject] = tween
end

function UITween.KillSizeShow(gameObject)
	if Tween_SizeTab[gameObject] then
		Tween_SizeTab[gameObject]:Kill()
		Tween_SizeTab[gameObject] = nil
	end
end

local Tween_AlpahTab = {}
function UITween.AlpahShowPanel(gameObject, is_show, tween_time, show_type, callback ,update_cb)
	if Tween_AlpahTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local obj_transform = gameObject.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	obj_transform.alpha = is_show and 0 or 1
	local tween = is_show and obj_transform:DoAlpha(0, 1, TWEEN_TIME) or obj_transform:DoAlpha(1, 0, TWEEN_TIME)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnUpdate(function ()
		if update_cb then
			update_cb()
		end
	end)
	tween:OnComplete(function ()
		obj_transform.alpha = is_show and 1 or 0
		Tween_AlpahTab[gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_AlpahTab[gameObject] = tween
	return tween
end

function UITween.ReleaseAlpahTabByObj(gameObject)
	if not gameObject then
		return
	end
	Tween_AlpahTab[gameObject] = nil
end

function UITween.KillAlpahTabByObj(gameObject)
	if not gameObject then
		return
	end
	if Tween_AlpahTab[gameObject] then
		Tween_AlpahTab[gameObject]:Kill()
	end
	Tween_AlpahTab[gameObject] = nil
end

local Tween_MoveSceleTab = {}
function UITween.MoveScaleShowPanel(gameObject, start_pos, tween_time, scale, show_type, callback )
	if Tween_MoveSceleTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local x, y = gameObject.transform.anchoredPosition.x, gameObject.transform.anchoredPosition.y
	gameObject.transform.anchoredPosition = start_pos
	gameObject.transform.localScale = u3dpool.vec3.zero
	local tween_scale = gameObject.transform:DOScale(scale or 1, TWEEN_TIME)
	local tween_move = gameObject.transform:DOAnchorPos(u3dpool.vec3(x, y, 0), TWEEN_TIME)
    local tween = DG.Tweening.DOTween.Sequence()
    tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:Join(tween_move)
	tween:OnComplete(function ()
		Tween_MoveSceleTab[gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_MoveSceleTab[gameObject] = tween
end

local Tween_MoveToSceleTab = {}
function UITween.MoveToScaleAndShowPanel(gameObject, start_pos, end_pos, scale, tween_time, show_type, callback)
	if Tween_MoveToSceleTab[gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	local x, y = gameObject.transform.anchoredPosition.x, gameObject.transform.anchoredPosition.y
	gameObject.transform.anchoredPosition = start_pos
	local tween_scale = gameObject.transform:DOScale(scale or 1, TWEEN_TIME)
	local tween = gameObject.transform:DOAnchorPos(end_pos, TWEEN_TIME)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnComplete(function ()
		Tween_MoveToSceleTab[gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_MoveToSceleTab[gameObject] = tween
end



function UITween.DoScrollRectVerticalPosition(scroll_rect, from, to, tween_time, callback,show_type)
	show_type = show_type or DG.Tweening.Ease.Linear
	return scroll_rect.scroll_rect:DoVerticalPosition(from, to, tween_time, callback):SetEase(show_type)
end

function UITween.DoScrollRectHorizontalPosition(scroll_rect, from, to, tween_time, callback,show_type)
	show_type = show_type or DG.Tweening.Ease.Linear
	return scroll_rect.scroll_rect:DoHorizontalPosition(from, to, tween_time, callback):SetEase(show_type)
end

--数字变化
function UITween.DONumberTo(text, from, to, tween_time, update_fun, complete_fun)
	if nil ~= rawget(getmetatable(text), "DoFloatNumberTo") then
		text:DoFloatNumberTo(from, to, tween_time, complete_fun, update_fun)
	else
		text:DoNumberTo(from, to, tween_time, complete_fun)
	end
end

-- 部分界面动效(从上往下砸效果)
function UITween.DoUpDownCrashTween(obj, tween_info, callback)
	tween_info = tween_info or {}
	local show_type = tween_info.show_type or DG.Tweening.Ease.OutBack
	local scale_time = tween_info.scale_time or 0.5

	obj.transform.localScale = tween_info.start_scale or u3dpool.vec3(2,2,2)
	local tween = obj.transform:DOScale(Vector3(1,1,1), scale_time)
	tween:SetEase(show_type)
	tween:OnComplete(function ()
		if callback then
			callback()
		end
	end)

	return tween
end

-- 部分界面动效(Duang一下渐显弹出效果)
local TWEEN_DUANG_OPEN = {}
function UITween.DoViewDuangOpenTween(obj_1, obj_2, tween_info, callback)
	if TWEEN_DUANG_OPEN[obj_1] then
		return
	end

	tween_info = tween_info or {}
	local show_tweener = DG.Tweening.DOTween.Sequence()
	obj_1.transform.localScale = u3dpool.vec3(0.8, 0.8, 0.8)

	if obj_2 then
		obj_2.transform.localScale = u3dpool.vec3(0.6, 0.6, 0.6)
	end

	show_tweener:Append(obj_1.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	if obj_2 then
		show_tweener:Join(obj_2.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	end

	if obj_1.canvas_group then
		show_tweener:Join(obj_1.canvas_group:DoAlpha(0.6, 1, 0.3))
	end
	show_tweener:Append(obj_1.rect:DOScale(Vector3(1, 1, 1), 0.05))
	if obj_2 then
		show_tweener:Join(obj_2.rect:DOScale(Vector3(1, 1, 1), 0.05))
	end
	show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	show_tweener:OnComplete(function()
		TWEEN_DUANG_OPEN[obj_1] = nil
		if callback then
			callback()
		end
	end)

	TWEEN_DUANG_OPEN[obj_1] = show_tweener
end

-- 部分界面动效(Duang一下渐隐消失效果)
local TWEEN_DUANG_CLOSE = {}
function UITween.DoViewDuangCloseTween(obj, tween_info, callback)
	if not obj or TWEEN_DUANG_CLOSE[obj] then
		return
	end

	tween_info = tween_info or {}
	local show_tweener = DG.Tweening.DOTween.Sequence()
	show_tweener:Append(obj.canvas_group:DoAlpha(1, 0, 0.5))
	show_tweener:SetEase(DG.Tweening.Ease.Linear)
	show_tweener:OnComplete(function()
		TWEEN_DUANG_CLOSE[obj] = nil
		if callback then
			callback()
		end
	end)

	TWEEN_DUANG_CLOSE[obj] = show_tweener
end

-- 宝箱摇摆
--左右摆动 interval_time 摆动间隔时间 默认 2
function UITween.ShakeAnimi(trans, sequence, interval_time)
	interval_time = interval_time or 2
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 8), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -16), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 16), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -14), 0.1, DG.Tweening.RotateMode.Fast))
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 14), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -14), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 10), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -5), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.1, DG.Tweening.RotateMode.Fast))
    sequence:AppendInterval(interval_time)
    sequence:SetEase(DG.Tweening.Ease.Linear)
    sequence:SetLoops(-1)
end

-- 铃铛摇摆
function UITween.ShakeNodeAnimi(trans, sequence, not_is_loop)
	sequence = sequence or DG.Tweening.DOTween.Sequence()
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 1, DG.Tweening.RotateMode.Fast)) 		--恢复0
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -30), 0.1, DG.Tweening.RotateMode.Fast)) 	--左50
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 25), 0.2, DG.Tweening.RotateMode.Fast))	--右40
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -20), 0.2, DG.Tweening.RotateMode.Fast))	--左30
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 15), 0.2, DG.Tweening.RotateMode.Fast))	--右20
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, -10), 0.2, DG.Tweening.RotateMode.Fast))	--左10
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 5), 0.2, DG.Tweening.RotateMode.Fast))	--右5
	sequence:Append(trans:DOLocalRotate(u3dpool.vec3(0, 0, 0), 0.1, DG.Tweening.RotateMode.Fast))	--恢复 0
    sequence:SetEase(DG.Tweening.Ease.Linear)
	
	if not not_is_loop then
		sequence:SetLoops(1)
	end
end

local Sequence_ScaleLoop = {}
function UITween.ScaleLoop(gameObj, small_scale, big_scale, tween_time, show_type)
	if not gameObj then return end
	if Sequence_ScaleLoop[gameObj] then
		return
	end
	local TWEEN_TIME = tween_time or 1
	gameObj.transform.localScale = u3dpool.vec3(small_scale or 1, small_scale or 1, small_scale or 1)
	Sequence_ScaleLoop[gameObj] = DG.Tweening.DOTween.Sequence()
	local tween_big = gameObj.transform:DOScale(big_scale or 1.1, TWEEN_TIME)-- 放大
	local tween_small = gameObj.transform:DOScale(small_scale or 1, TWEEN_TIME)	-- 缩小

	tween_big:SetEase(show_type or DG.Tweening.Ease.InOutSine)
	tween_small:SetEase(show_type or DG.Tweening.Ease.InOutSine)
	Sequence_ScaleLoop[gameObj]:Append(tween_big)
	Sequence_ScaleLoop[gameObj]:Append(tween_small)
	Sequence_ScaleLoop[gameObj]:OnComplete(function()
		Sequence_ScaleLoop[gameObj]:Restart()
	end)
end

function UITween.KillScaleLoop(gameObject, is_reset_scale, vector_scale)
	if not gameObject then return end
	if Sequence_ScaleLoop[gameObject] then
		Sequence_ScaleLoop[gameObject]:Kill()
		Sequence_ScaleLoop[gameObject] = nil
		if is_reset_scale then
			vector_scale = vector_scale or u3dpool.vec3(1, 1, 1)
			gameObject.transform.localScale = vector_scale
		end
	end
end

--=============================================================================
function UITween.CleanAllTween(view_name)
    UITween.CleanAlphaShow(view_name)
    UITween.CleanAllMoveToShowPanel(view_name)
	UITween.CleanMoveAlphaShow(view_name)
    UITween.CleanRotateAlphaShow(view_name)
    UITween.CleanScaleShow(view_name)
	UITween.CleanScaleAlaphaShow(view_name)
end

function UITween.CanvasGroup(gameObject)
	return gameObject.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
end

-- 假隐藏
function UITween.FakeHideShow(gameObject, alpha)
	if gameObject then
		local obj_canvas_group = UITween.CanvasGroup(gameObject)
		obj_canvas_group.alpha = alpha or 0
		obj_canvas_group.interactable = false
		obj_canvas_group.blocksRaycasts = false
	end
end

function UITween.FakeToShow(gameObject)
	if gameObject then
		local obj_canvas_group = UITween.CanvasGroup(gameObject)
		obj_canvas_group.alpha = 1
		obj_canvas_group.interactable = true
		obj_canvas_group.blocksRaycasts = true
	end
end

-- 图片填充动画
function UITween.ImgFillDoValue(gameObject, from, to, tween_time)
	if gameObject then
		gameObject.image.fillAmount = from or 0
		gameObject.image:DOFillAmount(to or 1, tween_time)
	end
end

-- 渐显动画
local Tween_AlphaShow = {}
function UITween.CleanAlphaShow(view_name)
	if not IsEmptyTable(Tween_AlphaShow[view_name]) then
		for k, v in pairs(Tween_AlphaShow[view_name]) do
			v:Kill()
		end
		Tween_AlphaShow[view_name] = {}
	end

end

function UITween.AlphaShow(view_name, gameObject, from_alpha, to_alpha, tween_time, show_type, complete_callback, update_callback)
	if Tween_AlphaShow[view_name] == nil then
		Tween_AlphaShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_AlphaShow[view_name][gameObject] then
		return
	end

	from_alpha = from_alpha or 0
	to_alpha = to_alpha or 1
	tween_time = tween_time or 0.5
	local obj_transform = UITween.CanvasGroup(gameObject)
	obj_transform.alpha = from_alpha
	local tween = obj_transform:DoAlpha(from_alpha, to_alpha, tween_time)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnUpdate(function ()
		if update_callback then
			update_callback()
		end
	end)

	tween:OnComplete(function ()
		Tween_AlphaShow[view_name][gameObject] = nil
		obj_transform.alpha = to_alpha
		obj_transform.interactable = true
		obj_transform.blocksRaycasts = true
		if complete_callback then
			complete_callback()
		end
	end)

	Tween_AlphaShow[view_name][gameObject] = tween
	return tween
end

-- 移动 + 渐显
-- tween_info = {FromAlpha = , ToAlpha = , StartPosition = u3dpool.vec2(), EndPosition = u3dpool.vec2(),
--				AlphaTweenTime = , MoveTweenTime = , AlphaTweenType = , MoveTweenType = ,}
local Tween_MoveAlphaShow = {}
function UITween.CleanMoveAlphaShow(view_name)
	if not IsEmptyTable(Tween_MoveAlphaShow[view_name]) then
		for k, v in pairs(Tween_MoveAlphaShow[view_name]) do
			v:Kill()
		end
		Tween_MoveAlphaShow[view_name] = {}
	end
	Tween_MoveAlphaShow = {}
end

function UITween.KillMoveAlphaTween(view_name,gameObject)
	if IsNil(gameObject) then
		return
	end

	if Tween_MoveAlphaShow[view_name] and Tween_MoveAlphaShow[view_name][gameObject] then
		Tween_MoveAlphaShow[view_name][gameObject]:Kill()
		Tween_MoveAlphaShow[view_name][gameObject] = nil
	end
end

function UITween.MoveAlphaShow(view_name, gameObject, tween_info, complete_callback, update_callback)
	if Tween_MoveAlphaShow[view_name] == nil then
		Tween_MoveAlphaShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_MoveAlphaShow[view_name][gameObject] then
		return
	end

	tween_info = tween_info or {}
	local from_alpha = tween_info.FromAlpha or 0
	local to_alpha = tween_info.ToAlpha or 1
	local start_position = tween_info.StartPosition or u3dpool.vec2(gameObject.rect.anchoredPosition.x, gameObject.rect.anchoredPosition.y)
	local end_position = tween_info.EndPosition or u3dpool.vec2(0, 0)
	local alpha_tween_type = tween_info.AlphaTweenType or DG.Tweening.Ease.OutCubic
	local move_tween_type = tween_info.MoveTweenType or DG.Tweening.Ease.Linear
	local alpha_tween_time = tween_info.AlphaTweenTime or 0.6
	local move_tween_time = tween_info.MoveTweenTime or alpha_tween_time
	gameObject.transform.anchoredPosition = start_position
	local tween_move = gameObject.transform:DOAnchorPos(end_position, move_tween_time):SetEase(move_tween_type)
	local tween_alpha = UITween.AlphaShow(view_name,gameObject, from_alpha, to_alpha, alpha_tween_time, alpha_tween_type)

	local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_move)
	tween:Join(tween_alpha)
	tween:OnUpdate(function ()
		if update_callback then
			update_callback()
		end
	end)

	tween:OnComplete(function ()
		Tween_MoveAlphaShow[view_name][gameObject] = nil
		if complete_callback then
			complete_callback()
		end
	end)
	Tween_MoveAlphaShow[view_name][gameObject] = tween
	return tween
end

function UITween.MoveAlphaShowPos(view_name,gameObject, start_pos, end_pos, tween_time, show_type, callback, replace)
	if Tween_MoveAlphaShow[view_name] == nil then
		Tween_MoveAlphaShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_MoveAlphaShow[view_name][gameObject] then
		return
	end

	if Tween_MoveAlphaShow[view_name] and Tween_MoveAlphaShow[view_name][gameObject] then
		if not replace then
			return
		end
		Tween_MoveAlphaShow[view_name][gameObject]:Kill()
		Tween_MoveAlphaShow[view_name][gameObject] = nil
	end

	local TWEEN_TIME = tween_time or 0.5
	gameObject.transform.anchoredPosition = start_pos
	local canvas_group = gameObject.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local tween_alpah = canvas_group:DoAlpha(0, 1, TWEEN_TIME)
	local tween_move = gameObject.transform:DOAnchorPos(end_pos, TWEEN_TIME)
	local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_alpah)
	tween:Join(tween_move)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnComplete(function ()
		Tween_MoveAlphaShow[view_name][gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_MoveAlphaShow[view_name][gameObject] = tween
end

function UITween.MoveAlphaShowPanelByEndPos(view_name,gameObject, start_pos, end_pos, tween_time, show_type, callback)
	if Tween_MoveAlphaShow[view_name] == nil then
		Tween_MoveAlphaShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_MoveAlphaShow[view_name][gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	-- local x, y = gameObject.transform.anchoredPosition.x, gameObject.transform.anchoredPosition.y
	gameObject.transform.anchoredPosition = start_pos
	local canvas_group = gameObject.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	local tween_alpah = canvas_group:DoAlpha(0, 1, TWEEN_TIME)
	local tween_move = gameObject.transform:DOAnchorPos(end_pos, TWEEN_TIME)
	local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_alpah)
	tween:Join(tween_move)
	tween:SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnComplete(function ()
		Tween_MoveAlphaShow[view_name][gameObject] = nil
		if callback then
			callback()
		end
	end)
	Tween_MoveAlphaShow[view_name][gameObject] = tween
end

-- （翻牌）对象旋转 + 渐显
-- tween_info = {FromAlpha = , ToAlpha = , StartRotatetion = u3dpool.vec3(), EndRotatetion = u3dpool.vec3(),
--				AlphaTweenTime = , RotateTweenTime = , AlphaTweenType = , RotateTweenType = ,}
local Tween_RotateAlphaShow = {}
function UITween.CleanRotateAlphaShow(view_name)
	if not IsEmptyTable(Tween_RotateAlphaShow[view_name]) then
		for k, v in pairs(Tween_RotateAlphaShow[view_name]) do
			v:Kill()
		end
		Tween_RotateAlphaShow[view_name] = {}
	end
end

function UITween.RotateAlphaShow(view_name, gameObject, tween_info, complete_callback, update_callback)
	if Tween_RotateAlphaShow[view_name] == nil then
		Tween_RotateAlphaShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_RotateAlphaShow[view_name][gameObject] then
		return
	end

	tween_info = tween_info or {}
	local from_alpha = tween_info.FromAlpha or 0
	local to_alpha = tween_info.ToAlpha or 1
	local start_rotation = tween_info.StartRotatetion or u3dpool.vec3(45, 0, 0)
	local end_rotation = tween_info.EndRotatetion or u3dpool.vec3(0, 0, 0)
	local alpha_tween_type = tween_info.AlphaTweenType or DG.Tweening.Ease.OutCubic
	local rotate_tween_type = tween_info.RotateTweenType or DG.Tweening.Ease.Linear
	local alpha_tween_time = tween_info.AlphaTweenTime or 0.6
	local rotate_tween_time = tween_info.RotateTweenTime or 0.6
	gameObject.transform.localRotation = Quaternion.Euler(start_rotation.x, start_rotation.y, start_rotation.z)
	local tween_rotate = gameObject.transform:DOLocalRotate(end_rotation, rotate_tween_time)
	local tween_alpha = UITween.AlphaShow(view_name,gameObject, from_alpha, to_alpha, alpha_tween_time, alpha_tween_type)

	local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_rotate)
	tween:Join(tween_alpha)
	tween:SetEase(rotate_tween_type)
	tween:OnUpdate(function ()
		if update_callback then
			update_callback()
		end
	end)

	tween:OnComplete(function ()
		Tween_RotateAlphaShow[view_name][gameObject] = nil
		if complete_callback then
			complete_callback()
		end
	end)
	Tween_RotateAlphaShow[view_name][gameObject] = tween
end

--移动
local Tween_MoveToTab = {}
function UITween.CleanAllMoveToShowPanel(view_name)
	if not IsEmptyTable(Tween_MoveToTab[view_name]) then
		for k, v in pairs(Tween_MoveToTab[view_name]) do
			v:Kill()
		end
		Tween_MoveToTab[view_name] = {}
	end
end

function UITween.MoveToShowPanel(view_name, gameObject, start_pos, end_pos, tween_time, show_type, callback)
	if Tween_MoveToTab[view_name] == nil then
		Tween_MoveToTab[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_MoveToTab[view_name][gameObject] then
		return
	end

	local TWEEN_TIME = tween_time or 0.5
	gameObject.transform.anchoredPosition = start_pos
	local tween = gameObject.transform:DOAnchorPos(end_pos, TWEEN_TIME):SetEase(show_type or DG.Tweening.Ease.OutCubic)
	tween:OnComplete(function ()
		Tween_MoveToTab[view_name][gameObject] = nil
		if callback then
			callback()
		end
	end)
    Tween_MoveToTab[view_name][gameObject] = tween
    return tween
end

local Tween_ScaleShow = {}
function UITween.CleanScaleShow(view_name)
	if not IsEmptyTable(Tween_ScaleShow[view_name]) then
		for k, v in pairs(Tween_ScaleShow[view_name]) do
			v:Kill()
		end
		Tween_ScaleShow[view_name] = {}
	end
end

-- 对象移动 + 缩放
-- tween_info = {FromScale = , ToScale = , StartPosition = u3dpool.vec2(), EndPosition = u3dpool.vec2(),
--				ScaleTweenTime = , MoveTweenTime = , ScaleTweenType = , MoveTweenType = ,}
function UITween.DoScaleMoveShow(view_name, gameObject, tween_info, complete_callback, update_callback)
	if Tween_ScaleShow[view_name] == nil then
		Tween_ScaleShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_ScaleShow[view_name][gameObject] then
		return
    end

    local x, y = gameObject.transform.localScale.x, gameObject.transform.localScale.y
    tween_info = tween_info or {}
	local from_scale = tween_info.FromScale or u3dpool.vec3(x, y, 1)
    local to_scale = tween_info.ToScale or u3dpool.vec3(x, y, 1)
    local start_position = tween_info.StartPosition or u3dpool.vec2(gameObject.rect.anchoredPosition.x, gameObject.rect.anchoredPosition.y)
    local end_position = tween_info.EndPosition or u3dpool.vec2(gameObject.rect.anchoredPosition.x, gameObject.rect.anchoredPosition.y)
	local scale_tween_type = tween_info.ScaleTweenType or DG.Tweening.Ease.OutCubic
	local move_tween_type = tween_info.MoveTweenType or DG.Tweening.Ease.Linear
	local scale_tween_time = tween_info.ScaleTweenTime or 0.6
    local move_tween_time = tween_info.MoveTweenTime or 0.6
    gameObject.transform.localScale = from_scale
    local tween_move = UITween.MoveToShowPanel(view_name,gameObject, start_position, end_position, move_tween_time, move_tween_type, complete_callback)
    local tween_scale = gameObject.transform:DOScale(to_scale, scale_tween_time)
    tween_scale:SetEase(scale_tween_type)

    local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(tween_move)
	tween:Join(tween_scale)
    tween:OnUpdate(function ()
		if update_callback then
			update_callback()
		end
	end)
	tween:OnComplete(function ()
		if complete_callback then
			complete_callback()
		end
	end)
    Tween_ScaleShow[view_name][gameObject] = tween
    return tween
end

local Tween_ScaleAlaphShow = {}
function UITween.CleanScaleAlaphaShow(view_name)
	if not IsEmptyTable(Tween_ScaleAlaphShow[view_name]) then
		for k, v in pairs(Tween_ScaleAlaphShow[view_name]) do
			v:Kill()
		end
		Tween_ScaleAlaphShow[view_name] = {}
	end
end

-- 缩放 + 渐显
function UITween.DoScaleAlaphaShow(view_name, gameObject, tween_info, complete_callback, update_callback)
	if Tween_ScaleAlaphShow[view_name] == nil then
		Tween_ScaleAlaphShow[view_name] = {}
	end

	if view_name == nil or gameObject == nil or Tween_ScaleAlaphShow[view_name][gameObject] then
		return
	end

	local x, y, z = gameObject.transform.localScale.x, gameObject.transform.localScale.y, gameObject.transform.localScale.z
	tween_info = tween_info or {}
	local from_scale = tween_info.FromScale or u3dpool.vec3(x, y, z)
	local to_scale = tween_info.ToScale or u3dpool.vec3(x, y, z)
	gameObject.transform.localScale = from_scale
	local scale_tween_time = tween_info.ScaleTweenTime or 0.6
	local scale_tween_type = tween_info.ScaleTweenType or DG.Tweening.Ease.OutCubic
	local tween_scale = gameObject.transform:DOScale(to_scale, scale_tween_time)

	local from_alpha = tween_info.FromAlpha or 0
	local to_alpha = tween_info.ToAlpha or 1
	local alpha_tween_time = tween_info.AlphaTweenTime or 0.6
	local alpha_tween_type = tween_info.AlphaTweenType or DG.Tweening.Ease.OutCubic
	local tween_alpha = UITween.AlphaShow(view_name, gameObject, from_alpha, to_alpha, alpha_tween_time, alpha_tween_type)

	local tween = DG.Tweening.DOTween.Sequence()

	tween:Append(tween_scale)
	tween:Join(tween_alpha)
	tween:SetEase(scale_tween_type)
	tween:OnUpdate(function ()
		if update_callback then
			update_callback()
		end
	end)

	tween:OnComplete(function ()
		if complete_callback then
			complete_callback()
		end
	end)

	Tween_ScaleAlaphShow[view_name][gameObject] = tween
	return tween
end


function UITween.ShowFadeUpToObj(gameObject, pos, start_pos)
	local MOVE_TIME = 0.3
	local MOVE_DISTANCE = 25
	-- local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	-- canvas_group.alpha = 0
	local transform = gameObject.transform
	local x, y = transform.anchoredPosition.x, transform.anchoredPosition.y
	if pos ~= nil then
		x = pos.x
		y = pos.y
	end

	transform.anchoredPosition = start_pos or u3dpool.vec3(0, 0, 0)

	local tween = transform:DOAnchorPos(u3dpool.vec3(x, y, 0), MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.OutCubic)

	-- local on_tween_updata = function ()
		-- canvas_group.alpha = canvas_group.alpha + UnityEngine.Time.deltaTime / MOVE_TIME
	-- end

	local on_tween_complete = function ()
		-- canvas_group.alpha = 1
		if not IsNil(transform) then
			transform.anchoredPosition = u3dpool.vec3(x, y, 0)
		end
	end

	-- tween:OnUpdate(on_tween_updata)
	tween:OnComplete(on_tween_complete)

	return tween
end

function UITween.HideFadeUpToObj(gameObject, pos, end_pos)
	local MOVE_TIME = 0.3
	-- local canvas_group = self.root_node_transform:GetComponent(typeof(UnityEngine.CanvasGroup))
	-- canvas_group.alpha = 1
	local transform = gameObject.transform
	local x, y = transform.anchoredPosition.x, transform.anchoredPosition.y
	if pos ~= nil then
		x = pos.x
		y = pos.y
	end

	transform.anchoredPosition = u3dpool.vec3(x, y, 0)

	local tween = transform:DOAnchorPos(end_pos or u3dpool.vec3(0, 0, 0), MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.Linear)

	-- local on_tween_updata = function ()
	-- 	if not IsNil(canvas_group) then
	-- 		canvas_group.alpha = canvas_group.alpha - UnityEngine.Time.deltaTime / MOVE_TIME
	-- 	end
	-- end

	local on_tween_complete = function ()
		-- if not IsNil(canvas_group) then
		-- 	canvas_group.alpha = 1
		-- end

		if not IsNil(transform) then
			transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
		end
	end
	return tween, on_tween_complete
end

function UITween.ShowCommonTiaoZhanJieSuanPanelTween(view, is_suc, root_alpha_node, call_back)
	if nil == view or nil == is_suc or IsEmptyTable(view.node_list) then
		return
	end

	if root_alpha_node and root_alpha_node.canvas_group then
		root_alpha_node.canvas_group.alpha = 0
	end

	local win_sign_str = "win_"
	local lose_sign_str = "lose_"
	local target_sign_str = is_suc and win_sign_str or lose_sign_str
	local hide_sign_str = is_suc and lose_sign_str or win_sign_str

	local panel = view.node_list[target_sign_str .. "panel"]
	local hide_panel = view.node_list[hide_sign_str .. "panel"]
	local title_bg = view.node_list[target_sign_str .. "title_bg"]
	local titlt = view.node_list[target_sign_str .. "title"]
	local bg = view.node_list[target_sign_str .. "bg"]
	local title_text = view.node_list[target_sign_str .. "title_text"]

	if nil == panel or nil == hide_panel or nil == title_bg or nil == titlt or nil == bg or nil == title_text then
		return 
	end

	hide_panel:SetActive(false)
	panel:SetActive(true)
	title_bg.transform.localScale = Vector3(0, 0, 0)
	title_bg.transform.anchoredPosition = Vector2(0, 17)
	titlt.transform.localScale = Vector3(0, 0, 0)
	title_text:SetActive(false)
	bg.transform.anchoredPosition = Vector2(-243, 24)
	bg.rect.sizeDelta = u3dpool.vec2(0, 296)

	local tween = DG.Tweening.DOTween.Sequence()
	tween:Append(title_bg.transform:DOScale(Vector3(1, 1, 1), 0.2):SetEase(DG.Tweening.Ease.OutExpo))
	tween:Append(titlt.transform:DOScale(Vector3(1, 1, 1), 0.2):SetEase(DG.Tweening.Ease.OutBack))
	tween:Append(title_bg.rect:DOAnchorPosX(-240, 0.15):SetEase(DG.Tweening.Ease.OutBounce):SetDelay(0.2))
	tween:Join(bg.rect:DOSizeDelta(Vector2(700, 296), 0.15):SetEase(DG.Tweening.Ease.OutBounce))
	tween:Join(bg.rect:DOAnchorPosX(106, 0.15):SetEase(DG.Tweening.Ease.OutBounce))

	tween:OnComplete(function ()
		title_text:SetActive(true)
		if root_alpha_node and root_alpha_node.canvas_group then
			root_alpha_node.canvas_group.alpha = 1
		end

		if call_back then
			call_back()
		end
	end)
end
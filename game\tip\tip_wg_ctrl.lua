require("game/tip/base_tip")
require("game/tip/base_tip_render")
require("game/tip/item_tip")
require("game/tip/display_item_tip")
require("game/tip/item_tip_goods")
require("game/tip/role_equip_tip")
require("game/tip/shop_tip")

require("game/tip/bless_tip")
require("game/tip/tip_wg_data")
require("game/tip/tips_system_view")
require("game/tip/zc_tips_system_view")
require("game/tip/tips_system_manager")
require("game/tip/zc_tips_system_manager")
require("game/tip/tips_system_notice_view")
require("game/tip/system_scroll_view")
require("game/tip/system_beast_equip_scroll_view")

require("game/tip/tips_speaker")
require("game/tip/tips_number_show_view")
require("game/tip/tips_number_show_manager")
require("game/tip/tips_getitem_show_view")
require("game/tip/tips_get_new_skill_view")
require("game/tip/tips_open_fun_fly_view")
require("game/tip/tips_func_trailer_view")
require("game/tip/alert_icon")
require("game/tip/tips_get_reward1")
require("game/tip/tips_get_reward2")
require("game/tip/tips_get_reward3")
require("game/tip/tips_get_reward4")
require("game/tip/tips_get_boss_reward")
require("game/mainui/buff_tip")
require("game/tip/tips_enter_common_scene_view")
require("game/tip/tips_show_chapter_view")
require("game/tip/tips_show_storytext_view")
require("game/tip/act_fireworks_reward")
require("game/tip/tips_floating_manager")
require("game/tip/tips_floating_view")
require("game/login/reconnect_view")
require("game/tip/zc_run_msg_right_view")
require("game/tip/zc_run_msg_center_view")
require("game/tip/equip_getway_view")
require("game/tip/fly_effect_view")
require("game/tip/tips_open_view")
require("game/tip/second_confirmation_view")
require("game/tip/common_effect_view")
require("game/serveractivity/act_subview/exchange_panel")
require("gameui/view/common_input_view")
require("game/eternal_night/eternal_night_item_tip")
require("game/tip/tip_mount_pet_equip_compare_view")
require("game/tip/tips_easy_fly_view")
require("game/tip/drop_equip_show_tip")
require("game/tip/tips_bagua_compare_view")
require("game/tip/play_srceen_eff_view")
require("game/tip/item_cell_fly_view")
require("game/tip/tips_bagua_unlock_view")
require("game/tip/tips_exit_game_view")
require("game/tip/tips_auto_open_gfit_view")
require("game/tip/tips_img_system_manager")
require("game/tip/tip_img_system_view")
require("game/tip/tips_gift_view")
require("game/tip/common_draw_result_view")
require("game/tip/tip_gailv_show_view")
require("game/tip/tips_reward_display")
require("game/tip/tips_attr_view")
require("game/tip/tips_common_reward_record_view")
require("game/tip/tips_common_draw_reward_view")
require("game/tip/tips_common_reward_pro_view")
require("game/tip/tips_common_reward_pro_group_view")
require("game/tip/tips_common_buy_item_view")
require("game/tip/tips_common_custom_buy_item_view")
require("game/tip/common_draw_record_view")
require("game/tip/tip_suit_special_item_view")
require("game/tip/common_fight_start_tips")
require("game/tip/sp_display_item_tip")
require("game/tip/tip_common_use_view")
require("game/tip/tips_big_reward_show_view")
require("game/tip/common_skill_pre_view")
require("game/tip/tips_no_item_view")

TipWGCtrl = TipWGCtrl or BaseClass(BaseWGCtrl)

function TipWGCtrl:__init()
	if TipWGCtrl.Instance ~= nil then
		error("[TipWGCtrl] attempt to create singleton twice!")
		return
	end
	TipWGCtrl.Instance = self

	self.item_tip = ItemTip.New(GuideModuleName.ItemTipView)
	self.item_tip_goods = ItemTipGoods.New(GuideModuleName.ItemGoodsTipView)
	self.tip_data = TipWGData.New()
	self.role_equip_tip = RoleEquipTip.New()
	-- self.contrast_item_tip = RoleEquipTip.New()
	self.display_item_tip = DisplayItemTip.New(GuideModuleName.DisplayItemTipView)
	self.shop_tip = ShopTip.New()
	-- self.desc_tip = DescTip.New()
	self.rule_tip = RuleTip.New()
	self.tab_rule_tip = TabRuleTip.New()
	self.rank_tip = RankBtnTip.New()
	self.buff_tip = BuffTip.New()
	-- self.attr_tip = AttrTip.New()
	self.bless_tip = BlessTip.New()
	self.pop_num_view = NumKeypad.New(GuideModuleName.NumKeypad)
	self.special_num_view = SpecialKeypad.New(GuideModuleName.SpecialKeypadView)
	self.alert_tips = Alert.New(GuideModuleName.Alert)
	self.confirm_alert_tips = Alert.New(GuideModuleName.ConfirmAlert)
	self.confirm_alert_tips:SetUseOneSign(true)
	self.alertIcon_tips = AlertIcon.New()
	self.enter_common_scene_view = TipsEneterCommonSceneView.New(GuideModuleName.TipsEneterCommonSceneView)
	self.show_chapter_view = TipsShowChapterView.New(GuideModuleName.TipsShowChapterView)
	self.tips_show_storytext_view = TipsShowStoryTextView.New(GuideModuleName.TipsShowStoryTextView)

	self.system_notice_view = TipSystemNoticeView.New()
	self.system_scroll_view = TipSystemScrollView.New()
	self.system_beast_equip_scroll_view = TipSystemBeastEquipScrollView.New()

	self.tip_speaker = TipSpeakerView.New()
	self.tips_number_show_manager = TipsNumberShowManager.New()
	self.tips_getitem_show_view = TipsGetItemShowView.New()
	self.get_new_skill_view = TipsGetNewSkillView.New()
	self.tips_func_trailer_view = TipsFuncTrailerView.New()
	self.tips_open_fun_fly_view = TipOpenFunctionFlyView.New(GuideModuleName.OpenFunFlyView)
	self.tips_easy_fly_view = TipsEasyFlyView.New()
	self.tips_get_reward_view = TipsGetRewardView.New(GuideModuleName.TipsGetRewardView)
	self.tips_get_best_reward_view = TipsGetBestRewardView.New()
	self.tips_get_common_reward_view = TipsGetCommonRewardView.New()
	self.tip_get_value_reward_view = TipsGetValueRewardView.New()
	self.tips_gailv_show_view = TipsGaiLvCommonShowView.New()
	self.tips_get_common_boss_reward_view = TipsGetCommonBossRewardView.New()
	self.tips_big_reward_show_view = TipsBigRewardShowView.New()

	self.zc_run_msg_right = ZCRunMsgRightView.New()
	self.zc_run_msg_center = ZCRunMsgCenterView.New()

	self.fire_work_reward_view = ActYanHuaRewardView.New()
	self.disconnect_tips = ReconnectView.New(GuideModuleName.ReconnectView)
	self.equip_getway_view = EquipGetWayView.New()
	self.tips_open_view = TipsOpenView.New()

    self.common_effect_view = CommonEffectView.New()

	self.second_confirmation_view = SecondConfirmationView.New()

	self.exchange_panel = ExChangePanelView.New()
	self.common_input_view = CommonInputView.New()
	self.eternal_night_item_tip = EternalNightItemTip.New()
	self.drop_equip_show_tip = DropEquipShowTips.New()
    self.bagua_compare_tip = BaGuaContrastTip.New()
    self.mount_pet_equip_compare_tip = MountPetEquipCompareTip.New()
	self.play_srceen_eff_view = PlaySrceenEffView.New()
	self.bagua_unlock_view = TipsBaGuaUnLockView.New()
	self.item_cell_fly_view = ItemCellFlyView.New()
	self.exit_game_tip = TipsExitGameView.New()
	self.auto_gift_view = TipsAutoOpenGiftView.New()
	self.check_box_tip = Alert.New()
	self.tips_img_system_manager = TipsImgSystemManager.New()
	self.tips_gift_view = TipsGiftView.New()
	self.tips_draw_result_view = TipsCommonDrawResult.New()
	self.tips_reward_display_view = TipsRewardDisplayView.New()
	self.tips_attr_view = TipsAttrView.New()
	self.tips_reward_record_view = TipsRewardRecordView.New()
	self.tips_draw_reward_view = TipsDrawRewardView.New()
	-- 通用概率界面
	self.tips_reward_pro_view = TipsRewardProView.New()
	self.tips_reward_pro_group_view = TipsCommonRewardProGroupView.New()
	--
	self.tips_buy_item_view = TipsBuyItemView.New()
	self.tips_custom_buy_item_view = TipsCustomBuyItemView.New()
	self.tip_suit_special_item_view = TipSuitSpecialItemView.New()
	self.common_draw_record_view = TipsCommonDrawRecordView.New()
	self.common_fight_start_tip = CommonFightStartTips.New()
	self.sp_display_item_tip = SPDisplayItemTip.New()
	self.tip_common_use_view = TipCommonUseView.New()
	self.common_skill_pre_view = CommonSkillPreView.New()
	self.tips_no_item_view = TipsNoItemView.New(GuideModuleName.TipsNoItemView)

	self.fly_effect_list = {}
	self.icon_fly_cacha = {}
	self.fly_effect_pool = {}
	self.fly_effect_num = {}
	self.check_fly_log = {}
	self.tips_gift_mark_list = {}

	self.item_tips_wait_open_cache = {}
end

function TipWGCtrl:__delete()
	TipWGCtrl.Instance = nil

	self.tips_open_view:DeleteMe()
	if self.item_tip then
		self.item_tip:DeleteMe()
		self.item_tip = nil
	end

	if self.item_cell_fly_view then
		self.item_cell_fly_view:DeleteMe()
		self.item_cell_fly_view = nil
	end

	if self.tips_func_trailer_view then
		self.tips_func_trailer_view:DeleteMe()
		self.tips_func_trailer_view = nil
	end

	if self.tip_speaker then
		self.tip_speaker:DeleteMe()
		self.tip_speaker = nil
	end

	if self.tips_get_best_reward_view then
		self.tips_get_best_reward_view:DeleteMe()
		self.tips_get_best_reward_view = nil
	end

	if self.item_tip_goods then
		self.item_tip_goods:DeleteMe()
		self.item_tip_goods = nil
	end

	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end

	if self.confirm_alert_tips then
		self.confirm_alert_tips:DeleteMe()
		self.confirm_alert_tips = nil
	end

	if self.alertIcon_tips then
		self.alertIcon_tips:DeleteMe()
		self.alertIcon_tips = nil
	end

	if self.tips_getitem_show_view then
		self.tips_getitem_show_view:DeleteMe()
		self.tips_getitem_show_view = nil
	end

	if self.tip_data then
		self.tip_data:DeleteMe()
		self.tip_data = nil
	end

	if self.role_equip_tip then
		self.role_equip_tip:DeleteMe()
		self.role_equip_tip = nil
	end

	if self.display_item_tip then
		self.display_item_tip:DeleteMe()
		self.display_item_tip = nil
	end

	if self.shop_tip then
		self.shop_tip:DeleteMe()
		self.shop_tip = nil
	end

	if self.rank_tip then
		self.rank_tip:DeleteMe()
		self.rank_tip = nil
	end

	if self.get_new_skill_view then
		self.get_new_skill_view:DeleteMe()
		self.get_new_skill_view = nil
	end

	if self.buff_tip then
		self.buff_tip:DeleteMe()
		self.buff_tip = nil
	end

    if self.common_effect_view then
        self.common_effect_view:DeleteMe()
        self.common_effect_view = nil
    end

	if self.rule_tip then
		self.rule_tip:DeleteMe()
		self.rule_tip = nil
	end

	if self.tab_rule_tip then
		self.tab_rule_tip:DeleteMe()
		self.tab_rule_tip = nil
	end

	if self.enter_common_scene_view then
		self.enter_common_scene_view:DeleteMe()
		self.enter_common_scene_view = nil
	end

	if self.show_chapter_view then
		self.show_chapter_view:DeleteMe()
		self.show_chapter_view = nil
	end

	if self.tips_show_storytext_view then
		self.tips_show_storytext_view:DeleteMe()
		self.tips_show_storytext_view = nil
	end

	-- self.attr_tip:DeleteMe()
	-- self.attr_tip = nil

	if self.bless_tip then
		self.bless_tip:DeleteMe()
		self.bless_tip = nil
	end

	if self.disconnect_tips then
		self.disconnect_tips:DeleteMe()
		self.disconnect_tips = nil
	end

	if self.tips_number_show_manager then
		self.tips_number_show_manager:DeleteMe()
		self.tips_number_show_manager = nil
	end

	if self.tips_img_system_manager then
		self.tips_img_system_manager:DeleteMe()
		self.tips_img_system_manager = nil
	end

	if self.pop_num_view then
		self.pop_num_view:DeleteMe()
		self.pop_num_view = nil
	end

	if self.special_num_view then
		self.special_num_view:DeleteMe()
		self.special_num_view = nil
	end

	if self.tips_open_fun_fly_view then
		self.tips_open_fun_fly_view:DeleteMe()
		self.tips_open_fun_fly_view = nil
	end

	if self.tips_get_reward_view then
		self.tips_get_reward_view:DeleteMe()
		self.tips_get_reward_view = nil
	end

	if self.system_notice_view then
		self.system_notice_view:DeleteMe()
		self.system_notice_view = nil
	end

	if self.system_scroll_view then
		self.system_scroll_view:DeleteMe()
		self.system_scroll_view = nil
	end

	if self.system_beast_equip_scroll_view then
		self.system_beast_equip_scroll_view:DeleteMe()
		self.system_beast_equip_scroll_view = nil
	end

	if self.fire_work_reward_view then
		self.fire_work_reward_view:DeleteMe()
		self.fire_work_reward_view = nil
	end

	if self.equip_getway_view then
		self.equip_getway_view:DeleteMe()
		self.equip_getway_view = nil
	end

	if self.zc_run_msg_right then
		self.zc_run_msg_right:DeleteMe()
		self.zc_run_msg_right = nil
	end

	if self.zc_run_msg_center then
		self.zc_run_msg_center:DeleteMe()
		self.zc_run_msg_center = nil
	end

	if self.tips_big_reward_show_view then
		self.tips_big_reward_show_view:DeleteMe()
		self.tips_big_reward_show_view = nil
	end

	for _, v1 in pairs(self.fly_effect_pool) do
		for _, v2 in ipairs(v1) do
			v2:DeleteMe()
		end
	end

	self.fly_effect_num = nil

	if self.second_confirmation_view then
		self.second_confirmation_view:DeleteMe()
		self.second_confirmation_view = nil
	end

	if self.exchange_panel then
		self.exchange_panel:DeleteMe()
		self.exchange_panel = nil
	end

	if self.common_input_view then
		self.common_input_view:DeleteMe()
		self.common_input_view = nil
	end

	if self.eternal_night_item_tip then
		self.eternal_night_item_tip:DeleteMe()
		self.eternal_night_item_tip = nil
	end

	if self.drop_equip_show_tip then
		self.drop_equip_show_tip:DeleteMe()
		self.drop_equip_show_tip = nil
	end

	if self.bagua_compare_tip then
		self.bagua_compare_tip:DeleteMe()
		self.bagua_compare_tip = nil
    end

    if self.mount_pet_equip_compare_tip then
		self.mount_pet_equip_compare_tip:DeleteMe()
		self.mount_pet_equip_compare_tip = nil
    end


	if self.play_srceen_eff_view ~= nil then
		self.play_srceen_eff_view:DeleteMe()
		self.play_srceen_eff_view = nil
	end

	if self.bagua_unlock_view then
		self.bagua_unlock_view:DeleteMe()
		self.bagua_unlock_view = nil
	end
	if self.check_box_tip then
		self.check_box_tip:DeleteMe()
		self.check_box_tip = nil
	end

	if self.exit_game_tip ~= nil then
		self.exit_game_tip:DeleteMe()
		self.exit_game_tip = nil
	end

	if self.auto_gift_view then
		self.auto_gift_view:DeleteMe()
		self.auto_gift_view = nil
	end

	if self.tips_gift_view then
		self.tips_gift_view:DeleteMe()
		self.tips_gift_view = nil
	end

	if self.tips_draw_result_view then
		self.tips_draw_result_view:DeleteMe()
		self.tips_draw_result_view = nil
	end

	if self.tips_get_common_reward_view then
		self.tips_get_common_reward_view:DeleteMe()
		self.tips_get_common_reward_view = nil
	end

	if self.tip_get_value_reward_view then
		self.tip_get_value_reward_view:DeleteMe()
		self.tip_get_value_reward_view = nil
	end

	if self.tips_gailv_show_view then
		self.tips_gailv_show_view:DeleteMe()
		self.tips_gailv_show_view = nil
	end

	if self.tips_reward_display_view then
		self.tips_reward_display_view:DeleteMe()
		self.tips_reward_display_view = nil
	end

	if self.tips_attr_view then
		self.tips_attr_view:DeleteMe()
		self.tips_attr_view = nil
	end

	if self.tips_reward_record_view then
		self.tips_reward_record_view:DeleteMe()
		self.tips_reward_record_view = nil
	end

	if self.tips_draw_reward_view then
		self.tips_draw_reward_view:DeleteMe()
		self.tips_draw_reward_view = nil
	end

	if self.tips_reward_pro_view then
		self.tips_reward_pro_view:DeleteMe()
		self.tips_reward_pro_view = nil
	end

	if self.tips_reward_pro_group_view then
		self.tips_reward_pro_group_view:DeleteMe()
		self.tips_reward_pro_group_view = nil
	end

	if self.tips_buy_item_view then
		self.tips_buy_item_view:DeleteMe()
		self.tips_buy_item_view = nil
	end

	if self.tips_custom_buy_item_view then
		self.tips_custom_buy_item_view:DeleteMe()
		self.tips_custom_buy_item_view = nil
	end

	if self.tip_suit_special_item_view then
		self.tip_suit_special_item_view:DeleteMe()
		self.tip_suit_special_item_view = nil
	end

	if self.common_draw_record_view then
		self.common_draw_record_view:DeleteMe()
		self.common_draw_record_view = nil
	end

	if self.common_fight_start_tip then
		self.common_fight_start_tip:DeleteMe()
		self.common_fight_start_tip = nil
	end

	if self.sp_display_item_tip then
		self.sp_display_item_tip:DeleteMe()
		self.sp_display_item_tip = nil
	end

	if self.tip_common_use_view then
		self.tip_common_use_view:DeleteMe()
		self.tip_common_use_view = nil
	end

	if self.common_skill_pre_view then
		self.common_skill_pre_view:DeleteMe()
		self.common_skill_pre_view = nil
	end

	if self.tips_no_item_view then
		self.tips_no_item_view:DeleteMe()
		self.tips_no_item_view = nil
	end
	
	self.fly_effect_pool = {}
	self.check_fly_log = {}
	self.callback = nil
end

function TipWGCtrl:OpenRankTips(btn_list)
	self.rank_tip:SetData(btn_list)
	self.rank_tip:Open()
end

function TipWGCtrl:ReFlushBuffTip()
    if self.buff_tip:IsOpen() then
        self.buff_tip:ReFlush()
    end
end

function TipWGCtrl:OpenAlertTips(text_dec, ok_fun, cancel_fun, alignment_type, close_func, auto_close_time, is_btn_dislocation, ok_btn_text, cancel_btn_text,title_view_name)
	self.alert_tips:SetLableString(text_dec)
	self.alert_tips:SetOkFunc(ok_fun)
	self.alert_tips:SetCancelFunc(cancel_fun)
	self.alert_tips:SetAlignment(alignment_type)
	self.alert_tips:SetCloseFunc(close_func)
	self.alert_tips:SetAutoCloseTime(auto_close_time)
	self.alert_tips:SetBtnDislocation(is_btn_dislocation)
	self.alert_tips:SetOkString(ok_btn_text)
	self.alert_tips:SetCancelString(cancel_btn_text)
	self.alert_tips:SetTextViewName(title_view_name)
	if self.alert_tips:IsOpen() then
		self.alert_tips:SetAutoClose()
	end
	self.alert_tips:Open()
end

function TipWGCtrl:OpenAlertTipsByRoleBag(text_dec, ok_fun, cancel_fun, alignment_type, close_func, auto_close_time, is_btn_dislocation)
	self.alert_tips:SetLableString(text_dec, true)
	self.alert_tips:SetOkFunc(ok_fun)
	self.alert_tips:SetCancelFunc(cancel_fun)
	self.alert_tips:SetAlignment(alignment_type)
	self.alert_tips:SetCloseFunc(close_func)
	self.alert_tips:SetAutoCloseTime(auto_close_time)
	self.alert_tips:SetBtnDislocation(is_btn_dislocation)
	if self.alert_tips:IsOpen() then
		self.alert_tips:SetAutoClose()
	end
	self.alert_tips:Open()
end

function TipWGCtrl:CloseAlertTips()
	if self.alert_tips:IsOpen() then
		self.alert_tips:Close()
	end
end

function TipWGCtrl:OpenConfirmAlertTips(text_dec, ok_fun, ok_btn_text, no_close_btn, is_scene_loading)
	self.confirm_alert_tips:SetLableString(text_dec)
	self.confirm_alert_tips:SetOkFunc(ok_fun)
	self.confirm_alert_tips:SetOkString(ok_btn_text)
	self.confirm_alert_tips:NoCloseButton(no_close_btn)
	self.confirm_alert_tips:SetIsSceneLoading(is_scene_loading)

	self.confirm_alert_tips:Open()
end

-- 背包格子 or 仓库格子 扩展
function TipWGCtrl:OpenAlertIconTips(open_num, select_index)
	self.alertIcon_tips:Open()
	self.alertIcon_tips:Flush(0, "tip_info", {open_num = open_num, select_index = select_index})
end

function TipWGCtrl:ShowDisconnected(custom_disconnect_notice_type)
	print_error("TipWGCtrl:ShowDisconnected", custom_disconnect_notice_type)
	self.disconnect_tips:SetAutoConnect(custom_disconnect_notice_type)
	ViewManager.Instance:Open(GuideModuleName.ReconnectView)
	-- self.disconnect_tips:Open()
	-- self.disconnect_tips:Flush()
end

function TipWGCtrl:CloseDisconnected()
	-- self.disconnect_tips:Close()
	ViewManager.Instance:Close(GuideModuleName.ReconnectView)
end

function TipWGCtrl:OpenAttrdan(attr_type, attr_list)

end

function TipWGCtrl:OpenItem(data, fromView, param_t, item_pos_x, btn_callback_event)
    if data.item_id == MARRY_OTHER_TYPE.ITEM_ID_TXS then --同心锁特殊处理，，本来是装备，现在要显示不是装备的tips
        data.item_id = MARRY_OTHER_TYPE.VIRTUAL_TXS_ID
    end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg == nil then
		TestTrackLog("[TipWGCtrl]", "没有物品配置:::", data.item_id)
		return
	end

	if GLOBAL_SHOW_ITEM_ID_SWITCH then
		print_error("-----物品id-----", data.item_id, item_cfg) --方便查看勿删
	end

	--如果是永世装备 打开特定弹窗
	if item_cfg.grade_tips_type and item_cfg.grade_tips_type ~= "" and item_cfg.grade_tips_type > ITEM_DINGZHI_TIP_VALUE then
		self.tip_suit_special_item_view:SetData(data)
		return
	end

	--判断是否是神兽装备 打开神兽tips
	if ShenShouWGData.Instance:GetIsShenShouEquip(data.item_id) then
		if IsEmptyTable(data.attr_list) and data.param and data.param.star_level and data.star_count == nil then
			data.star_count = data.param.star_level
		end
		local shenshou_from_view = ShenShouEquipTip.FROM_EQUIMENT_HECHENG

		if fromView == ItemTip.FROME_MARKET_GOUMAI then
			shenshou_from_view = ShenShouEquipTip.FROM_MARKET_GOUMAI
		end
		ShenShouWGCtrl.Instance:OpenShenShouEquipTip(data, shenshou_from_view)
		return
    end

    --判断是否是骑宠装备 打开骑宠装备tips
	if MountLingChongEquipWGData.Instance:GetIsMountLingChongEquip(data.item_id) then
		self.mount_pet_equip_compare_tip:SetEquipData(data, fromView, param_t, item_pos_x, btn_callback_event)
		return
	end

	--龙神殿装备  打开龙神tip
	if DragonTempleWGData.Instance:GetIsDragonTempleEquip(data.item_id) then
		DragonTempleWGCtrl.Instance:OpenDragonTempleEquipTip(data.item_id, fromView)
		return
	end

	local is_biping_unlimite,new_data = ServerActivityWGData.Instance:GetIsOpenServerBiPinUnLimiteItem(data.item_id)
	if is_biping_unlimite then --开服比拼特殊打折
		data = new_data

	end

	if fromView == ItemTip.FROM_TIANSHEN_JICHENG then
		self.item_tip:SetData(data, fromView, param_t, item_pos_x, btn_callback_event)

	elseif ItemWGData.GetIsXiaogGui(item_cfg.id) then
		self.item_tip:SetData(data, fromView, param_t, item_pos_x, btn_callback_event)
		return
	elseif self.tip_data:IsShowContrast(data, fromView) then
		self.role_equip_tip:SetData(data, fromView, param_t, nil, nil, nil, btn_callback_event)
	elseif item_cfg.is_display_role and item_cfg.is_display_role ~= 0 and item_cfg.is_display_role ~= "" then
		self.display_item_tip:SetData(data, fromView, param_t, item_pos_x, btn_callback_event)
	elseif ExpAdditionWGData.Instance:IsEquipmentItem(data.virtual_id) then
		self:OpenItemTipGetWay({item_id = data.virtual_id})
		return
	elseif TianShenBaGuaWGData.Instance:GetIsBaGuaItem(data.item_id) then--and from_view == ItemTip.FROM_TIANSHEN_BAGUA_BAG then
		 self.bagua_compare_tip:SetEquipData(data,fromView,param_t,item_pos_x,btn_callback_event)
	elseif item_cfg.get_msg ~= nil and item_cfg.get_msg ~= "" then
		if item_cfg.max_open_num and item_cfg.max_open_num > 0 then
 			local select_gift_data = nil
			local gift_item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
			if gift_item_cfg and gift_item_cfg.star_describe == 1 then
				select_gift_data = ItemWGData.Instance:GetGiftConfig(data.item_id)
			elseif gift_item_cfg and gift_item_cfg.star_describe == 3 then
				select_gift_data = ItemWGData.Instance:GetResetGiftConfig(data.item_id)
			else
				select_gift_data = ItemWGData.Instance:GetItemListInGift(data.item_id)
			end

			local handle_types = TipWGData.Instance:GetOperationLabelByType(data, fromView)
			if #handle_types == 0 and fromView ~= 0 and fromView ~= ItemTip.FROM_GET_REWARD then
				FunctionChooseWGCtrl.Instance:SetSelectGifData(data.index, item_cfg.max_open_num, select_gift_data, data.item_id, true)
				return
			end
		end
		self.item_tip_goods:SetData(data, fromView, param_t, item_pos_x, btn_callback_event)

	elseif fromView == ItemTip.FROM_ETERNAL_NIGHT then
		self.eternal_night_item_tip:SetData(data,fromView,param_t)
	else
		self.item_tip:SetData(data, fromView, param_t, item_pos_x, btn_callback_event)
	end
end

function TipWGCtrl:OpenItemTipGetWay(data, need_num)
    if data.item_id == MARRY_OTHER_TYPE.ITEM_ID_TXS then --同心锁特殊处理，，本来是装备，现在要显示不是装备的tips
        data.item_id = MARRY_OTHER_TYPE.VIRTUAL_TXS_ID
    end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		print_error('该物品找不到配置   是不是传错了itemid:',data.item_id)
		return
	end

	if need_num and need_num > 0 and item_cfg.auto_open_gift == 1 then
		local gift_list, stuff_num = ItemWGData.Instance:GetOptionalGiftItem(data.item_id)
		if stuff_num >= need_num and not IsEmptyTable(gift_list) then
			self.auto_gift_view:Open()
			self.auto_gift_view:Flush(0, "gift_info", {gift_list = gift_list, target_data = data, need_num = need_num})
			return
		end
	end

	if GLOBAL_SHOW_ITEM_ID_SWITCH then
		print_error("-----物品id-----", data.item_id, item_cfg) --方便查看勿删
	end

	local is_biping_unlimite,new_data = ServerActivityWGData.Instance:GetIsOpenServerBiPinUnLimiteItem(data.item_id)
	if is_biping_unlimite then --开服比拼特殊打折
		data = new_data
	end

	self.item_tip_goods:SetData(data)
end

--设置隐藏掉购买按钮
function TipWGCtrl:SetRemoveBuyPanelHide() 
	self.item_tip_goods:SetRemoveBuyPanelHide()
end

function TipWGCtrl:FlushQuickBuyPanel()
	if self.item_tip_goods:IsOpen() then
		self.item_tip_goods:Flush(0, "FlushQuickBuyPanel")
	end
end

function TipWGCtrl:CloseNowGoodsTips()
	if self.item_tip_goods:IsOpen() then
		self.item_tip_goods:Close()
	end
end

function TipWGCtrl:OpenContrastItemTip(data, item_pos_x)
	self.role_equip_tip:SetData(data, ItemTip.EQUIPMENT_CONTRAST, nil, item_pos_x)
end

function TipWGCtrl:CloseContrastItemTip()
	self.role_equip_tip:Close()
end

function TipWGCtrl:OpenPanelByName(panel_name)
	self.item_tip:OpenPanelByName(panel_name)
end

function TipWGCtrl:CloseNormalItemTip()
	self.item_tip:Close()
end

function TipWGCtrl:GetPopNumView()
	return self.pop_num_view
end

function TipWGCtrl:ShowGetNewSkillView(skill_id, from_mark)
	if self.get_new_skill_view then
		self.get_new_skill_view:ShowView(skill_id, from_mark)
	end
end


function TipWGCtrl:ShowGetNewSkillView2(skill_cfg)
	if self.get_new_skill_view then
		self.get_new_skill_view:ShowViewByCfg(skill_cfg)
	end
end

function TipWGCtrl:ShowBaGuaUnLockView(skill_cfg)
	if self.bagua_unlock_view then
		self.bagua_unlock_view:ShowViewByCfg(skill_cfg)
	end
end


-- 中间提示框
function TipWGCtrl:ShowSystemMsg(msg, speed)
	TipsSystemManager.Instance:ShowSystemTips(msg, speed)
end
-- 中间提示框
function TipWGCtrl:ShowSystemMsg2(convertion,msg, speed)
	TipsSystemManager.Instance:ShowSystemTips2(convertion,msg, speed)
end

--战场传闻
function TipWGCtrl:ShowZCRuneMsg(msg, speed)
    ZCTipsSystemManager.Instance:ShowSystemTips(msg, speed)
end

-- 中间传闻 （放大后缩小）
function TipWGCtrl:ShowSystemNotice(str)
 	self.system_notice_view:SetNotice(str)
end

function TipWGCtrl:ClearSystemNotice()
 	self.system_notice_view:ClearNoticeList()
end
-- 传闻  （左右滚动)
function TipWGCtrl:ShowSystemScroll(str)
	self.system_scroll_view:SetNotice(str)
end

-- 传闻  （左右滚动, 带玩家信息和物品展示)
function TipWGCtrl:ShowBeastEquipSystemScroll(str)
	self.system_beast_equip_scroll_view:SetNotice(str)
end

--中间提示 带图片和文字的
function TipWGCtrl:ShowImgSystemMsg(msg, speed)
	self.tips_img_system_manager:ShowSystemTips(msg, speed)
end

--喇叭
function TipWGCtrl:ShowSpeakerNotive(msg_info)
	self.tip_speaker:SetNotice(msg_info)
end

--系统特殊喇叭
function TipWGCtrl:SetLowPriorityNotice(msg_info)
	self.tip_speaker:SetLowPriorityNotice(msg_info)
end

function TipWGCtrl:OpenRuleTip()
	self.rule_tip:Open()
end

function TipWGCtrl:SetRuleContent(content ,title_name, evil, explain_info, shield_icon)
	self.rule_tip:SetContent(content ,title_name, evil, explain_info, shield_icon)
end

function TipWGCtrl:CloseRuleTip()
	self.rule_tip:Close()
end

function TipWGCtrl:OpenTabRuleTip(data, open_index)
	self.tab_rule_tip:SetData(data, open_index)
end

function TipWGCtrl:ShowNumberMsg(msg, speed, pos, fontSize, from, is_has_bg, ui_layout)
	-- print_error("飘字",msg,speed,pos,fontSize)
	self.tips_number_show_manager:ShowSystemTips(msg, speed, pos, fontSize, from, is_has_bg, ui_layout)
end

function TipWGCtrl:CancleTeamPos( pos )
	self.tips_number_show_manager:CancleTeamPos(pos)
end

function TipWGCtrl:OpenFunTrailerTip()
	self.tips_func_trailer_view:Open()
end

function TipWGCtrl:CloseFunTrailerTip()
	self.tips_func_trailer_view:Close()
end

function TipWGCtrl:FlushFunTrailerTip(...)
	if self.tips_func_trailer_view:IsOpen() then
		self.tips_func_trailer_view:Flush(...)
	end
end

function TipWGCtrl:ShowGetItem(item_data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if item_cfg then
		-- 进背包前 自动使用类道具 不飘动画
		if item_cfg.not_put_flag and item_cfg.not_put_flag == 1 then
			return
		end
		self.tips_getitem_show_view:Show(item_data)
	end
end

function TipWGCtrl:ShowOpenFunFlyView(cfg, target_obj)
	if nil == cfg or nil == target_obj then
		TipWGCtrl.Instance:PlayAniTab(GuideModuleName.OpenFunFlyView, "sub")
		if self.icon_fly_cacha[1] then
			cfg = self.icon_fly_cacha[1].cfg
			target_obj = self.icon_fly_cacha[1].target_obj
			table.remove(self.icon_fly_cacha, 1)
		else
			return
		end
	end

	if self.tips_open_fun_fly_view:IsOpen() then
		if #self.icon_fly_cacha < 2 then
			table.insert(self.icon_fly_cacha, {cfg = cfg, target_obj = target_obj})
		else
			self.icon_fly_cacha[2] = {cfg = cfg, target_obj = target_obj}
		end
		return
	end

	local ani_tab = self.tip_data:GetWaitAniTab()
	if nil == ani_tab[GuideModuleName.OpenFunFlyView] then
		self.tips_open_fun_fly_view:SetTargetObj(target_obj, cfg)
		self.tips_open_fun_fly_view:SetData(cfg)
		self:PlayAniTab(GuideModuleName.OpenFunFlyView, "add")
	end

	if cfg.open_audio ~= nil and cfg.open_audio ~= "" then
		AudioManager.PlayAndForget(ResPath.GetUisVoiceRes(cfg.open_audio))
	end
end

function TipWGCtrl:PlayAniTab(ModuleName, state)
	self.tip_data:SetWaitAniTabState(ModuleName, state)

	local ani_tab = self.tip_data:GetWaitAniTab()

	local next_play_ani
	for k,v in pairs(ani_tab) do
		if v == "playing" then
			return
		end
		next_play_ani = k
	end

	if next_play_ani then
		ViewManager.Instance:Open(next_play_ani)
		self.tip_data:SetWaitAniTabState(next_play_ani, "playing")
	end
end

function TipWGCtrl:OpenEasyFlyView()
	self.tips_easy_fly_view:Open()
end

function TipWGCtrl:FlushEasyFlyView(...)
	self.tips_easy_fly_view:Flush(...)
end

function TipWGCtrl:ShowGetReward(item_id, id_list, is_jipin, again_func, other_info, no_need_sort, is_yinji, sure_func)
	-- print_error("----物品恭喜获得----", item_id, id_list)
	if not is_jipin then
		self.tips_get_reward_view:SetData(item_id, id_list, again_func, other_info, no_need_sort, is_yinji, sure_func)
		if self.tips_get_reward_view:IsLoaded() and self.tips_get_reward_view:IsOpen() then
			self.tips_get_reward_view:Flush()
		else
			self.tips_get_reward_view:Open()
		end
	else
		self.tips_get_best_reward_view:SetData(item_id, id_list, again_func, other_info, nil, nil, sure_func)
		self.tips_get_best_reward_view:Open()
	end
end

--抽奖结果
-- item_list 抽奖结果list  again_func 再来一次多此回调  other_info 其他信息  no_need_sort 是否排序 sure_func 确认回调方法 top_title_png 
-- other_info.again_text	-- 再来一次文本
-- other_info.stuff_id		-- 消耗材料，不传入材料不显示材料和购买信息
-- other_info.times			-- 再来一次的抽奖个数
-- other_info.spend			-- 抽奖一次购买材料的价格
-- other_info.best_data		-- 神品大奖数据体(不赋值不展示)	
							-- best_data.item_id -- 神品大奖  best_data.best_des -- 神品大奖描述 best_data.best_text -- 神品大奖标题
							-- best_data.item_ids -- 额外的神品大奖（物品id列表）
-- other_info.top_title_png -- 底板标题图片替换 RawImagePng
-- other_info.show_spend	-- 显示购买价格 默认显示
-- other_info.is_not_auti_move	-- 是否不自动上滚
-- other_info.get_skip_anim_func	--获取是否跳过动画回调
-- other_info.set_skip_anim_func	--点击跳过动画按钮回调
function TipWGCtrl:ShowGetCommonReward(id_list, again_func, other_info, no_need_sort, sure_func)
	self.tips_get_common_reward_view:SetData(id_list, again_func, other_info, no_need_sort, sure_func)
	if self.tips_get_common_reward_view:IsLoaded() and self.tips_get_common_reward_view:IsOpen() then
		self.tips_get_common_reward_view:Flush()
	else
		self.tips_get_common_reward_view:Open()
	end
end
function TipWGCtrl:CloseGetCommonReward()
	if self.tips_get_common_reward_view then
		self.tips_get_common_reward_view:Close()
	end
end

--抽奖结果 带商品价值展示
-- item_list 抽奖结果list  again_func 再来一次多此回调  other_info 其他信息  no_need_sort 是否排序 sure_func 确认回调方法
-- other_info.again_text	-- 再来一次文本
-- other_info.stuff_id		-- 消耗材料，不传入材料不显示材料和购买信息
-- other_info.times			-- 再来一次的抽奖个数
-- other_info.spend			-- 抽奖一次购买材料的价格
-- other_info.best_data		-- 神品大奖数据体(不赋值不展示)	
-- other_info.total_price_desc1  -- 描述 本次寻宝
-- other_info.total_price_desc2  -- 描述 总价值
-- best_data.item_id -- 单个大奖 神品大奖  best_data.best_des -- 神品大奖描述 best_data.best_text -- 神品大奖标题
-- best_data.item_ids -- {[1] = 26042, [2] = 26042}  额外的神品大奖（物品id列表）
function TipWGCtrl:ShowGetValueReward(id_list, again_func, other_info, no_need_sort, sure_func)
	self.tip_get_value_reward_view:SetData(id_list, again_func, other_info, no_need_sort, sure_func)
	if self.tip_get_value_reward_view:IsLoaded() and self.tip_get_value_reward_view:IsOpen() then
		self.tip_get_value_reward_view:Flush()
	else
		self.tip_get_value_reward_view:Open()
	end
end

--抽奖结果
-- item_list 抽奖结果list  again_func 再来一次多此回调  other_info 其他信息  no_need_sort 是否排序 sure_func 确认回调方法
-- other_info.again_text	-- 再来一次文本
-- other_info.stuff_id		-- 消耗材料，不传入材料不显示材料和购买信息
-- other_info.times			-- 再来一次的抽奖个数
-- other_info.spend			-- 抽奖一次购买材料的价格
-- other_info.best_data		-- 神品大奖数据体(不赋值不展示)	
							-- best_data.item_id -- 神品大奖  best_data.best_des -- 神品大奖描述 best_data.best_text -- 神品大奖标题
							-- best_data.item_ids -- 额外的神品大奖（物品id列表）
function TipWGCtrl:ShowGetCommonBossReward(id_list, again_func, other_info, no_need_sort, sure_func)
	self.tips_get_common_boss_reward_view:SetData(id_list, again_func, other_info, no_need_sort, sure_func)
	if self.tips_get_common_boss_reward_view:IsLoaded() and self.tips_get_common_reward_view:IsOpen() then
		self.tips_get_common_boss_reward_view:Flush()
	else
		self.tips_get_common_boss_reward_view:Open()
	end
end

function TipWGCtrl:OpenGaiLvShowView(info)
	self.tips_gailv_show_view:SetDataAndOpen(info)
end

function TipWGCtrl:CloseShowGetReward()
	if self.tips_get_reward_view then
		self.tips_get_reward_view:Close()
	end
end

--抽奖结果
-- item_list 抽奖结果list  stuff_id 抽奖道具id   stuff_num 需要消耗道具的数量  btn_text 按钮文本  ok_func 抽奖回调
function TipWGCtrl:ShowGetDrawResult(item_list, stuff_id, stuff_num, btn_text, ok_func)
	self.tips_draw_result_view:SetData(item_list, stuff_id, stuff_num, btn_text, ok_func)
	if self.tips_draw_result_view:IsLoaded() and self.tips_draw_result_view:IsOpen() then
		self.tips_draw_result_view:Flush()
	else
		self.tips_draw_result_view:Open()
	end
end

-- 奖励展示
-- item_list 奖励展示list
function TipWGCtrl:ShowRewardDisplay(item_list)
	self.tips_reward_display_view:SetData(item_list)
	if self.tips_reward_display_view:IsLoaded() and self.tips_reward_display_view:IsOpen() then
		self.tips_reward_display_view:Flush()
	else
		self.tips_reward_display_view:Open()
	end
end

--显示经验
function TipWGCtrl:ShowExp(exp_num, from)
	self:ShowNumberMsg(exp_num, nil, GameEnum.RD_FLOAT_V, nil, from)
end

function TipWGCtrl:ShowEneterCommonSceneView(scene_id)
	self.enter_common_scene_view:SetSceneId(scene_id)
end

function TipWGCtrl:CloseEneterCommonSceneView()
	if self.enter_common_scene_view:IsOpen() then
		self.enter_common_scene_view:Close()
	end
end

function TipWGCtrl:ShowChapterTipView(task_info)
	self.show_chapter_view:SetTaskInfo(task_info)
end

function TipWGCtrl:ShowStoryTextTipView(task_info)
	self.tips_show_storytext_view:SetTaskInfo(task_info)
end

function TipWGCtrl:CloseChapterTipView()
	if self.show_chapter_view:IsOpen() then
		self.show_chapter_view:Close()
	end
end

function TipWGCtrl:OpenActYanHuaRewradView(data)
	self.fire_work_reward_view:SetData(data)
	self.fire_work_reward_view:Open()
end

function TipWGCtrl:ShowFloatingLabel(msg)
	TipsFloatingManager.Instance:ShowFloatingTips(msg)
end
--点击获取途径的时候关闭其他二级界面事件(每次打开获取途径是传入退出时清空)
function TipWGCtrl:CloseOtherView(callback)
	self.callback = callback
end

function TipWGCtrl:GetCloseOtherView()
	return self.callback
end

function TipWGCtrl:IsShowSomeTip()
	return self.get_new_skill_view:IsOpen() or
			self.tips_open_fun_fly_view:IsOpen()
end

function TipWGCtrl:AddZCRuneMsgRight(info)
	if not self.right_zc_msg_list then
		self.right_zc_msg_list = {}
	end
	table.insert(self.right_zc_msg_list, info)
	if nil == self.show_right_zc_msg_delay then
		self:ShowZCRuneMsgRight()
	end
end

function TipWGCtrl:ShowZCRuneMsgRight()
	local param = table.remove(self.right_zc_msg_list, 1)
	if param == nil then
		self.show_right_zc_msg_delay = nil
		return
	end
	self.zc_run_msg_right:Show(param)
	if not self.show_right_zc_msg_delay then
		self.show_right_zc_msg_delay = GlobalTimerQuest:AddDelayTimer(function()
			self.show_right_zc_msg_delay = nil
			self:ShowZCRuneMsgRight()
		end, 1)
	end
end

--离开场景 清除击杀传闻
function TipWGCtrl:ClearZCRuneMsgCenter()
 	if self.center_zc_msg_list then
		self.center_zc_msg_list = {}
	end
end

function TipWGCtrl:AddZCRuneMsgCenter(info)
	if not self.center_zc_msg_list then
		self.center_zc_msg_list = {}
	end
	local can_show = TipWGData.Instance:CurCanShowKillHeadMsg(info)
	if not can_show then
		return
	end
	table.insert(self.center_zc_msg_list, info)
	if nil == self.show_center_zc_msg_delay then
		self:ShowZCRuneMsgCenter()
	end
end

function TipWGCtrl:ShowZCRuneMsgCenter()
	local param = table.remove(self.center_zc_msg_list, 1)
	if param == nil then
		self.show_center_zc_msg_delay = nil
		return
	end
	
	if self.zc_run_msg_center then
		self.zc_run_msg_center:Show(param)
	end

	if not self.show_center_zc_msg_delay then
		self.show_center_zc_msg_delay = GlobalTimerQuest:AddDelayTimer(function()
			self.show_center_zc_msg_delay = nil
			self:ShowZCRuneMsgCenter()
		end, 1)
	end
end

--主界面图标飞行结束的时候触发事件
function TipWGCtrl:FlyEndEvent(cfg)
	if cfg then
		if cfg.open_param == GuideModuleName.MarryNotice then
			MarryWGCtrl.Instance:OpenMarryNoticeView(true)
		end
	end
end

function TipWGCtrl:OpenEquipGetWayView(item_id, get_way_id_list)
	local data
	if item_id ~= nil then
		data = self.tip_data:GetEquipGetWayData(item_id)
	else
		data = self.tip_data:GetGetWayDataByList(get_way_id_list)
	end

	self.equip_getway_view:SetData(data)
	self.equip_getway_view:Open()
end

function TipWGCtrl:OpenEquipGetWayViewTwo(data)
	self.equip_getway_view:SetData(data)
	self.equip_getway_view:Open()
end

function TipWGCtrl:ClsoeEquipGetWayView()
	self.equip_getway_view:Close()
end


--ease为速率曲线(http://robertpenner.com/easing/easing_demo.html)
--duration为时间,
--need_repeat是否需要重复回调（每个粒子完成移动都会回调一次）
--effect_count 可为空，默认为8
--is_camber是否为曲线
--camber_power弯曲程度
--is_bag是否是背包界面的特效
--end_target_pos=外部传入的UI界面飞行结束位置
function TipWGCtrl:ShowFlyEffectManager(from_view, bundle, asset, 
		start_obj, end_obj, ease, 
		duration, callback, need_repeat, effect_count,
		section, end_offest_ver2, is_camber, is_bag,
		end_target_pos, camber_power,star_target_pos,
		parent_transform, creat_max_time)
	if from_view == nil or ((start_obj == nil or end_obj == nil) and (star_target_pos == nil or end_target_pos == nil)) then
		return
	end

	-- if self.check_fly_log[from_view] ~= nil then
	-- 	local time = Status.NowTime
	-- 	if Status.NowTime - self.check_fly_log[from_view].time < 1 then
	-- 		self.check_fly_log[from_view].num = self.check_fly_log[from_view].num + 1
	-- 		if self.check_fly_log[from_view].num > 3 then
	-- 			print_error("ShowFlyEffectManager 调用的太频繁了，检测下是不是有问题!!!!", from_view, self.check_fly_log[from_view].num)
	-- 		end
	-- 	else
	-- 		self.check_fly_log[from_view] = nil
	-- 	end
	-- else
	-- 	self.check_fly_log[from_view] = {time = Status.NowTime, num = 1}
	-- end
	local view = self:TryGetFlyEffectViewByPool(from_view, bundle, asset,
	start_obj, end_obj, ease, 
	duration, callback, need_repeat,
	effect_count, section, end_offest_ver2, is_camber, is_bag,
	end_target_pos, camber_power,star_target_pos,parent_transform, creat_max_time)
	if view ~= nil then
		view:Open()
	end
end

function TipWGCtrl:DestroyFlyEffectByViewName(from_view)
	-- if self.fly_effect_list[from_view] then
	-- 	for _, v in ipairs(self.fly_effect_list[from_view]) do
	-- 		v:DeleteMe()
	-- 	end
	-- 	self.fly_effect_list[from_view] = nil
	-- end
end

function TipWGCtrl:PopFlyEffectToPool(view, asset)
	-- 池里最多存10个
	if view ~= nil then
		if #self.fly_effect_pool < 10 then
			self.fly_effect_pool[#self.fly_effect_pool + 1] = view
		else
			view:DeleteMe()
		end
	end

	if self.fly_effect_num[asset] then
		self.fly_effect_num[asset] = self.fly_effect_num[asset] - 1
	end
end

function TipWGCtrl:TryGetFlyEffectViewByPool(from_view, bundle, asset, start_obj, end_obj, ease, duration, callback,
	 need_repeat, effect_count,
	  section, end_offest_ver2, is_camber, is_bag, 
	  end_target_pos, camber_power,star_target_pos,parent_transform, creat_max_time)
	
	if UI_Effect_MAX_NUM[asset] then
		-- 财神票主界面特效太多限制一下
		if self.fly_effect_num[asset] and self.fly_effect_num[asset] >= UI_Effect_MAX_NUM[asset] then
			return
		end
	end
	
	self.fly_effect_num[asset] = self.fly_effect_num[asset] and (self.fly_effect_num[asset] + 1) or 1
	
	local view = nil
	if #self.fly_effect_pool > 0 then
		view = table.remove(self.fly_effect_pool, 1)
	else
		view = FlyEffectView.New()
	end

	if view ~= nil then
		asset = asset or Ui_Effect.UI_jinyanqiu_guangqiu
		bundle = bundle or ("effects2/prefab/ui/" .. string.lower(asset) .. "_prefab")
		ease = ease or DG.Tweening.Ease.Linear
		duration = duration or 1
		view:SetAsset(bundle, asset)
		view:SetStartObj(start_obj)
		view:SetStartTargetPos(star_target_pos)
		view:SetEndObj(end_obj)
		view:SetEndTargetPos(end_target_pos)
		view:SetEase(ease)
		view:SetDuration(duration)
		view:SetCompleteCallBack(callback)
		view:SetNeedRepeat(need_repeat)
		view:SetEffectCount(effect_count)
		view:SetSection(section)
		view:SetEndOffest(end_offest_ver2)
		view:SetIsCamber(is_camber)
		view:SetIsBag(is_bag)
		view:SetShowParentPanel(parent_transform)
		view:SetCreatMaxTime(creat_max_time)
		if camber_power ~= nil then
			view:SetCamberPower(camber_power)
		end
	end

	return view
end

function TipWGCtrl:OpenTipsOpenView(data,title_text,btn_text,callback,item_desc)
	self.tips_open_view:SetData(data,title_text,btn_text,callback,item_desc)
end

function TipWGCtrl:OpenSecondConfirmationView(content_txt,ok_func,close_func,ok_txt,count_down_time)
	self.second_confirmation_view:SetData(content_txt,ok_func,close_func,ok_txt,count_down_time)
end

function TipWGCtrl:ShowEffect(data)
    self.common_effect_view:SetData(data)
end

function TipWGCtrl:ForceHideEffect()
	self.common_effect_view:ForceHideEffect()
end

function TipWGCtrl:OpenExchangePanel(data, max_times, exchange_func)
	self.exchange_panel:SetData(data, max_times, exchange_func)
	self.exchange_panel:Open()
end

function TipWGCtrl:OpenCommonInputView(title_text,ok_func,max_text,placeholder_text)
	self.common_input_view:SetData(title_text,ok_func,max_text,placeholder_text)
	self.common_input_view:Open()
end

-- 把点击的位置装换为当前界面的UI坐标
function TipWGCtrl:TurnBaseCellPos(pos, root_node)
	if self.uicamera == nil then
		self.uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
	end
	local screen_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.uicamera, pos)
	if root_node then
		local rect = root_node:GetComponent(typeof(UnityEngine.RectTransform))
		local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos, self.uicamera, Vector2(0, 0))
		return local_pos_tbl
	end
end

function TipWGCtrl:OpenDropEquipShow(data)
	local open_func = function ()
		if not self.drop_equip_show_tip:IsOpen() then
			if self.last_drop_timestamp and self.last_drop_timestamp == data.last_drop_timestamp then
				return
			end
			self.last_drop_timestamp = data.last_drop_timestamp
			self.drop_equip_show_tip:Open()
			self.drop_equip_show_tip:SetShowData(data)
			self.drop_equip_show_tip:Flush()
		else
			self.last_drop_timestamp = data.last_drop_timestamp
			self.drop_equip_show_tip:SetShowData(data)
			self.drop_equip_show_tip:Flush()
		end
	end

	if not self.special_slot_open_func_tab then
		self.special_slot_open_func_tab = {}
	end


	self.special_slot_open_func_tab[1] = {}
	self.special_slot_open_func_tab[1].func = open_func
	self.special_slot_open_func_tab[1].opened = false
	self:ShowNextSpecialTips()
	-- if AchievementWGCtrl.Instance:CheckAchievementTipIsOpen() then
	-- 	AchievementWGCtrl.Instance:StopShowAchievementTip(open_func)
	-- else
	-- 	open_func()
	-- end
end

function TipWGCtrl:OpenDropEquipShow2(func)
	if not self.special_slot_open_func_tab then
		self.special_slot_open_func_tab = {}
	end
	self.special_slot_open_func_tab[2] = {}
	self.special_slot_open_func_tab[2].func = func
	self.special_slot_open_func_tab[2].opened = false
	self:ShowNextSpecialTips()
end

function TipWGCtrl:CheckDropEquipTipIsOpen()
	if self.drop_equip_show_tip then
		return self.drop_equip_show_tip:IsOpen()
	else
		return false
	end
end

function TipWGCtrl:ShowNextSpecialTips()
	if self:CheckDropEquipTipIsOpen() then return end

	local is_in = EquipTargetWGData.Instance:IsVIPBossScene()
	if is_in then
		if EquipTargetWGCtrl.Instance:CheckTargetTipIsOpen() then 
			EquipTargetWGCtrl.Instance:FlushTargetTip()
		end
	else
		if EquipTargetWGCtrl.Instance:CheckTargetTipIsOpen() then return end
	end


	if not self.special_slot_open_func_tab then return end

	if self.special_slot_open_func_tab[1] and not self.special_slot_open_func_tab[1].opened then
		if AchievementWGCtrl.Instance:CheckAchievementTipIsOpen() then
	 		AchievementWGCtrl.Instance:StopShowAchievementTip(self.special_slot_open_func_tab[1].func)
	 	else
	 		AchievementWGCtrl.Instance:SaveNextNeedShowData({})
	 		self.special_slot_open_func_tab[1].func()
	 	end
		self.special_slot_open_func_tab[1].opened = true

	elseif self.special_slot_open_func_tab[2] and not self.special_slot_open_func_tab[2].opened then
		if AchievementWGCtrl.Instance:CheckAchievementTipIsOpen() then
			AchievementWGCtrl.Instance:StopShowAchievementTip(self.special_slot_open_func_tab[2].func)
		else
			AchievementWGCtrl.Instance:SaveNextNeedShowData({})
			self.special_slot_open_func_tab[2].func()
		end
		self.special_slot_open_func_tab[2].opened = true
	else
		AchievementWGCtrl.Instance:ContinueShowAchieveTip()
	end
end

function TipWGCtrl:PlaySrceenEffect(effect_type, dealy_close_time)
	self.play_srceen_eff_view:SetData(effect_type, dealy_close_time)
end

function TipWGCtrl:OpenCheckAlertTips(text_dec, ok_fun, check_string,chect_text_str)
	self.check_box_tip:SetLableString(text_dec)
	self.check_box_tip:SetOkFunc(ok_fun)
	self.check_box_tip:SetCheckBoxDefaultSelect(false)
	self.check_box_tip:SetShowCheckBox(true)
	self.check_box_tip:SetCurLoginNoRemindKey(check_string)
	if chect_text_str and chect_text_str ~= "" then
		self.check_box_tip:SetCheckBoxText(chect_text_str)
	end
	self.check_box_tip:Open()
end

function TipWGCtrl:OpenCheckTodayAlertTips(text_dec, ok_fun, check_string, chect_text_today_str, ok_dec, cancel_dec)
	self.check_box_tip:SetLableString(text_dec)
	self.check_box_tip:SetOkFunc(ok_fun)
	self.check_box_tip:SetCheckBoxDefaultSelect(false)
	self.check_box_tip:SetShowCheckBox(true, check_string)
	if chect_text_today_str and chect_text_today_str ~= "" then
		self.check_box_tip:SetCheckBoxText(chect_text_today_str)
	end
	if ok_dec and ok_dec ~= "" then
		self.check_box_tip:SetOkString(ok_dec)
	end
	if cancel_dec and cancel_dec ~= "" then
		self.check_box_tip:SetCancelString(cancel_dec)
	end
	self.check_box_tip:Open()
end

--设置跳转文本信息
function TipWGCtrl:OpenAlertTipsTextLink(link_text_dec, link_text_func, text_dec, ok_fun, cancel_fun, alignment_type, close_func, auto_close_time, is_btn_dislocation, ok_btn_text, cancel_btn_text)
	self.alert_tips:SetTextLink(link_text_dec, link_text_func)
	self:OpenAlertTips(text_dec, ok_fun, cancel_fun, alignment_type, close_func, auto_close_time, is_btn_dislocation, ok_btn_text, cancel_btn_text)
end

--绑玉不足时是否消耗仙玉二级确认框
function TipWGCtrl:OpenAlertByNotBindYu(ok_fun, from_view_close_func)
	--print_error("FFF==== 绑玉不足时是否消耗仙玉二级确认框")
	--设置跳转文本信息
	if self.alert_tips and ok_fun then
		local link_func = function()
   			ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_tqtz)
   			if from_view_close_func then
   				from_view_close_func()
   			end
   		end
		--self.alert_tips:SetTextLink(Language.Guild.BindGoldNoLink, link_func)
		self:OpenAlertTips(Language.Guild.BindGoldNo, ok_fun)
	end
end

-- 打开物品格子飞行面板
function TipWGCtrl:OpenItemCellFlyView(item_data, start_pos, end_pos, fly_end_callback)
	self.item_cell_fly_view:SetData(item_data, start_pos, end_pos, fly_end_callback)
end

function TipWGCtrl:ItemCellFlyViewIsOpen()
	if self.item_cell_fly_view then
		return self.item_cell_fly_view:IsOpen()
	end
	return false
end

function TipWGCtrl:OpenExitGameTip()
	self.exit_game_tip:Open()
end

---[[ 礼包 新增 过期 之类的提示
function TipWGCtrl:OpenGiftTipView(info, mark)
	self.tips_gift_view:Open()
	self.tips_gift_view:Flush(0, "gift_info", {gift_info = info})
end

-- 重复不提示标志
function TipWGCtrl:GetTipsGiftMark(mark)
	return self.tips_gift_mark_list[mark]
end

function TipWGCtrl:SetTipsGiftMark(mark, _bool)
	self.tips_gift_mark_list[mark] = _bool
end
--]]

--打开属性加成界面.
--[[
local tips_data = {
	title_text,                          --名字.
	attr_data = { attr_str, attr_value }, --属性数据.
	is_need_space,                       --是否需要空格(默认需要).
	is_need_maohao,                      --是否需要冒号.
	prefix_text,                         --是否需要前缀.
	add_attr_data                        --自定义加成属性.
}
--]]
function TipWGCtrl:OpenTipsAttrView(tips_data)
	self.tips_attr_view:SetData(tips_data)
end

-- 打开通用提示框提示抽奖(直接调用会检测是否仙玉不足)
--[[
local tips_data = {
	item_id,				-- 抽奖物品
	price,					-- 抽奖物品单价
	draw_count,				-- 抽奖次数
	has_checkbox,			-- 是否有提示今日弹出勾选框
	checkbox_str,			-- 今日弹出勾选复选框唯一识别字段
	cost_type,				-- 货币不足时，消耗哪种货币抽奖（默认消耗灵玉 COST_TYPE.LINGYU）
	is_can_gold_buy,		--是否可以用仙玉抽奖.默认true
	is_need_open_recharge_view,		--是否需要在货币不足的时候打开充值提示.默认true
}
--]]
function TipWGCtrl:OpenTipsDrawRewardView(tips_data, confirm_fun, cancel_fun)
	if nil == tips_data.is_can_gold_buy then
		tips_data.is_can_gold_buy = true
	end

	if nil == tips_data.is_need_open_recharge_view then
		tips_data.is_need_open_recharge_view = true
	end

	self.tips_draw_reward_view:SetData(tips_data, confirm_fun, cancel_fun)
end

-- 打开通用的奖励记录界面
--[[ 记录列表 record_data = 
	[1] = {item_data = nil, consume_time = 0, role_name = ""},
	...
--]]
function TipWGCtrl:OpenTipsRewardRecordView(record_data, title_name)
	self.tips_reward_record_view:SetData(record_data, title_name)
end

--打开通用的奖励记录界面 包含全服和个人
-- all_list 全服记录list  person_list 个人记录list  title_str 标题文本 
function TipWGCtrl:OpenTreasureRecordView(all_list, person_list, title_str)
	self.common_draw_record_view:SetData(all_list, person_list, title_str)
	if self.common_draw_record_view:IsLoaded() and self.common_draw_record_view:IsOpen() then
		self.common_draw_record_view:Flush()
	else
		self.common_draw_record_view:Open()
	end
end

-- 通用的奖励概率界面
--[[ 概率列表 reward_pro_data={
	[1] = {number = 0, item_id = 0, random_count = 0},
	...
}
--]]
function TipWGCtrl:OpenTipsRewardProView(reward_pro_data)
	self.tips_reward_pro_view:SetData(reward_pro_data)
end

-- 通用的奖励概率界面带分组 分普通和特殊
--[[ 概率列表 reward_pro_data={
	target_grade = 1, (nil默认为1)
	normal_title = "",
	special_title = "",
	group_list = [1] = {
		group_name = "",
		quality_list = {
			[1] = {
				quality_title = "",
				reward_list =  {[1] = {number = 0, item_id = 0, random_count = 0},...},
			}
		}
	},
	...
}
--]]
function TipWGCtrl:OpenTipsRewardProGroupView(reward_pro_data)
	self.tips_reward_pro_group_view:SetData(reward_pro_data)
end


-- 打开通用购买物品提示框(货币不足时，弹出货币不足提示)
--[[
local tips_data = {
	title_view_name,		-- 弹窗标题（默认-购买提示）
	item_id,				-- 物品ID
	expend_item_id,			-- 需要消耗的物品ID
	expend_item_num,		-- 需要消耗的物品数量（单价）
	has_checkbox,			-- 是否有提示今日弹出勾选框
	checkbox_str,			-- 今日弹出勾选复选框唯一识别字段
}
--]]
function TipWGCtrl:OpenBuyItemTipsView(tips_data, confirm_fun, cancel_fun)
	self.tips_buy_item_view:SetData(tips_data, confirm_fun, cancel_fun)
end

-- 打开通用自定义数量购买物品提示框(货币不足时，弹出货币不足提示)
--[[
local tips_data = {
	title_view_name,		-- 弹窗标题（默认-购买提示）
	item_id,				-- 物品ID
	expend_item_id,			-- 需要消耗的物品ID
	expend_item_num,		-- 需要消耗的物品数量（单价）
	max_buy_count,			-- 限购数量（最大上限）
	is_show_limit,			-- 是否显示限购
}
confirm_fun = function(num)
--]]
function TipWGCtrl:OpenCustomBuyItemTipsView(tips_data, confirm_fun, cancel_fun)
	self.tips_custom_buy_item_view:SetData(tips_data, confirm_fun, cancel_fun)
end

-- 在当前物品tips界面点击物品打开新物品tips，当关闭新物品tips，重新打开旧的tips
function TipWGCtrl:ItemtipsOpenOtherItemTips(data, fromView, param_t, item_pos_x, btn_callback_event)
	local cur_item_tips = nil
	if not cur_item_tips and ViewManager.Instance:IsOpen(GuideModuleName.ItemTipView) then
		cur_item_tips = ViewManager.Instance:GetView(GuideModuleName.ItemTipView)
	end

	if not cur_item_tips and ViewManager.Instance:IsOpen(GuideModuleName.ItemGoodsTipView) then
		cur_item_tips = ViewManager.Instance:GetView(GuideModuleName.ItemGoodsTipView)
	end

	if not cur_item_tips and ViewManager.Instance:IsOpen(GuideModuleName.DisplayItemTipView) then
		cur_item_tips = ViewManager.Instance:GetView(GuideModuleName.DisplayItemTipView)
	end

	if not cur_item_tips then
		return
	end

	local cahche_data = {
		data = cur_item_tips.data,
		fromView = cur_item_tips.from_view,
		param_t = cur_item_tips.handle_param_t,
		btn_callback_event = cur_item_tips.btn_callback_event
	}

	table.insert(self.item_tips_wait_open_cache, cahche_data)
	self.this_time_tiemtips_close_invalid = true
	cur_item_tips:Close()
	TryDelayCall(self, function ()
		TipWGCtrl.Instance:OpenItem(data, fromView, param_t, item_pos_x, btn_callback_event)
	end, 0, "item_tips_wait_open")
end

function TipWGCtrl:TryOpenWaitItemTips()
	if self.this_time_tiemtips_close_invalid then
		self.this_time_tiemtips_close_invalid = nil
		return
	end

	if not self.item_tips_wait_open_cache or #self.item_tips_wait_open_cache == 0 then
		return
	end

	local wait_data = table.remove(self.item_tips_wait_open_cache)
	TryDelayCall(self, function ()
		TipWGCtrl.Instance:OpenItem(wait_data.data, wait_data.fromView, wait_data.param_t, nil, wait_data.btn_callback_event)
	end, 0, "item_tips_wait_open")
end

function TipWGCtrl:ShowCommonFightStarTip()
	if self.common_fight_start_tip and not self.common_fight_start_tip:IsOpen() then
		self.common_fight_start_tip:Open()
	end
end

--[[带slider通用弹窗
	data = {
		必传
		item_data = {item_id, num}, has_num = 拥有数量, max_num = 最大数量, ok_func = 确认按钮回调,会传入购买数量作为参数, price = 单价
		选传
		need_huobi_root = 需要展示货币框, has_stuff_str = 拥有货币的文本, need_stuff_str = 价格文本, add_error = 达到max值时弹出错误码
		huobi_name = 货币名, btn_ok_str = 确认按钮文本, is_sell = 是否是出售, is_show_icon = 显示图标
	}
]]--
function TipWGCtrl:ShowTipCommonUseView(data)
	self.tip_common_use_view:SetDataAndOpen(data)
end

--[[神品大奖通用弹窗
	info = {
		{model_show_itemid = xxx},
		{model_show_itemid = xxx},
		...
	}
]]--
function TipWGCtrl:OpenBigRewardView(info, callback)
	self.tips_big_reward_show_view:SetDataAndOpen(info)
	self.tips_big_reward_show_view:SetViewCloseCallBack(callback)
end

-- 打开技能预览界面
function TipWGCtrl:OpenCommonSkillPreView(skill_id)
	self.common_skill_pre_view:SetShowSkillId(skill_id)

	if not self.common_skill_pre_view:IsOpen() then
		self.common_skill_pre_view:Open()
	else
		self.common_skill_pre_view:Flush()
	end
end

-- 打开道具不足弹窗，带获取途径
-- other_info.cb 点击跳转后的回调，如跳转同时关闭当前界面
function TipWGCtrl:OpenTipsNoItemView(item_id, other_info)
	self.tips_no_item_view:SetData(item_id, other_info)

	if not self.tips_no_item_view:IsOpen() then
		self.tips_no_item_view:Open()
	else
		self.tips_no_item_view:Flush()
	end
end
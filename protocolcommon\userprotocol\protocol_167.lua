------------------------------ 百倍爆装  start  ------------------------------
CSHundredfoldDropClientReq = CSHundredfoldDropClientReq or BaseClass(BaseProtocolStruct)

function CSHundredfoldDropClientReq:__init()
	self.msg_type = 16706
end

function CSHundredfoldDropClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param_1)
end

--百倍爆率 - 总信息
SCHundredfoldDropTotalInfo = SCHundredfoldDropTotalInfo or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropTotalInfo:__init()
	self.msg_type = 16707
end

function SCHundredfoldDropTotalInfo:Decode()
	self.level = MsgAdapter.ReadUChar()							--爆装等级
	self.rmb_buy_level = MsgAdapter.ReadUChar()					--直购等级
	self.real_recharge_reward_flag = bit:d2b_l2h(MsgAdapter.ReadUShort(), nil, true)	--真充奖励领取标识 01010101
	self.real_recharge_num = MsgAdapter.ReadUInt()				--真充额度
	self.target_reward_flag = bit:d2b_l2h(MsgAdapter.ReadUShort(), nil, true)		--爆率达标奖励领取标识	
	self.is_open_flag = MsgAdapter.ReadUChar()--bit:d2b_l2h(MsgAdapter.ReadUChar(), nil, true)		--系统开启标识
	local reserver_ch = MsgAdapter.ReadUChar()
	self.exp_per_times = MsgAdapter.ReadLL()					-- 累计活跃值(升级爆装等级用)

	local MSG_MAX_TASK_COUNT = 255
	self.task_list = {}											--任务列表.
	for i = 0, MSG_MAX_TASK_COUNT do
		self.task_list[i] = {}
		self.task_list[i].complete_times = MsgAdapter.ReadShort()	--今日完成次数.
		self.task_list[i].process = MsgAdapter.ReadShort()			--任务进度.
	end
end

--百倍爆率 - 真充额度更新
SCHundredfoldDropRealRechargeNumUpdate = SCHundredfoldDropRealRechargeNumUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropRealRechargeNumUpdate:__init()
	self.msg_type = 16700
end

function SCHundredfoldDropRealRechargeNumUpdate:Decode()
	self.real_recharge_num = MsgAdapter.ReadUInt()		--真充额度
end

--百倍爆率 - 真充奖励领取状态更新
SCHundredfoldDropRealRechargeRewardFlagUpdate = SCHundredfoldDropRealRechargeRewardFlagUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropRealRechargeRewardFlagUpdate:__init()
	self.msg_type = 16701
end

function SCHundredfoldDropRealRechargeRewardFlagUpdate:Decode()
	self.real_recharge_reward_flag = bit:d2b_l2h(MsgAdapter.ReadUShort(), nil, true)	--真充奖励领取标识 01010101
end

--百倍爆率 - 直购等级更新
SCHundredfoldDropRmbBuyLevelUpdate = SCHundredfoldDropRmbBuyLevelUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropRmbBuyLevelUpdate:__init()
	self.msg_type = 16702
end

function SCHundredfoldDropRmbBuyLevelUpdate:Decode()
	self.rmb_buy_level = MsgAdapter.ReadUChar()		--直购等级
end

--百倍爆率 - 真充boss状态更新
SCHundredfoldDropRealRechargeBossStatusUpdate = SCHundredfoldDropRealRechargeBossStatusUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropRealRechargeBossStatusUpdate:__init()
	self.msg_type = 16703
end

function SCHundredfoldDropRealRechargeBossStatusUpdate:Decode()
	self.boss_state =  bit:d2b_l2h(MsgAdapter.ReadUChar(), nil, true)		--//boss状态 0100101 0:不存在 1:存活
end

--百倍爆率 - 爆率达标奖励
SCHundredfoldDropTargetRewardFlagUpdate = SCHundredfoldDropTargetRewardFlagUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropTargetRewardFlagUpdate:__init()
	self.msg_type = 16704
end

function SCHundredfoldDropTargetRewardFlagUpdate:Decode()
	self.target_reward_flag = bit:d2b_l2h(MsgAdapter.ReadUShort(), nil, true)		--领取状态 010010101 0:未领取 1:已领取
end

--百倍爆率 - 活跃值更新
SCHundredfoldDropExpPerTimesUpdate = SCHundredfoldDropExpPerTimesUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropExpPerTimesUpdate:__init()
	self.msg_type = 16705
end

function SCHundredfoldDropExpPerTimesUpdate:Decode()
	-- self.seq = MsgAdapter.ReadUChar() --任务索引
	-- self.type = MsgAdapter.ReadUChar() --任务类型
	-- self.state = MsgAdapter.ReadUChar() --任务状态
	-- MsgAdapter.ReadUChar() -- 预留
	-- self.process = MsgAdapter.ReadUInt() --进度

	self.exp_per_times = MsgAdapter.ReadLL()					-- 累计活跃值(升级爆装等级用)
end

--百倍爆率 - 单个任务更新
SCHundredfoldDropTaskUpdate = SCHundredfoldDropTaskUpdate or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropTaskUpdate:__init()
	self.msg_type = 16709
end

function SCHundredfoldDropTaskUpdate:Decode()
	self.task_type = MsgAdapter.ReadInt()
	self.task_list = {}
	self.task_list.complete_times = MsgAdapter.ReadShort()	--今日完成次数.
	self.task_list.process = MsgAdapter.ReadShort()			--任务进度.
end

--百倍爆率 - 排行榜信息
SCHundredfoldDropRankInfo = SCHundredfoldDropRankInfo or BaseClass(BaseProtocolStruct)
function SCHundredfoldDropRankInfo:__init()
	self.msg_type = 16708
end

function SCHundredfoldDropRankInfo:Decode()
	self.my_rank = MsgAdapter.ReadInt()						--自己的排名.
	self.my_wave = MsgAdapter.ReadInt()						--自己的波数.

	local MAX_ITEM_NUM = 15
	self.rank_item_list = {}
	for i = 1, MAX_ITEM_NUM do
		self.rank_item_list[i] = ProtocolStruct.ReadHundredfoldRankItem()
	end
end
------------------------------ 百倍爆装  end  -------------------------------


------------------------------ 易容  start  -------------------------------
CSDiyAppearanceOperate = CSDiyAppearanceOperate or BaseClass(BaseProtocolStruct)

function CSDiyAppearanceOperate:__init()
	self.msg_type = 16710
end

function CSDiyAppearanceOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

CSSetDiyAppearance = CSSetDiyAppearance or BaseClass(BaseProtocolStruct)
function CSSetDiyAppearance:__init()
	self.msg_type = 16711
end

function CSSetDiyAppearance:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.project_id) 
	MsgAdapter.WriteChar(self.sex) 
	MsgAdapter.WriteChar(self.prof)

	MsgAdapter.WriteShort(self.default_face_res_id)
	MsgAdapter.WriteShort(self.default_hair_res_id)
	MsgAdapter.WriteShort(self.default_body_res_id)

	MsgAdapter.WriteUChar(self.hair_color and self.hair_color.r or 0)
	MsgAdapter.WriteUChar(self.hair_color and self.hair_color.g or 0 )
	MsgAdapter.WriteUChar(self.hair_color and self.hair_color.b or 0)
	MsgAdapter.WriteUChar(self.hair_color and self.hair_color.a or 0)

	MsgAdapter.WriteUShort(self.eye_size)
	MsgAdapter.WriteUShort(self.eye_position)
	MsgAdapter.WriteUChar(self.eye_shadow_color and self.eye_shadow_color.r or 0)
	MsgAdapter.WriteUChar(self.eye_shadow_color and self.eye_shadow_color.g or 0 )
	MsgAdapter.WriteUChar(self.eye_shadow_color and self.eye_shadow_color.b or 0)
	MsgAdapter.WriteUChar(self.eye_shadow_color and self.eye_shadow_color.a or 0)

	MsgAdapter.WriteUShort(self.left_pupil_type)
	MsgAdapter.WriteUShort(self.left_pupil_size)
	MsgAdapter.WriteUChar(self.left_pupil_color and self.left_pupil_color.r or 0)
	MsgAdapter.WriteUChar(self.left_pupil_color and self.left_pupil_color.g or 0 )
	MsgAdapter.WriteUChar(self.left_pupil_color and self.left_pupil_color.b or 0)
	MsgAdapter.WriteUChar(self.left_pupil_color and self.left_pupil_color.a or 0)


	MsgAdapter.WriteUShort(self.right_pupil_type)
	MsgAdapter.WriteUShort(self.right_pupil_size)
	MsgAdapter.WriteUChar(self.right_pupil_color and self.right_pupil_color.r or 0)
	MsgAdapter.WriteUChar(self.right_pupil_color and self.right_pupil_color.g or 0 )
	MsgAdapter.WriteUChar(self.right_pupil_color and self.right_pupil_color.b or 0)
	MsgAdapter.WriteUChar(self.right_pupil_color and self.right_pupil_color.a or 0)

	MsgAdapter.WriteUShort(self.mouth_size)
	MsgAdapter.WriteUShort(self.mouth_position)
	MsgAdapter.WriteUChar(self.mouth_color and self.mouth_color.r or 0)
	MsgAdapter.WriteUChar(self.mouth_color and self.mouth_color.g or 0 )
	MsgAdapter.WriteUChar(self.mouth_color and self.mouth_color.b or 0)
	MsgAdapter.WriteUChar(self.mouth_color and self.mouth_color.a or 0)

	MsgAdapter.WriteUShort(self.face_decal_id)
	MsgAdapter.WriteShort(self.preset_seq)
	
	MsgAdapter.WriteLL(0)
	MsgAdapter.WriteLL(0)
	MsgAdapter.WriteLL(0)
	MsgAdapter.WriteLL(0)
	MsgAdapter.WriteLL(0)
end


local function GetSCDiyAppearanceItem(project_id)
    local data = {}
	data.is_set = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	data.new_face = MsgAdapter.ReadShort()
	data.new_hair = MsgAdapter.ReadShort()
	data.new_body = MsgAdapter.ReadShort()
	data.project_name = MsgAdapter.ReadName()
	data.role_diy_appearance = ProtocolStruct.ReadRoleDiyAppearance() 
	data.project_id = project_id

    return data
end

--捏脸方案
SCDiyAppearanceInfo = SCDiyAppearanceInfo or BaseClass(BaseProtocolStruct)
function SCDiyAppearanceInfo:__init()
	self.msg_type = 16712
end

function SCDiyAppearanceInfo:Decode()
	self.dressing_diy_app_list = {}
	for i = 0, 9 do
		local data = {}
		data.use_project_id = MsgAdapter.ReadInt()
		data.project_list = {}
		for project_id = 0, 9 do
			data.project_list[project_id] = GetSCDiyAppearanceItem(project_id)
		end
		self.dressing_diy_app_list[i] = data 
	end
end

-- 捏脸方案更新
SCDiyAppearanceProjectUpdate = SCDiyAppearanceProjectUpdate or BaseClass(BaseProtocolStruct)
function SCDiyAppearanceProjectUpdate:__init()
	self.msg_type = 16713
end

function SCDiyAppearanceProjectUpdate:Decode()

	local data = {}
    data.seq = MsgAdapter.ReadInt()
	data.use_project_id = MsgAdapter.ReadInt()
	data.project_list = {}
	for project_id = 0, 9 do
		data.project_list[project_id] = GetSCDiyAppearanceItem(project_id)
	end

	self.change_data = data
end

-- 类型激活信息
SCDiyAppearanceTypeActiveInfo = SCDiyAppearanceTypeActiveInfo or BaseClass(BaseProtocolStruct)
function SCDiyAppearanceTypeActiveInfo:__init()
	self.msg_type = 16714
end

function SCDiyAppearanceTypeActiveInfo:Decode()
	self.type_active_list = {}

	for i = 0, 9 do
		local flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)
		self.type_active_list[i] = flag
	end
end

CSDiyAppearanceProjectSetName = CSDiyAppearanceProjectSetName or BaseClass(BaseProtocolStruct)
function CSDiyAppearanceProjectSetName:__init()
	self.msg_type = 16715
end

function CSDiyAppearanceProjectSetName:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.project_id)
	MsgAdapter.WriteChar(self.sex)
	MsgAdapter.WriteChar(self.prof)
	MsgAdapter.WriteShort(0)
	MsgAdapter.WriteStrN(self.project_name, 32)
end

------------------------------ 易容  end  -------------------------------

------------------------------ 跨服红包天降  start  ------------------------------

--红包弹幕.
SCCrossRedPaperFallingGetRewardInfo = SCCrossRedPaperFallingGetRewardInfo or BaseClass(BaseProtocolStruct)
function SCCrossRedPaperFallingGetRewardInfo:__init()
	self.msg_type = 16716
end

function SCCrossRedPaperFallingGetRewardInfo:Decode()
	self.uuid = MsgAdapter.ReadUUID()
	self.role_name = MsgAdapter.ReadName()
	self.item_id = MsgAdapter.ReadInt()
	self.num = MsgAdapter.ReadInt()
end
------------------------------ 跨服红包天降  end  -------------------------------

------------------------------ 背包-空间不足  start  ------------------------------

--红包弹幕.
SCKnapsackGridNotEnough = SCKnapsackGridNotEnough or BaseClass(BaseProtocolStruct)
function SCKnapsackGridNotEnough:__init()
	self.msg_type = 16717
end

function SCKnapsackGridNotEnough:Decode()
	self.bag_type = MsgAdapter.ReadInt()
end
------------------------------ 跨服红包天降  end  -------------------------------

------------------------------ 摩天轮 start ------------------------------------
SCOAFortuneCatRewardInfo = SCOAFortuneCatRewardInfo or BaseClass(BaseProtocolStruct)
function SCOAFortuneCatRewardInfo:__init()
	self.msg_type = 16718
end

function SCOAFortuneCatRewardInfo:Decode()
	self.consume_gold_type = MsgAdapter.ReadInt()
	self.get_num = MsgAdapter.ReadInt()
	self.cur_index = MsgAdapter.ReadInt() 		--当前抽中的索引
end
-------------------------------- 摩天轮 end ------------------------------------

SCTeamCommonBossFBHurtInfo = SCTeamCommonBossFBHurtInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonBossFBHurtInfo:__init()
	self.msg_type = 16719
end

function SCTeamCommonBossFBHurtInfo:Decode()
	self.team_hurt_list = {}

	for i = 1, 5 do
		local data = {}
		data.uuid = MsgAdapter.ReadUUID()
		data.role_name = MsgAdapter.ReadStrN(32)
		data.is_robot = MsgAdapter.ReadInt()
		data.hurt_count = MsgAdapter.ReadLL()
		data.count = i
		self.team_hurt_list[i] = data
	end
end


-------------------------------------------圣器残页start----------------------------------------------
SCHolyWeaponAllInfo = SCHolyWeaponAllInfo or BaseClass(BaseProtocolStruct)
function SCHolyWeaponAllInfo:__init()
	self.msg_type = 16720
end

function SCHolyWeaponAllInfo:Decode()
	local max_holy_weapon = 32
	local holy_weapon_info_list = {}
	local info
	for i = 1, max_holy_weapon do
		info = {}
		info.level = MsgAdapter.ReadShort()
		info.purchase_times = MsgAdapter.ReadShort()
		table.insert(holy_weapon_info_list, info)
	end
	self.holy_weapon_info_list = holy_weapon_info_list

	local holy_skill_process_list = {}
	for i = 1, max_holy_weapon do
		holy_skill_process_list[i] = MsgAdapter.ReadInt()
	end

	self.holy_skill_process_list = holy_skill_process_list
end

SCHolyWeaponUpdateInfo = SCHolyWeaponUpdateInfo or BaseClass(BaseProtocolStruct)
function SCHolyWeaponUpdateInfo:__init()
	self.msg_type = 16721
end

function SCHolyWeaponUpdateInfo:Decode()
	self.id = MsgAdapter.ReadInt()
	self.level = MsgAdapter.ReadShort()
	self.purchase_times = MsgAdapter.ReadShort()
end

SCHolyWeaponSkillProcessUpdateInfo = SCHolyWeaponSkillProcessUpdateInfo or BaseClass(BaseProtocolStruct)
function SCHolyWeaponSkillProcessUpdateInfo:__init()
	self.msg_type = 16723
end

function SCHolyWeaponSkillProcessUpdateInfo:Decode()
	self.id = MsgAdapter.ReadInt()
	self.skill_process = MsgAdapter.ReadInt()
end
-------------------------------------------圣器残页end------------------------------------------------


--------------------------------------副本组队爬塔通用 start------------------------------------
SCTeamCommonTowerFBSceneInfo = SCTeamCommonTowerFBSceneInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonTowerFBSceneInfo:__init()
	self.msg_type = 16724
end

function SCTeamCommonTowerFBSceneInfo:Decode()
	self.fb_seq = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt() --档次
	self.level = MsgAdapter.ReadInt() -- 关卡
	self.wava = MsgAdapter.ReadInt() -- 波数
	self.last_wave_monster_num = MsgAdapter.ReadShort() -- 当前波击败monster数量
	self.is_end = MsgAdapter.ReadChar()   -- 0 未结束 1 结束
	self.is_pass = MsgAdapter.ReadChar()  -- 0 未通关 1 通关
	self.kick_out_time = MsgAdapter.ReadUInt() -- 踢出副本时间
	self.fb_end_time = MsgAdapter.ReadUInt() -- 关卡结束时间
	self.next_refresh_monster_time = MsgAdapter.ReadUInt() --下拨刷怪时间
	self.next_level_time = MsgAdapter.ReadUInt() --下个关卡时间
end

SCTeamCommonTowerFBHurtInfo = SCTeamCommonTowerFBHurtInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonTowerFBHurtInfo:__init()
	self.msg_type = 16725
end

function SCTeamCommonTowerFBHurtInfo:Decode()
	self.team_hurt_list = {}

	for i = 1, 5 do
		local data = {}
		data.uuid = MsgAdapter.ReadUUID()
		data.role_name = MsgAdapter.ReadStrN(32)
		data.hurt_count = MsgAdapter.ReadLL()
		data.count = i
		self.team_hurt_list[i] = data
	end
end

SCTeamCommonTowerFBDrawInfo = SCTeamCommonTowerFBDrawInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonTowerFBDrawInfo:__init()
	self.msg_type = 16726
end

function SCTeamCommonTowerFBDrawInfo:Decode()
	self.nor_draw_reward_list = {}
	self.spe_draw_reward_list = {}

	self.draw_end_time = MsgAdapter.ReadInt()
	local count = MsgAdapter.ReadInt()
	for i = 1, count do
		local data = {}
		data.index = MsgAdapter.ReadInt() -- 0-4
		data.uuid = MsgAdapter.ReadUUID()
		data.role_name = MsgAdapter.ReadStrN(32)
		data.item_id = MsgAdapter.ReadUShort()
		data.is_bind = MsgAdapter.ReadChar()
		data.is_added = MsgAdapter.ReadChar() -- 0普通  1花钱
		data.num = MsgAdapter.ReadInt()
		if data.is_added == 0 then
			self.nor_draw_reward_list[data.index] = data
		else
			self.spe_draw_reward_list[data.index] = data
		end
	end
end

SCTeamCommonTowerFBSummaryInfo = SCTeamCommonTowerFBSummaryInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonTowerFBSummaryInfo:__init()
	self.msg_type = 16731
end

function SCTeamCommonTowerFBSummaryInfo:Decode()
	self.fb_seq = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt() 	

	self.mvp_player_info = {}
	self.mvp_player_info.mvp_uuid  = MsgAdapter.ReadUUID()
	self.mvp_player_info.mvp_name = MsgAdapter.ReadStrN(32)
	self.mvp_player_info.mvp_is_robot = MsgAdapter.ReadChar() 
	self.mvp_player_info.sex = MsgAdapter.ReadChar() 
	MsgAdapter.ReadShort()
	self.mvp_player_info.avatar_timestamp = MsgAdapter.ReadLL()
	self.mvp_player_info.shizhuang_photoframe = MsgAdapter.ReadInt()
	self.mvp_player_info.prof = MsgAdapter.ReadInt()

	self.is_help = MsgAdapter.ReadChar()
	self.is_pass  = MsgAdapter.ReadChar()


	local count = MsgAdapter.ReadShort()
	self.reward_item_list = {}
    for i = 1, count do
        self.reward_item_list[i] = ProtocolStruct.ReadMsgItem()
    end
end


--------------------------------------副本组队爬塔通用end-------------------------------------------------------------------------------圣器残页end------------------------------------------------

-------------------------------------------通天降临(幻兽特惠)start------------------------------------------------

-- 携手同行 信息
SCTreasureWalkTogetherInfo = SCTreasureWalkTogetherInfo or BaseClass(BaseProtocolStruct)
function SCTreasureWalkTogetherInfo:__init()
	self.msg_type = 16732
end

function SCTreasureWalkTogetherInfo:Decode()
	self.use_item_count = MsgAdapter.ReadInt()									--使用道具数量
	local reward_flag = MsgAdapter.ReadInt()										--奖励标记
	self.reward_flag = bit:d2b_l2h(reward_flag, nil, true)			
	self.team_uid_list = {}														--组队队员信息
	for i = 1, 3 do
		self.team_uid_list[i] = ProtocolStruct.ReadTreasureTogetherPlayerInfo()
	end
end

-- 携手同行-收到邀请
SCTreasureWalkTogetherInvite = SCTreasureWalkTogetherInvite or BaseClass(BaseProtocolStruct)
function SCTreasureWalkTogetherInvite:__init()
	self.msg_type = 16733
end

function SCTreasureWalkTogetherInvite:Decode()
	self.inviter_uid = MsgAdapter.ReadInt()									-- 邀请者uid
	self.inviter_name = MsgAdapter.ReadName()								-- 邀请者名字
end

-- 携手同行-可邀请列表
SCTreasureWalkTogetherFriendInfo = SCTreasureWalkTogetherFriendInfo or BaseClass(BaseProtocolStruct)
function SCTreasureWalkTogetherFriendInfo:__init()
	self.msg_type = 16734
end

function SCTreasureWalkTogetherFriendInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.friend_list = {}
	for i = 1, count do
		self.friend_list[i] = ProtocolStruct.ReadTreasureTogetherFriendInfo()
	end
end

-- 携手同行-兑换商店信息
SCTreasureConvertShopInfo =  SCTreasureConvertShopInfo or BaseClass(BaseProtocolStruct)
function SCTreasureConvertShopInfo:__init()
	self.msg_type = 16735
end

function SCTreasureConvertShopInfo:Decode()
	self.convert_flag_list = {}
	for i = 0, 49 do
		self.convert_flag_list[i] = MsgAdapter.ReadUShort()
	end
end

-- 川流不息信息
SCTreasureFlowingInfo =  SCTreasureFlowingInfo or BaseClass(BaseProtocolStruct)
function SCTreasureFlowingInfo:__init()
	self.msg_type = 16736
end

function SCTreasureFlowingInfo:Decode()
	local reward_flag = MsgAdapter.ReadInt()										--奖励领取标记
	self.reward_flag = bit:d2b_l2h(reward_flag, nil, true)	
end

-- 奕者谋定信息
SCTreasureSchemeInfo =  SCTreasureSchemeInfo or BaseClass(BaseProtocolStruct)
function SCTreasureSchemeInfo:__init()
	self.msg_type = 16738
end

function SCTreasureSchemeInfo:Decode()
	self.status = MsgAdapter.ReadInt()			-- 状态 0准备 1开启 2结算
	self.team = MsgAdapter.ReadInt()			-- 阵营  -1无阵营
	self.team_member_num = {}					-- 阵营人数列表
	for i = 0, 2 do
		self.team_member_num[i] = MsgAdapter.ReadInt()
	end
end

-- 迷影寻踪任务信息
SCTreasurePursuitTaskInfo =  SCTreasurePursuitTaskInfo or BaseClass(BaseProtocolStruct)
function SCTreasurePursuitTaskInfo:__init()
	self.msg_type = 16740
end

function SCTreasurePursuitTaskInfo:Decode()
	local max_task_count = 30
	self.task_list = {}
	for i = 0, max_task_count - 1 do
		self.task_list[i] = ProtocolStruct.ReadPursuitTask()
	end
end

-- 迷影寻踪单个任务更新信息		
SCTreasurePursuitTaskUpdateInfo =  SCTreasurePursuitTaskUpdateInfo or BaseClass(BaseProtocolStruct)
function SCTreasurePursuitTaskUpdateInfo:__init()
	self.msg_type = 16741
end

function SCTreasurePursuitTaskUpdateInfo:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.task_item = ProtocolStruct.ReadPursuitTask()
end

-- 迷影寻踪格子信息
SCTreasurePursuitGridInfo =  SCTreasurePursuitGridInfo or BaseClass(BaseProtocolStruct)
function SCTreasurePursuitGridInfo:__init()
	self.msg_type = 16742
end

function SCTreasurePursuitGridInfo:Decode()
	self.dart_num = MsgAdapter.ReadShort()						-- 飞镖数量
	self.badge_num = MsgAdapter.ReadChar()						-- 徽章数
	local re_ch = MsgAdapter.ReadChar()
	
	self.grid_list = {}
	local grid_max_num = 16
	for i = 1, grid_max_num do
		local data = {}
		data.index = i
		data.break_flag = MsgAdapter.ReadChar()				-- 是否打破	0: 未破碎 1: 已破碎
		data.reward_type  = MsgAdapter.ReadChar()			-- 徽章类型 0没有1普通2幸运
		data.re_sh = MsgAdapter.ReadShort()
		data.x = math.floor((i - 1) / 4) + 1
		data.y = ((i - 1) % 4) + 1
		self.grid_list[i] = data
	end
end

-- 迷影寻踪轮次信息
SCTreasurePursuitInfo =  SCTreasurePursuitInfo or BaseClass(BaseProtocolStruct)
function SCTreasurePursuitInfo:__init()
	self.msg_type = 16743
end

function SCTreasurePursuitInfo:Decode()
	self.turn = MsgAdapter.ReadInt()							-- 轮次
	local reward_flag = MsgAdapter.ReadInt()						-- 奖励标记
	self.reward_flag = bit:d2b_l2h(reward_flag, nil, true)	
end

-------------------------------------------通天降临(幻兽特惠)end------------------------------------------------

-------------------------------------------跨服藏宝start----------------------------------------------
-- 跨服藏宝操作
CSCrossTreasureOperate = CSCrossTreasureOperate or BaseClass(BaseProtocolStruct)
function CSCrossTreasureOperate:__init()
	self.msg_type = 16727
end

function CSCrossTreasureOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

-- 灵珠信息(服务器信息)
SCCrossTreasureInfo = SCCrossTreasureInfo or BaseClass(BaseProtocolStruct)
function SCCrossTreasureInfo:__init()
	self.msg_type = 16728
end

function SCCrossTreasureInfo:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.server_id = MsgAdapter.ReadInt()

	self.level_count_list = {}
	for i = 1, CROSS_TREASURE_TYPE.MAX_LEVEL do
		self.level_count_list[i] = MsgAdapter.ReadInt()
	end
end

-- 灵珠玩家个人信息
SCCrossTreasureRoleInfo = SCCrossTreasureRoleInfo or BaseClass(BaseProtocolStruct)
function SCCrossTreasureRoleInfo:__init()
	self.msg_type = 16729
end

function SCCrossTreasureRoleInfo:Decode()	
	self.treasure_item_list = {}

	for i = 1, CROSS_TREASURE_TYPE.MSG_MAX_TYPE_COUNT do
		self.treasure_item_list[i] = ProtocolStruct.ReadMsgTreasureItem()
	end		
end

-- 跨服藏宝基础信息
SCCrossTreasureBaseInfo = SCCrossTreasureBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossTreasureBaseInfo:__init()
	self.msg_type = 16730
end

function SCCrossTreasureBaseInfo:Decode()	
	self.loot_times = MsgAdapter.ReadInt()				-- 灵珠掠夺次数
	self.beast_gather_times = MsgAdapter.ReadInt()		-- 幻兽捕捉次数
	self.beast_gather_buy_times = MsgAdapter.ReadInt()	-- 幻兽捕捉购买次数
end


-- 跨服藏宝幻兽信息
SCCrossTreasureBeastInfo = SCCrossTreasureBeastInfo or BaseClass(BaseProtocolStruct)
function SCCrossTreasureBeastInfo:__init()
	self.msg_type = 16737
end

function SCCrossTreasureBeastInfo:Decode()	
	self.server_id = MsgAdapter.ReadInt()				-- 服务器id
	self.grade = MsgAdapter.ReadInt()					-- 当前档次
	self.special_beast_num = MsgAdapter.ReadInt()		-- 特殊幻兽个数
	self.beast_num = MsgAdapter.ReadInt()				-- 幻兽个数
end

-------------------------------------------跨服藏宝end------------------------------------------------

-------------------------------------------购物车start----------------------------------------------

SCTenBillionSubSidyTrolleyInfo = SCTenBillionSubSidyTrolleyInfo or BaseClass(BaseProtocolStruct)
function SCTenBillionSubSidyTrolleyInfo:__init()
	self.msg_type = 16744
end

function SCTenBillionSubSidyTrolleyInfo:Decode()	
	self.lock_time = MsgAdapter.ReadUInt()				-- 锁定时间
	-- print_error("lock_time====",self.lock_time)
	self.shop_cart_list = {}
	for i = 1, BILLION_SUBSIDY_SHOP_TYPE_MAX do
		self.shop_cart_list[i] = {}
		local flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)
		self.shop_cart_list[i].shop_choose_flag = flag
		self.shop_cart_list[i].item_count_list = {}
		for j = 0, BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX-1 do
			self.shop_cart_list[i].item_count_list[j] = MsgAdapter.ReadChar()
		end
	end
end

-------------------------------------------购物车end------------------------------------------------

------------------------------------------- 一元活动 ------------------------------------------------
SCTenBillionSubsidyOneYuanInfo = SCTenBillionSubsidyOneYuanInfo or BaseClass(BaseProtocolStruct)
function SCTenBillionSubsidyOneYuanInfo:__init()
	self.msg_type = 16751
end

function SCTenBillionSubsidyOneYuanInfo:Decode()
	self.one_yuan_grade = MsgAdapter.ReadUChar() 	--一元福利档位
	self.one_yuan_day = MsgAdapter.ReadUChar() 		--一元福利天数
	self.one_yuan_buy_flag = MsgAdapter.ReadChar() 	--一元福利购买标记
	self.re_ch = MsgAdapter.ReadChar()
	self.one_yuan_day_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true) --一元福利奖励领取标记
end
------------------------------------------- 一元活动end ------------------------------------------------

-------------------------------------------集卡活动start--------------------------------------------
----集卡活动卡牌信息
SCAOCollectCardInfo = SCAOCollectCardInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardInfo:__init()
	self.msg_type = 16739
end

function SCAOCollectCardInfo:Decode()	
	self.grade = MsgAdapter.ReadInt()								-- 当前档次
	self.reward_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)	-- 奖励领取标记
	self.draw_times = MsgAdapter.ReadInt()							-- 抽奖次数
	self.has_draw_times = MsgAdapter.ReadInt()						-- 已经抽奖的次数

	self.card_list = {}
	for i = 0, ACTIVITY_COLLECT_CARD_TYPE.MAX_CARD_COUNT - 1 do
		self.card_list[i] = MsgAdapter.ReadInt()	
	end
	self.collect_reward_flag = bit:d2b_two(MsgAdapter.ReadInt())	-- 奖励领取标记
end

----集卡活动任务信息
SCAOCollectCardTaskInfo = SCAOCollectCardTaskInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardTaskInfo:__init()
	self.msg_type = 16745
end

function SCAOCollectCardTaskInfo:Decode()	
	self.task_process = {}					--// 任务进度
	for i = 0, ACTIVITY_COLLECT_CARD_TYPE.MAX_TASK_COUNT - 1 do
		self.task_process[i] = MsgAdapter.ReadInt()	
	end

	self.task_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)	-- 任务奖励领取标记
end

----集卡活动任务信息更新
SCAOCollectCardTaskUpdateInfo = SCAOCollectCardTaskUpdateInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardTaskUpdateInfo:__init()
	self.msg_type = 16746
end

function SCAOCollectCardTaskUpdateInfo:Decode()	
	self.task_id = MsgAdapter.ReadInt()								-- 当前档次
	self.task_process = MsgAdapter.ReadInt()						-- 任务进度
	self.task_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)	-- 任务奖励领取标记
end

----集卡活动索取信息
SCAOCollectCardSelfInfo = SCAOCollectCardSelfInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardSelfInfo:__init()
	self.msg_type = 16747
end

function SCAOCollectCardSelfInfo:Decode()	
	self.grade = MsgAdapter.ReadInt()								--//档次
	self.gain_times = MsgAdapter.ReadInt()							--//当前向别人索要的次数
	self.be_times = MsgAdapter.ReadInt()							--//当前被增送次数
	self.times = MsgAdapter.ReadInt()								--//当前主动赠送次数
	self.redemption_num = MsgAdapter.ReadInt()						--//当前兑换次数

	self.request_count = MsgAdapter.ReadInt()
	self.request_list = {}											--//别人的请求
	for i = 0, self.request_count - 1 do
		local data = {}
		data.uid = MsgAdapter.ReadInt()								--//用户
		data.seq = MsgAdapter.ReadInt()								--//索引
		data.name = MsgAdapter.ReadName()							--//名字
		self.request_list[i] = data
	end
end

----集卡活动索取信息更新
SCAOCollectCardUpdateInfo = SCAOCollectCardUpdateInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardUpdateInfo:__init()
	self.msg_type = 16749
end

function SCAOCollectCardUpdateInfo:Decode()	
	self.uid = MsgAdapter.ReadInt()								--//用户
	self.name = MsgAdapter.ReadName()							--//名字
	self.be_times = MsgAdapter.ReadInt()						--//当前被增送次数
	self.seq = MsgAdapter.ReadInt()								--//向他索取的卡牌seq
	self.flag = MsgAdapter.ReadInt()							--//标记
end

----集卡活动朋友索要信息
SCAOCollectCardFriendInfo = SCAOCollectCardFriendInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardFriendInfo:__init()
	self.msg_type = 16748
end

function SCAOCollectCardFriendInfo:Decode()	
	self.count = MsgAdapter.ReadInt()								--//用户

	self.friend_list = {}
	for i = 0, self.count - 1 do
		local data = {}
		data.uid = MsgAdapter.ReadInt()								--//用户
		data.name = MsgAdapter.ReadName()							--//名字
		data.be_times = MsgAdapter.ReadInt()						--//今日被增送次数
		data.gain_flag = bit:d2b_two(MsgAdapter.ReadInt())			--//是否已经向该好友索要
		self.friend_list[i] = data
	end
end

----集卡活动朋友索要信息
SCAOCollectCardRecordInfo = SCAOCollectCardRecordInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardRecordInfo:__init()
	self.msg_type = 16752
end

function SCAOCollectCardRecordInfo:Decode()	
	self.record_list = {}
	self.record_count = MsgAdapter.ReadInt()

	for i = 1, self.record_count do
		self.record_list[i] = {}
		self.record_list[i].seq = MsgAdapter.ReadInt()
		self.record_list[i].num = MsgAdapter.ReadInt()
	end
end

----集卡活动任务信息更新
SCAOCollectCardRecordUpdateInfo = SCAOCollectCardRecordUpdateInfo or BaseClass(BaseProtocolStruct)
function SCAOCollectCardRecordUpdateInfo:__init()
	self.msg_type = 16753
end

function SCAOCollectCardRecordUpdateInfo:Decode()	
	self.seq = MsgAdapter.ReadInt()								-- 记录卡牌索引
	self.num = MsgAdapter.ReadInt()								-- 记录卡牌索引
end
-------------------------------------------集卡活动end--------------------------------------------
-------------------------------------------双修start--------------------------------------------
-- 双修-好感
SCArtifactFavorInfo = SCArtifactFavorInfo or BaseClass(BaseProtocolStruct)
function SCArtifactFavorInfo:__init()
	self.msg_type = 16754
end

function SCArtifactFavorInfo:Decode()	
	self.send_gift_count = MsgAdapter.ReadInt()						-- 拥有的送礼次数
end

-- 双修-同游
SCArtifactTravelInfo = SCArtifactTravelInfo or BaseClass(BaseProtocolStruct)
function SCArtifactTravelInfo:__init()
	self.msg_type = 16778
end

function SCArtifactTravelInfo:Decode()	
	self.travel_item_list = {}
	for i = 0, 14 do
		local data = {}
		data.artifact_seq = i								-- 妹子索引
		data.scene_seq = MsgAdapter.ReadInt()				-- 索引
		data.reward_seq = MsgAdapter.ReadInt()				-- 奖励索引
		data.end_time = MsgAdapter.ReadUInt()				-- 结束时间
		data.fetch_falg = MsgAdapter.ReadInt()				-- 是否可领取
		self.travel_item_list[i] = data
	end
end
-------------------------------------------双修end--------------------------------------------

-------------------------------------------组队bossmvp--------------------------------------------
SCTeamCommonBossFBMvpInfo = SCTeamCommonBossFBMvpInfo or BaseClass(BaseProtocolStruct)
function SCTeamCommonBossFBMvpInfo:__init()
	self.msg_type = 16770
end

function SCTeamCommonBossFBMvpInfo:Decode()	
	self.mvp_player_info = {}

	self.mvp_player_info.mvp_show_time = MsgAdapter.ReadUInt()	
	self.mvp_player_info.mvp_uuid  = MsgAdapter.ReadUUID()
	self.mvp_player_info.mvp_name = MsgAdapter.ReadStrN(32)
	self.mvp_player_info.mvp_is_robot = MsgAdapter.ReadChar() 
	self.mvp_player_info.sex = MsgAdapter.ReadChar() 
	MsgAdapter.ReadShort()
	self.mvp_player_info.avatar_timestamp = MsgAdapter.ReadLL()
	self.mvp_player_info.shizhuang_photoframe = MsgAdapter.ReadInt()
	self.mvp_player_info.prof = MsgAdapter.ReadInt()
end

SCTeamCommonBossFBMvpLike = SCTeamCommonBossFBMvpLike or BaseClass(BaseProtocolStruct)
function SCTeamCommonBossFBMvpLike:__init()
	self.msg_type = 16771
end

function SCTeamCommonBossFBMvpLike:Decode()
	self.type = MsgAdapter.ReadInt()	
	self.player_info = {}
	self.player_info.player_uuid  = MsgAdapter.ReadUUID()
	self.player_info.player_name = MsgAdapter.ReadStrN(32)
	self.player_info.player_is_robot = MsgAdapter.ReadChar() 
	self.player_info.sex = MsgAdapter.ReadChar() 
	MsgAdapter.ReadShort()
	self.player_info.avatar_timestamp = MsgAdapter.ReadLL()
	self.player_info.shizhuang_photoframe = MsgAdapter.ReadInt()
	self.player_info.prof = MsgAdapter.ReadInt()
end

-------------------------------------------组队bossmvp end--------------------------------------------

------------------------------------------- 双人坐骑START ------------------------------------------------
local function GetMsgDoubleMountItem()
	local data = {star_level = MsgAdapter.ReadInt()}
	return data
end

CSDoubleMountOperate = CSDoubleMountOperate or BaseClass(BaseProtocolStruct)
function CSDoubleMountOperate:__init()
	self.msg_type = 16761
end

function CSDoubleMountOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end


SCDoubleMountBaseInfo = SCDoubleMountBaseInfo or BaseClass(BaseProtocolStruct)
function SCDoubleMountBaseInfo:__init()
	self.msg_type = 16755
end

function SCDoubleMountBaseInfo:Decode()
	self.use_seq = MsgAdapter.ReadInt()  -- 当前幻化的坐骑 坐骑表seq
end

SCDoubleMountItemInfo = SCDoubleMountItemInfo or BaseClass(BaseProtocolStruct)
function SCDoubleMountItemInfo:__init()
	self.msg_type = 16756
end

function SCDoubleMountItemInfo:Decode()
	local item_list = {}

	for i = 0, 1 do
		item_list[i] = GetMsgDoubleMountItem() --星级
	end

	self.item_list = item_list
end

SCDoubleMountItemUpdate = SCDoubleMountItemUpdate or BaseClass(BaseProtocolStruct)
function SCDoubleMountItemUpdate:__init()
	self.msg_type = 16757
end

function SCDoubleMountItemUpdate:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.item = GetMsgDoubleMountItem()
end

SCDoubleMountInvite = SCDoubleMountInvite or BaseClass(BaseProtocolStruct) -- 被邀请人收到
function SCDoubleMountInvite:__init()
	self.msg_type = 16758
end

function SCDoubleMountInvite:Decode()
	self.invite_uuid = MsgAdapter.ReadUUID()     -- 发邀请的人 
	self.invite_name = MsgAdapter.ReadStrN(32)   -- 发邀请人的名字
end

local function GetMsgRideItem(pos)
	local data = {
		pos = pos,
		uuid = MsgAdapter.ReadUUID(),
		obj_id = MsgAdapter.ReadUShort(),
		MsgAdapter.ReadShort(),
	}

	return data
end

SCDoubleMountRideItemInfo = SCDoubleMountRideItemInfo or BaseClass(BaseProtocolStruct)  -- AOI   状态变化时候发放
function SCDoubleMountRideItemInfo:__init()
	self.msg_type = 16759
end

function SCDoubleMountRideItemInfo:Decode()
	local multi_info_data = {}

	multi_info_data.main_uuid = MsgAdapter.ReadUUID()  -- 驾驶者
	multi_info_data.main_obj_id = MsgAdapter.ReadUShort()
	multi_info_data.level = MsgAdapter.ReadShort()
	multi_info_data.seq = MsgAdapter.ReadInt()
	multi_info_data.app_image_id = MsgAdapter.ReadInt()

	local ride_item_list = {}
	for i = 0, 4 do
		ride_item_list[i] = GetMsgRideItem(i)
	end
	multi_info_data.ride_item_list = ride_item_list

	self.multi_info_data = multi_info_data
end

SCDoubleMountRideDown = SCDoubleMountRideDown or BaseClass(BaseProtocolStruct)   -- 下坐骑
function SCDoubleMountRideDown:__init()
	self.msg_type = 16760
end

function SCDoubleMountRideDown:Decode()
	self.main_uuid = MsgAdapter.ReadUUID()  -- 驾驶者
	self.uuid = MsgAdapter.ReadUUID()     --本人
	self.main_obj_id = MsgAdapter.ReadUShort()
	self.obj_id = MsgAdapter.ReadUShort()
	self.pos = MsgAdapter.ReadInt()
end
------------------------------------------- 双人坐骑END -------------------------------------------------------------------------------------------组队bossmvp end--------------------------------------------
-------------------------------------------VIP客服start--------------------------------------------
SCPopSeviceInfo = SCPopSeviceInfo or BaseClass(BaseProtocolStruct)
function SCPopSeviceInfo:__init()
	self.msg_type = 16779
end

function SCPopSeviceInfo:Decode()
	self.pop_service_daily_reward_flag = MsgAdapter.ReadChar()		-- 弹窗客服每日礼包标记
end
-------------------------------------------VIP客服end--------------------------------------------

-------------------------------------------情缘提示start--------------------------------------------
SCQingYuanZhengHunNotice = SCQingYuanZhengHunNotice or BaseClass(BaseProtocolStruct)
function SCQingYuanZhengHunNotice:__init()
	self.msg_type = 16780
end

function SCQingYuanZhengHunNotice:Decode()
	self.user_id = MsgAdapter.ReadInt()
	self.user_name = MsgAdapter.ReadStrN(32)
	self.sex = MsgAdapter.ReadShort()
	local re_sh = MsgAdapter.ReadShort()
	self.avatar_timestamp  = MsgAdapter.ReadLL()
	self.prof = MsgAdapter.ReadInt()
	self.page = MsgAdapter.ReadInt()
	self.zhenghun_info = MsgAdapter.ReadStrN(128)
end
-------------------------------------------情缘提示end--------------------------------------------

-------------------------------------------幻兽内丹start------------------------------------------
-- 驭兽装备操作请求
CSBeastEquipClientOperateReq = CSBeastEquipClientOperateReq or BaseClass(BaseProtocolStruct)
function CSBeastEquipClientOperateReq:__init()
	self.msg_type = 16772
end

function CSBeastEquipClientOperateReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
	MsgAdapter.WriteInt(self.param_4)
end

-- 驭兽装备分解操作请求
CSBeastEquipDecomposeOperateReq = CSBeastEquipDecomposeOperateReq or BaseClass(BaseProtocolStruct)
function CSBeastEquipDecomposeOperateReq:__init()
	self.msg_type = 16773
end

function CSBeastEquipDecomposeOperateReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.count)

	for i, v in ipairs(self.decompose_list) do
		MsgAdapter.WriteUShort(v)
	end
end

-- 驭兽装备总信息
SCBeastEquipAllInfo = SCBeastEquipAllInfo or BaseClass(BaseProtocolStruct)
function SCBeastEquipAllInfo:__init()
	self.msg_type = 16774
end

function SCBeastEquipAllInfo:Decode()	
	self.fight_slot_list = {}

	for i = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_FIGHT_SLOT_SEQ - 1 do
		self.fight_slot_list[i] = ProtocolStruct.ReadBeastEquipSlotItemListInfo()
	end
end

-- 驭兽装备单个槽位信息
SCBeastEquipSingleInfo = SCBeastEquipSingleInfo or BaseClass(BaseProtocolStruct)
function SCBeastEquipSingleInfo:__init()
	self.msg_type = 16775
end

function SCBeastEquipSingleInfo:Decode()	
	self.fight_slot = MsgAdapter.ReadUShort()
	self.slot = MsgAdapter.ReadUShort()
	self.slot_equip = ProtocolStruct.ReadBeastEquipSlotItemInfo()
end

-- 驭兽装备 - 背包信息
SCBeastEquipMsgBag = SCBeastEquipMsgBag or BaseClass(BaseProtocolStruct)
function SCBeastEquipMsgBag:__init()
	self.msg_type = 16776
end

function SCBeastEquipMsgBag:Decode()	
	self.grid_count = MsgAdapter.ReadInt()
	self.grid_list = {}

	for i = 0, self.grid_count - 1 do
		self.grid_list[i] = ProtocolStruct.ReadBeastEquipSlotItemGridInfo()
	end
end

-- 驭兽装备 - 背包信息变化
SCBeastEquipMsgBagGrid = SCBeastEquipMsgBagGrid or BaseClass(BaseProtocolStruct)
function SCBeastEquipMsgBagGrid:__init()
	self.msg_type = 16777
end

function SCBeastEquipMsgBagGrid:Decode()	
	self.grid_item = ProtocolStruct.ReadBeastEquipSlotItemGridInfo()
end
-------------------------------------------幻兽内丹end--------------------------------------------

 SCTeamCommonBossFBRoleBaseInfo =  SCTeamCommonBossFBRoleBaseInfo or BaseClass(BaseProtocolStruct)
function  SCTeamCommonBossFBRoleBaseInfo:__init()
	self.msg_type = 16781
end

function  SCTeamCommonBossFBRoleBaseInfo:Decode()	
	self.like_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, false)
end

--------------------------------------仙盟新功能start--------------------------------------

-- 仙盟操作
CSGuildOperate = CSGuildOperate or BaseClass(BaseProtocolStruct)
function CSGuildOperate:__init()
    self.msg_type = 16784
end

function CSGuildOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.opera_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

-- 仙盟签到信息
SCGuildSignInfo = SCGuildSignInfo or BaseClass(BaseProtocolStruct)
function SCGuildSignInfo:__init()
	self.msg_type = 16785
end

function SCGuildSignInfo:Decode()
	self.sign_num = MsgAdapter.ReadInt()			-- 签到人数
	self.sign_redpaper_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)	-- 签到红包发放标记
end

-- 仙盟商店-砍价
SCGuildGiftBargainInfo = SCGuildGiftBargainInfo or BaseClass(BaseProtocolStruct)
function SCGuildGiftBargainInfo:__init()
	self.msg_type = 16786
end

function SCGuildGiftBargainInfo:Decode()
	self.gift_bargain_times = MsgAdapter.ReadInt()			-- 砍价次数
	self.gift_bargain_value = MsgAdapter.ReadInt()			-- 已砍价格
end

-- 仙盟签到 个人信息
SCGuildRoleSignInfo = SCGuildRoleSignInfo or BaseClass(BaseProtocolStruct)
function SCGuildRoleSignInfo:__init()
	self.msg_type = 16787
end

function SCGuildRoleSignInfo:Decode()
	self.sign_flag  = MsgAdapter.ReadChar()			-- 签到标记
	self.re_ch = MsgAdapter.ReadChar()				
	self.re_sh = MsgAdapter.ReadShort()
end

-- 仙盟商店 个人信息
SCGuildRoleShopInfo = SCGuildRoleShopInfo or BaseClass(BaseProtocolStruct)
function SCGuildRoleShopInfo:__init()
	self.msg_type = 16788
end

function SCGuildRoleShopInfo:Decode()
	self.guild_coin = MsgAdapter.ReadLL()					-- 仙盟货币
	self.gift_bargain_flag = MsgAdapter.ReadChar()			-- 砍价标记
	self.gift_buy_flag = MsgAdapter.ReadChar()				-- 礼包购买标记
	self.re_sh  = MsgAdapter.ReadShort()
	self.shop_buy_list = {}									-- 商品列表
	for i = 0, 29 do
		self.shop_buy_list[i] = MsgAdapter.ReadShort()
	end
end

-- 仙盟商店 成员砍价礼包信息更新
SCGuildMemberGiftStatusUpdate = SCGuildMemberGiftStatusUpdate or BaseClass(BaseProtocolStruct)
function SCGuildMemberGiftStatusUpdate:__init()
	self.msg_type = 16791
end

function SCGuildMemberGiftStatusUpdate:Decode()
	self.uid = MsgAdapter.ReadInt()
	self.gift_bargain_flag = MsgAdapter.ReadChar()			-- 砍价标记
	self.gift_buy_flag = MsgAdapter.ReadChar()				-- 礼包购买标记
	self.re_sh  = MsgAdapter.ReadShort()
end

-- 仙盟商店 成员砍价记录
SCGuildMemberGiftBargainInfo = SCGuildMemberGiftBargainInfo or BaseClass(BaseProtocolStruct)
function SCGuildMemberGiftBargainInfo:__init()
	self.msg_type = 16792
end

function SCGuildMemberGiftBargainInfo:Decode()
	local count = MsgAdapter.ReadInt()
	self.item_list = {}
	for i = 1, count do
		self.item_list[i] = ProtocolStruct.ReadGuildGiftBargainRecord()
	end
end

-- 仙盟商店 成员砍价记录-单条
SCGuildMemberGiftBargainUpdate = SCGuildMemberGiftBargainUpdate or BaseClass(BaseProtocolStruct)
function SCGuildMemberGiftBargainUpdate:__init()
	self.msg_type = 16793
end

function SCGuildMemberGiftBargainUpdate:Decode()
	self.item = ProtocolStruct.ReadGuildGiftBargainRecord()
end
--------------------------------------仙盟新功能end--------------------------------------

-------------------------------------------守护特权 start--------------------------------------------
CSGuardPrivilegeOperate = CSGuardPrivilegeOperate or BaseClass(BaseProtocolStruct)
function CSGuardPrivilegeOperate:__init()
    self.msg_type = 16789
end

function CSGuardPrivilegeOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
end

SCGuardPrivilegeInfo = SCGuardPrivilegeInfo or BaseClass(BaseProtocolStruct)
function SCGuardPrivilegeInfo:__init()
	self.msg_type = 16790
end

function SCGuardPrivilegeInfo:Decode()	
	self.privilege_buy_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)			-- 特权购买标记
	self.fetch_exp_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)				-- 经验领取标记
	self.save_exp = MsgAdapter.ReadLL()												-- 储存经验
end
-------------------------------------------守护特权 end--------------------------------------------

----------------------------------------- 口令红包 --------------------------------------------------------
SCOAPasswordRedpaperInfo = SCOAPasswordRedpaperInfo or BaseClass(BaseProtocolStruct)
function SCOAPasswordRedpaperInfo:__init()
	self.msg_type = 16794
end

function SCOAPasswordRedpaperInfo:Decode()
	self.next_turn_time = MsgAdapter.ReadUInt() 	-- 下一轮时间
	self.activity_day = MsgAdapter.ReadUShort() 	-- 活动天数
	self.word_index = MsgAdapter.ReadChar() 		-- 口令字数下标
	self.fetch_reward_flag = MsgAdapter.ReadChar() 	-- 领取奖励标记
	self.repaper_num = MsgAdapter.ReadInt() 		-- 已领取红包数量
	self.grade = MsgAdapter.ReadInt() 				-- 档次
end
----------------------------------------- 口令红包end --------------------------------------------------------

-------------------------------------------一剑霜寒start--------------------------------------------
SCOASwordFrostbiteInfo = SCOASwordFrostbiteInfo or BaseClass(BaseProtocolStruct)
function SCOASwordFrostbiteInfo:__init()
	self.msg_type = 16795
end

function SCOASwordFrostbiteInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.round = MsgAdapter.ReadInt()					-- 轮次
	self.dart_num = MsgAdapter.ReadInt()				-- 令牌数量
	self.box_list = {}									-- 宝箱列表
	for i = 0, 19 do
		if i <= ONESWORD_FROSTBITE_TYPE.MAX_BOX_NUM - 1 then
			self.box_list[i] = MsgAdapter.ReadInt()
		else
			MsgAdapter.ReadInt()
		end
	end

	self.task_list = {}									-- 任务列表
	for i = 0, 49 do
		local task_item = {}
		task_item.process = MsgAdapter.ReadInt()
		task_item.fetch_flag = MsgAdapter.ReadChar()
		local re_ch = MsgAdapter.ReadChar()
		local re_sh = MsgAdapter.ReadShort()
		self.task_list[i] = task_item
	end
end

SCOASwordFrostbiteTaskUpdate = SCOASwordFrostbiteTaskUpdate or BaseClass(BaseProtocolStruct)
function SCOASwordFrostbiteTaskUpdate:__init()
	self.msg_type = 16796
end

function SCOASwordFrostbiteTaskUpdate:Decode()
	self.task_seq = MsgAdapter.ReadInt()
	self.dart_num = MsgAdapter.ReadInt()
	self.task_item = {}
	self.task_item.process = MsgAdapter.ReadInt()
	self.task_item.fetch_flag = MsgAdapter.ReadChar()
end

SCOASwordFrostbiteBoxUpdate = SCOASwordFrostbiteBoxUpdate or BaseClass(BaseProtocolStruct)
function SCOASwordFrostbiteBoxUpdate:__init()
	self.msg_type = 16797
end

function SCOASwordFrostbiteBoxUpdate:Decode()
	self.box_seq = MsgAdapter.ReadInt()
	self.dart_num = MsgAdapter.ReadInt()
	self.reward_seq = MsgAdapter.ReadInt()
end
-------------------------------------------一剑霜寒end--------------------------------------------

------------------------------------------- 兑换商店 ---------------------------------------------
SCRAYanHuaShengDian3ExchangeShopInfo = SCRAYanHuaShengDian3ExchangeShopInfo or BaseClass(BaseProtocolStruct)
function SCRAYanHuaShengDian3ExchangeShopInfo:__init()
	self.msg_type = 16798
end

function SCRAYanHuaShengDian3ExchangeShopInfo:Decode()
	self.exchange_shop_list = {}
	local count = 49
	for i = 0, count do
		self.exchange_shop_list[i] = MsgAdapter.ReadInt()
	end
end
------------------------------------------- 兑换商店end ---------------------------------------------
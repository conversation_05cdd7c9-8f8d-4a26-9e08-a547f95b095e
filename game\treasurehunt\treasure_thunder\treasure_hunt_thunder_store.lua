TreasureHuntThunderStoreView = TreasureHuntThunderStoreView or BaseClass(SafeBaseView)

function TreasureHuntThunderStoreView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(60, -27), sizeDelta = Vector2(932, 522)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_thunder_store")
    self:SetMaskBg(true, true)
end

function TreasureHuntThunderStoreView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TreasureHunt.ThunderStoreTitle
    if not self.store_grid_list then
		self.store_grid_list = AsyncBaseGrid.New()         
        local bundle = "uis/view/treasurehunt_ui_prefab"
		local asset = "thunder_store_item_render"        
        self.store_grid_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_store_grid"],
        assetBundle = bundle, assetName = asset, itemRender = HuntThunderStoreCell})
        self.store_grid_list:SetStartZeroIndex(false)
    end
end

function TreasureHuntThunderStoreView:ReleaseCallBack()
    if self.store_grid_list then
        self.store_grid_list:DeleteMe()
        self.store_grid_list = nil
    end
end

function TreasureHuntThunderStoreView:CloseCallBack()

end

function TreasureHuntThunderStoreView:OnFlush(param)
    local info_list = TreasureHuntThunderWGData.Instance:GetExchangeRewardList()
    if IsEmptyTable(info_list) then
        return
    end

    self.store_grid_list:SetDataList(info_list)
    local other_cfg = TreasureHuntThunderWGData.Instance:GetOtherInfo()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.excahnge_show_item_id)
    local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
    self.node_list.icon.image:LoadSprite(bundle, asset)

    local score = TreasureHuntThunderWGData.Instance:GetCurScore()
    self.node_list.score_text.text.text = score
end


----------------------------------------------------------------------------------
HuntThunderStoreCell = HuntThunderStoreCell or BaseClass(BaseRender)

function HuntThunderStoreCell:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.cell_pos)
    end

    self.node_list.btn_buy.button:AddClickListener(BindTool.Bind(self.OnClickBuy, self))
end

function HuntThunderStoreCell:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function HuntThunderStoreCell:OnFlush()
	if not self.data then
		return
    end

    local cfg = self.data.cfg

    self.item_cell:SetData({item_id = cfg.item_id})
    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)
    self.node_list.Text.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

    local other_cfg = TreasureHuntThunderWGData.Instance:GetOtherInfo()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.excahnge_show_item_id)
    local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
    self.node_list.icon.image:LoadSprite(bundle, asset)

    local score = TreasureHuntThunderWGData.Instance:GetCurScore()
    local color = score >= cfg.price and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.price_text.text.text = ToColorStr(cfg.price, color)

    local buy_type_name = Language.TreasureHunt.ThunderBuyType[cfg.limit_type] or ""
    if cfg.limit_type == 0 then
        self.node_list.limit_buy_times.text.text = buy_type_name
    elseif self.data.buy_times >= cfg.limit_num then
        self.node_list.limit_buy_times.text.text = Language.TreasureHunt.ThunderBuyType[5]
    else
        self.node_list.limit_buy_times.text.text = string.format("%s:%s/%s", buy_type_name, self.data.buy_times, cfg.limit_num)
    end
end

function HuntThunderStoreCell:OnClickBuy()
    if not self.data then
		return
    end
  
    local cfg = self.data.cfg
    local score = TreasureHuntThunderWGData.Instance:GetCurScore()
    if self.data.buy_times >= cfg.limit_num and cfg.limit_type ~= 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TreasureHunt.ThunderBuyLimit[0])
        return
    end

    if score < cfg.price then
        TipWGCtrl.Instance:ShowSystemMsg(Language.TreasureHunt.ThunderBuyLimit[1])
        return
    end

    TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.EXCHANGE, cfg.seq, 1)
end
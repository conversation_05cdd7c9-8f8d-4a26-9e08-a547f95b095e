CultivationWGData = CultivationWGData or BaseClass()

function CultivationWGData:__init()
	if CultivationWGData.Instance then
		print_error("[CultivationWGData]:Attempt to create singleton twice!")
	end
	CultivationWGData.Instance = self

	self.level = 1
	self.stage = 1
	self.lingli_exp = 0
	self.lingli_exp_add = {}
	self.use_dan_item_num = {}
	self.lingli = 0
	self.total_exp = 0
	self.exanged_times = 0
	self.next_tequan_end_time = 0
	self.every_day_flag = 0
	self.stage_reward_flag = {}
	-- 怒气
	self.current_nuqi = 0
	self.nuqi_bianshen_flag = 0
	self.active_nuqi_flag = -1
	self.nuqi_type = -1
	self.nuqi_level = {}

	local xiuwei_auto = ConfigManager.Instance:GetAutoConfig("xiuwei_auto")
	self.other_cfg = xiuwei_auto.other[1]
	self.level_cfg = xiuwei_auto.level
	self.level_stage_cfg = ListToMap(xiuwei_auto.level, "stage")
	self.stage_cfg = ListToMap(xiuwei_auto.stage, "stage")

	self.xiuwei_privilege_cfg = xiuwei_auto.xiuwei_privilege[1]
	-- 每个境界几个等级
	self.level_count = 0
	if #self.level_stage_cfg ~= 0 then
		self.level_count = #self.level_cfg/#self.level_stage_cfg
	end
	-- 添加新配置 怒气技能、渡劫
	self.stage_preview_cfg = xiuwei_auto.stage_preview
	self.nuqi_active_cfg = xiuwei_auto.nuqi_active
	self.nuqi_active_item_cfg =  ListToMap(xiuwei_auto.nuqi_active, "item_id")
	self.nuqi_add_cfg = xiuwei_auto.nuqi_add
	-- self.skill_improve_cfg = ListToMap(xiuwei_auto.skill_improve, "skill_id", "skill_level")
	self.nuqi_upgrade = ListToMap(xiuwei_auto.nuqi_upgrade, "nuqi_type", "level")
	self.nuqi_skill_upgrade = ListToMap(xiuwei_auto.nuqi_skill_upgrade, "nuqi_type", "skill_index", "level")
	self.nuqi_buff = ListToMap(xiuwei_auto.nuqi_buff, "nuqi_type", "level")
	self.nuqi_level_effect_cfg = xiuwei_auto.nuqi_level_effect
	
	-- 效率加成
	self.exp_add_cfg = xiuwei_auto.exp_add
	self.correct_sub_type_cfg =  ListToMap(xiuwei_auto.correct_type, "sub_type")
	self.correct_type_cfg = ListToMap(xiuwei_auto.correct_type, "type")
	self.correct_big_type_cfg = ListToMapList(xiuwei_auto.correct_type, "big_type")
	self.correct_type_max = 0
	self.accordion_tab = {}
	self.big_type_data = {}
	self:SetAccordionTable()
	
	RemindManager.Instance:Register(RemindName.XiuWei, BindTool.Bind(self.GetXiuWeiRemind, self))
	RemindManager.Instance:Register(RemindName.Dujie_Spirit, BindTool.Bind(self.GetAngerSpiritRemind, self))
	RemindManager.Instance:Register(RemindName.BiZuo_Cultivation,BindTool.Bind(self.BuffRedShow,self))
	self:InitCache()
	self:Esoterica__init()
	self:InitCharmCfg()
end

function CultivationWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.XiuWei)
	RemindManager.Instance:UnRegister(RemindName.Dujie_Spirit)
	RemindManager.Instance:UnRegister(RemindName.BiZuo_Cultivation)
	CultivationWGData.Instance = nil
	self.cultivation_item_cache = nil
	self.exp_item_cache = nil
	self.exp_add_item_cache = nil
	self.judgment_hp_cache = nil
	self.skill_xiuwei_cache_list = nil	-- 技能列表，包含一个普攻
	self.skill_xiuwei_normal_cache_list = nil	-- 普攻列表
	self.big_type_data = {}

	self:Esoterica__delete()
	self:DeleteCharmData()
end

function CultivationWGData:InitCache()
	local cultivation_item_cache = {}
	local exp_item_cache = {}
	local exp_add_item_cache = {}
	local judgment_hp_cache = {}
	self.client_skill_cache = {}
	self.skill_xiuwei_cache = {}
	self.skill_xiuwei_lv_cache = {}
	self.skill_xiuwei_cache_list = {}
	self.skill_xiuwei_normal_cache_list = {}
	self.xiuwei_image_list_cache = {}
	self.xiuwei_buff_up_item = {}
	self.xiuwei_image_skill_cache = {}

	local stage_iucky_item = self.other_cfg.stage_item_id
	cultivation_item_cache[stage_iucky_item] = stage_iucky_item

	for k, v in pairs(self.stage_cfg) do
		local cost_item = v.cost_stage_item.item_id

		if not cultivation_item_cache[cost_item] then
			cultivation_item_cache[cost_item] = cost_item
		end
	end

	local exp_item_id = self.other_cfg.exp_item_id
	local exp_item_list = string.split(exp_item_id, "|")
	for k, v in pairs(exp_item_list) do
		local item = tonumber(v)
		exp_item_cache[item] = item
	end

	local exp_add_item_id = self.other_cfg.xiuwei_item_id
	local exp_add_item_list = string.split(exp_add_item_id, "|")
	for k, v in pairs(exp_add_item_list) do
		local item = tonumber(v)
		exp_add_item_cache[item] = item
	end

	local judgment_xiuwei = self.other_cfg.judgment_xiuwei
	local judgment_xiuwei_list = string.split(judgment_xiuwei, "|")

	local judgment_xiuwei_show = self.other_cfg.judgment_xiuwei_show
	local icon_list = string.split(judgment_xiuwei_show, "|")
	
	for k, v in pairs(judgment_xiuwei_list) do
		local tab = {}
		tab.xiuwei = tonumber(v)
		tab.hp = math.ceil(tab.xiuwei * self.other_cfg.judgment_hp_rate)
		tab.item_id = tonumber(icon_list[k])
		table.insert(judgment_hp_cache,tab)
	end

	if self.nuqi_active_cfg then
		for active_type, active_data in pairs(self.nuqi_active_cfg) do
			if not self.client_skill_cache[active_type] then
				self.client_skill_cache[active_type] = {}
				self.client_skill_cache[active_type][1] = active_data.skill_icon
			end

			-- 隔开首个变身技能
			if active_data and active_data.client_skill_id then
				local client_skills = string.split(active_data.client_skill_id, ",")
				for i, skill_id in ipairs(client_skills) do
					self.client_skill_cache[active_type][i + 1] = tonumber(skill_id) or 0
				end
			end

			-- 
			if active_data and active_data.skill_id then
				self.skill_xiuwei_cache_list[active_type] = {}
				self.skill_xiuwei_normal_cache_list[active_type] = {}
				local client_skills = string.split(active_data.skill_id, ",")
				local nor = tonumber(client_skills[1]) or 0
				self.skill_xiuwei_cache_list[active_type][0] = nor

				for i = 1, 4 do
					local temp_skill_id = tonumber(client_skills[i]) or 0
					self.skill_xiuwei_normal_cache_list[active_type][i] = temp_skill_id
				end

				for i = 5, 8 do
					local temp_skill_id = tonumber(client_skills[i]) or 0
					table.insert(self.skill_xiuwei_cache_list[active_type], temp_skill_id)
				end
			end

			if active_data and active_data.image_list then
				self.xiuwei_image_list_cache[active_type] = {}

				local image_list = string.split(active_data.image_list, "|")
				for i, image_lv in ipairs(image_list) do
					self.xiuwei_image_list_cache[active_type][i] = tonumber(image_lv) or 0
				end
			end

			if active_data and active_data.image_skill then
				self.xiuwei_image_skill_cache[active_type] = {}

				local image_list = string.split(active_data.image_skill, "|")
				for i, image_lv in ipairs(image_list) do
					self.xiuwei_image_skill_cache[active_type][i] = tonumber(image_lv) or 0
				end
			end
		end
	end

	if self.stage_cfg then
		local curr_init_nuqi = nil
		local curr_nuqi_skill_level = nil
		
		for i, stage_data in ipairs(self.stage_cfg) do
			local is_not_same = false
			local is_not_same_level = false

			if curr_init_nuqi == nil then
				is_not_same = true
			elseif curr_init_nuqi ~= stage_data.init_nuqi then
				is_not_same = true
			end

			if is_not_same then
				curr_init_nuqi = stage_data.init_nuqi
				table.insert(self.skill_xiuwei_cache, stage_data)
			end

			if curr_nuqi_skill_level == nil then
				is_not_same_level = true
			elseif curr_nuqi_skill_level ~= stage_data.nuqi_skill_level then
				is_not_same_level = true
			end

			if is_not_same_level then
				curr_nuqi_skill_level = stage_data.nuqi_skill_level
				table.insert(self.skill_xiuwei_lv_cache, stage_data)
			end
		end
	end

	local nuqi_up_item = self.other_cfg.nuqi_up_item
	local nuqi_up_item_list = string.split(nuqi_up_item, "|")
	local buff_up_cache = {}

	for i, item_str in ipairs(nuqi_up_item_list) do
		local nuqi_item_data_list = string.split(item_str, ",")

		local item_data = {}
		item_data.item_id = tonumber(nuqi_item_data_list[1]) or 0
		item_data.num = 0--tonumber(nuqi_item_data_list[2]) or 0
		buff_up_cache[i] = item_data
	end

	self.exp_item_cache = exp_item_cache
	self.cultivation_item_cache = cultivation_item_cache
	self.exp_add_item_cache = exp_add_item_cache
	self.judgment_hp_cache = judgment_hp_cache
	self.xiuwei_buff_up_item = buff_up_cache
end

function CultivationWGData:SetRoleXiuWeiInfo(protocol)
	self.old_lingli_exp = self.lingli_exp

	self.stage = protocol.stage  								-- 当前境界
	self.level = protocol.level      							-- 当前等级
	self.lingli_exp = protocol.lingli_exp 						-- 当前灵力(修为值)
	self.lingli_exp_add = protocol.lingli_exp_add 				-- 灵力经验(修为值)加成 table
	self.use_dan_item_num = protocol.use_dan_item_num 			-- 增加灵力经验丹药道具每日使用数量
	self.next_tequan_end_time = protocol.next_tequan_end_time   -- 特权结束时间
	self.every_day_flag = protocol.every_day_flag  				-- 特权每日领取标记
	self.stage_reward_flag = protocol.stage_reward_flag  		-- 境界奖励标记

	self.current_nuqi = protocol.current_nuqi					-- 当前怒气值
	self.nuqi_bianshen_flag = protocol.nuqi_bianshen_flag   	-- 当前怒气变身状态
	-- self.active_nuqi_flag = bit:d2b_l2h(protocol.active_nuqi_flag, nil, true)		-- 当前激活境界怒气类型 0人,1仙,2魔
	self.nuqi_type = protocol.nuqi_type							-- 当前选择境界怒气类型 0人,1仙,2魔	-1没有选择
	self.capability = protocol.capability						-- 各系统总战力
	self.nuqi_level = protocol.nuqi_level						-- 怒气技能等级

	self.nuqi_open_flag = protocol.nuqi_open_flag				-- 怒气变身开启标志
end

function CultivationWGData:SetRoleXiuWeiBaseInfo(protocol)
	self.old_lingli_exp = self.lingli_exp
	
	self.stage = protocol.stage  								-- 当前境界
	self.level = protocol.level      							-- 当前等级
	self.lingli_exp = protocol.lingli_exp 						-- 当前灵力(修为值)
	self.lingli_exp_add = protocol.lingli_exp_add 				-- 灵力经验(修为值)加成 table
	self.use_dan_item_num = protocol.use_dan_item_num 			-- 增加灵力经验丹药道具每日使用数量
	self.next_tequan_end_time = protocol.next_tequan_end_time  	-- 特权结束时间
	self.every_day_flag = protocol.every_day_flag  				-- 特权每日领取标记
	self.stage_reward_flag = protocol.stage_reward_flag  		-- 境界奖励标记
	self.capability = protocol.capability  						-- 各系统总战力
end

function CultivationWGData:SetRoleXiuWeiNuqiInfo(protocol)
	self.current_nuqi = protocol.current_nuqi				--	当前怒气值
	self.nuqi_bianshen_flag = protocol.nuqi_bianshen_flag   --  当前怒气变身状态
	-- self.active_nuqi_flag = bit:d2b_l2h(protocol.active_nuqi_flag, nil, true)		--  当前激活境界怒气类型 0人,1仙,2魔
	self.nuqi_type = protocol.nuqi_type						--  当前选择境界怒气类型 0人,1仙,2魔 -1没有选择

end
function CultivationWGData:SetRoleXiuWeiAngerLevelInfo(protocol)
	if self.nuqi_level then
		self.nuqi_level[protocol.nuqi_type] = protocol.nuqi_level
	end
end

function CultivationWGData:GetAngerLevelInfo(nuqi_type)
	return (self.nuqi_level or {})[nuqi_type]
end

function CultivationWGData:GetAngerBuffLevel(nuqi_type)
	if self.nuqi_level[nuqi_type] then
		return self.nuqi_level[nuqi_type].nuqi_buff_level or 0
	end
	return 0
end

function CultivationWGData:GetAngerBuffExp(nuqi_type)
	if self.nuqi_level[nuqi_type] then
		return self.nuqi_level[nuqi_type].nuqi_buff_exp or 0
	end
	return 0
end

function CultivationWGData:GetAngerBuffCfg(nuqi_type, buff_level)

	if self.nuqi_buff[nuqi_type] and self.nuqi_buff[nuqi_type][buff_level] then
		return self.nuqi_buff[nuqi_type][buff_level]
	end
	return nil
end

function CultivationWGData:IsGetPrivilege()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	return server_time < self.next_tequan_end_time
end

function CultivationWGData:IsGetTodayReward()
	return self.every_day_flag == 1
end

function CultivationWGData:GetPrivitegeEndTime()
	return self.next_tequan_end_time
end

function CultivationWGData:GetXiuWeiLevel()
	return self.level
end

function CultivationWGData:GetXiuWeiState()
	return self.stage
end

function CultivationWGData:GetXiuWeiBigState()
	return math.ceil(self.stage / 3)

end

function CultivationWGData:GetXiuWeiExp()
	return self.lingli_exp
end

function CultivationWGData:GetXiuWeiOldExp()
	return self.old_lingli_exp
end

function CultivationWGData:GetXiuWeiLingLi()
	return self.lingli
end

function CultivationWGData:GetXiuWeiTotalExp()
	return self.total_exp
end

function CultivationWGData:GetXiuWeiExAngedTimes()
	return self.exanged_times
end

-- 获取修为是否一次使用10个
function CultivationWGData:SetCultivationGetIsTen(value)
	self.get_view_is_ten = value
end

-- 获取修为是否一次使用10个
function CultivationWGData:GetCultivationGetIsTen()
	return self.get_view_is_ten
end

-- 获取当前的怒气值
function CultivationWGData:GetRoleCurrNuqi()
	return self.current_nuqi or 0
end

-- 获取当前是否变身
function CultivationWGData:GetRoleBianshenFlag()
	return self.nuqi_bianshen_flag
end

-- 获取当前类型的等级(激活状态) 0属于未激活
function CultivationWGData:GetAngerLevel(nuqi_type)
	return (self.nuqi_level and self.nuqi_level[nuqi_type]) and self.nuqi_level[nuqi_type].nuqi_upgrade_level or 0
	-- return self.active_nuqi_flag and self.active_nuqi_flag[nuqi_type] or 0
end


-- 获取怒气技能等级
function CultivationWGData:GetAngerSkillLevel(nuqi_type, skill_index)
	if self.nuqi_level[nuqi_type] and self.nuqi_level[nuqi_type].nuqi_skill_level[skill_index] then
		return self.nuqi_level[nuqi_type].nuqi_skill_level[skill_index]
	end
	return 0
end


-- 获取当前类型的激活状态（全部）
function CultivationWGData:GetRoleAllNuqiLevel()
	return self.nuqi_level 
end

-- 获取当前幻化的类型
function CultivationWGData:GetRoleCurrNuqiType()
	return self.nuqi_type or -1
end

-- 获取是否属于怒气形象激活道具
function CultivationWGData:GetIsRoleNuqiActiveItem(item_id)
	return self.nuqi_active_item_cfg and self.nuqi_active_item_cfg[item_id]
end

-- 获取是否属于怒气形象激活道具
function CultivationWGData:GetSpiritAttrByItemId(item_id)
	local cfg = self:GetIsRoleNuqiActiveItem(item_id)
	local level_cfg = nil

	if cfg then
		level_cfg = self:GetNuqiUpgradeCfg(cfg.nuqi_type, 1)
	end

	return level_cfg
end

-- 获取怒气红点
-- function CultivationWGData:GetRoleNuqiRed()
-- 	for _, active_data in pairs(self.nuqi_active_cfg) do
-- 		local nuqi_type = active_data and active_data.nuqi_type or -1
-- 		if self:GetRoleNuqiTypeRed(nuqi_type) then
-- 			return true
-- 		end
-- 	end

-- 	return false
-- end

-- 获取怒气红点
-- function CultivationWGData:GetRoleNuqiTypeRed(nuqi_type)
-- 	local active_data = self:GetActiveCfgByType(nuqi_type)
-- 	if not active_data then
-- 		return false
-- 	end

--     local act_id = CultivationWGData.Instance:GetAngerLevel(nuqi_type)
-- 	if act_id > 0 then
-- 		return false
-- 	end

-- 	local item_num = ItemWGData.Instance:GetItemNumInBagById(active_data.item_id)
-- 	if item_num >= active_data.item_num then
-- 		return true
-- 	end

-- 	return false
-- end

-- 获取当前类型的激活状态（全部）
function CultivationWGData:CheckNuqiActiveStatus(old_nuqi_level)
	local now_nuqi_level = self:GetRoleAllNuqiLevel()

	if (not now_nuqi_level) or (not old_nuqi_level) then
		return nil
	end

	for _, active_data in pairs(self.nuqi_active_cfg) do
		local nuqi_type = active_data and active_data.nuqi_type or -1
		if old_nuqi_level[nuqi_type].nuqi_upgrade_level == 0 and now_nuqi_level[nuqi_type].nuqi_upgrade_level ~= 0 then
			return nuqi_type
		end
	end

	return nil
end

function CultivationWGData:GetXiuWeiOtherCfg()
	return self.other_cfg or {}
end

function CultivationWGData:GetCultivationItemList()

	if self.other_cfg then
		local item_list = Split(self.other_cfg.xiuwei_item_id,"|")
		local item_id_list = {}
		-- 插入0表示界面跳转 固定跳转某个界面
		for index, value in pairs(item_list) do
			local item = {}
			item.item_id = tonumber(value)
			item.index = index
			table.insert(item_id_list,item)
		end
		-- 跳转日常
		local item = {}
		item.item_id = 0
		item.index = 0
		table.insert(item_id_list,item)
		-- 跳转boss
		-- local item = {}
		-- item.item_id = 1
		-- item.index = 0
		-- table.insert(item_id_list,item)
		-- 九重劫塔
		local item = {}
		item.item_id = 2
		item.index = 0
		table.insert(item_id_list,item)

		return item_id_list
	end
	return {}
end

-- 境界预览
function CultivationWGData:GetCultivationPreviewCfg()
	return self.stage_preview_cfg or {}
end

-- 每个境界有前中后
function CultivationWGData:GetCultivationPreviewCfgByStage(stage)
	return self.stage_preview_cfg[stage] or {}
end

-- 效率加成类型
function CultivationWGData:GetCorrectTypeCfg()
	return self.correct_sub_type_cfg or {}
end

-- 根据子类型获取描述名称和描述
function CultivationWGData:GetCorrectNameBySubType(sub_type)
	local cfg=self.correct_sub_type_cfg[sub_type]
	if nil == cfg then
		return ""
	end
	return cfg.name,cfg.desc
end

function CultivationWGData:GetXiuWeiLevelCfgByLevel(level)
	return self.level_cfg[(self.stage-1)+level] or {}
end

function CultivationWGData:GetXiuWeiLevelCfg()
	return self.level_cfg
end

function CultivationWGData:GetXiuWeiStageCfgByState(stage)
	return self.stage_cfg[stage] or {}
end

function CultivationWGData:GetXiuweiStageTitleByState(stage)
	if stage == nil then
		stage = self.stage
	end
	return self:GetXiuWeiStageCfgByState(stage).stage_title or ""
end

function CultivationWGData:GetXiuWeiStageCfg()
	return self.stage_cfg
end

function CultivationWGData:GetCurXiuWeiStageCfg()
	return self.stage_cfg[self.stage] or {}
end

function CultivationWGData:GetNextXiuWeiStageCfg()
	return self.stage_cfg[self.stage + 1] or {}
end

function CultivationWGData:GetLastXiuWeiStageCfg()
	return self.stage_cfg[self.stage - 1] or {}
end

function CultivationWGData:GetLastXiuWeiLevelCfg()
	return self.level_cfg[(self.stage-1)*self.level_count + self.level] or {}
end

function CultivationWGData:GetCurXiuWeiLevelCfg()
	return self.level_cfg[(self.stage-1)*self.level_count + self.level + 1] or {}
end

function CultivationWGData:GetNextXiuWeiLevelCfg()
	return self.level_cfg[(self.stage-1)*self.level_count + self.level + 2] or {}
end

function CultivationWGData:GetXiuWeiExChangeNextLevel()
	local other_cfg = self:GetXiuWeiOtherCfg()
	local exchange_need_exp = other_cfg.exchange_need_exp
	local exp_diff = self:GetXiuWeiTotalExp() - exchange_need_exp
	exp_diff = exp_diff > 0 and exp_diff or 0
	local next_level = 0

	if exp_diff <= 0 then
		return next_level
	end

	local exp_count = 0
	local xiuwei_level_list_cfg = self:GetXiuWeiLevelCfg()
	for k, v in pairs(xiuwei_level_list_cfg) do
		exp_count = exp_count + v.need_exp
		if exp_diff >= exp_count then
			next_level = v.level + 1
		else
			break
		end
	end

	local max_level = ((xiuwei_level_list_cfg or {})[#xiuwei_level_list_cfg] or {}).level or 0
	next_level = next_level > max_level and max_level or next_level

	return next_level
end


-- 策划要求每日兑换红点只显示一次
-- function CultivationWGData:IsShowChargeRedPoint()
-- 	local uuid = RoleWGData.Instance:GetUUid()
-- 	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "CultivationChange")
-- 	local remind_day = PlayerPrefsUtil.GetInt(key)
-- 	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
-- 	if cur_day ~= remind_day then
-- 		return true
-- 	end
-- end

-- function CultivationWGData:SetChangeRedPoint()
-- 	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
-- 	local uuid = RoleWGData.Instance:GetUUid()
-- 	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "CultivationChange")
-- 	PlayerPrefsUtil.SetInt(key, cur_day)
-- end

-- 是否可以突破
-- return 是否可以突破, 是否满级，是否满境界
function CultivationWGData:IsCanBreak()
	local next_stage_cfg = self:GetNextXiuWeiStageCfg()
	local is_max_stage = IsEmptyTable(next_stage_cfg)
	local is_max_level = false

	if is_max_stage then
		return false, is_max_level, is_max_stage
	end

	local next_level_cfg = XiuWeiWGData.Instance:GetLevelStageCfg(self.stage, self.level + 1)
	is_max_level = IsEmptyTable(next_level_cfg)
	local task_is_all_complete = true
	local task_cfg = XiuWeiWGData.Instance:GetTaskCfg()
	if task_cfg then
		for k, v in pairs(task_cfg) do
			local task_data = XiuWeiWGData.Instance:GetTaskDataBySeq(v.seq)
			local task_state = task_data and task_data.status or 0
			if task_state ~= XiuWeiWGData.TaskState.Complete then
				task_is_all_complete = false
				break
			end
		end
	end

	return (is_max_level and task_is_all_complete), is_max_level, false
end

function CultivationWGData:IsCompleteAllTask()
	local task_is_all_complete = true

	local task_cfg = XiuWeiWGData.Instance:GetTaskCfg()
	if task_cfg then
		for k, v in pairs(task_cfg) do
			local task_data = XiuWeiWGData.Instance:GetTaskDataBySeq(v.seq)
			local task_state = task_data and task_data.status or 0
			if task_state ~= XiuWeiWGData.TaskState.Complete then
				task_is_all_complete = false
				break
			end
		end
	end

	return task_is_all_complete
end

function CultivationWGData:IsCultivationCostItem(change_item_id)
	return nil ~= self.cultivation_item_cache[change_item_id]
end

function CultivationWGData:IsCultivationExpCostItem(change_item_id)
	return nil ~= self.exp_item_cache[change_item_id]
end

function CultivationWGData:IsCultivationExpAddItem(change_item_id)
	return nil ~= self.exp_add_item_cache[change_item_id]
end


function CultivationWGData:GetCultivationPrivilegeCfg()
	return self.xiuwei_privilege_cfg
end


function CultivationWGData:GetCultivationCap()
	local cur_xiuwei_cfg = self:GetCurXiuWeiLevelCfg()
	local cur_stage_cfg = self:GetCurXiuWeiStageCfg()
	local privilege_cfg = self:GetCultivationPrivilegeCfg()

	local xiuwei_attr_list, xiuwei_cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_xiuwei_cfg, "attr_id", "attr_value")
	local stage_attr_list, stage_cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_stage_cfg, "attr_id", "attr_value")

	local is_get_privilege = self:IsGetPrivilege()
	local add_rate = is_get_privilege and privilege_cfg.add_zhanli_rate / 10000 or 0
	local target_cap = xiuwei_cap + stage_cap
	return math.floor(target_cap + target_cap * add_rate)
end


-------------------效率加成-----------------
-- 设置页签数据
function CultivationWGData:SetAccordionTable()
	self.correct_type_max = #self.correct_type_cfg

	local tab
	for i, v in ipairs(self.correct_type_cfg) do
		tab = {}
		tab.type_str = v.type_name
		tab.type_sort = v.type
		tab.child = {}
		tab.child_data = {}
		self.accordion_tab[v.type] = tab
	end

	for i, v in pairs(self.correct_sub_type_cfg) do
		tab = {}
		tab.type_sort = v.type
		tab.icon_type = v.icon_type
		tab.client_sort = v.sub_type
		tab.open_panel_name = v.open_panel_name
		tab.num_tips = v.num_tips
		tab.client_type_str = v.sub_type_name
		tab.cfg_list = {}
		self.accordion_tab[v.type].child[tab.client_sort]=tab
		table.insert(self.accordion_tab[v.type].child_data, tab)
	end

	for i,v in pairs(self.exp_add_cfg) do
		local row_cfg=self.correct_sub_type_cfg[v.type]
		local cfg_list=self.accordion_tab[row_cfg.type].child[v.type].cfg_list
		local data = {
			seq = v.seq,
			type = v.type,
			index = v.index,
			target = v.target,
			target_param = v.target_param,
			value_int = v.value_int,
			value_per = v.value_per,
			open_panel_name = self.accordion_tab[row_cfg.type].child[v.type].open_panel_name,
		}
		table.insert(cfg_list, data)
		table.sort(cfg_list, function(a,b)
			return a.index < b.index
		end)
	end
end
-- 获取页签数据
function CultivationWGData:GetAccordionTable()
	return self.accordion_tab
end

function CultivationWGData:GetBigAccordionTable()
	return self.correct_big_type_cfg
end

-- 获取子列表数据
function CultivationWGData:GetAccordionDataList(type)
	if self.big_type_data[type] then
		return self.big_type_data[type]
	else
		self.big_type_data[type] = {}
		for k, v in pairs(self.correct_big_type_cfg[type]) do
			table.insert(self.big_type_data[type], self.accordion_tab[v.type].child[v.sub_type])
		end
	end

	return self.big_type_data[type]
end

function CultivationWGData:GetAccordionDataListJumpIndex(data_list)
	local jump_index = -1
	for k, v in pairs(data_list) do
		local is_remind = self:GetBuffRemindBySubType(v.client_sort)
		if is_remind then
			jump_index = k - 1
			break
		end
	end

	return jump_index
end

-- 获取对应的页签，子页签的数据
function CultivationWGData:GetCultivationBuffTable(type,sub_type)
	if self.accordion_tab[type] == nil then
		return {}
	end
	if self.accordion_tab[type].child[sub_type] == nil then
		return {}
	end
	return self.accordion_tab[type].child[sub_type].cfg_list
end

-- 获取对应的页签，子页签排序好的数据
function CultivationWGData:GetCultivationBuffTableSoft(type,sub_type)
	if self.accordion_tab[type] == nil then
		return {}
	end
	if self.accordion_tab[type].child[sub_type] == nil then
		return {}
	end

	table.sort(self.accordion_tab[type].child[sub_type].cfg_list, function(a, b)
		local a_state = CultivationWGData.Instance:IsReceivedBuffByIndex(a.type, a.index) and 3 or 2
		local b_state = CultivationWGData.Instance:IsReceivedBuffByIndex(b.type, b.index) and 3 or 2
		if a_state == b_state and a_state ~= 3 then
			a_state = CultivationWGData.Instance:IsRichBuffByData(a) and 1 or 2
			b_state = CultivationWGData.Instance:IsRichBuffByData(b) and 1 or 2
		end

		if a_state ~= b_state then
			return a_state < b_state
		else
			return a.index < b.index
		end
	end)
	return self.accordion_tab[type].child[sub_type].cfg_list
end

function CultivationWGData:GetDanItemNum(index)
	return self.use_dan_item_num[index] or 0
end

-- 是否已领取
function CultivationWGData:IsReceivedBuffByIndex(type,index)
	if self.lingli_exp_add[type] then
		if self.lingli_exp_add[type][index] then
			return self.lingli_exp_add[type][index] == 1
		end
	end
	return true
end

function CultivationWGData:GetCapability(type)
	return self.capability[type] or 0
end

function CultivationWGData:IsRichBuffByData(data)
	local is_rich = false
	if data then
		local type = data.type

		if type == CULTIVATION_EXP_ADD_TYPE.ROLE_LEVEL then
			local level = RoleWGData.Instance:GetRoleLevel()
			is_rich = level >=data.target

		elseif type >= CULTIVATION_EXP_ADD_TYPE.EQUIP and type <= CULTIVATION_EXP_ADD_TYPE.MONSTER then
			local capability = CultivationWGData.Instance:GetCapability(type)
			is_rich = capability >=data.target

		elseif type == CULTIVATION_EXP_ADD_TYPE.APPEARANCE then
			local quality = data.target
			local need_count = data.target_param
			local active_count = NewAppearanceWGData.Instance:GetActivaCountByQuality(quality)
			is_rich = active_count >= need_count

		elseif type == CULTIVATION_EXP_ADD_TYPE.VIP_LEVEL then
			local level = VipWGData.Instance:GetRoleVipLevel()
			is_rich = level >=data.target
		end
	end
	return is_rich
end



-- 获取总加成
function CultivationWGData:GetTotalExpAdd()
	local number = 0
	local rate = 0
	for i, v in pairs(self.exp_add_cfg) do
		if self:IsReceivedBuffByIndex(v.type,v.index) then
			number = number + v.value_int
			rate = rate + v.value_per
		end
	end
	return number, rate/100
end

-- 获取大类加成
function CultivationWGData:GetExpAddCountByType(type)
	local count = 0
	for i, v in pairs(self.correct_sub_type_cfg) do
		if type == v.type and self.lingli_exp_add[v.sub_type] then
			for index, value in pairs(self.lingli_exp_add[v.sub_type]) do
				if value == 1 then
					count = count + 1
				end
			end
		end
	end
	return count
end
-------------------效率加成END-----------------

-----------------境界预览----------------

-- 是否已领取
function CultivationWGData:GetIsReceivedPreviewReward(stage)
	if self.stage_reward_flag[stage - 1] then
		return self.stage_reward_flag[stage - 1] == 1
	end
	return true
end

-----------------渡劫------------------------------------
-- 获取修为转血量数据
function CultivationWGData:GetXiuweiToHpData()
	return self.judgment_hp_cache or {}
end

----------------红点----------------------

function CultivationWGData:GetXiuWeiRemind()
	local is_opened = FunOpen.Instance:GetFunIsOpened(FunName.CultivationView)
	if not is_opened then
		return 0
	end

	-- 突破红点
	if self:GetCultivationDuJieRemind() then
		return 1
	end

	-- 特权红点
	if self:GetPrivilegeRemind() then
		return 1
	end

	-- 仙法红点
	if self:GetEsotericaAllRemind() == 1 then
		return 1
	end

	-- 提升红点
	if self:GetUpgradeRemind() then
		return 1
	end

	-- 境界奖励
	if self:GetPreviewAwradRemin() then
		return 1
	end

	-- 怒气技能可激活红点
	-- if self:GetRoleNuqiRed() then
	-- 	return 1
	-- end

	-- 获得修为
	if self:GetCultivationItemRemind() then
		return 1
	end

	if self:GetBuffRemind() then
		return 1
	end

	-- --修为兑换
	-- if cur_xianwei_level >= cur_stage_cfg.exchange_need_level then
	-- 	local exchange_time = self:GetXiuWeiExAngedTimes()
	-- 	local can_exchange_time = other_cfg.exchange_daily_times - exchange_time

	-- 	if can_exchange_time > 0 then
	-- 		if total_exp >= other_cfg.exchange_need_exp then
	-- 			return 1
	-- 		end
	-- 	end
	-- end

	if XiuWeiWGData.Instance:HasCanFetchTaskReward() then
		return 1
	end
	
	return 0
end

-- 获得修为
function CultivationWGData:GetCultivationItemRemind()
	local item_list = Split(self.other_cfg.xiuwei_item_id,"|")
	for index, value in pairs(item_list) do
		local item_id = tonumber(value)

		local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
        local can_use_num = 0 --可使用数量(拥有的和限制数量取最小值)
		
        if item_cfg then
            local use_count = CultivationWGData.Instance:GetDanItemNum(index)
            if item_cfg.use_daytimes ~= 0 then
                local use_num = item_cfg.use_daytimes - use_count
                if use_num > 0 then
                    can_use_num = math.min(use_num, item_num)
                end
            else
                can_use_num = item_num
            end
        end

		if can_use_num > 0 then
			return true
		end
	end
	return false
end

-- 特权红点
function CultivationWGData:GetPrivilegeRemind()
	local is_get_privilege = self:IsGetPrivilege()

	if is_get_privilege then
		if not self:IsGetTodayReward() then
			return true
		end
	end

	return false
end

-- 突破红点
function CultivationWGData:GetCultivationDuJieRemind()
	if XiuWeiWGData.Instance then
		return XiuWeiWGData.Instance:GetSingleLevelIsCanUp(self.stage, self.level)
	end

	return false
	-- local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
	-- local item_id = cur_stage_cfg.cost_stage_item.item_id
	-- local cost_num = cur_stage_cfg.cost_stage_item.num
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

	-- if item_num < cost_num then
	-- 	return false
	-- end

	-- return true
end

-- 是否为最大修为等级
function CultivationWGData:IsXiuWeiMaxLevel()
	local next_xiuwei_cfg = self:GetNextXiuWeiLevelCfg()
	local is_max_level = IsEmptyTable(next_xiuwei_cfg)
	if is_max_level then
		return true
	end
	return false
end

-- 提升红点
function CultivationWGData:GetUpgradeRemind()
	local is_max_level = self:IsXiuWeiMaxLevel()
	if is_max_level then
		return false
	end

	local can_break, is_max_stage = self:IsCanBreak()
	if can_break then
		return true
	end

	if not is_max_stage then
		-- 当前等级的经验和上限
		local cur_xiuwei_level_cfg = self:GetCurXiuWeiLevelCfg()
		local cur_exp = self:GetXiuWeiExp()
		if cur_exp >= cur_xiuwei_level_cfg.need_exp then
			return true
		end
	end

	return false
end

-- 境界预览红点
function CultivationWGData:GetPreviewAwradRemin()
	local preview_cfg = self:GetCultivationPreviewCfg()
	
	for key, value in ipairs(preview_cfg) do
		local is_received = self:GetIsReceivedPreviewReward(value.stage_no)
    	local is_can_receive = not is_received and self.stage >= value.need_stage

		if is_can_receive then
			return true, value.stage_no
		end
	end

	local cur_jingjie_cfg = self:GetCultivationPreviewCfgByStage(self.stage)

	return false, cur_jingjie_cfg.client_stage or 1
end

-- 境界效率加成红点
function CultivationWGData:BuffRedShow()
	for key, value in pairs(CULTIVATION_EXP_ADD_TYPE) do
		if self:GetBuffRemindByType(value) then
			return 1
		end
	end

	return 0
end

-- 境界效率加成红点
function CultivationWGData:GetBuffRemind()
	local is_opened = FunOpen.Instance:GetFunIsOpened(FunName.BiZuo)
	if not is_opened then
		return false
	end
	for key, value in pairs(CULTIVATION_EXP_ADD_TYPE) do
		if self:GetBuffRemindByType(value) then 
			return true
		end
	end
	return false
end

function CultivationWGData:GetBuffRemindTypeIndex()
	for key, cfg in pairs(self.correct_big_type_cfg) do
		if self:GetBuffRemindByBigType(key) then
			return key
		end
	end

	return -1
end

-- 境界效率加成红点 根据大类型
function CultivationWGData:GetBuffRemindByBigType(big_type)
	-- 获取大类型所有的类型
	for key, cfg in pairs(self.correct_big_type_cfg[big_type]) do
		if self:GetBuffRemindByType(cfg.type) then
			return true
		end
	end

	return false
end

-- 境界效率加成红点 根据类型
function CultivationWGData:GetBuffRemindByType(type)
	-- 获取类型所有的子类型
	for key, cfg in pairs(self.correct_sub_type_cfg) do
		if cfg.type == type then
			if self:GetBuffRemindBySubType(cfg.sub_type) then
				return true
			end
		end
	end
	return false
end

-- 境界效率加成红点 根据子类型
function CultivationWGData:GetBuffRemindBySubType(sub_type)
	for i, v in pairs(self.exp_add_cfg) do
		if v.type == sub_type and not self:IsReceivedBuffByIndex(v.type,v.index) then
			if self:IsRichBuffByData(v) then
				return true
			end
		end
	end
	return false
end

-- 怒气技能是否开启
-- 玩家等级和开服天数限制
function CultivationWGData:IsOpenAngerSkill(nuqi_type)
	local cfg = self.nuqi_active_cfg[nuqi_type]

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	if role_level >= cfg.level_limit and open_day>= cfg.open_day_limit then
		return true
	else
		return false
	end
end

-- 怒气技能红点
function CultivationWGData:GetAngerSpiritRemind()
	if not DragonTrialWGData.Instance:IsCanActivate() then
		self:SetAngerSkillStrengthen(MAINUI_TIP_TYPE.DUJIE_ANGER_SPIRIT, 0)
        return 0
    end

	if self.nuqi_active_cfg then
		for k, v in pairs(self.nuqi_active_cfg) do
			if self:IsOpenAngerSkill(v.nuqi_type) then
				-- 怒气本体是否可升级
				if self:IsCanAngerUpgrade(v.nuqi_type) then
					self:SetAngerSkillStrengthen(MAINUI_TIP_TYPE.DUJIE_ANGER_SPIRIT, 1)
					return 1
				end

				-- 本体激活后才可升级技能和buff
				if self:IsActiveAnger(v.nuqi_type) then
					-- 怒气buff 是否可升级
					if self:IsCanAngerBuffUpgrade(v.nuqi_type) then
						self:SetAngerSkillStrengthen(MAINUI_TIP_TYPE.DUJIE_ANGER_SPIRIT, 1)
						return 1
					end
				end
			end

		end
	end

	self:SetAngerSkillStrengthen(MAINUI_TIP_TYPE.DUJIE_ANGER_SPIRIT, 0)
	return 0
end

-- 元神变强更新
function CultivationWGData:SetAngerSkillStrengthen(tip_type, value)
	if value == 1 then
		MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.DujieView)
            return true
        end)
	else
		MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
	end
end

-- 怒气提升红点
function  CultivationWGData:GetAngerSkillRemind()
	if self.nuqi_active_cfg then
		for k, v in pairs(self.nuqi_active_cfg) do
			-- 怒气本体是否可升级
			if self:IsCanAngerUpgrade(v.nuqi_type) then
				return 1
			end

			-- 本体激活后才可升级技能和buff
			if self:IsActiveAnger(v.nuqi_type) then
				-- 怒气buff 是否可升级
				if self:BuffUplevelStuffRed(v.nuqi_type) then
					return 1
				end
				-- 怒气技能是否可升级
				if self:IsCanAllAngerSkillUpgrade(v.nuqi_type) then
					return 1
				end
			end
		end
	end
	-- print_error("没有怒气红点")
	return 0
end
------------------------------------------------------------------------------
-- 获取怒气数据根据类型
function CultivationWGData:GetActiveCfgByType(nuqi_type)
	local empty = {}
	return (self.nuqi_active_cfg or empty)[nuqi_type]
end

-- 获取怒气技能列表根据类型
function CultivationWGData:GetActiveSkillListByType(nuqi_type)
	local empty = {}
	return (self.client_skill_cache or empty)[nuqi_type]
end

-- 获取怒气普攻技能列表根据类型
function CultivationWGData:GetActiveNormalSkillListByType(nuqi_type)
	local empty = {}
	return (self.skill_xiuwei_normal_cache_list or empty)[nuqi_type]
end

-- 获取所有的怒气数据的列表
function CultivationWGData:GetActiveCfg()
	return self.nuqi_active_cfg
end

-- 获取怒气等级满级
function CultivationWGData:GetNuqiMaxLevel(nuqi_type)
	if not nuqi_type then
		return 0
	end
	return #self.nuqi_upgrade[nuqi_type]
end

-- 怒气是否激活
function CultivationWGData:IsActiveAnger(nuqi_type)
	local anger_level = self:GetAngerLevel(nuqi_type)
	return anger_level > 0
end

-- 怒气本体是否满级
function CultivationWGData:IsMaxAngerLevel(nuqi_type)
	local anger_level = self:GetAngerLevel(nuqi_type)
	local anger_max_level = self:GetNuqiMaxLevel(nuqi_type)

	return anger_level >= anger_max_level
end


-- 怒气技能是否满级 skill_index从0开始
function CultivationWGData:IsMaxAngerSkillLevel(nuqi_type, skill_index)
	local skill_level = self:GetAngerSkillLevel(nuqi_type, skill_index)
	local skill_max_level = self:GetNuqiSkillMaxLevel(nuqi_type, skill_index)
	return skill_level >= skill_max_level
end

-- 获取怒气升级配置
function CultivationWGData:GetNuqiUpgradeCfg(nuqi_type, level)
	if self.nuqi_upgrade[nuqi_type] and self.nuqi_upgrade[nuqi_type][level] then
		return self.nuqi_upgrade[nuqi_type][level]
	end
	return nil
end

-- 获取怒气技能等级满级
function CultivationWGData:GetNuqiSkillMaxLevel(nuqi_type, skill_index)
	if not nuqi_type or not skill_index then
		return 0
	end
	if self.nuqi_skill_upgrade[nuqi_type] and self.nuqi_skill_upgrade[nuqi_type][skill_index] then
		return #self.nuqi_skill_upgrade[nuqi_type][skill_index]
	end
	return 0
end

-- 获取怒气技能升级配置
function CultivationWGData:GetNuqiSkillUpgradeCfg(nuqi_type, skill_index, level)
	if self.nuqi_skill_upgrade[nuqi_type] and self.nuqi_skill_upgrade[nuqi_type][skill_index] and self.nuqi_skill_upgrade[nuqi_type][skill_index][level] then
		return self.nuqi_skill_upgrade[nuqi_type][skill_index][level]
	end
	return nil
end

-- 获取怒气buff等级满级
function CultivationWGData:GetAngerBuffMaxLevel(nuqi_type)
	if not nuqi_type then
		return 0
	end
	return #self.nuqi_buff[nuqi_type]
end

-- 当前怒气buff是否已经满级
function CultivationWGData:IsMaxAngerBuff(anger_type)
	local anger_buff_level =self:GetAngerBuffLevel(anger_type)
    local buff_max_level = self:GetAngerBuffMaxLevel(anger_type)

	return anger_buff_level >= buff_max_level
end

-- 当前怒气buff升级道具是否足够
function CultivationWGData:IsRichItemAngerBuff(anger_type)
	local anger_buff_level =self:GetAngerBuffLevel(anger_type)
	local buff_upgrade_cfg = self:GetAngerBuffCfg(anger_type, anger_buff_level)
	local is_act = self:IsActiveAnger(anger_type)

    if buff_upgrade_cfg and is_act then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(buff_upgrade_cfg.item_id)
		return item_num >= buff_upgrade_cfg.item_num, buff_upgrade_cfg.item_id
    end
	return false, 0
end

-- 当前怒气本体升级道具是否足够
function CultivationWGData:IsRichItemAnger(anger_type)
	local anger_level =self:GetAngerLevel(anger_type)
	local upgrade_cfg = self:GetNuqiUpgradeCfg(anger_type, anger_level)
    if upgrade_cfg then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(upgrade_cfg.item_id)
		return item_num >= upgrade_cfg.item_num, upgrade_cfg.item_id
        
    end
	return false, 0
end

-- 当前怒气的所有技能是否可升级
function CultivationWGData:IsCanAllAngerSkillUpgrade(anger_type)
	if self.nuqi_skill_upgrade and self.nuqi_skill_upgrade[anger_type] then
		local list = self.nuqi_skill_upgrade[anger_type]
		for k, v in pairs(list) do
			if self:IsCanAngerSkillUpgrade(anger_type, k) then
				return true
			end
		end
	end

	return false
end

-- 当前怒气技能升级道具是否足够
function CultivationWGData:IsRichItemAngerSkill(anger_type, skill_index)
	local anger_skill_level =self:GetAngerSkillLevel(anger_type, skill_index)
	local skill_upgrade_cfg = self:GetNuqiSkillUpgradeCfg(anger_type,skill_index, anger_skill_level)
    if skill_upgrade_cfg then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(skill_upgrade_cfg.item_id)
		return item_num >= skill_upgrade_cfg.item_num, skill_upgrade_cfg.item_id
    end
	return false, 0
end

-- 怒气变身是否已开启
function CultivationWGData:IsOpenNuQi()
	return self.nuqi_open_flag == 1
end

-- 怒气本体是否可升级 
function  CultivationWGData:IsCanAngerUpgrade(nuqi_type)
	if not self:IsMaxAngerLevel(nuqi_type) then
		if self:IsRichItemAnger(nuqi_type) then
			return true
		end
	end
	return false
end

-- 怒气buff 是否可升级
function CultivationWGData:IsCanAngerBuffUpgrade(nuqi_type)
	if not self:IsMaxAngerBuff(nuqi_type) then
		if self:IsRichItemAngerBuff(nuqi_type) then
			return true
		end
	end
	return false
end

-- 怒气技能 是否可升级 skill_index从0开始
function CultivationWGData:IsCanAngerSkillUpgrade(anger_type, skill_index)
	if not self:IsMaxAngerSkillLevel(anger_type, skill_index) then
		if self:IsRichItemAngerSkill(anger_type, skill_index) then
			return true
		end
	end
	return false
end



-- 获取怒气数据增加衰减列表
function CultivationWGData:GetSkillXiuweiCache()
	return self.skill_xiuwei_cache
end

-- 获取怒气数据等级列表
function CultivationWGData:GetSkillXiuweiLvCache()
	return self.skill_xiuwei_lv_cache
end

-- 获取怒气技能的怒气描述
function CultivationWGData:GetSkillImproveBySkillId(skill_id)
	local empty = {}
	return (self.skill_improve_cfg or empty)[skill_id]
end

-- 获取怒气变声的Order
function CultivationWGData:GetMainUISkillOrder(nuqi_type)
	-- local empty = {}
	-- local order = {}
	-- local nor_skill_list = (self.skill_xiuwei_normal_cache_list or empty)[nuqi_type] or {}
	-- local skill_list = (self.skill_xiuwei_cache_list or empty)[nuqi_type] or {}

	-- for i, v in ipairs(nor_skill_list) do
	-- 	table.insert(order, v)
	-- end

	-- for i, v in ipairs(skill_list) do
	-- 	table.insert(order, v)
	-- end

	-- return order, {1,1,1,1,1,1,1,1,1,1}

	return self.skill_xiuwei_cache_list[nuqi_type], {1,1,1,1,1,1,1,1,1,1}
end

-- 获取怒气变声技能id
function CultivationWGData:GetSkillListByNuqiType(nuqi_type)
    local skill_list = {}
    local order_list = self:GetMainUISkillOrder(nuqi_type)
    for i = 0, #order_list do
        local data = SkillWGData.Instance:GetXiuXianSkillConfig(order_list[i], 1)
        table.insert(skill_list, data)
    end

    return skill_list
end

-- 获取怒气普攻技能id
function CultivationWGData:GetSkillByAttackSeq(attack_index, skill_id)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.special_appearance ~= SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN then
		return nil
	end

	return skill_id + attack_index - 1
end

function CultivationWGData:SetIsUseGuaranteeItem(is_use)
	self.break_is_use_guarantee = is_use
end

function CultivationWGData:GetIsUseGuaranteeItem()
	return self.break_is_use_guarantee or false
end

-- 仙修(境界) 装备界限文本
function CultivationWGData:GetEquipLevelLimitStr()
	local cur_stage_cfg = self:GetCurXiuWeiStageCfg()
	-- 0的时候不显示
	if cur_stage_cfg.equip_level_limit and cur_stage_cfg.equip_level_limit>0 then
		return ToColorStr(string.format(Language.Cultivation.EquipLevelTipStr2, cur_stage_cfg.equip_level_limit), COLOR3B.C8)
	end
	return ""
end

-- 仙修(境界) 装备界限 获取需要减少的穿戴等级限制
function CultivationWGData:GetEquipLevelLimit()
	local cur_stage_cfg = self:GetCurXiuWeiStageCfg()
	if cur_stage_cfg.equip_level_limit then
		return cur_stage_cfg.equip_level_limit
	end
	return 0
end

function CultivationWGData:GetBuffUplevelStuffList()
	return self.xiuwei_buff_up_item
end

function CultivationWGData:CheckIsBuffUplevelStuff(item_id)
	if not self.xiuwei_buff_up_item then
		return
	end

	for i, v in ipairs(self.xiuwei_buff_up_item) do
		if v.item_id == item_id then
			return true
		end
	end

	return false
end

function CultivationWGData:BuffUplevelStuffRed(nuqi_type)
	if not self.xiuwei_buff_up_item then
		return false, nil
	end

	local is_full = self:IsMaxAngerBuff(nuqi_type)
	local is_act = self:IsActiveAnger(nuqi_type)
	local item_id = nil

	for i, v in ipairs(self.xiuwei_buff_up_item) do
		if item_id == nil then
			item_id = v.item_id
		end

		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if item_num >= 1 and (not is_full) and is_act then
			return true, item_id
		end
	end

	return false, item_id
end

--------------------------------------------修为飞仙幻化-----------------------------------------------------

-- 获取属性
function CultivationWGData:GetAttrListByCfg(cfg, next_cfg)
	local cur_table = {}
	local return_table = {}

	if cfg then
		for i = 1, 6 do
			local key = cfg["attr_id" .. i]

			if key then
				cur_table[key] = {}
				cur_table[key].attr_str = key
				cur_table[key].attr_value = cfg["attr_value" .. i]
	
				if next_cfg then
					cur_table[key].add_value = next_cfg["attr_value" .. i] - cur_table[key].attr_value
				end
			end
		end
	end

	for _, attr_data in pairs(cur_table) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

-- 获取对应类型得形象列表
function CultivationWGData:GetImageListByType(active_type)
	local empty = {}
	return (self.xiuwei_image_list_cache or empty)[active_type] or empty
end

function CultivationWGData:GetProgressImage(active_type)
	local list = self:GetImageListByType(active_type)
	local skill_list = self:GetImageSkillListByType(active_type)
	local lv = self:GetAngerLevel(active_type)
	local cur_lv = 0
	local cur_index = 1
	local back_list = {}

	for i, v in ipairs(list) do
		local data = {}
		data.image_lv = v
		data.skill_id = skill_list and skill_list[i] or 0 
		data.is_lock = lv < v

		if not data.is_lock then
			cur_lv = v
			cur_index = i
		end

		table.insert(back_list, data)
	end

	return back_list, cur_lv, cur_index
end

-- 获取对应类型得形象列表
function CultivationWGData:GetImageSkillListByType(active_type)
	local empty = {}
	return (self.xiuwei_image_skill_cache or empty)[active_type] or empty
end

-- 获取对应类型得形象列表
function CultivationWGData:GetImageSkillBySkillId(skill_id)
	local empty = {}
	return (self.nuqi_level_effect_cfg or empty)[skill_id]
end

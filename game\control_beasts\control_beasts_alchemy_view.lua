local NOT_ACTIVE_COLOR = '#A8A5A4FF'
local ALCHEMY_SHARE_TIME = 15
local BEASTS_ALCHEMY_BTN_TYPE = {
    BEASTS_ALCHEMY_ATTR_BTN = 0,
    BEASTS_ALCHEMY_BAG_BTN = 1,
}

function ControlBeastsWGView:LoadAlchemyViewCallBack()
    if not self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid = AsyncListView.New(AlchemyBattleBagItem, self.node_list.beasts_alchemy_bag_grid)
        self.beasts_alchemy_bag_grid:SetSelectCallBack(BindTool.Bind(self.BeastsAlchemyBagSelect,self))
        self.beasts_alchemy_bag_grid:SetLimitSelectFunc(BindTool.Bind(self.BeastsAlchemyBagSelectLimit,self))
        self.beasts_alchemy_bag_grid:SetUseRenderClick(true)
    end

    if not self.beasts_alchemy_model then
		self.beasts_alchemy_model = RoleModel.New()
		self.beasts_alchemy_model:SetUISceneModel(self.node_list["alchemy_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.beasts_alchemy_model, TabIndex.beasts_alchemy)
	end

    -- 星数
    if not self.beasts_alchemy_star_list then
        self.beasts_alchemy_star_list = {}
        for i = 1, 5 do
            self.beasts_alchemy_star_list[i] = self.node_list["beasts_alchemy_star" .. i]
        end
    end

    if not self.alchemy_item_cell then
        self.alchemy_item_cell = ItemCell.New(self.node_list.alchemy_item_pos)
    end

    if not self.alchemy_item_tag then
        self.alchemy_item_tag = {}

        for i = 1, 5 do
            local cell_obj = self.node_list.alchemy_tag:FindObj(string.format("alchemy_tag_%d", i))
            if cell_obj then
                local cell = BeststsAlchemyTagRender.New(cell_obj)
                cell:SetIndex(i)
                self.alchemy_item_tag[i] = cell
            end
        end
    end

    -- 基础属性
    if self.alchemy_base_attrlist == nil then
        self.alchemy_base_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.alchemy_base_attrlist:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetAttrNameNeedSpace(true)
                cell:SetIndex(i)
                self.alchemy_base_attrlist[i] = cell
            end
        end
    end

    -- 附加词条
    if self.alchemy_additional_list == nil then
        self.alchemy_additional_list = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.alchemy_additional_list:FindObj(string.format("additional_0%d", i))
            if attr_obj then
                local cell = AlchemyAdditionalRender.New(attr_obj)
                cell:SetIndex(i)
                self.alchemy_additional_list[i] = cell
            end
        end
    end

    -- 套装属性
    if self.alchemy_suit_list == nil then
        self.alchemy_suit_list = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.alchemy_suit_root:FindObj(string.format("alchemy_suit_render_%d", i))
            if attr_obj then
                local cell = AlchemySuitTipsAttrRender.New(attr_obj)
                cell:SetIsCenter(true)
                cell:SetIndex(i)
                self.alchemy_suit_list[i] = cell
            end
        end
    end

    if not self.alchemy_equip_bag_grid then
        self.alchemy_equip_bag_grid = AsyncBaseGrid.New()
        self.alchemy_equip_bag_grid:CreateCells({
										col = 4, 
										cell_count = BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_BAG_NUM, 
										list_view = self.node_list["alchemy_equip_bag_grid"], 
										itemRender = AlchemyNormalBagItem,
										change_cells_num = 0,
										assetBundle = "uis/view/control_beasts_ui_prefab",
										assetName = "layout_beasts_alchemy_equip_bag_item",
		})
		self.alchemy_equip_bag_grid:SetStartZeroIndex(false)
        self.alchemy_equip_bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectBeastEquipBagCallBack, self))
	end
    
    -- 装备类型
    if self.alchemy_equip_bag_type_list == nil then
        self.alchemy_equip_bag_type_list = {}
        for i = 0, 5 do
            local attr_obj = self.node_list.alchemy_equip_bag_type_list:FindObj(string.format("alchemy_bag_type_%d", i))
            if attr_obj then
                local cell = BeastsAlchemyBagTypeItemRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetClickCallBack(BindTool.Bind(self.AlchemyEquipBagTypeClickCallBack, self))
                self.alchemy_equip_bag_type_list[i] = cell
            end
        end
    end

    self:InitAlchemyEquipList()
    XUI.AddClickEventListener(self.node_list.beasts_alchemy_attr_btn, BindTool.Bind2(self.BeastsAlchemyTypeBtnClick, self, BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_ATTR_BTN))        
    XUI.AddClickEventListener(self.node_list.beasts_alchemy_bag_btn, BindTool.Bind2(self.BeastsAlchemyTypeBtnClick, self, BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_BAG_BTN))        
    XUI.AddClickEventListener(self.node_list.beasts_alchemy_culture_btn, BindTool.Bind(self.OpenBeastAlchemyCultureClick, self))    
    XUI.AddClickEventListener(self.node_list.beasts_alchemy_all_attr_btn, BindTool.Bind(self.OpenBeastAlchemyAllAttrClick, self))    
    XUI.AddClickEventListener(self.node_list.beasts_alchemy_get_btn, BindTool.Bind(self.OpenBeastGetEquipClick, self))    
    XUI.AddClickEventListener(self.node_list.alchemy_hole_close_caozuo_btn, BindTool.Bind(self.OnClickHoleCloseAndRemove, self))    -- 关闭操作并卸下
    XUI.AddClickEventListener(self.node_list.alchemy_hole_xiulian_btn, BindTool.Bind(self.OnClickHoleXiuLian, self))                -- 孔位修炼
    XUI.AddClickEventListener(self.node_list.alchemy_hole_fenxiang_btn, BindTool.Bind(self.OnClickHoleShare, self))                 -- 孔位分享
    XUI.AddClickEventListener(self.node_list.alchemy_quick_wear_btn, BindTool.Bind(self.OnClickQuickWearAllSlot, self))             -- 一键穿戴
    XUI.AddClickEventListener(self.node_list.beasts_alchemy_hole_operate_close, BindTool.Bind(self.OnClickCloseHoleClose, self))    -- 关闭当前操作
end


function ControlBeastsWGView:OpenAlchemyViewCallBack()
end

function ControlBeastsWGView:CloseAlchemyViewCallBack()
end

function ControlBeastsWGView:ShowAlchemyViewCallBack()
end

function ControlBeastsWGView:ReleaseAlchemyViewCallBack()
    if self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:DeleteMe()
        self.beasts_alchemy_bag_grid = nil
    end

    if self.beasts_alchemy_model then
        self.beasts_alchemy_model:DeleteMe()
        self.beasts_alchemy_model = nil
    end

    if self.alchemy_item_cell then
        self.alchemy_item_cell:DeleteMe()
        self.alchemy_item_cell = nil
    end

    if self.beasts_alchemy_cambered_list then
        self.beasts_alchemy_cambered_list:DeleteMe()
        self.beasts_alchemy_cambered_list = nil
    end

    if self.alchemy_item_tag then
        for i, v in ipairs(self.alchemy_item_tag) do
            v:DeleteMe()
        end

        self.alchemy_item_tag = nil
    end

    if self.alchemy_base_attrlist and #self.alchemy_base_attrlist > 0 then
		for _, base_attr_cell in ipairs(self.alchemy_base_attrlist) do
			base_attr_cell:DeleteMe()
			base_attr_cell = nil
		end

		self.alchemy_base_attrlist = nil
	end

    if self.alchemy_additional_list and #self.alchemy_additional_list > 0 then
		for _, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
			alchemy_additional_cell:DeleteMe()
			alchemy_additional_cell = nil
		end

		self.alchemy_additional_list = nil
	end

    if self.alchemy_suit_list and #self.alchemy_suit_list > 0 then
		for _, alchemy_suit_cell in ipairs(self.alchemy_suit_list) do
			alchemy_suit_cell:DeleteMe()
			alchemy_suit_cell = nil
		end

		self.alchemy_suit_list = nil
	end

    if self.alchemy_equip_bag_grid then
        self.alchemy_equip_bag_grid:DeleteMe()
        self.alchemy_equip_bag_grid = nil
    end

    if self.alchemy_equip_bag_type_list then
        for i = 0, 5 do
            local cell = self.alchemy_equip_bag_type_list[i]
            if cell then
                cell:DeleteMe()
                cell = nil
            end
        end

        self.alchemy_equip_bag_type_list = nil
    end

    self.is_init_list = nil
    self.beasts_alchemy_star_list = nil
    self.beasts_alchemy_model_res_id = nil
    self.beasts_alchemy_bag_select_index = nil
    self.beasts_alchemy_bag_select_data = nil
    self.is_show_beasts_alchemy_operate_btns = nil
    self.alchemy_equip_index = nil

    self:RemoveAlchemyCoolingDelayTimer()
    self.alchemy_hole_share_cooling = nil
end

function ControlBeastsWGView:InitAlchemyEquipList()
    local cambered_list_data = {
		item_render = AlchemyEquiipItem,
		asset_bundle = "uis/view/control_beasts_ui_prefab",
		asset_name = "layout_beasts_alchemy_hole_render",

		scroll_list = self.node_list.scroll_conter,
		center_x = 0, center_y = 0,
		radius_x = 296, radius_y = 90,-- x 椭圆半长轴,y 椭圆半短轴
		angle_delta = Mathf.PI * 2 / 5,
		origin_rotation = Mathf.PI,
		is_drag_horizontal = true,
		scale_min = 0.5,			-- 最小缩放比例
        alpha_min = 0,
		need_change_scale = true,
		need_updown_click = true,
		up_drag_root = self.node_list.scroll_top_drag,
		down_drag_root = self.node_list.scroll_down_drag,
		arg_adjust = 0.9,			-- 手动拖动时的速度控制
		is_assist = true,

		click_item_cb = BindTool.Bind(self.BeastsAlchemyEquipClick, self),
		drag_to_next_cb = BindTool.Bind(self.BeastsAlchemyEquipNext, self),
		drag_to_last_cb = BindTool.Bind(self.BeastsAlchemyEquipLast, self),
		on_drag_end_cb = BindTool.Bind(self.BeastsAlchemyEquipDragEnd, self),
		set_item_pos_cb = BindTool.Bind(self.BeastsAlchemyEquipSetPos, self),
        on_begin_drag_cb = BindTool.Bind(self.BeastsAlchemyEquipBeginDrag, self),
	}

	self.beasts_alchemy_cambered_list = CamberedList.New(cambered_list_data)
end

-- 点击目标
function ControlBeastsWGView:BeastsAlchemyEquipClick(item)
    if (not item) or (not item.data) then
        return
    end

    local index = item:GetIndex()
    if self.alchemy_equip_index == index then
        self:CheckNeedCloseAlchemyBeastHoleOperate(true)
    else
        self.alchemy_equip_index = index
        self:FlushAlchemyBeastHoleOperate(false)
        local btn_item_list = self.beasts_alchemy_cambered_list:GetRenderList()

        for k, item_cell in ipairs(btn_item_list) do
            item_cell:SetEquipSelect(false)
        end

        self:OnSelectedAlchemyEquipChange(function ()
            if self:IsOpen() then
                self:FlushAlchemyBeastEquipSelect(true)
            end
        end, true)
    end
end

-- 拖拽到下一个
function ControlBeastsWGView:BeastsAlchemyEquipNext()
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO 
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local max_count = list ~= nil and #list + COMMON_CONSTS.NUMBER_ONE or COMMON_CONSTS.NUMBER_ONE
    self.alchemy_equip_index = self.alchemy_equip_index + COMMON_CONSTS.NUMBER_ONE
	self.alchemy_equip_index = self.alchemy_equip_index + COMMON_CONSTS.NUMBER_ONE > max_count and COMMON_CONSTS.NUMBER_ZERO  or self.alchemy_equip_index
end

-- 拖拽到上一个
function ControlBeastsWGView:BeastsAlchemyEquipLast()
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local max_count = list ~= nil and #list + COMMON_CONSTS.NUMBER_ONE or COMMON_CONSTS.NUMBER_ONE
    self.alchemy_equip_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
	self.alchemy_equip_index = self.alchemy_equip_index -COMMON_CONSTS.NUMBER_ONE < COMMON_CONSTS.NUMBER_ZERO  and max_count or self.alchemy_equip_index
end

-- 开始拖拽
function ControlBeastsWGView:BeastsAlchemyEquipBeginDrag()
    self:FlushAlchemyBeastHoleOperate(false)
    local btn_item_list = self.beasts_alchemy_cambered_list:GetRenderList()
    for k, item_cell in ipairs(btn_item_list) do
        item_cell:SetEquipSelect(false)
    end
end

-- 拖拽完成回调
function ControlBeastsWGView:BeastsAlchemyEquipDragEnd()
    self:OnSelectedAlchemyEquipChange(function ()
        if self:IsOpen() then
            self:FlushAlchemyBeastEquipSelect(true)
        end
	end, true, self.alchemy_equip_index)
end

-- 设置为之后的回调
function ControlBeastsWGView:BeastsAlchemyEquipSetPos()

end

-- 选中出战位
function ControlBeastsWGView:BeastsAlchemyBagSelect(beast_alchemy_item, cell_index, is_default, is_click)
    if beast_alchemy_item == nil or is_default or beast_alchemy_item.data == nil then
        return
    end

    if self.beasts_alchemy_bag_select_index == cell_index and is_click then
        return
    end

    self.beasts_alchemy_bag_select_index = cell_index
    self.beasts_alchemy_bag_select_data = beast_alchemy_item.data
    if is_click then
        self.alchemy_equip_index = nil
    end
    self:FlushAlchemySelectMessage()
end

-- 点击按钮
function ControlBeastsWGView:BeastsAlchemyBagSelectLimit(beast_slot_item)
    if beast_slot_item == nil or beast_slot_item.data == nil then
        return
    end

    if beast_slot_item.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyTabSelectError2)
        return true
    end

    if beast_slot_item.data.beasts_bag_id == -1 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyTabSelectError)
        self:ChangeToIndex(TabIndex.beasts_battle)
        return true
    end

    return false
end

-- 背包点击
function ControlBeastsWGView:SelectBeastEquipBagCallBack(cell)
    if (not cell) or (not cell.index) then
        return
    end

    local data = cell:GetData()
    -- 属性值 基础属性=装备自带属性+强化属性
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local equip_data = ControlBeastsCultivateWGData.Instance:GetEquipCfg(data.item_id)
    local now_equip_data = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipBySlot(aim_hole_id, equip_data.hole)

    local item_data = {
        item_id = data.item_id, 
        is_bind = 0,
        num = 1,
        index = data.index,
        knapsack_type = 13,
        equip_info = data, 
        is_bag_equip = true, 
        fight_slot = aim_hole_id, 
        equip_slot = equip_data.hole,
        equip_slot_lv = now_equip_data and now_equip_data.level or 0,
    }

    local item_tips_btn_click_callback = {}
    local operate_data = {}
    operate_data.btn_text = Language.Tip.ButtonLabel[ItemTip.HANDLE_EQUIP]
    operate_data.show_red = false
	operate_data.btn_click = function()
        if ControlBeastsCultivateWGData.Instance:IsHasBeastEquipByFightSlot(aim_hole_id, equip_data.hole) then
            TipWGCtrl.Instance:CloseContrastItemTip()
        else
            TipWGCtrl.Instance:CloseNormalItemTip()
        end
        
        ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperatePut(aim_hole_id, equip_data.hole, data.index)
    end

    local operate_sell_data = {}
    operate_sell_data.btn_text = Language.Tip.ButtonLabel[ItemTip.SHICHANG_WORLD_SELL]
    operate_sell_data.show_red = false
	operate_sell_data.btn_click = function()
        if ControlBeastsCultivateWGData.Instance:IsHasBeastEquipByFightSlot(aim_hole_id, equip_data.hole) then
            TipWGCtrl.Instance:CloseContrastItemTip()
        else
            TipWGCtrl.Instance:CloseNormalItemTip()
        end
        
        MarketWGCtrl.Instance:OpenMarketTipItemView(item_data)
    end
   
    table.insert(item_tips_btn_click_callback, operate_sell_data)
    table.insert(item_tips_btn_click_callback, operate_data)

    local operate_click_back = nil
    local now_sort_score = data.sort_score or COMMON_CONSTS.NUMBER_ZERO
    if now_sort_score ~= COMMON_CONSTS.NUMBER_ZERO then
        operate_click_back = item_tips_btn_click_callback
    end

    TipWGCtrl.Instance:OpenItem(item_data, ItemTip.BEAST_ALCHEMY_EQUIP_BAG, nil, nil, operate_click_back)
end

-- 背包类型点击
function ControlBeastsWGView:AlchemyEquipBagTypeClickCallBack(cell)
    if (not cell) or (not cell.index) then
        return
    end

    local index = cell:GetIndex()
    local final_type = self.now_alchemy_bag_equip_type + COMMON_CONSTS.NUMBER_ONE
    if final_type == index then
        return
    end

    self.now_alchemy_bag_equip_type = index - 1
    self:FlushAlchemyBeastEquipBag()
end
-----------------------------------------------------------------------------
function ControlBeastsWGView:FlushAlchemyViewCallBack(param_t)
	-- for k,v in pairs(param_t) do
	-- 	if k == "all" then
    --     elseif k == "main" then
    --     elseif k == "assist" then
    --     end
    -- end
    self:FlushAlchemyBagList()
    -- self:FlushAlchemyBeastEquipBag()
    -- self:FlushAlchemyBeastShowType()
end

-- 刷新出战背包列表
function ControlBeastsWGView:FlushAlchemyBagList()
    local list = ControlBeastsWGData.Instance:GetAllBattleBattleList()
    self.beasts_alchemy_bag_grid:SetDataList(list)

    if not self.beasts_alchemy_bag_select_index then
        self.beasts_alchemy_bag_select_index = COMMON_CONSTS.NUMBER_ONE
    end

    self.beasts_alchemy_bag_grid:JumpToIndex(self.beasts_alchemy_bag_select_index, 6)
end

-- 刷新装备数据
function ControlBeastsWGView:FlushAlchemySelectMessage()
    if not self.beasts_alchemy_bag_select_data then
        return
    end    

    local select_data = self.beasts_alchemy_bag_select_data
    self:FlushAlchemyBeastEquip(select_data)

    if select_data.state == BEASTS_HOLE_STATUS.ACTIVE and select_data.beasts_bag_id ~= -1 then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(select_data.beasts_bag_id)
        self:FlushAlchemyBeastModel(beast_data)
        self:FlushAlchemyBeastMessage(beast_data)
    end

    local is_can_cultivate = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanCultivateForHole(select_data.hole_id, true, true)
    self.node_list.beasts_alchemy_culture_red:CustomSetActive(is_can_cultivate)
end

-- 刷新模型
function ControlBeastsWGView:FlushAlchemyBeastModel(beast_data)
    if not self:CheckHaveDataAndServerData(beast_data) then
        return
    end

    local server_data = beast_data.server_data
    local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(server_data.beast_id, server_data.use_skin)

    if self.beasts_alchemy_model_res_id ~= res_id then
        self.beasts_alchemy_model_res_id = res_id
        local bundle, asset = ResPath.GetBeastsModel(res_id)
        self.beasts_alchemy_model:SetMainAsset(bundle, asset)
        self.beasts_alchemy_model:SetUSAdjustmentNodeLocalPosition(-0.6, 0, 0)
        self.beasts_alchemy_model:PlayRoleAction(SceneObjAnimator.Rest)
    end
end

-- 刷新中间的模型星级和等级
function ControlBeastsWGView:FlushAlchemyBeastMessage(beast_data)
    if not self:CheckHaveDataAndServerData(beast_data) then
        return
    end

    local server_data = beast_data.server_data
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    if beast_cfg then
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
        self.node_list.beasts_alchemy_color_img.image:LoadSprite(bundle, asset, function()
            self.node_list.beasts_alchemy_color_img.image:SetNativeSize()
        end)

        if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
            bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
            self.node_list.beasts_alchemy_color_img:ChangeAsset(bundle, asset)
        end

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
        local no_have_star = "a3_ty_xx_zc0"
    	for k,v in pairs(self.beasts_alchemy_star_list) do
            v:CustomSetActive(star_res_list[k] ~= no_have_star)
        	v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
    	end

        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
        self.node_list.beasts_alchemy_element_img.image:LoadSprite(bundle, asset, function()
            self.node_list.beasts_alchemy_element_img.image:SetNativeSize()
        end)

        self.node_list.beasts_alchemy_name.text.text = beast_cfg.beast_name
        self.node_list.beasts_alchemy_level.text.text = server_data.beast_level
    end
end

-- 刷新中间的装备
function ControlBeastsWGView:FlushAlchemyBeastEquip(beast_data)
    local list, jump_index = ControlBeastsCultivateWGData.Instance:GetFightBeastCamberedEquipList(beast_data.hole_id)
    if list then
        -- 红点切换
        if not self.alchemy_equip_index then
            self.alchemy_equip_index = jump_index or COMMON_CONSTS.NUMBER_ONE
        end
   
        if not self.is_init_list then
            local client_count = #list
            self.beasts_alchemy_cambered_list:CreateCellList(client_count)
            self.is_init_list = true
        end
   
        local btn_item_list = self.beasts_alchemy_cambered_list:GetRenderList()
        for k, item_cell in ipairs(btn_item_list) do
            local item_data = list[k]
            item_cell:SetData(item_data)
        end

        self:OnSelectedAlchemyEquipChange(function()
            self:FlushAlchemyBeastEquipSelect(false)
        end, false, self.alchemy_equip_index)
    end
end

-- 选中某一个
function ControlBeastsWGView:OnSelectedAlchemyEquipChange(callback, is_click, drag_index)
	local to_index = drag_index ~= nil and drag_index or self.alchemy_equip_index or COMMON_CONSTS.NUMBER_ONE
	self.beasts_alchemy_cambered_list:ScrollToIndex(to_index, callback, is_click)
end

-- 选中装备
function ControlBeastsWGView:FlushAlchemyBeastEquipSelect(is_click)
    if (not self.beasts_alchemy_bag_select_data) or (not self.alchemy_equip_index) then
        return
    end

    -- 属性值 基础属性=装备自带属性+强化属性
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local slot_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
    local aim_data = list and list[slot_index]

    local btn_item_list = self.beasts_alchemy_cambered_list:GetRenderList()
    for k, item_cell in ipairs(btn_item_list) do
        item_cell:SetEquipSelect(self.alchemy_equip_index == k)
    end

    self:FlushAlchemyBeastEquipMessage(aim_data, aim_hole_id, slot_index)        -- 刷新详细信息
end

-- 选中装备
function ControlBeastsWGView:FlushAlchemyBeastEquipMessage(aim_data, aim_hole_id, slot_index)
    local is_final_lock, level_limit, now_lv = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipSlotIsLock(aim_hole_id, slot_index)
    self.node_list.beasts_alchemy_full_root:CustomSetActive(is_final_lock)
    self.node_list.beasts_alchemy_tips_root:CustomSetActive(is_final_lock)
    local color = now_lv >= level_limit and COLOR3B.GREEN or COLOR3B.RED
    local pro_str = string.format("%d/%d", now_lv, level_limit)
    pro_str = ToColorStr(pro_str, color)
    self.node_list.beasts_alchemy_tips_txt.text.text = string.format(Language.ContralBeastsAlchemy.HoleUnlockConditionTips, pro_str)
    self:FlushAlchemyBeastHoleOperate(false)

    -- 刷新装备数据
    self.now_alchemy_bag_equip_type = slot_index
    local equip_info = aim_data and aim_data.equip_info
    local equip_item_id = equip_info and equip_info.item_id or COMMON_CONSTS.NUMBER_ZERO
    self.node_list.beasts_alchemy_not_item_root:CustomSetActive(not is_final_lock and equip_item_id == COMMON_CONSTS.NUMBER_ZERO)
    self.node_list.beasts_alchemy_message_root:CustomSetActive(not is_final_lock and equip_item_id ~= COMMON_CONSTS.NUMBER_ZERO)

    if is_final_lock then
        self:FlushAlchemyBeastEquipBag()
        self:FlushAlchemyBeastShowType()
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyEquipSlotLock)
        return
    end

    if equip_item_id ~= COMMON_CONSTS.NUMBER_ZERO then
        local item_data = {
            item_id = equip_item_id, 
            equip_info = equip_info, 
            is_bag_equip = false, 
            is_wear = true,
            fight_slot = aim_hole_id, 
            equip_slot = slot_index,
            equip_slot_lv = aim_data.level,
        }

        self.alchemy_item_cell:SetData(item_data)
        local item_cfg = ItemWGData.Instance:GetItemConfig(equip_item_id)

        if item_cfg then
            local lv_str = ""
            local name_str = item_cfg.name

            if aim_data.level ~= 0 then
                lv_str = ToColorStr(string.format("+%d", aim_data.level), COLOR3B.GREEN) 
                name_str = string.format("%s(%s)", item_cfg.name, lv_str)
            end

            local str = ToColorStr(name_str, ITEM_COLOR[item_cfg.color])
            self.node_list.alchemy_name.text.text = str
        end

        local equip_cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(equip_item_id)
        -- 类型
        self.node_list.alchemy_condition.text.text = string.format(Language.Tip.ZhuangBeiLeiXing_1, COLOR3B.DEFAULT, equip_cfg.hole_name or "")
        local score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(
            equip_info, 
            false,
            aim_hole_id, 
            slot_index, 
            aim_data.level
        )
        self.node_list.alchemy_score.text.text = score
        self.now_beast_alchemy_show_type = BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_ATTR_BTN
        self:FlushAlchemyBeastEquipAttr(aim_data, aim_hole_id, slot_index)           -- 刷新基础信息
        self:FlushAlchemyBeastEquipAdditional(aim_data, aim_hole_id, slot_index)     -- 刷新附加信息
        self:FlushAlchemyBeastEquipSuit(aim_data, aim_hole_id, slot_index)           -- 刷新套装信息
    else
        self.now_beast_alchemy_show_type = BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_BAG_BTN
    end

    self:FlushAlchemyBeastEquipBag()
    self:FlushAlchemyBeastShowType()
    local words_list = equip_info and equip_info.words_list or {}
    local server_alchemy_tag_index = COMMON_CONSTS.NUMBER_ZERO
    local is_show_tag = false

    for i, alchemy_tag_cell in ipairs(self.alchemy_item_tag) do
        local ward_data = words_list[server_alchemy_tag_index]
		if ward_data then
            local random_value = ward_data.words_seq % 100
			local real_seq = math.floor(ward_data.words_seq / 100)
            local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)

            if cfg and cfg.simple_desc and cfg.simple_desc ~= "" then
                is_show_tag = true
                alchemy_tag_cell:SetVisible(true)
                alchemy_tag_cell:SetData(cfg)
            else
                alchemy_tag_cell:SetVisible(false)
            end
        end

        server_alchemy_tag_index = server_alchemy_tag_index + 1
    end

    self.node_list.alchemy_tag:CustomSetActive(is_show_tag)

    if is_show_tag then
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_tag.rect)
    end

    local is_can_strengthern = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanStrengthen(slot_index, aim_data)
    local is_can_succinctsave = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanSuccinctSave(aim_data)
    self.node_list.alchemy_hole_xiulian_red:CustomSetActive(is_can_strengthern or is_can_strengthern)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_message_panel.rect)
end

-- 刷新基础信息
function ControlBeastsWGView:FlushAlchemyBeastEquipAttr(aim_data, aim_hole_id, slot_index)   
    local equip_info = aim_data and aim_data.equip_info
    local slot_lv = aim_data and aim_data.level
    local attr_list = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipAttrList(equip_info, slot_index, slot_lv)
    local color = slot_lv > 0 and COLOR3B.GREEN or COLOR3B.WHITE

    for i, compose_attr_cell in ipairs(self.alchemy_base_attrlist) do
        compose_attr_cell:SetVisible(attr_list[i] ~= nil)

        if attr_list[i] ~= nil then
            local slot_attr_value = attr_list[i].attr_value or 0
            attr_list[i].add_value = 0
            compose_attr_cell:SetData(attr_list[i])

            if slot_attr_value and slot_attr_value ~= 0 then
                local is_per = attr_list[i].is_per or EquipmentWGData.Instance:GetAttrIsPer(attr_list[i].attr_str)
                local per_desc = is_per and "%" or ""
                local value_str = is_per and slot_attr_value / 100 or slot_attr_value
                compose_attr_cell:ResetAttrVlaue(value_str, color)
        
            end
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_base_attrlist.rect)
end 

-- 刷新附加信息
function ControlBeastsWGView:FlushAlchemyBeastEquipAdditional(aim_data, aim_hole_id, slot_index)
    local equip_info = aim_data and aim_data.equip_info
    local words_list = equip_info and equip_info.words_list or {}
    local is_show_words_list = false
    local words_start_index = COMMON_CONSTS.NUMBER_ZERO

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        local word_data = words_list[words_start_index]
        alchemy_additional_cell:SetVisible(word_data ~= nil and word_data.words_seq ~= -1)

        if word_data ~= nil and word_data.words_seq ~= -1 then
            is_show_words_list = true
            alchemy_additional_cell:SetData(word_data)
        end

        words_start_index = words_start_index + 1
    end

    self.node_list.alchemy_additional_empty:CustomSetActive(not is_show_words_list)
    self.node_list.alchemy_additional_list:CustomSetActive(is_show_words_list)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_additional_list.rect)
end 

-- 刷新套装信息
function ControlBeastsWGView:FlushAlchemyBeastEquipSuit(aim_data, aim_hole_id, server_index) 
    if (not self.beasts_alchemy_bag_select_data) or (not self.alchemy_equip_index) then
        return
    end

    -- 刷新装备数据
    local equip_info = aim_data and aim_data.equip_info
    local equip_item_id = equip_info and equip_info.item_id or COMMON_CONSTS.NUMBER_ZERO
    local cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(equip_info.item_id)
    local suit_type = cfg and cfg.suit_type or -1
    self.node_list.alchemy_suit_root:CustomSetActive(suit_type ~= -1)
    self.node_list.alchemy_suit_empty:CustomSetActive(suit_type == -1)

    if suit_type ~= -1 then
        local suit_num = ControlBeastsCultivateWGData.Instance:GetFightBeastAllEquipSuitNum(aim_hole_id, suit_type)
        local list = ControlBeastsCultivateWGData.Instance:GetSuitListCfg(suit_type)
    
        for i, alchemy_suit_cell in ipairs(self.alchemy_suit_list) do
            alchemy_suit_cell:SetVisible(list[i] ~= nil)
    
            if list[i] ~= nil then
                alchemy_suit_cell:SetNumSuitNum(suit_num)
                alchemy_suit_cell:SetSuitStatusColor(COLOR3B.GREEN, COLOR3B.DEFAULT, true)
                alchemy_suit_cell:SetData(list[i])
            end
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_suit_root.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.beasts_alchemy_message_content.rect)
end          

-- 刷新装备数据背包
function ControlBeastsWGView:FlushAlchemyBeastEquipBag()
    if not self.now_alchemy_bag_equip_type then
        self.now_alchemy_bag_equip_type = -1
    end

    -- 属性值 基础属性=装备自带属性+强化属性
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipBag() or {}
    local grid_list = {}

    for k, v in pairs(list) do
        if v.item_id ~= 0 then
            local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
            v.color = item_cfg.color or 0
            v.equip_score = 0
            local cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(v.item_id)
            local hole = cfg and cfg.hole or -1
            v.now_equip_data = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipBySlot(aim_hole_id, hole)
            local is_lock = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipSlotIsLock(aim_hole_id, hole)
     
            if self.now_alchemy_bag_equip_type == -1 then
                v.aim_hole_id = aim_hole_id
                v.equip_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(v)
                table.insert(grid_list, v)
            else
                if hole == self.now_alchemy_bag_equip_type then
                    v.aim_hole_id = aim_hole_id
                    v.equip_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(v)
                    table.insert(grid_list, v)
                end
            end

            v.sort_score = is_lock and 0 or 1
            v.now_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(v.now_equip_data.equip_info)

            if v.equip_score > v.now_score and v.now_score ~= COMMON_CONSTS.NUMBER_ZERO then
                v.sort_score = v.sort_score + 10
            end
        end
    end

    table.sort(grid_list, SortTools.KeyUpperSorters("color", "sort_score", "equip_score"))
    self.alchemy_equip_bag_grid:SetDataList(grid_list)
    self:FlushAlchemyBeastEquipBagTypeSelect()

    local can_wear = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanWearForHole(aim_hole_id)
    self.node_list.alchemy_bag_btn_remind:CustomSetActive(can_wear)
    self.node_list.alchemy_quick_wear_remind:CustomSetActive(can_wear)
end

-- 刷新装备数据背包
function ControlBeastsWGView:FlushAlchemyBeastEquipBagTypeSelect()
    -- 属性值 基础属性=装备自带属性+强化属性
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  

    local final_type = self.now_alchemy_bag_equip_type + COMMON_CONSTS.NUMBER_ONE
    for i = 0, 5 do
        local cell = self.alchemy_equip_bag_type_list and self.alchemy_equip_bag_type_list[i]
        cell:SetData({hole_id = aim_hole_id})
        cell:FlushSelectHl(final_type == i)
    end
end

-- 展示选择类型按钮
function ControlBeastsWGView:FlushAlchemyBeastShowType(is_refresh_bag)
    if not self.now_beast_alchemy_show_type then
        self.now_beast_alchemy_show_type = BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_ATTR_BTN
        -- 这里查看背包红点确定是否切换
    end

    self:FlushAlchemyBeastShowTypeStatus()
end

-- 展示选择类型按钮
function ControlBeastsWGView:FlushAlchemyBeastShowTypeStatus()
    if not self.node_list then
        return
    end

    self.node_list.beasts_alchemy_attr_root:CustomSetActive(self.now_beast_alchemy_show_type == BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_ATTR_BTN)
    self.node_list.beasts_alchemy_bag_root:CustomSetActive(self.now_beast_alchemy_show_type == BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_BAG_BTN)
    self.node_list.alchemy_attr_btn_Image_hl:CustomSetActive(self.now_beast_alchemy_show_type == BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_ATTR_BTN)
    self.node_list.alchemy_bag_btn_Image_hl:CustomSetActive(self.now_beast_alchemy_show_type == BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_BAG_BTN)
    self.node_list.alchemy_attr_btn_Image_nor:CustomSetActive(self.now_beast_alchemy_show_type ~= BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_ATTR_BTN)
    self.node_list.alchemy_bag_btn_Image_nor:CustomSetActive(self.now_beast_alchemy_show_type ~= BEASTS_ALCHEMY_BTN_TYPE.BEASTS_ALCHEMY_BAG_BTN)
end

-- 打开操作面板
function ControlBeastsWGView:FlushAlchemyBeastHoleOperate(is_show)
    self.is_show_beasts_alchemy_operate_btns = is_show
    self.node_list.beasts_alchemy_hole_operate_root:CustomSetActive(is_show)
end

-- 获取锁定状态
function ControlBeastsWGView:CheckNeedCloseAlchemyBeastHoleOperate(is_show)
    if (not self.beasts_alchemy_bag_select_data) or (not self.alchemy_equip_index) then
        return
    end

    -- 属性值 基础属性=装备自带属性+强化属性
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local slot_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
    local aim_data = list and list[slot_index]

    if aim_data and aim_data.equip_info and aim_data.equip_info.item_id ~= 0 then
        if self.is_show_beasts_alchemy_operate_btns == is_show then
            is_show = not is_show
        end

        self:FlushAlchemyBeastHoleOperate(is_show)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyEquipSlotError1)
    end
end

-----------------------------------------------------------------------------
-----------------------------------------------------------------------------
-----------------------------------------------------------------------------
-- 切换页签
function ControlBeastsWGView:BeastsAlchemyTypeBtnClick(beast_alchemy_show_type)
    if self.now_beast_alchemy_show_type == beast_alchemy_show_type then
        return
    end

    self.now_beast_alchemy_show_type = beast_alchemy_show_type
    self:FlushAlchemyBeastShowTypeStatus()
    self:FlushAlchemyBeastEquipBag()
end

-- 点击修炼
function ControlBeastsWGView:OpenBeastAlchemyCultureClick()
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local slot_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
    local aim_data = list and list[slot_index]

    if aim_data and aim_data.equip_info and aim_data.equip_info.item_id ~= 0 then
        ControlBeastsCultivateWGCtrl.Instance:OpenCultivateView(aim_hole_id, slot_index)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.HoleSelectError2)
    end
end

-- 点击属性总览
function ControlBeastsWGView:OpenBeastAlchemyAllAttrClick()
    -- print_error("点击属性总览")
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO 
    ControlBeastsCultivateWGCtrl.Instance:OpenBeastsAlchemyAttrView(aim_hole_id)
end

-- 点击获取装备
function ControlBeastsWGView:OpenBeastGetEquipClick()
    -- print_error("点击获取装备")

end

function ControlBeastsWGView:OnClickHoleCloseAndRemove()    -- 关闭操作并卸下
    if (not self.beasts_alchemy_bag_select_data) or (not self.alchemy_equip_index) then
        return
    end

    -- 属性值 基础属性=装备自带属性+强化属性
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local slot_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
    local aim_data = list and list[slot_index]
    self:FlushAlchemyBeastHoleOperate(false)
    ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateUnPut(aim_hole_id, slot_index)
end

function ControlBeastsWGView:OnClickHoleXiuLian()                 -- 孔位修炼
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local slot_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
    ControlBeastsCultivateWGCtrl.Instance:OpenCultivateView(aim_hole_id, slot_index)
end

function ControlBeastsWGView:OnClickHoleShare()                  -- 孔位分享
    if self.alchemy_hole_share_cooling then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyEquipSlotShareCool)
        return
    end

    self:RemoveAlchemyCoolingDelayTimer()
    self.alchemy_hole_share_cooling = true
    self.alchemy_hole_share_cooling_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self.alchemy_hole_share_cooling = false
	end, ALCHEMY_SHARE_TIME)

    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)
    local slot_index = self.alchemy_equip_index - COMMON_CONSTS.NUMBER_ONE
    local aim_data = list and list[slot_index]
    ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateShare(aim_hole_id, slot_index, aim_data.equip_info.index)
end

--移除回调
function ControlBeastsWGView:RemoveAlchemyCoolingDelayTimer()
    if self.alchemy_hole_share_cooling_timer then
        GlobalTimerQuest:CancelQuest(self.alchemy_hole_share_cooling_timer)
        self.alchemy_hole_share_cooling_timer = nil
    end
end

-- 快速一键穿戴
function ControlBeastsWGView:OnClickQuickWearAllSlot()
    local select_data = self.beasts_alchemy_bag_select_data
    local aim_hole_id = select_data and select_data.hole_id or COMMON_CONSTS.NUMBER_ZERO  
    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(aim_hole_id)

    local check_can_quick_wear = function(equip_data, slot_id)
        local score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(equip_data)
        local best_data = ControlBeastsCultivateWGData.Instance:GetBestAlchemyBeastEquipForGrid(slot_id, score)
        local is_lock = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipSlotIsLock(aim_hole_id, slot_id)

        if is_lock then
            return nil
        end

        if best_data ~= nil then    -- 发现更好的直接穿戴
            ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperatePut(aim_hole_id, slot_id, best_data.index)
        end
        return best_data
    end


    local is_have_best = false
	if list ~= nil then
		if list[COMMON_CONSTS.NUMBER_ZERO] ~= nil then
			local best_data = check_can_quick_wear(list[COMMON_CONSTS.NUMBER_ZERO].equip_info, COMMON_CONSTS.NUMBER_ZERO) 
            is_have_best = best_data ~= nil
		end

		for i, v in ipairs(list) do
			local best_data = check_can_quick_wear(v.equip_info, i) 

            if not is_have_best then
                is_have_best = best_data ~= nil
            end
		end
	end

    if not is_have_best then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyEquipSlotError2)
    end
end

-- 关闭当前内丹操作
function ControlBeastsWGView:OnClickCloseHoleClose()
    self:FlushAlchemyBeastHoleOperate(false)
end
-----------------------------------------------------------------------------
------------------------------------ 出战背包丹药物品-------------------------
AlchemyBattleBagItem = AlchemyBattleBagItem or BaseClass(BaseRender)
function AlchemyBattleBagItem:LoadCallBack()
    if not self.beast_item then
        self.beast_item = ItemCell.New(self.node_list.item_pos)
		self.beast_item:SetIsShowTips(false)
		self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
    end
end

function AlchemyBattleBagItem:ReleaseCallBack()
    if self.beast_item then
        self.beast_item:DeleteMe()
        self.beast_item = nil
    end
end

function AlchemyBattleBagItem:OnClick()
	BaseRender.OnClick(self)
end

function AlchemyBattleBagItem:OnFlush()
    if not self.data then
        return 
    end

    self.node_list.empty_lock_bg:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE or self.data.beasts_bag_id == -1)
    self.node_list.empty_add:CustomSetActive(self.data.state ~= BEASTS_HOLE_STATUS.NOT_ACTIVE and self.data.beasts_bag_id == -1)
    self.node_list.empty_lock:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE)
    self.node_list.item_pos:CustomSetActive(self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id ~= -1)
    
    if self.data.state == BEASTS_HOLE_STATUS.ACTIVE and self.data.beasts_bag_id ~= -1 then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.beasts_bag_id)
        self.beast_item:SetData(beast_data)

        local server_data = beast_data.server_data
        if not self.data.is_egg and server_data and (not self.data.is_preview) then
            self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)
            self.beast_item:SetBeastLevel(server_data.beast_level)
        end

        local red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanWearForHole(self.data.hole_id)
        local is_cultivate_red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanCultivateForHole(self.data.hole_id, true, true)
        self.beast_item:SetCanOperateIconVisible(red or is_cultivate_red)
    end
end

-- 选中改变
function AlchemyBattleBagItem:OnSelectChange(is_select)
	local bundle, asset = ResPath.GetCommon("a3_ty_xz1")
	self.beast_item:SetSelectSpEffectImageRes(bundle, asset)
	self.beast_item:SetSelectEffectSp(is_select)	
end

------------------------------------------- 装备内丹-----------------------------------------------
AlchemyEquiipItem = AlchemyEquiipItem or BaseClass(BaseRender)
function AlchemyEquiipItem:OnClickSelf(is_on)
	if self.click_callback then
		self.click_callback(self, is_on)
	end
end

function AlchemyEquiipItem:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function AlchemyEquiipItem:ReleaseCallBack()
    self.click_callback = nil
    self.is_final_lock = nil
end

function AlchemyEquiipItem:OnFlush()
    if not self.data then
        return
    end

    local server_data = self.data.server_data
    local fight_slot = self.data.fight_slot or COMMON_CONSTS.NUMBER_ZERO
    self.node_list.alchemy_hole_lock:CustomSetActive(self.data.is_final_lock)
    self.node_list.alchemy_hole_un_lock:CustomSetActive(not self.data.is_final_lock)

    local a3_hsnd_nddi_str = "a3_hsnd_nddi_0"

    if not self.data.is_final_lock then
        self.node_list.alchemy_hole_lv.text.text = server_data.level
        self.node_list.alchemy_hole_icon:CustomSetActive(server_data.equip_info.item_id ~= 0)
        self.node_list.alchemy_hole_add_btn:CustomSetActive(server_data.equip_info.item_id == 0)

        if server_data.equip_info.item_id ~= 0 then
            local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hsnd_nd_slot_%d", self.data.slot_index))
            self.node_list.alchemy_hole_icon.image:LoadSprite(bundle, asset)
            local item_cfg = ItemWGData.Instance:GetItemConfig(server_data.equip_info.item_id)
            a3_hsnd_nddi_str = string.format("a3_hsnd_nddi_%d", item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO) 
        end

        self.node_list.alchemy_hole_red:CustomSetActive(self.data.is_remind)
    end

    local bundle, asset = ResPath.GetControlBeastsImg(a3_hsnd_nddi_str)
    self.node_list.alchemy_hole_bg.image:LoadSprite(bundle, asset)
end

-- 设置选中
function AlchemyEquiipItem:SetEquipSelect(is_select)
    self.node_list.select:CustomSetActive(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
end
----------------------------------- 内丹词条标签 ----------------------------------------
BeststsAlchemyTagRender = BeststsAlchemyTagRender or BaseClass(BaseRender)
function BeststsAlchemyTagRender:OnFlush()
    if not self.data then
        return
    end

    local color = self.data.simple_desc_bg == 0 and COLOR3B.DEFAULT or "#fff9b8"
    local desc = self.data.simple_desc or ""
    local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_hsnd_bq_%d", self.data.simple_desc_bg or 0))
    self.node_list.alchemy_tag_bg.image:LoadSprite(bundle, asset)
    self.node_list.alchemy_tag_txt.text.text = ToColorStr(desc, color)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

----------------------------------- 内丹词条 ----------------------------------------
AlchemyAdditionalRender = AlchemyAdditionalRender or BaseClass(BaseRender)
function AlchemyAdditionalRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.operate_btn, BindTool.Bind(self.OnClickLookSkillDesc, self))               -- 属性详情

    if self.node_list.additional_lock_btn then
        XUI.AddClickEventListener(self.node_list.additional_lock_btn, BindTool.Bind(self.OnClickChooseLockStatus, self))               -- 属性详情
    end

    self.additional_lock = false
end

function AlchemyAdditionalRender:ReleaseCallBack()
    self.appoint_words_seq = nil
    self.additional_lock = false
    self.change_lock_status_callback = nil
    self.lock_limit_fun = nil
end

function AlchemyAdditionalRender:SetSelectLockBtnClickCallBack(fun)
    self.change_lock_status_callback = fun
end

-- 设置锁定限制
function AlchemyAdditionalRender:SetSelectLockBtnClickLimit(fun)
    self.lock_limit_fun = fun
end

function AlchemyAdditionalRender:OnClickChooseLockStatus()
    if not self.additional_lock then
        if self.lock_limit_fun and self.lock_limit_fun(self) then
            return
        end
    end

    self:ChangeNowlockStatus(nil, true)

    if self.change_lock_status_callback then
        self.change_lock_status_callback()
    end
end

function AlchemyAdditionalRender:GetCurrLockStatus()
    return self.additional_lock or false
end

function AlchemyAdditionalRender:ChangeNowlockStatus(additional_lock_status, is_click)
    if is_click then
        self.additional_lock = not self.additional_lock
    else
        self.additional_lock = additional_lock_status
    end

    if self.node_list.lock_status then
        self.node_list.lock_status:CustomSetActive(self.additional_lock)
    end

    if self.node_list.un_lock_status then
        self.node_list.un_lock_status:CustomSetActive(not self.additional_lock)
    end
end

function AlchemyAdditionalRender:SetNotNeedLockStatus()
    self.additional_lock = false

    if self.node_list.additional_lock_btn then
        self.node_list.additional_lock_btn:CustomSetActive(false)
    end
end

function AlchemyAdditionalRender:SetIsNewStatus(status)
    if self.node_list.additional_new_skill then
        self.node_list.additional_new_skill:CustomSetActive(status)
    end
end

-- 设置指定的seq
function AlchemyAdditionalRender:SetAppointWordsSeq(appoint_words_seq)
    self.appoint_words_seq = appoint_words_seq
end

function AlchemyAdditionalRender:OnClickLookSkillDesc()
    if not self.data then
        return
    end
    

    local final_seq = self.appoint_words_seq or self.data.words_seq
    local real_seq = math.floor(final_seq / 100)
    local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)

    if cfg and cfg.words_type ~= 1 then
        local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(cfg.param1)
        if skill_cfg then
            local show_data = {
                icon = skill_cfg.skill_icon,
                top_text = skill_cfg.skill_name,
                body_text = skill_cfg.skill_des,
                x = 0,
                y = 0,
                set_pos2 = true,
                hide_next = true,
                is_active_skill = false,
                skill_level = skill_cfg.skill_level or 0,
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

function AlchemyAdditionalRender:OnFlush()
    if not self.data then
        return
    end

    local final_seq = self.appoint_words_seq or self.data.words_seq
    local random_value = final_seq % 100
    local real_seq = math.floor(final_seq / 100)
    local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)
    self:SetIsNewStatus(false)

    if cfg then
        self.node_list.operate_btn:CustomSetActive(cfg.words_type ~= 1)

        if cfg.words_type == 1 then
            self.node_list.additional_name.text.text = EquipmentWGData.Instance:GetAttrName(cfg.param1, true, false)
            self.node_list.additional_value.text.text = ToColorStr(math.floor(cfg.param2 * (random_value / 100)), COLOR3B.GREEN)
        else
            local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(cfg.param1)
            if skill_cfg then
                local skill_color = skill_cfg.color or COMMON_CONSTS.NUMBER_ZERO
                self.node_list.additional_name.text.text = ToColorStr(skill_cfg.skill_name, ITEM_COLOR[skill_color]) 
                if skill_cfg.skill_level ~= 0 then
                    self.node_list.additional_value.text.text = string.format(Language.Common.Level1, skill_cfg.skill_level)
                else
                    self.node_list.additional_value.text.text = ""
                end
            end

            self:SetIsNewStatus(self.appoint_words_seq ~= nil and self.appoint_words_seq ~= self.data.words_seq)
        end
    end
end

----------------------------------- 背包丹药物品 ----------------------------------------
AlchemyNormalBagItem = AlchemyNormalBagItem or BaseClass(BaseRender)
function AlchemyNormalBagItem:LoadCallBack()
    if not self.bag_item_cell then
        self.bag_item_cell = ItemCell.New(self.node_list.item_pos)
        self.bag_item_cell:SetClickCallBack(BindTool.Bind(self.OnClickBagItem, self))
        self.bag_item_cell:SetIsShowTips(false)
    end
end

function AlchemyNormalBagItem:ReleaseCallBack()
    if self.bag_item_cell then
        self.bag_item_cell:DeleteMe()
        self.bag_item_cell = nil
    end
end

function AlchemyNormalBagItem:OnClickBagItem()
    BaseRender.OnClick(self)
end

function AlchemyNormalBagItem:OnFlush()
    if (not self.data) or IsEmptyTable(self.data) then
        self.bag_item_cell:SetData({})
    else
        self.bag_item_cell:SetItemTipFrom(ItemTip.BEAST_ALCHEMY_EQUIP_BAG)
        self.bag_item_cell:SetData({item_id = self.data.item_id, equip_info = self.data})

        -- 设置箭头
        local role_lv = RoleWGData.Instance:GetRoleLevel()
        local aim_hole_id = self.data.aim_hole_id or COMMON_CONSTS.NUMBER_ZERO  
        local equip_data = ControlBeastsCultivateWGData.Instance:GetEquipCfg(self.data.item_id)
        local self_level_limit = equip_data and equip_data.level_limit or COMMON_CONSTS.NUMBER_ZERO  
        local now_equip_data = self.data.now_equip_data
        local is_lock = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipSlotIsLock(aim_hole_id, equip_data.hole)
        if is_lock then       --
            return
        end

        --背包内装备评分＞镶嵌位且可装备时，对应装备增加绿色箭头显示												
        --背包内装备评分＞镶嵌位且不可装备时，对应装备增加黄色箭头显示								                            
        --附加箭头：背包内装备附加词条的评分＞镶嵌中对应位置装备时，新增绿色箭头+附加文本的显示								                          							
        if now_equip_data == nil then   -- 未装备，全部绿色箭头
            self.bag_item_cell:SetTopUpFlagIconVisible(true)
            self.bag_item_cell:SetTopUpFlagIcon(ResPath.GetCommon("a3_ty_up_1"))
        else
            local now_score = self.data.now_score
            local self_score = self.data.equip_score
            local word_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(now_equip_data.equip_info)
            local self_word_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(self.data)
            local str = ""

            if self_level_limit < role_lv then
                if self_score > now_score then
                    str = "a3_ty_up_1"
                elseif self_word_score > word_score then
                    str = "a3_hsnd_xl_jb_fj" 
                end
            else
                if self_score > now_score then
                    str = "a3_ty_up_2"
                elseif self_word_score > word_score then
                    str = "a3_hsnd_xl_jb_fj" 
                end
            end

            if str ~= "" then
                self.bag_item_cell:SetTopUpFlagIconVisible(true)
                self.bag_item_cell:SetTopUpFlagIcon(ResPath.GetCommon(str))
            else
                self.bag_item_cell:SetTopUpFlagIconVisible(false)
            end
        end
    end
end
----------------------------------- 内丹套装(物品格子) ----------------------------------------
AlchemySuitTipsAttrRender = AlchemySuitTipsAttrRender or BaseClass(BaseRender)
function AlchemySuitTipsAttrRender:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}

		for i = 1, 6 do
			local desc_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
			local cell = CommonAttrRender.New(desc_obj)
			cell:SetAttrNameNeedSpace(true)
            cell:SetOperateBtnClickCallBack(BindTool.Bind(self.OnOperateBtnClick, self))
			self.attr_list[i] = cell
		end
	end
end

function AlchemySuitTipsAttrRender:ReleaseCallBack()
    self.now_suit_number = nil
    self.reach_color = nil
    self.un_reach_color = nil
    self.is_only_attr = nil
    self.is_pos_center = nil

    if self.attr_list and #self.attr_list > 0 then
		for _, render_cell in ipairs(self.attr_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.attr_list = nil
	end
end

function AlchemySuitTipsAttrRender:SetNumSuitNum(suit_num)
    self.now_suit_number = suit_num
end

function AlchemySuitTipsAttrRender:SetSuitStatusColor(reach_color, un_reach_color, is_only_attr)
    self.reach_color = reach_color
    self.un_reach_color = un_reach_color
    self.is_only_attr = is_only_attr
end

function AlchemySuitTipsAttrRender:SetIsCenter(is_center)
    self.is_pos_center = is_center
end

function AlchemySuitTipsAttrRender:OnOperateBtnClick(cell)
    if (not cell) or (not cell.data) or (not cell.data.skill_id) then
        return
    end

    local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(cell.data.skill_id)
    if skill_cfg then
        local show_data = {
            icon = skill_cfg.skill_icon,
            top_text = skill_cfg.skill_name,
            body_text = skill_cfg.skill_des,
            x = self.is_pos_center and 0 or -360,
            y = 0,
            set_pos2 = true,
            hide_next = true,
            is_active_skill = false,
            skill_level = skill_cfg.skill_level or 0,
        }
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

function AlchemySuitTipsAttrRender:OnFlush()
	if not self.data then return end
    local now_suit_num = self.now_suit_number or COMMON_CONSTS.NUMBER_ZERO
    local color = now_suit_num >= self.data.num and self.reach_color or self.un_reach_color
	local title_str = string.format("%s(%d/%d)", self.data.suit_name, now_suit_num, self.data.num)

    if self.is_only_attr then
        title_str = string.format("%s(%s)", self.data.suit_name, ToColorStr(string.format("%d/%d", now_suit_num, self.data.num), color))
        title_str = ToColorStr(title_str, self.un_reach_color)
    else
        title_str = ToColorStr(title_str, color)
    end

	local list = self:AssembleSelfAttr()
	self.node_list.title_txt.text.text = title_str
    
    local render_cell_max = 0
	for i, render_cell in ipairs(self.attr_list) do
		render_cell:SetVisible(list[i] ~= nil)

		if list[i] ~= nil then
            render_cell:SetOperateBtnStatus(false)
			render_cell:SetData(list[i])
            render_cell_max = i + 1
            render_cell:ResetAttrColor(color, self.is_only_attr)
		end
	end

    if self.data.skill_id ~= nil and self.data.skill_id ~= 0 and self.attr_list and self.attr_list[render_cell_max] then
        local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(self.data.skill_id)
        if skill_cfg then
            local cell_render = self.attr_list[render_cell_max]
            cell_render:SetVisible(true)
            cell_render:SetOperateBtnStatus(true)
            cell_render:SetData({skill_id = self.data.skill_id})
            cell_render:ResetName(skill_cfg.skill_name, color)
            if skill_cfg.skill_level ~= 0 then
                cell_render:ResetAttrVlaue(string.format(Language.Common.Level1, skill_cfg.skill_level), color)
            else
                cell_render:ResetAttrVlaue("")
            end
        end
    else
        
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.attr_list.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

function AlchemySuitTipsAttrRender:AssembleSelfAttr()
	if not self.data then return {} end

	local attr_list = {}
	for i = 1, 6 do
		local key = self.data["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

			attr_list[key].attr_value = self.data["attr_value" .. i]
		end
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

--------------------------------背包幻兽类型item-----------------------
BeastsAlchemyBagTypeItemRender = BeastsAlchemyBagTypeItemRender or BaseClass(BaseRender)
function BeastsAlchemyBagTypeItemRender:OnFlush()
    if not self.data then
        return
    end

    local show_red = false

    if self.index == 0 then
        show_red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanWearForHole(self.data.hole_id)
    else
        local slot_id = self.index - COMMON_CONSTS.NUMBER_ONE
		local is_lock = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipSlotIsLock(self.data.hole_id, slot_id)
        if not is_lock then
            local now_equip_data = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipBySlot(self.data.hole_id, slot_id)
            local now_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(now_equip_data.equip_info)
            show_red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanWearForGrid(slot_id, now_score)
        end
    end

    self.node_list.red:CustomSetActive(show_red)
end

-- 刷新选中状态
function BeastsAlchemyBagTypeItemRender:FlushSelectHl(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

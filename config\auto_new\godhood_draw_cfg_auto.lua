-- S-神格抽奖.xls
local item_table={
[1]={item_id=28002,num=1,is_bind=1},
[2]={item_id=28003,num=1,is_bind=1},
[3]={item_id=28004,num=1,is_bind=1},
[4]={item_id=28005,num=1,is_bind=1},
[5]={item_id=28006,num=1,is_bind=1},
[6]={item_id=28007,num=1,is_bind=1},
[7]={item_id=28008,num=1,is_bind=1},
[8]={item_id=28009,num=1,is_bind=1},
[9]={item_id=28010,num=1,is_bind=1},
[10]={item_id=28011,num=1,is_bind=1},
[11]={item_id=28012,num=1,is_bind=1},
[12]={item_id=28013,num=1,is_bind=1},
[13]={item_id=28014,num=1,is_bind=1},
[14]={item_id=28015,num=1,is_bind=1},
[15]={item_id=28016,num=1,is_bind=1},
[16]={item_id=47166,num=1,is_bind=1},
[17]={item_id=47167,num=1,is_bind=1},
[18]={item_id=47168,num=1,is_bind=1},
[19]={item_id=47169,num=1,is_bind=1},
[20]={item_id=47170,num=1,is_bind=1},
[21]={item_id=47171,num=1,is_bind=1},
[22]={item_id=47172,num=1,is_bind=1},
[23]={item_id=47173,num=1,is_bind=1},
[24]={item_id=47174,num=1,is_bind=1},
[25]={item_id=47175,num=1,is_bind=1},
[26]={item_id=27505,num=1,is_bind=1},
[27]={item_id=28001,num=1,is_bind=1},
[28]={item_id=38174,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
grade={
{},
{min_draw_count=100,max_draw_count=220,grade=2,},
{min_draw_count=220,max_draw_count=380,grade=3,},
{min_draw_count=380,max_draw_count=520,grade=4,},
{min_draw_count=520,max_draw_count=9999,grade=5,}
},

grade_meta_table_map={
},
reward_pool={
{show_item=1,},
{seq=1,item=item_table[1],},
{seq=2,item=item_table[2],},
{seq=3,item=item_table[3],},
{seq=4,item=item_table[4],},
{seq=5,item=item_table[5],},
{seq=6,item=item_table[6],},
{seq=7,item=item_table[7],},
{seq=8,item=item_table[8],},
{seq=9,item=item_table[9],},
{seq=10,item=item_table[10],},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{seq=13,item=item_table[13],},
{seq=14,item=item_table[14],},
{seq=15,item=item_table[15],is_rare=0,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,}
},

reward_pool_meta_table_map={
[49]=1,	-- depth:1
[17]=49,	-- depth:2
[65]=17,	-- depth:3
[33]=65,	-- depth:4
[74]=10,	-- depth:1
[41]=9,	-- depth:1
[42]=74,	-- depth:2
[43]=11,	-- depth:1
[44]=12,	-- depth:1
[76]=44,	-- depth:2
[55]=7,	-- depth:1
[73]=41,	-- depth:2
[56]=8,	-- depth:1
[72]=56,	-- depth:2
[75]=43,	-- depth:2
[59]=75,	-- depth:3
[60]=76,	-- depth:3
[39]=55,	-- depth:2
[71]=39,	-- depth:3
[57]=73,	-- depth:3
[58]=42,	-- depth:3
[40]=72,	-- depth:3
[24]=40,	-- depth:4
[23]=71,	-- depth:4
[25]=57,	-- depth:4
[26]=58,	-- depth:4
[13]=16,	-- depth:1
[6]=1,	-- depth:1
[27]=59,	-- depth:4
[4]=6,	-- depth:2
[3]=6,	-- depth:2
[2]=6,	-- depth:2
[28]=60,	-- depth:4
[15]=16,	-- depth:1
[5]=6,	-- depth:2
[14]=16,	-- depth:1
[64]=16,	-- depth:1
[61]=13,	-- depth:2
[62]=14,	-- depth:2
[63]=15,	-- depth:2
[30]=62,	-- depth:3
[67]=3,	-- depth:3
[68]=4,	-- depth:3
[69]=5,	-- depth:3
[70]=6,	-- depth:2
[77]=61,	-- depth:3
[78]=30,	-- depth:4
[66]=2,	-- depth:3
[18]=66,	-- depth:4
[21]=69,	-- depth:4
[20]=68,	-- depth:4
[31]=63,	-- depth:3
[32]=64,	-- depth:2
[34]=18,	-- depth:5
[35]=67,	-- depth:4
[36]=20,	-- depth:5
[37]=21,	-- depth:5
[38]=70,	-- depth:3
[79]=31,	-- depth:4
[45]=77,	-- depth:4
[46]=78,	-- depth:5
[47]=79,	-- depth:5
[48]=32,	-- depth:3
[22]=38,	-- depth:4
[50]=34,	-- depth:6
[51]=35,	-- depth:5
[52]=36,	-- depth:6
[53]=37,	-- depth:6
[54]=22,	-- depth:5
[29]=45,	-- depth:5
[19]=51,	-- depth:6
[80]=48,	-- depth:4
},
baodi={
{random_item={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11]},random_item_list="28009,2000|28010,2000|28011,2000|28012,2000",},
{grade=2,need_lucky=40,},
{grade=3,need_lucky=80,},
{grade=4,need_lucky=70,},
{grade=5,}
},

baodi_meta_table_map={
[2]=1,	-- depth:1
},
convert={
{need_score=50000,},
{seq=1,item=item_table[16],need_score=4800,sort_id=2,},
{seq=2,item=item_table[17],need_score=4000,sort_id=3,},
{seq=3,item=item_table[18],sort_id=4,},
{seq=4,item=item_table[19],sort_id=5,},
{seq=5,item=item_table[20],need_score=900,sort_id=6,},
{seq=6,item=item_table[21],need_score=900,sort_id=7,},
{seq=7,item=item_table[22],need_score=1600,sort_id=8,},
{seq=8,item=item_table[23],need_score=1600,sort_id=9,},
{seq=9,item=item_table[24],need_score=2600,sort_id=10,},
{seq=10,item=item_table[25],need_score=2600,sort_id=11,},
{seq=11,time_limit=50,item=item_table[26],need_score=100,sort_id=12,}
},

convert_meta_table_map={
},
times_reward={
{}
},

times_reward_meta_table_map={
},
item_random_desc={
{grade=1,show_type=1,},
{grade=1,item_id=28011,},
{grade=1,item_id=28010,},
{grade=1,item_id=28009,},
{grade=1,random_count=10,},
{number=5,item_id=28007,},
{grade=1,random_count=10,},
{number=7,item_id=28005,},
{grade=1,random_count=90,},
{grade=1,random_count=90,},
{number=10,item_id=28002,},
{grade=1,random_count=90,},
{grade=2,random_count=1,},
{grade=2,random_count=1,},
{grade=2,random_count=1,},
{grade=2,random_count=1,},
{grade=2,number=4,item_id=28008,random_count=20,},
{number=5,item_id=28007,},
{number=6,item_id=28006,},
{number=7,item_id=28005,},
{grade=2,number=8,item_id=28004,random_count=80,},
{number=9,item_id=28003,},
{number=10,item_id=28002,},
{number=11,item_id=28001,},
{item_id=28016,show_type=1,},
{number=1,item_id=28015,},
{number=2,item_id=28014,},
{number=3,item_id=28013,},
{number=4,random_count=4,},
{number=5,item_id=28011,},
{number=6,item_id=28010,},
{number=7,item_id=28009,},
{number=8,item_id=28008,},
{number=9,item_id=28007,},
{number=10,item_id=28006,random_count=24,},
{number=11,item_id=28005,},
{number=12,item_id=28004,},
{number=13,item_id=28003,},
{number=14,item_id=28002,random_count=72,show_type=1,},
{number=15,item_id=28001,},
{grade=4,random_count=1,},
{grade=4,random_count=1,},
{number=2,item_id=28014,},
{grade=4,random_count=1,},
{grade=4,random_count=12,},
{grade=4,random_count=12,},
{grade=4,random_count=12,},
{grade=4,random_count=12,},
{grade=4,random_count=40,show_type=1,},
{number=9,item_id=28007,},
{number=10,item_id=28006,},
{number=11,item_id=28005,},
{grade=4,number=12,item_id=28004,random_count=48,},
{number=13,item_id=28003,},
{number=14,item_id=28002,},
{number=15,item_id=28001,},
{grade=5,item_id=28016,random_count=5,},
{number=1,item_id=28015,},
{number=2,item_id=28014,},
{number=3,item_id=28013,},
{grade=5,number=4,random_count=20,show_type=1,},
{number=5,item_id=28011,},
{number=6,item_id=28010,},
{number=7,item_id=28009,},
{grade=5,random_count=48,},
{grade=5,random_count=48,},
{grade=5,random_count=48,},
{number=11,item_id=28005,},
{grade=5,random_count=28,},
{number=13,item_id=28003,},
{number=14,item_id=28002,},
{number=15,item_id=28001,}
},

item_random_desc_meta_table_map={
[45]=29,	-- depth:1
[41]=57,	-- depth:1
[34]=35,	-- depth:1
[33]=35,	-- depth:1
[32]=29,	-- depth:1
[31]=29,	-- depth:1
[30]=29,	-- depth:1
[28]=25,	-- depth:1
[27]=25,	-- depth:1
[26]=25,	-- depth:1
[36]=35,	-- depth:1
[13]=1,	-- depth:1
[43]=41,	-- depth:2
[3]=27,	-- depth:2
[46]=30,	-- depth:2
[47]=31,	-- depth:2
[48]=32,	-- depth:2
[54]=53,	-- depth:1
[55]=53,	-- depth:1
[56]=53,	-- depth:1
[2]=26,	-- depth:2
[58]=57,	-- depth:1
[59]=57,	-- depth:1
[60]=57,	-- depth:1
[65]=33,	-- depth:2
[66]=34,	-- depth:2
[67]=35,	-- depth:1
[68]=67,	-- depth:2
[69]=53,	-- depth:1
[70]=69,	-- depth:2
[42]=58,	-- depth:2
[4]=28,	-- depth:2
[44]=60,	-- depth:2
[18]=17,	-- depth:1
[19]=17,	-- depth:1
[20]=17,	-- depth:1
[22]=21,	-- depth:1
[23]=21,	-- depth:1
[24]=21,	-- depth:1
[12]=24,	-- depth:2
[40]=39,	-- depth:1
[10]=22,	-- depth:2
[9]=21,	-- depth:1
[11]=9,	-- depth:2
[7]=19,	-- depth:2
[38]=39,	-- depth:1
[37]=39,	-- depth:1
[71]=69,	-- depth:2
[5]=17,	-- depth:1
[6]=5,	-- depth:2
[8]=5,	-- depth:2
[72]=69,	-- depth:2
[49]=33,	-- depth:2
[64]=61,	-- depth:1
[15]=3,	-- depth:3
[14]=2,	-- depth:3
[50]=49,	-- depth:3
[51]=49,	-- depth:3
[16]=4,	-- depth:3
[52]=49,	-- depth:3
[62]=61,	-- depth:1
[63]=61,	-- depth:1
},
show_reward={
{},
{grade=2,reward_item={[0]=item_table[2],[1]=item_table[3],[2]=item_table[4],[3]=item_table[5],[4]=item_table[6],[5]=item_table[7],[6]=item_table[8],[7]=item_table[9]},name="精品魂池",show_box_modelid="3_2_zi",effect_bunble="effects/prefab/ui/ui_shenge_posui_lan_prefab",effect_asset="ui_shenge_posui_lan",},
{grade=3,reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8],[5]=item_table[9],[6]=item_table[10],[7]=item_table[11]},name="上品魂池",show_box_modelid="3_3_cheng",effect_bunble="effects/prefab/ui/ui_shenge_posui_zi_prefab",effect_asset="ui_shenge_posui_zi",},
{grade=4,reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11],[6]=item_table[12],[7]=item_table[13]},name="极品魂池",show_box_modelid="3_4_hong",effect_bunble="effects/prefab/ui/ui_shenge_posui_cheng_prefab",effect_asset="ui_shenge_posui_cheng",},
{grade=5,reward_item={[0]=item_table[8],[1]=item_table[9],[2]=item_table[10],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13],[6]=item_table[14],[7]=item_table[15]},name="仙品魂池",show_box_modelid="3_5_feng",effect_bunble="effects/prefab/ui/ui_shenge_posui_hong_prefab",effect_asset="ui_shenge_posui_hong",}
},

show_reward_meta_table_map={
},
other_default_table={rare_color=5,consume_item=39144,open_day=8,draw_cost=1000,add_score=1,add_lucky=1,},

grade_default_table={min_draw_count=0,max_draw_count=100,grade=1,},

reward_pool_default_table={grade=1,seq=0,item=item_table[27],is_rare=1,show_item=0,},

baodi_default_table={grade=1,need_lucky=50,random_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15]},random_item_list="28013,2000|28014,2000|28015,2000|28016,2000",},

convert_default_table={seq=0,time_limit=1,item=item_table[28],need_score=500,sort_id=1,},

times_reward_default_table={seq=0,need_draw_times=1,add_score=1,},

item_random_desc_default_table={grade=3,number=0,item_id=28012,random_count=0.1,show_type=2,},

show_reward_default_table={grade=1,reward_item={[0]=item_table[27],[1]=item_table[1],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4],[5]=item_table[5],[6]=item_table[6],[7]=item_table[7]},name="凡品魂池",model_bundle_name="model/tianshen/10112_prefab",model_asset_name=10112,model_scale=0.8,model_pos="0|0.2|0",model_rot="0|0|0",show_box_modelid="3_1_lan",effect_bunble="effects/prefab/ui/ui_shenge_posui_lv_prefab",effect_asset="ui_shenge_posui_lv",}

}


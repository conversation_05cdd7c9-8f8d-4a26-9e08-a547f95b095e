ActCollectCardOperateView = ActCollectCardOperateView or BaseClass(SafeBaseView)
local OPERATE_TYPE = {
	OPERATE_CARD_MESSAGE = 0,				-- 信息
	OPERATE_GET_CARD = 1,					-- 索要
	OPERATE_SEND_CARD = 2,					-- 赠送
	FRIEND_LIST = 3,						-- 请求列表
}
local COLLECT_NUM_ONE = 1

function ActCollectCardOperateView:__init()
	self:SetMaskBg(true, true)
	self.is_need_depth = true
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_open_server_collect_card_operate")
end

function ActCollectCardOperateView:ReleaseCallBack()
	if self.collect_card_operate_list then
		self.collect_card_operate_list:DeleteMe()
		self.collect_card_operate_list = nil
	end

	self.cur_select_type = nil
	self.cur_operate_seq = nil
end

function ActCollectCardOperateView:SetCurrOperateSeq(operate_seq)
	self.cur_operate_seq = operate_seq
	self.cur_select_type = OPERATE_TYPE.OPERATE_CARD_MESSAGE
end

function ActCollectCardOperateView:LoadCallBack()
	if not self.collect_card_operate_list then
		self.collect_card_operate_list = AsyncListView.New(CollectCardOperateRender, self.node_list.collect_card_operate_list)
		self.collect_card_operate_list:SetRefreshCallback(BindTool.Bind(self.RefreshCallBack, self))
		self.collect_card_operate_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.operate_type_get_btn, BindTool.Bind(self.Close, self))
	XUI.AddClickEventListener(self.node_list.operate_type_request_btn, BindTool.Bind(self.OnClickOperateTypeEnter, self, OPERATE_TYPE.OPERATE_GET_CARD))
	XUI.AddClickEventListener(self.node_list.operate_type_compose_btn, BindTool.Bind(self.OnClickOperateTypeCompose, self))
	XUI.AddClickEventListener(self.node_list.operate_type_send_btn, BindTool.Bind(self.OnClickOperateTypeEnter, self, OPERATE_TYPE.OPERATE_SEND_CARD))
	XUI.AddClickEventListener(self.node_list.operate_friend_request_btn, BindTool.Bind(self.OnClickOperateTypeEnter, self, OPERATE_TYPE.FRIEND_LIST))
	XUI.AddClickEventListener(self.node_list.operate_card_back_btn, BindTool.Bind(self.OnClickOperateCardBack, self))
end

-- 刷新格子回调
function ActCollectCardOperateView:RefreshCallBack(item_cell, cell_index)
	if not item_cell then
		return
	end

	item_cell:SetNowRenderStatus(self.cur_operate_seq, self.cur_select_type)
end

function ActCollectCardOperateView:OnFlush(param_t)
	if not self.cur_operate_seq then
		return
	end

	local lh_image_str = string.format("a3_kfjk_card_lh_%d", self.cur_operate_seq)
	self.node_list.card_lh_image.image:LoadSprite(ResPath.GetNoPackPNG(lh_image_str))
	local card_cfg = ActivityCollectCardWGData.Instance:GetCardBySeq(self.cur_operate_seq)
	self.node_list.operate_card_name.text.text = card_cfg and card_cfg.name or ""
	self.node_list.operate_card_desc.text.text = card_cfg and card_cfg.des or ""

	if self.cur_select_type == nil then
		self.cur_select_type = OPERATE_TYPE.OPERATE_CARD_MESSAGE
	end

	self:FlushButtonStatus()
	-- 特殊需求<=1 索取， >=1 赠送
	local card_num = ActivityCollectCardWGData.Instance:GetCollectCardInfoBySeq(self.cur_operate_seq)
	local is_can_send = card_num > COLLECT_NUM_ONE
	self.node_list.operate_type_request_btn:CustomSetActive(not is_can_send)
	self.node_list.operate_type_compose_btn:CustomSetActive(not is_can_send)
	self.node_list.operate_type_send_btn:CustomSetActive(is_can_send)
	self.node_list.operate_friend_request_btn:CustomSetActive(is_can_send)

	if is_can_send then
		local show_red = ActivityCollectCardWGData.Instance:GetCurGradeCollectCardRequsetRedBySeq(self.cur_operate_seq) and 1 or 0
		self.node_list.friend_request_remind:CustomSetActive(show_red > 0)
	end

	local base_cfg = ActivityCollectCardWGData.Instance:GetBaseCfg()
	local card_cfg = ActivityCollectCardWGData.Instance:GetCardBySeq(self.cur_operate_seq)
	local ship_item = base_cfg and base_cfg.ship_id or 0
	local compose_cost = card_cfg and card_cfg.compose_cost or 0
	local item_num = ItemWGData.Instance:GetItemNumInBagById(ship_item)
	local color = item_num >= compose_cost and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.compose_card_spend_num.text.text = ToColorStr(string.format("%d/%d", item_num, compose_cost) , color)
	local icon_id = ItemWGData.Instance:GetItemIconByItemId(ship_item)
	self.node_list.compose_card_spend_image.image:LoadSprite(ResPath.GetItem(icon_id))
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.compose_card_spend_root.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_card_name_root.rect)
	self:OnClickOperateTypeEnter()
end

function ActCollectCardOperateView:FlushButtonStatus()
	self.node_list.operate_btn_root:CustomSetActive(self.cur_select_type == OPERATE_TYPE.OPERATE_CARD_MESSAGE)
	self.node_list.operate_card_get_root:CustomSetActive(self.cur_select_type == OPERATE_TYPE.OPERATE_CARD_MESSAGE)
	self.node_list.operate_card_back_btn:CustomSetActive(self.cur_select_type ~= OPERATE_TYPE.OPERATE_CARD_MESSAGE)
	self.node_list.day_send_times:CustomSetActive(self.cur_select_type ~= OPERATE_TYPE.OPERATE_CARD_MESSAGE and self.cur_select_type == OPERATE_TYPE.OPERATE_SEND_CARD)
	self.node_list.collect_card_operate_list_root:CustomSetActive(self.cur_select_type ~= OPERATE_TYPE.OPERATE_CARD_MESSAGE)
end

function ActCollectCardOperateView:OnClickOperateCardBack()
	self.cur_select_type = OPERATE_TYPE.OPERATE_CARD_MESSAGE
	self.node_list.empty_list_tips:CustomSetActive(false)
	self:FlushButtonStatus()
end

-- 索要操作（只有索要）
function ActCollectCardOperateView:OnClickOperateTypeEnter(select_type)
	if not self.cur_operate_seq then
		return
	end

	self.node_list.empty_list_tips:CustomSetActive(false)
	self.cur_select_type = select_type or self.cur_select_type
	if self.cur_select_type == OPERATE_TYPE.OPERATE_CARD_MESSAGE then
		return
	end

	local list = nil
	local empty_str = ""

	if self.cur_select_type == OPERATE_TYPE.OPERATE_GET_CARD or self.cur_select_type == OPERATE_TYPE.OPERATE_SEND_CARD then
		-- 这里筛选是否切换页签
		list = ActivityCollectCardWGData.Instance:GetCollectCardFriendInfo()
		empty_str = string.format("%s%s", Language.Common.ZanWu, Language.OpenServer.SevenCollectCandNoFriend)
	else
		-- 这里筛选是否切换页签
		list = ActivityCollectCardWGData.Instance:GetCurGradeCollectCardRequsetList(self.cur_operate_seq)
		empty_str = string.format("%s%s", Language.Common.ZanWu, Language.OpenServer.SevenCollectCandNoAskFor)
	end

	local is_have_data = not IsEmptyTable(list)
	self.node_list.empty_list_tips:CustomSetActive(not is_have_data)
	self.node_list.collect_card_operate_list:CustomSetActive(is_have_data)

	if is_have_data then
		self.collect_card_operate_list:SetDataList(list)
	else
		self.node_list.empty_list_tips_txt.text.text = empty_str
	end

	local card_self_info = ActivityCollectCardWGData.Instance:GetCollectCardSelfInfo()
	local base_cfg = ActivityCollectCardWGData.Instance:GetBaseCfg()
	local day_times = base_cfg and base_cfg.times or 0
	local send_times = card_self_info and card_self_info.times or 0
	local last_times = day_times - send_times
	local color = last_times > 0 and COLOR3B.GREEN or COLOR3B.RED
	local str = string.format(Language.OpenServer.DaySendTimes, string.format("%s/%d", ToColorStr(last_times, color), day_times))
	self.node_list.day_send_times.text.text = str

	self:FlushButtonStatus()
end

-- 合成
function ActCollectCardOperateView:OnClickOperateTypeCompose()
	if not self.cur_operate_seq then
		return
	end

	local base_cfg = ActivityCollectCardWGData.Instance:GetBaseCfg()
	local card_cfg = ActivityCollectCardWGData.Instance:GetCardBySeq(self.cur_operate_seq)
	local ship_item = base_cfg and base_cfg.ship_id or 0
	local compose_cost = card_cfg and card_cfg.compose_cost or 0
	local item_num = ItemWGData.Instance:GetItemNumInBagById(ship_item)
	local is_enougth = item_num >= compose_cost

	if is_enougth then
		ServerActivityWGCtrl.Instance:RequestAOCollectCardCompose(self.cur_operate_seq, 1)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OperationActivity.ShenQiStr3)
	end
	self:Close()
end

-------------------------------------------------------------------------------------------------------------------------
CollectCardOperateRender = CollectCardOperateRender or BaseClass(BaseRender)
function CollectCardOperateRender:__init()
	XUI.AddClickEventListener(self.node_list.btn_operate_send, BindTool.Bind(self.OnClickOperateSend, self))
	XUI.AddClickEventListener(self.node_list.btn_operate_get, BindTool.Bind(self.OnClickOperateGet, self))
	XUI.AddClickEventListener(self.node_list.btn_operate_refuse, BindTool.Bind(self.OnClickOperateRefuse, self))
end

function CollectCardOperateRender:__delete()
	self.operate_seq = nil
end

function CollectCardOperateRender:SetNowRenderStatus(operate_seq, select_type)
	self.operate_seq = operate_seq
	local is_gain = self.data.gain_flag and self.data.gain_flag[self.operate_seq] == 1
	self.node_list.btn_operate_get:CustomSetActive(select_type == OPERATE_TYPE.OPERATE_GET_CARD and (not is_gain))
	self.node_list.btn_operate_get_flag:CustomSetActive(select_type == OPERATE_TYPE.OPERATE_GET_CARD and is_gain)
	self.node_list.btn_operate_refuse:CustomSetActive(select_type == OPERATE_TYPE.FRIEND_LIST)
	self.node_list.btn_operate_send:CustomSetActive(select_type == OPERATE_TYPE.OPERATE_SEND_CARD or select_type == OPERATE_TYPE.FRIEND_LIST)
end

-- 操作赠送
function CollectCardOperateRender:OnClickOperateSend()
	if (not self.data) or (not self.operate_seq) then
		return
	end

	local has_num = ActivityCollectCardWGData.Instance:GetCollectCardInfoBySeq(self.operate_seq)
	if has_num == nil or has_num <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.SendCollectCandError)
        return
	end

	local card_self_info = ActivityCollectCardWGData.Instance:GetCollectCardSelfInfo()
	local base_cfg = ActivityCollectCardWGData.Instance:GetBaseCfg()
	local day_times = base_cfg and base_cfg.times or 0
	local send_times = card_self_info and card_self_info.times or 0
	local last_times = day_times - send_times
	if last_times <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.SendCollectCandError2)
        return
	end

	ServerActivityWGCtrl.Instance:RequestAOCollectCardSend(self.data.uid, self.operate_seq)
end

-- 操作索取
function CollectCardOperateRender:OnClickOperateGet()
	if (not self.data) or (not self.operate_seq) then
		return
	end

	ServerActivityWGCtrl.Instance:RequestAOCollectCardGain(self.data.uid, self.operate_seq)
end

-- 操作拒绝
function CollectCardOperateRender:OnClickOperateRefuse()
	if (not self.data) or (not self.operate_seq) then
		return
	end

	ServerActivityWGCtrl.Instance:RequestAOCollectCardRefus(self.data.uid, self.operate_seq)
end

function CollectCardOperateRender:OnFlush()
	if not self.data then
		return
	end
	
	self.node_list.description.text.text = self.data.name
end

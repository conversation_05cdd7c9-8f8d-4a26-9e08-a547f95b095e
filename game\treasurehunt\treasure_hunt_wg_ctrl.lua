--合W6的寻宝 符文、装备、巅峰、至尊
require("game/treasurehunt/treasure_hunt_view")
require("game/treasurehunt/treasure_hunt_money_bar")
require("game/treasurehunt/treasure_hunt_render")
require("game/treasurehunt/treasure_hunt_mingwen")
require("game/treasurehunt/treasure_hunt_wg_data")
require("game/treasurehunt/treasure_hunt_cangku")
require("game/treasurehunt/treasure_hunt_store")
require("game/treasurehunt/treasure_hunt_reward_view")
require("game/treasurehunt/treasure_hunt_gailv")
require("game/treasurehunt/treasure_mingwen_record")
require("game/treasurehunt/treasure_baodi_tips_view")
require("game/treasurehunt/treasure_hunt_record")
require("game/treasurehunt/treasure_hunt_mingwen_yulan")
require("game/treasurehunt/treasure_xukongliefeng_view")
require("game/treasurehunt/treasure_hunt_spine")
require("game/treasurehunt/treasure_thunder/treasure_hunt_thunder_view")


TreasureHuntWGCtrl = TreasureHuntWGCtrl or BaseClass(BaseWGCtrl)

function TreasureHuntWGCtrl:__init()
	if TreasureHuntWGCtrl.Instance then
        error("[TreasureHuntWGCtrl]:Attempt to create singleton twice!")
	end
	TreasureHuntWGCtrl.Instance = self

	self.data = TreasureHuntWGData.New()
    self.view = TreasureHuntView.New(GuideModuleName.TreasureHunt)
    self.treasurehunt_storage_view = TreasureHuntStorageView.New()
    self.treasurehunt_store = TreasureHuntStoreView.New(GuideModuleName.TreasureHuntStoreView)
    self.treasurehunt_reward_view = TreasureHuntRewardView.New()
    self.treasurehunt_probability_view = TreasureHuntProbabilityView.New()
    self.mingwen_record_view = TreasureHuntMingwenRecord.New()
    self.treasurehunt_record = TreasureHuntRecordTip.New()
    self.mingwen_yulan_view = TreasureHuntMingWenYuLanView.New()
    self.xukongliefeng_view = TreasureXuKongLieFengView.New(GuideModuleName.TreasureXuKongLieFengView)
    self.treasurehunt_spine_view = TreasureHuntSpine.New()

    self.baodi_tips = TreasureBaodiTips.New()

	self:RegisterAllProtocals()
end

function TreasureHuntWGCtrl:__delete()
    TreasureHuntWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
	self.view:DeleteMe()
    self.view = nil
    if self.treasurehunt_storage_view then
        self.treasurehunt_storage_view:DeleteMe()
        self.treasurehunt_storage_view = nil
    end

    if self.treasurehunt_store then
        self.treasurehunt_store:DeleteMe()
        self.treasurehunt_store = nil
    end

    if self.treasurehunt_reward_view then
        self.treasurehunt_reward_view:DeleteMe()
        self.treasurehunt_reward_view = nil
    end

    if nil ~= self.not_enough_alert then
        self.not_enough_alert:DeleteMe()
        self.not_enough_alert = nil
    end

    if nil ~= self.treasurehunt_record then
        self.treasurehunt_record:DeleteMe()
        self.treasurehunt_record = nil
    end

    if self.mingwen_yulan_view then
        self.mingwen_yulan_view:DeleteMe()
        self.mingwen_yulan_view = nil
    end

    if self.xukongliefeng_view then
        self.xukongliefeng_view:DeleteMe()
        self.xukongliefeng_view = nil
    end

    if self.treasurehunt_spine_view then
        self.treasurehunt_spine_view:DeleteMe()
        self.treasurehunt_spine_view = nil
    end
end

function TreasureHuntWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSPosyShopOpera) --铭纹寻宝
    self:RegisterProtocol(CSChestShopOperate)  --其他寻宝
    self:RegisterProtocol(CSChestShopConvertOperate)  --寻宝商店

    self:RegisterProtocol(SCChestStorageInfo, "OnSCChestStorageInfo") --寻宝仓库信息
    self:RegisterProtocol(SCChestShopBaseInfo, "OnSCChestShopBaseInfo") --寻宝信息
    self:RegisterProtocol(SCChestShopBuyResultInfo, "OnSCChestShopBuyResultInfo") --抽中结果
    self:RegisterProtocol(SCChestshopRecord, "OnSCChestshopRecord") --寻宝记录
    self:RegisterProtocol(SCWorldChestshopRecord, "OnSCWorldChestshopRecord") --寻宝世界日志（6.5 大奖记录改成世界上显示）
    --self:RegisterProtocol(SCWorldChestshopBigRecord, "OnSCWorldChestshopBigRecord") --寻宝秘闻
    self:RegisterProtocol(SCPosyShopInfo, "OnSCPosyShopInfo") -- 铭纹寻宝信息
    self:RegisterProtocol(SCPosyShopResult, "OnSCPosyShopResult") -- 铭纹寻宝结果
    self:RegisterProtocol(SCPosyShopPersonRecord, "OnSCPosyShopPersonRecord") -- 铭文寻宝个人记录
    self:RegisterProtocol(SCPosyShopWorldRecord, "OnSCPosyShopWorldRecord") -- 铭文寻宝世界记录
end


function TreasureHuntWGCtrl:OnSCPosyShopPersonRecord(protocol)
    --print_error(">>> 铭文寻宝个人记录",protocol)
    self.data:SetMingWenPersonRecord(protocol)
    if self.mingwen_record_view:IsOpen() then
        self.mingwen_record_view:Flush()
    end
end

function TreasureHuntWGCtrl:OnSCPosyShopWorldRecord(protocol)
    --print_error(">>> 铭文寻宝世界记录",protocol)
    self.data:SetMingWenWorldRecord(protocol)
    if self.mingwen_record_view:IsOpen() then
        self.mingwen_record_view:Flush()
    end
end

function TreasureHuntWGCtrl:OnSCPosyShopInfo(protocol)
    --print_error(">>> 铭纹寻宝信息",protocol)
    self.data:SetMingWenInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush(TreasureHuntView.TabIndex.Fuwen)
    end
    RemindManager.Instance:Fire(RemindName.TreasureHunt_FuWen)
    RemindManager.Instance:Fire(RemindName.TreasureHunt)
end

function TreasureHuntWGCtrl:OnSCPosyShopResult(protocol)
    --print_error(">>> 铭纹寻宝结果",protocol)
    self.data:SetMingWenResultInfo(protocol)
    self.data:SetIsMingwen(true)
    local mode = protocol.count == 1 and TreasureHuntWGData.MingwenMode.One or TreasureHuntWGData.MingwenMode.Ten --铭纹寻宝模式
    self.treasurehunt_reward_view:SetDataType(mode, protocol.count, true)

    if self.treasurehunt_spine_view:IsOpen() then
        self.treasurehunt_spine_view:Close()    
    end

    if not self.treasurehunt_reward_view:IsOpen() then
        self.treasurehunt_reward_view:Open()    
    else
        self.treasurehunt_reward_view:Flush()
    end
end

function TreasureHuntWGCtrl:OnSCChestStorageInfo(protocol)
    --print_error(">>>寻宝仓库信息",protocol)
    self.data:SetStorageInfo(protocol)
    if self.treasurehunt_storage_view:IsOpen() then
		self.treasurehunt_storage_view:Flush()
    end
    if self.view:IsOpen() then
		self.view:Flush(nil,"storage")
    end
    RemindManager.Instance:Fire(RemindName.TreasureHunt_DianFeng)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Equip)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Zhizun)
    RemindManager.Instance:Fire(RemindName.TreasureHunt)
end

function TreasureHuntWGCtrl:OnSCChestShopBaseInfo(protocol)
    --print_error(">>>寻宝基本信息",protocol)
    self.data:SetBaseData(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end
    if self.treasurehunt_store:IsOpen() then
        self.treasurehunt_store:Flush()
    end
    RemindManager.Instance:Fire(RemindName.TreasureHunt_DianFeng)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Equip)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Zhizun)
    RemindManager.Instance:Fire(RemindName.TreasureHunt)
    TreasureBossWGData.Instance:NeedShowTreasureBossIcon()
end

function TreasureHuntWGCtrl:OnSCChestShopBuyResultInfo(protocol)
    self.data:SetResultData(protocol)
    self.data:SetIsMingwen(false)
    self.treasurehunt_reward_view:SetDataType(protocol.shop_mode, protocol.count, false)

    if self.treasurehunt_spine_view:IsOpen() then
        self.treasurehunt_spine_view:Close()    
    end

    if not self.treasurehunt_reward_view:IsOpen() then
        self.treasurehunt_reward_view:Open()
    else
        self.treasurehunt_reward_view:Flush()
    end
end

function TreasureHuntWGCtrl:OnSCWorldChestshopBigRecord(protocol)
    --print_error(">>>寻宝秘闻记录",protocol.shop_mode,protocol)
    self.data:SetWorldBigRecord(protocol)
    local index = TreasureHuntView.TableIndexByMode[protocol.shop_mode + 1]
    if index then
        self.view:Flush(index, "record")
    end
end

function TreasureHuntWGCtrl:OnSCChestshopRecord(protocol)
    --print_error(">>>寻宝个人记录",protocol.shop_mode,protocol)
    self.data:SetPersonRecord(protocol)
    local index = TreasureHuntView.TableIndexByMode[protocol.shop_mode + 1]
    if index then
        if self.view:IsOpen() then
            self.view:Flush(index, "record")
        end
    end
end

function TreasureHuntWGCtrl:OnSCWorldChestshopRecord(protocol)
    --print_error(">>>寻宝世界记录",protocol.shop_mode,protocol)
    self.data:SetWorldRecord(protocol)
    local index = TreasureHuntView.TableIndexByMode[protocol.shop_mode + 1]
    if index then
        if self.view:IsOpen() then
            self.view:Flush(index, "record")
        end
    end
end

--其他寻宝操作
function TreasureHuntWGCtrl:DoOperation(operate, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChestShopOperate)
    protocol.operate = operate
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

--铭纹寻宝操作
function TreasureHuntWGCtrl:DoMingWenOpera(operate, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPosyShopOpera)
    protocol.operate = operate
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function TreasureHuntWGCtrl:GetOutOneItem(index)
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_FETCH_ITEM, index)
end

function TreasureHuntWGCtrl:DoShopOperation(operate, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChestShopConvertOperate)
    protocol.operate = operate
    protocol.param1 = param1
    protocol.param2 = param2
	protocol:EncodeAndSend()
end

--请求积分，周领取次数基本信息
function TreasureHuntWGCtrl:DoSendBaseInfo()
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_REQ_BASE_INFO)
end


function TreasureHuntWGCtrl:OpenStroageView()
    self.treasurehunt_storage_view:Open()
end


function TreasureHuntWGCtrl:OpenStoreView()
    self.treasurehunt_store:Open()
    local treasure_type = TreasureHuntWGCtrl.Instance:GetTreasureType() or 1
    TreasureHuntWGData.Instance:SetTreasureHuntTab(treasure_type)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_DianFeng)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Equip)
    RemindManager.Instance:Fire(RemindName.TreasureHunt_Zhizun)
    if self.view:IsOpen() then
        self.view:Flush()
    end
end

function TreasureHuntWGCtrl:OpenProbabilityView()
    self.treasurehunt_probability_view:Open()
end

function TreasureHuntWGCtrl:OpenBaodiTips()
    self.baodi_tips:Open()
end

function TreasureHuntWGCtrl:OpenMingwenRecordView()
    self.mingwen_record_view:Open()
end

function TreasureHuntWGCtrl:GetTreasureType()
    return self.data:GetCurShowType()
end

function TreasureHuntWGCtrl:OnClickBtn(btn_index)
    local treasure_type = self:GetTreasureType()
    local treasure_cfg = TreasureHuntWGData.Instance:GetTreasureCfgByType(treasure_type)
    if IsEmptyTable(treasure_cfg) then
        return
    end
    local cur_cfg = treasure_cfg[btn_index]
    local mode = treasure_cfg[btn_index].mode
    
    -- -- vip特权处理
    local cfg_need_num = cur_cfg.stuff_num
    local mingwen_vip_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(treasure_cfg[btn_index].mode)
    local need_num = not IsEmptyTable(mingwen_vip_cfg) and mingwen_vip_cfg.vip_stuff_num or cfg_need_num
    
    -- 抽奖
    local tips_data = {}
    tips_data.item_id = cur_cfg.stuff_id
    tips_data.price = treasure_cfg[1].cost_gold 
    tips_data.draw_count = need_num
    tips_data.has_checkbox = true
    tips_data.checkbox_str = string.format("treasure_hunt%s%s", treasure_type, btn_index) 
    TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind2(self.SendTreasure, self, mode), nil)
    LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_FIND_WARD)
end


function TreasureHuntWGCtrl:SendTreasure(mode)
    local treasure_type = self:GetTreasureType()
    local is_skip = self.data:GetSkipSpineStatusByType(treasure_type)
    if is_skip then
        TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_BUY, mode)
    else
        -- TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_BUY, mode)

        TreasureHuntWGCtrl.Instance:OpenTreasureSpine(function()
            TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_BUY, mode)
        end)
    end
end

function TreasureHuntWGCtrl:SendMingwenTreasure(index)
    TreasureHuntWGCtrl.Instance:DoMingWenOpera(TreasureHuntWGData.POSY_OPERATOR_TYPE.POSY_SHOP_OPERATOR_TYPE_BUY, index, 0)
end


function TreasureHuntWGCtrl:OnClickMingwenBtn(btn_index)
    if TreasureHuntWGData.Instance:GetIsGoOverDayLimit() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.TreasureHunt.OverDaylimit)
        return
    end

    local free_time = TreasureHuntWGData.Instance:GetMingWenInfo().free_timestamp
    local treasure_cfg = TreasureHuntWGData.Instance:GetMingwenModeCfg()
    local cur_cfg = treasure_cfg[btn_index]
    local mode = treasure_cfg[btn_index].mode
    if btn_index == 1 and free_time <= TimeWGCtrl.Instance:GetServerTime() then
        TreasureHuntWGCtrl.Instance:DoMingWenOpera(TreasureHuntWGData.POSY_OPERATOR_TYPE.POSY_SHOP_OPERATOR_TYPE_BUY, mode, 1) --免费抽奖
        return
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.stuff_id) --拥有的数量
    --local need_num = cur_cfg.stuff_num - has_num
    local cfg_need_stuff = cur_cfg.stuff_num
    local mingwen_vip_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(treasure_cfg[btn_index].mode, true)
    local need_num = not IsEmptyTable(mingwen_vip_cfg) and (mingwen_vip_cfg.vip_stuff_num) or cfg_need_stuff

    local tips_data = {}
    tips_data.item_id = cur_cfg.stuff_id
    tips_data.price = treasure_cfg[1].cost_gold 
    tips_data.draw_count = need_num
    tips_data.has_checkbox = true
    tips_data.checkbox_str = string.format("treasure_hunt%s%s", 4, btn_index) 
    TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind2(self.SendMingwenTreasure, self, mode), nil)
    LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_FIND_WARD)
end

function TreasureHuntWGCtrl:OpenTreasureRecordTip(treasure_type)
    if self.treasurehunt_record then
        self.treasurehunt_record:SetCurTreasureType(treasure_type)
        self.treasurehunt_record:Open()
    end
end

function TreasureHuntWGCtrl:FlushTreasureRecordView()
    if self.treasurehunt_record and self.treasurehunt_record:IsOpen() then
        self.treasurehunt_record:Flush()
    end
end

function TreasureHuntWGCtrl:FlushTreasureBossList()
    --if self.view:IsOpen() then
        --self.view:Flush(nil, "boss_info_change")
    -- end

    if self.xukongliefeng_view:IsOpen() then
        self.xukongliefeng_view:Flush()
    end
end

function TreasureHuntWGCtrl:ShowBossActivityTip()
    ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.TREASURE_HUNT_BOSS_ACTIVITY)
end

function TreasureHuntWGCtrl:ShowBossReflushIcon(start_time,end_time)
    ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.TREASURE_HUNT_BOSS_ACTIVITY, ACTIVITY_STATUS.OPEN,
    end_time, start_time, end_time, RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
end

function TreasureHuntWGCtrl:CloseBossReflushIcon()
    if not self:BossActivityIsOpen() then
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.TREASURE_HUNT_BOSS_ACTIVITY, ACTIVITY_STATUS.CLOSE,
        0, nil, nil, RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
    end
end

function TreasureHuntWGCtrl:StayBossReflushIcon(end_time)
    ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.TREASURE_HUNT_BOSS_ACTIVITY, ACTIVITY_STATUS.STANDY,end_time)--,nil,nil,RAND_ACTIVITY_OPEN_TYPE.RAND_ACTIVITY_OPEN_TYPE_NORMAL)
end

function TreasureHuntWGCtrl:BossActivityIsOpen()
    local flush_data = TreasureBossWGData.Instance:GetXunBaoBossRefreshTimeData()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local last_flush_time = 0

    for k,v in pairs(flush_data.time_data) do
        if v.definite_time <= cur_time then
            if v.definite_time > last_flush_time then
                last_flush_time = v.definite_time
            end
        end
    end

    local refresh_remind_time = 3600 --60分钟内
    local have_can_kill = TreasureHuntWGData.Instance:HaveTreasureBossCanKill()
    local is_open = have_can_kill and (last_flush_time + refresh_remind_time > cur_time)

    return is_open
end

function TreasureHuntWGCtrl:OpenMingWenYuLanView()
    if self.mingwen_yulan_view then
        self.mingwen_yulan_view:Open()
    end
end

function TreasureHuntWGCtrl:OpenXuKongLieFengView(treasure_type)
    if self.xukongliefeng_view then
        self.xukongliefeng_view:SetDataAndOpen(treasure_type)
    end
end

function TreasureHuntWGCtrl:OpenTreasureSpine(call_back)
    if self.treasurehunt_spine_view then
        self.treasurehunt_spine_view:SetDataAndOpen(call_back)
    end
end
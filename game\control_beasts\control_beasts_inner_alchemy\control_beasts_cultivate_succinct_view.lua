function ControlBeastsCultivateWGView:LoadSuccinctViewCallBack()
    if not self.beasts_alchemy_list then
		self.beasts_alchemy_list = AsyncListView.New(BeastsCultivateSlotRender, self.node_list.alchemy_strengthen_list)
        self.beasts_alchemy_list:SetStartZeroIndex(true)
		self.beasts_alchemy_list:SetSelectCallBack(BindTool.Bind(self.BeastsAlchemySlotSelect,self))
        self.beasts_alchemy_list:SetRefreshCallback(BindTool.Bind(self.BeastsAlchemySlotRefresh,self))
		self.beasts_alchemy_list:SetLimitSelectFunc(BindTool.Bind(self.BeastsAlchemySlotSelectLimit,self))
	end

    if not self.alchemy_succinct_left then
        self.alchemy_succinct_left = AlchemySuccinctRender.New(self.node_list.alchemy_succinct_left)
        self.alchemy_succinct_left:SetSuccinctAdditionalLockBack(BindTool.Bind(self.BeastsAlchemySuccinctLockBack, self))
    end

    if not self.alchemy_succinct_right then
        self.alchemy_succinct_right = AlchemySuccinctRender.New(self.node_list.alchemy_succinct_right)
    end

    if not self.alchemy_succinct_stuff_list then
        self.alchemy_succinct_stuff_list = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.alchemy_succinct_stuff_list:FindObj(string.format("stuff_cell_0%d", i))
            if cell_obj then
                local cell = AlchemySuccinctStuffSpendRender.New(cell_obj)
                cell:SetIndex(i)
                self.alchemy_succinct_stuff_list[i] = cell
            end
        end
    end

    XUI.AddClickEventListener(self.node_list.alchemy_succinct_btn, BindTool.Bind(self.OnClickAlchemySuccinctBtn, self))
    XUI.AddClickEventListener(self.node_list.alchemy_succinct_save_btn, BindTool.Bind(self.OnClickAlchemySuccinctSaveBtn, self))
    XUI.AddClickEventListener(self.node_list.alchemy_succinct_pre_btn, BindTool.Bind(self.OnClickAlchemySuccinctPreBtn, self))
end

function ControlBeastsCultivateWGView:OpenSuccinctViewCallBack()
end

function ControlBeastsCultivateWGView:CloseSuccinctViewCallBack()
end

function ControlBeastsCultivateWGView:ShowSuccinctViewCallBack()
end

function ControlBeastsCultivateWGView:ReleaseSuccinctViewCallBack()
    if self.beasts_alchemy_list then
        self.beasts_alchemy_list:DeleteMe()
        self.beasts_alchemy_list = nil
    end

    if self.alchemy_succinct_left then
        self.alchemy_succinct_left:DeleteMe()
        self.alchemy_succinct_left = nil
    end

    if self.alchemy_succinct_right then
        self.alchemy_succinct_right:DeleteMe()
        self.alchemy_succinct_right = nil
    end

    if self.alchemy_succinct_stuff_list then
        for i, v in ipairs(self.alchemy_succinct_stuff_list) do
            v:DeleteMe()
        end

        self.alchemy_succinct_stuff_list = nil
    end

    if self.alchemy_succinct_opea_alert_window then
		self.alchemy_succinct_opea_alert_window:DeleteMe()
		self.alchemy_succinct_opea_alert_window = nil
	end

    if self.alchemy_succinct_save_alert_window then
		self.alchemy_succinct_save_alert_window:DeleteMe()
		self.alchemy_succinct_save_alert_window = nil
	end

    self.num_list = nil
end
-----------------------------------------------------------------------------
function ControlBeastsCultivateWGView:FlushSuccinctViewCallBack(param_t)
	-- for k,v in pairs(param_t) do
	-- 	if k == "all" then
    --     elseif k == "main" then
    --     elseif k == "assist" then
    --     end
    -- end

    self:ShowSuccinctViewChangeHole()
end

--------------------------------------------------------------------------------
-- 切换上阵位置
function ControlBeastsCultivateWGView:ShowSuccinctViewChangeHole()
    if (not self.fight_slot_index) or (not self.fight_slot_data) then
        return
    end

    if self.beasts_alchemy_list == nil then
        return
    end

    local hole_id = self.fight_slot_index
    local now_slot_index = 0

    if self.beast_slot_index then
        now_slot_index = self.beast_slot_index
    end

    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(hole_id)
    self.beasts_alchemy_list:SetDataList(list)
    self.beasts_alchemy_list:JumpToIndex(now_slot_index, 6)
end

-- 刷新强化数据
function ControlBeastsCultivateWGView:BeastsFlushAlchemySlotSuccinctMessage(is_need_reset_lock)
    if not self.beast_slot_data then
        return
    end

    local hole_id = self.fight_slot_index
    self.alchemy_succinct_left:SetSuccinctIsStuff(false, hole_id, self.beast_slot_index)
    self.alchemy_succinct_right:SetSuccinctIsStuff(true, hole_id, self.beast_slot_index)
    self.alchemy_succinct_left:SetData(self.beast_slot_data)
    self.alchemy_succinct_left:SetIsNeedResetLock(is_need_reset_lock)
    self.alchemy_succinct_right:SetData(self.beast_slot_data)
    self:BeastsAlchemySuccinctLockBack()

    local equip_info = self.beast_slot_data and self.beast_slot_data.equip_info or {}
    local words_list = equip_info and equip_info.words_list or {}
    local words_start_index = COMMON_CONSTS.NUMBER_ZERO
    local is_has_succinct = false

	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
        local word_data = words_list[j]

        if word_data.can_replace_words ~= -1 then
            is_has_succinct = true
        end
    end

    local can_save = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanSuccinctSave(equip_info)
    self.node_list.alchemy_succinct_save_btn:CustomSetActive(is_has_succinct)
    self.node_list.alchemy_succinct_save_remind:CustomSetActive(can_save)
end

-- 刷新强化数据
function ControlBeastsCultivateWGView:BeastsAlchemySuccinctLockBack(num_list)
    --获取锁定的物品消耗
    self.num_list = num_list or self.num_list
    local final_lock_item = {}
    local reward_list = {}

    local add_item = function(item_data)
        if reward_list[item_data.item_id] then
            reward_list[item_data.item_id].num = reward_list[item_data.item_id].num + item_data.num
        else
            reward_list[item_data.item_id] = {}
            reward_list[item_data.item_id].item_id = item_data.item_id
            reward_list[item_data.item_id].num = item_data.num
            reward_list[item_data.item_id].is_bind = item_data.is_bind
        end
    end

    local base_cfg = ControlBeastsCultivateWGData.Instance:GetBaseCfg()
    local words_refresh_base_cost_item = base_cfg and base_cfg.words_refresh_base_cost_item
    if words_refresh_base_cost_item then
        add_item(words_refresh_base_cost_item)
    end

    if self.num_list then
        for i, v in ipairs(self.num_list) do
            local server_seq = v - 1
            local cfg = ControlBeastsCultivateWGData.Instance:GetWordRefreshListCfg(server_seq)
            if cfg and cfg.cost_item_list then
                if cfg.cost_item_list[0] then
                    add_item(cfg.cost_item_list[0])
                end
    
                for m, n in ipairs(cfg.cost_item_list) do
                    add_item(n)
                end
            end
        end
    end

    for k, v in pairs(reward_list) do
        if v and v.item_id then
            table.insert(final_lock_item, v)
        end
    end

    for i, v in ipairs(self.alchemy_succinct_stuff_list) do
        v:SetVisible(final_lock_item[i] ~= nil)

        if final_lock_item[i] ~= nil then
            v:SetData(final_lock_item[i])
        end
    end
end

--------------------------------------------------------------------------------
-- 丹药点击洗练
function ControlBeastsCultivateWGView:OnClickAlchemySuccinctBtn()
    if (not self.beast_slot_data) or (not self.beast_slot_index) then
        return
    end

    local hole_id = self.fight_slot_index
    local equip_info = self.beast_slot_data and self.beast_slot_data.equip_info or {}
    local lock_flag_table = {}
    local lock_final_num = -1
    local now_words_num = -1
    local reward_list = {}

    local add_item = function(item_data)
        if reward_list[item_data.item_id] then
            reward_list[item_data.item_id].num = reward_list[item_data.item_id].num + item_data.num
        else
            reward_list[item_data.item_id] = {}
            reward_list[item_data.item_id].item_id = item_data.item_id
            reward_list[item_data.item_id].num = item_data.num
            reward_list[item_data.item_id].is_bind = item_data.is_bind
        end
    end

    local base_cfg = ControlBeastsCultivateWGData.Instance:GetBaseCfg()
    local words_refresh_base_cost_item = base_cfg and base_cfg.words_refresh_base_cost_item
    if words_refresh_base_cost_item then
        add_item(words_refresh_base_cost_item)
    end

	local words_list = equip_info.words_list or {}
	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
		local ward_data = words_list[j]
		if ward_data.words_seq ~= -1 then
            if now_words_num == -1 then
                now_words_num = 0
            end

            now_words_num = now_words_num + 1
		end
	end

    if self.num_list then
        for i, v in ipairs(self.num_list) do
            local server_seq = v - 1
            lock_flag_table[server_seq] = 1

            if lock_final_num == -1 then
                lock_final_num = 0
            end
            lock_final_num = lock_final_num + 1

            local cfg = ControlBeastsCultivateWGData.Instance:GetWordRefreshListCfg(server_seq)
            if cfg and cfg.cost_item_list then
                if cfg.cost_item_list[0] then
                    add_item(cfg.cost_item_list[0])
                end
    
                for m, n in ipairs(cfg.cost_item_list) do
                    add_item(n)
                end
            end
        end
    end

    if now_words_num ~= -1 and now_words_num == lock_final_num then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.AlchemySuccinctError)
        return
    end

    for k, v in pairs(reward_list) do
        if v and v.item_id then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
            if item_num < v.num then
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = v.item_id })
                -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
                return
            end
        end
    end

    local can_save = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanSuccinctSave(equip_info)
    local lock_flag = bit:b2d_end(lock_flag_table)

    if can_save then
        if not self.alchemy_succinct_opea_alert_window then
            self.alchemy_succinct_opea_alert_window = Alert.New(nil, nil, nil, nil, true)
            self.alchemy_succinct_opea_alert_window:SetCheckBoxDefaultSelect(false)
        end

        self.alchemy_succinct_opea_alert_window:SetLableString(Language.ContralBeastsAlchemy.AlchemySuccinctError3)
        self.alchemy_succinct_opea_alert_window:SetOkFunc(function ()
            ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateRefresh(hole_id, self.beast_slot_index, nil, lock_flag)
        end)
        self.alchemy_succinct_opea_alert_window:Open()
    else
        ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateRefresh(hole_id, self.beast_slot_index, nil, lock_flag)
    end
end

-- 丹药点击洗练保存
function ControlBeastsCultivateWGView:OnClickAlchemySuccinctSaveBtn()
    if (not self.beast_slot_data) or (not self.beast_slot_index) then
        return
    end

    local hole_id = self.fight_slot_index
    local equip_info = self.beast_slot_data and self.beast_slot_data.equip_info or {}
    local can_save = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanSuccinctSave(equip_info)

    if not can_save then
        if not self.alchemy_succinct_save_alert_window then
            self.alchemy_succinct_save_alert_window = Alert.New(nil, nil, nil, nil, true)
            self.alchemy_succinct_save_alert_window:SetCheckBoxDefaultSelect(false)
        end

        self.alchemy_succinct_save_alert_window:SetLableString(Language.ContralBeastsAlchemy.AlchemySuccinctError2)
        self.alchemy_succinct_save_alert_window:SetOkFunc(function ()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.SaveSucc)
            ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateReplace(hole_id, self.beast_slot_index, nil)
        end)
        self.alchemy_succinct_save_alert_window:Open()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ScreenShot.SaveSucc)
        ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateReplace(hole_id, self.beast_slot_index, nil)
    end
end

-- 丹药点击洗练词条预览
function ControlBeastsCultivateWGView:OnClickAlchemySuccinctPreBtn()
    if (not self.beast_slot_data) or (not self.beast_slot_index) then
        return
    end

    local equip_info = self.beast_slot_data and self.beast_slot_data.equip_info or {}
    local equip_item_id = (equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    ControlBeastsCultivateWGCtrl.Instance:OpenCultivateSuccinctPreView(equip_item_id, self.beast_slot_index)
end

----------------------------------------AlchemyStuffSpendRender---------------------------------
AlchemySuccinctRender = AlchemySuccinctRender or BaseClass(BaseRender)
function AlchemySuccinctRender:LoadCallBack()
    if not self.succinct_item then
        self.succinct_item = ItemCell.New(self.node_list.succinct_item)
        self.succinct_item:SetIsShowTips(false)
    end

    -- 附加词条
    if self.alchemy_additional_list == nil then
        self.alchemy_additional_list = {}
        for i = 1, 4 do
            local attr_obj = self.node_list.alchemy_additional_list:FindObj(string.format("additional_0%d", i))
            if attr_obj then
                local cell = AlchemyAdditionalRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetSelectLockBtnClickCallBack(BindTool.Bind(self.GetNowLoockNumber, self))
                cell:SetSelectLockBtnClickLimit(BindTool.Bind(self.SelectLockBtnLimit, self))
                self.alchemy_additional_list[i] = cell
            end
        end
    end
end

function AlchemySuccinctRender:SetSuccinctIsStuff(is_stuff, fight_slot_index, slot_index)
    self.cur_succinct_is_stuff = is_stuff
    self.fight_slot_index = fight_slot_index
    self.slot_index = slot_index
end

function AlchemySuccinctRender:SetSuccinctAdditionalLockBack(fun)
    self.lock_btn_select_back = fun
end

function AlchemySuccinctRender:GetNowLoockNumber()
    local num_list = {}

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        if alchemy_additional_cell:GetCurrLockStatus() then
            table.insert(num_list, alchemy_additional_cell.index)
        end
    end

    if self.lock_btn_select_back then
        self.lock_btn_select_back(num_list)
    end
end

-- 是否能锁定
function AlchemySuccinctRender:SelectLockBtnLimit(cell)
    local equip_info = self.data and self.data.equip_info or {}
    local words_list = equip_info and equip_info.words_list or {}
    local now_word_count = 0
    local now_lock_count = 0

    for k, v in pairs(words_list) do
        if v and v.words_seq ~= -1 then
            now_word_count = now_word_count + 1
        end 
    end

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        if alchemy_additional_cell:GetCurrLockStatus() then
            now_lock_count = now_lock_count + 1
        end
    end

    if now_lock_count == now_word_count - 1 then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.ContralBeastsAlchemy.AlchemySuccinctLockError, now_lock_count))
        return true
    end

    return false
end

function AlchemySuccinctRender:ReleaseCallBack()
    if self.succinct_item then
        self.succinct_item:DeleteMe()
        self.succinct_item = nil
    end

    if self.alchemy_additional_list and #self.alchemy_additional_list > 0 then
		for _, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
			alchemy_additional_cell:DeleteMe()
			alchemy_additional_cell = nil
		end

		self.alchemy_additional_list = nil
	end

    self.cur_succinct_is_stuff = nil
    self.fight_slot_index = nil
    self.slot_index = nil
    self.lock_btn_select_back = nil
end

function AlchemySuccinctRender:SetIsNeedResetLock(is_need_reset)
    if is_need_reset then
        for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
            alchemy_additional_cell:ChangeNowlockStatus(false)
        end
    end
end

function AlchemySuccinctRender:OnFlush()
    if not self.data then
        return 
    end

    local is_empty_data = IsEmptyTable(self.data)

    if is_empty_data then
        self.node_list.has_data_root:CustomSetActive(not is_empty_data)
        self.node_list.not_has_data_root:CustomSetActive(is_empty_data)
        return
    end

    -- 不是材料设置数据
    local is_has_data = false
    local is_has_additional = false
    local equip_info = self.data and self.data.equip_info or {}
    local words_list = equip_info and equip_info.words_list or {}
    local words_start_index = COMMON_CONSTS.NUMBER_ZERO

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        local word_data = words_list[words_start_index]
        local final_words_seq = self.cur_succinct_is_stuff and word_data.can_replace_words or word_data.words_seq
        alchemy_additional_cell:SetVisible(word_data ~= nil and final_words_seq ~= -1)

        if word_data ~= nil and final_words_seq ~= -1 then
            if self.cur_succinct_is_stuff then
                is_has_data = true
                alchemy_additional_cell:SetAppointWordsSeq(word_data.can_replace_words)
                alchemy_additional_cell:SetNotNeedLockStatus()
            else
                alchemy_additional_cell:ChangeNowlockStatus(alchemy_additional_cell:GetCurrLockStatus())
            end

            is_has_additional = true
            alchemy_additional_cell:SetData(word_data)
        end

        words_start_index = words_start_index + 1
    end

    if (not self.cur_succinct_is_stuff) or (self.cur_succinct_is_stuff and is_has_data) then
        self:FlushEquipDataMessage()
    end

    self.node_list.alchemy_additional_list:CustomSetActive(is_has_additional)
    self.node_list.additional_empty:CustomSetActive(not is_has_additional)
    self.node_list.has_data_root:CustomSetActive((not self.cur_succinct_is_stuff) or (self.cur_succinct_is_stuff and is_has_data))
    self.node_list.not_has_data_root:CustomSetActive(self.cur_succinct_is_stuff and (not is_has_data))
end

-- 刷新物品数据
function AlchemySuccinctRender:FlushEquipDataMessage()
    if not self.data then
        return 
    end

    local data = self.data
    local hole_id = self.fight_slot_index or COMMON_CONSTS.NUMBER_ZERO
    local slot_id = self.slot_index or COMMON_CONSTS.NUMBER_ZERO
    local equip_item_id = (data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO

    if equip_item_id ~= 0 then
        self.succinct_item:SetData({item_id = equip_item_id})  
        local item_cfg = ItemWGData.Instance:GetItemConfig(equip_item_id)
        self.node_list.succinct_name.text.text = ToColorStr(item_cfg and item_cfg.name or "", ITEM_COLOR[item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO])
        local score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(self.data.equip_info, false, hole_id, slot_id, self.data.level, self.cur_succinct_is_stuff)
        
        if self.cur_succinct_is_stuff then
            if self.node_list.succinct_score_arrow then
                local compare_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(self.data.equip_info, false, hole_id, slot_id, self.data.level, false)
                local str = score > compare_score and "a3_ty_up_1" or "a3_ty_down_1"
                self.node_list.succinct_score_arrow:CustomSetActive(score ~= compare_score)
                self.node_list.succinct_score_arrow.image:LoadSprite(ResPath.GetCommon(str))
            end
        end
        if self.node_list.succinct_name_score then
            self.node_list.succinct_name_score.text.text = score
        end
    end
end

----------------------------------------AlchemyStuffSpendRender---------------------------------
AlchemySuccinctStuffSpendRender = AlchemySuccinctStuffSpendRender or BaseClass(BaseRender)
function AlchemySuccinctStuffSpendRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
    end
end

function AlchemySuccinctStuffSpendRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function AlchemySuccinctStuffSpendRender:OnFlush()
    if not self.data then
        return 
    end

    ---状态显示
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    local color = item_num >= self.data.num and COLOR3B.GREEN or COLOR3B.RED
    self.item_cell:SetData({item_id = self.data.item_id})
    self.item_cell:SetRightBottomTextVisible(true)
    self.item_cell:SetRightBottomColorText(item_num .. '/' .. self.data.num, color)
end
ControlBeastsAlchemyAttrView = ControlBeastsAlchemyAttrView or BaseClass(SafeBaseView)

function ControlBeastsAlchemyAttrView:__init()
	self:SetMaskBg(true, true)
	local bundle_name = "uis/view/control_beasts_alchemy_ui_prefab"
    self:AddViewResource(0, bundle_name, "layout_beasts_all_alchemy_attr_tip")
end

function ControlBeastsAlchemyAttrView:ReleaseCallBack()
	self.cur_fight_slot = nil

	if self.alchemy_base_attrlist and #self.alchemy_base_attrlist > 0 then
		for _, base_attr_cell in ipairs(self.alchemy_base_attrlist) do
			base_attr_cell:DeleteMe()
			base_attr_cell = nil
		end

		self.alchemy_base_attrlist = nil
	end

	if self.alchemy_additional_list and #self.alchemy_additional_list > 0 then
		for _, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
			alchemy_additional_cell:DeleteMe()
			alchemy_additional_cell = nil
		end

		self.alchemy_additional_list = nil
	end

    if self.alchemy_suit_list and #self.alchemy_suit_list > 0 then
		for _, alchemy_suit_cell in ipairs(self.alchemy_suit_list) do
			alchemy_suit_cell:DeleteMe()
			alchemy_suit_cell = nil
		end

		self.alchemy_suit_list = nil
	end
end

function ControlBeastsAlchemyAttrView:SetNowFightSlot(fight_slot)
	self.cur_fight_slot = fight_slot
end

function ControlBeastsAlchemyAttrView:LoadCallBack()
    -- 基础属性
    if self.alchemy_base_attrlist == nil then
        self.alchemy_base_attrlist = {}
        for i = 1, 6 do
            local attr_obj = self.node_list.alchemy_base_attrlist:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetAttrNameNeedSpace(true)
                cell:SetIndex(i)
                self.alchemy_base_attrlist[i] = cell
            end
        end
    end

	-- 附加词条
	if self.alchemy_additional_list == nil then
		self.alchemy_additional_list = {}
		for i = 1, 10 do
			local attr_obj = self.node_list.alchemy_additional_list:FindObj(string.format("additional_0%d", i))
			if attr_obj then
				local cell = AlchemyAdditionalPreviewRender.New(attr_obj)
				cell:SetIndex(i)
				self.alchemy_additional_list[i] = cell
			end
		end
	end

	-- 套装属性
	if self.alchemy_suit_list == nil then
		self.alchemy_suit_list = {}
		for i = 1, 6 do
			local attr_obj = self.node_list.alchemy_suit_root:FindObj(string.format("alchemy_suit_render_%d", i))
			if attr_obj then
				local cell = AlchemySuitPreviewRender.New(attr_obj)
				cell:SetIndex(i)
				self.alchemy_suit_list[i] = cell
			end
		end
	end
end

function ControlBeastsAlchemyAttrView:OnFlush(param_t)
	if not self.cur_fight_slot then
		return
	end

	local attr_table, additional_list, suit_list, all_score = self:SplitCurrEquipList()
	self.node_list.alchemy_score_txt.text.text = all_score
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_score_panel.rect)

	local is_empty_attr = self:ShowAlchemyBaseAttrList(attr_table)
	local is_empty_additional = self:ShowAlchemyAdditionalList(additional_list)
	local is_empty_suit = self:ShowAlchemySuitList(suit_list)
	local is_all_empty = is_empty_attr and is_empty_additional and is_empty_suit
	self.node_list.img_no_record:CustomSetActive(is_all_empty)
	self.node_list.attr_scroll:CustomSetActive(not is_all_empty)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.attr_scroll.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.center_root.rect)
end

-- 开始拆分穿戴的装备
function ControlBeastsAlchemyAttrView:SplitCurrEquipList()
	if not self.cur_fight_slot then
		return
	end

	local reward_list = {}
	local additional_list = {}
	local additional_skill_list = {}
	local suit_list = {}
	local all_score = 0
	-- 拆分所有的词条属性和套装属性
	local split_all_list = function(equip_info, slot_index, slot_lv)
		if (not equip_info) or equip_info.item_id == 0 then
			return
		end

		local cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(equip_info.item_id)
		if cfg then
			for i = 1, 6 do
				local key = cfg["attr_id" .. i]
				if key then
					if not reward_list[key] then
						reward_list[key] = {}
						reward_list[key].attr_str = key
						reward_list[key].attr_value = cfg["attr_value" .. i]
					else
						reward_list[key].attr_value = reward_list[key].attr_value + cfg["attr_value" .. i]
					end
				end
			end

			if cfg.suit_type and cfg.suit_type ~= -1 then
				if not suit_list[cfg.suit_type] then
					suit_list[cfg.suit_type] = {}
					suit_list[cfg.suit_type].suit_type = cfg.suit_type
					suit_list[cfg.suit_type].suit_num = 1
				else
					suit_list[cfg.suit_type].suit_num = suit_list[cfg.suit_type].suit_num + 1
				end
			end
			all_score = all_score + cfg.initial_score or 0
		end

		local slot_cfg = ControlBeastsCultivateWGData.Instance:GetSlotUpLvCfg(slot_index, slot_lv)
		-- 槽位强化属性
		if slot_cfg and not IsEmptyTable(slot_cfg) then
			for i = 1, 6 do
				local key = slot_cfg["attr_id" .. i]
				if key then
					local curr_value = slot_cfg["attr_value" .. i]
	
					if not reward_list[key] then
						reward_list[key] = {}
						reward_list[key].attr_str = key
						reward_list[key].attr_value = curr_value
					else
						reward_list[key].attr_value = reward_list[key].attr_value + curr_value
					end
				end
			end
		end

		local words_list = equip_info.words_list or {}

		for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
			local ward_data = words_list[j]
			if ward_data then
				local final_seq =  ward_data.words_seq
				local random_value = final_seq % 100
				local real_seq = math.floor(final_seq / 100)
	
				local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)
				if cfg then
					if cfg.words_type == 1 then
						local words_value = math.floor(cfg.param2 * (random_value / 100))

						if not additional_list[real_seq] then
							additional_list[real_seq] = {}
							additional_list[real_seq].words_type = cfg.words_type
							additional_list[real_seq].attr_str = cfg.param1
							additional_list[real_seq].attr_value = words_value
						else
							additional_list[real_seq].attr_value = additional_list[real_seq].attr_value + words_value
						end
					else
						additional_skill_list[real_seq] = {}
						additional_skill_list[real_seq].words_type = cfg.words_type
						additional_skill_list[real_seq].words_cfg = cfg
					end

					-- 词条评分
					all_score = all_score + ((cfg.entry_score or 0) * (random_value / 100))
				end
			end
		end
	end

	-- 开始拆分
	local equip_list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(self.cur_fight_slot)
	if equip_list then
		if equip_list[COMMON_CONSTS.NUMBER_ZERO] then
			split_all_list(equip_list[COMMON_CONSTS.NUMBER_ZERO].equip_info, COMMON_CONSTS.NUMBER_ZERO, equip_list[COMMON_CONSTS.NUMBER_ZERO].level)
			local equip_item_id = equip_list[COMMON_CONSTS.NUMBER_ZERO].equip_info and equip_list[COMMON_CONSTS.NUMBER_ZERO].equip_info.item_id or COMMON_CONSTS.NUMBER_ZERO
			
			--计算强化评分
			if equip_item_id ~= 0 then
				local slot_cfg = ControlBeastsCultivateWGData.Instance:GetSlotUpLvCfg(COMMON_CONSTS.NUMBER_ZERO, equip_list[COMMON_CONSTS.NUMBER_ZERO].level)
				local solt_lv_score = slot_cfg and slot_cfg.level_score or 0
				all_score = all_score + solt_lv_score
			end
		end

		for i, v in ipairs(equip_list) do
			split_all_list(v.equip_info, i, v.level)
			local equip_item_id = v.equip_info and v.equip_info.item_id or COMMON_CONSTS.NUMBER_ZERO

			--计算强化评分
			if equip_item_id ~= 0 then
				local slot_cfg = ControlBeastsCultivateWGData.Instance:GetSlotUpLvCfg(i, v.level)
				local solt_lv_score = slot_cfg and slot_cfg.level_score or 0
				all_score = all_score + solt_lv_score
			end
		end
	end

	-- 开始组合基础数据
	local return_attr_table = {}
	for _, attr_data in pairs(reward_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_attr_table, attr_data)
		end
	end

	table.sort(return_attr_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	-- 开始组合附加属性
	local return_additional_list = {}
	for _, additional_data in pairs(additional_list) do
		if additional_data and additional_data.words_type and additional_data.words_type ~= 0 then
			table.insert(return_additional_list, additional_data)
		end
	end

	for _, additional_skill_data in pairs(additional_skill_list) do
		if additional_skill_data and additional_skill_data.words_type and additional_skill_data.words_type ~= 0 then
			table.insert(return_additional_list, additional_skill_data)
		end
	end
	
	-- 开始组合套装属性
	local return_suit_list = {}
	for _, attr_data in pairs(suit_list) do
		if attr_data and attr_data.suit_type and attr_data.suit_type ~= -1 then
			for i = attr_data.suit_num, 1, -1 do
				local cfg = ControlBeastsCultivateWGData.Instance:GetSuitCfgByTypeNum(attr_data.suit_type, i)

				if cfg ~= nil then
					local data = {}
					data.cur_num = attr_data.suit_num
					data.cfg_data = cfg
					table.insert(return_suit_list, data)

					all_score = all_score + cfg.suit_score or 0
				end
			end
		end
	end
	
	return return_attr_table, return_additional_list, return_suit_list, all_score
end

-- 展示基础属性
function ControlBeastsAlchemyAttrView:ShowAlchemyBaseAttrList(attr_table)
	if not attr_table then
		return true
	end

	local is_empty_attr = IsEmptyTable(attr_table)
	if is_empty_attr then
		return true
	end

	for i, attrlist_cell in ipairs(self.alchemy_base_attrlist) do
        attrlist_cell:SetVisible(attr_table[i] ~= nil)

        if attr_table[i] ~= nil then
            attrlist_cell:SetData(attr_table[i])
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_base_attrlist.rect)
	return false
end

-- 展示附加属性
function ControlBeastsAlchemyAttrView:ShowAlchemyAdditionalList(additional_list)
	local is_empty_attr = false

	if not additional_list then
		is_empty_attr = true
	end

	is_empty_attr = IsEmptyTable(additional_list)
	if is_empty_attr then
		is_empty_attr = true
	end

	self.node_list.alchemy_additional_list:CustomSetActive(not is_empty_attr)
	self.node_list.alchemy_additional_empty:CustomSetActive(is_empty_attr)

	if is_empty_attr then
		return is_empty_attr
	end

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        local word_data = additional_list[i]
        alchemy_additional_cell:SetVisible(word_data ~= nil)

        if word_data ~= nil then
            alchemy_additional_cell:SetData(word_data)
        end
    end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_base_attrlist.rect)
	return false
end

-- 展示套装属性
function ControlBeastsAlchemyAttrView:ShowAlchemySuitList(suit_list)
	local is_empty_attr = false

	if not suit_list then
		is_empty_attr = true
	end

	is_empty_attr = IsEmptyTable(suit_list)
	if is_empty_attr then
		is_empty_attr = true
	end

	self.node_list.alchemy_suit_root:CustomSetActive(not is_empty_attr)
	self.node_list.alchemy_suit_empty:CustomSetActive(is_empty_attr)

	if is_empty_attr then
		return is_empty_attr
	end

	for i, alchemy_suit_cell in ipairs(self.alchemy_suit_list) do
		alchemy_suit_cell:SetVisible(suit_list[i] ~= nil)

		if suit_list[i] ~= nil then
			alchemy_suit_cell:SetData(suit_list[i])
		end
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.alchemy_suit_root.rect)
	return false
end

----------------------------------- 内丹词条 ----------------------------------------
AlchemyAdditionalPreviewRender = AlchemyAdditionalPreviewRender or BaseClass(BaseRender)
function AlchemyAdditionalPreviewRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.operate_btn, BindTool.Bind(self.OnClickLookSkillDesc, self))               -- 属性详情
end

function AlchemyAdditionalPreviewRender:OnClickLookSkillDesc()
    if (not self.data) or (not self.data.words_type == nil) or self.data.words_type ~= 2 then
        return
    end

    if self.data.words_cfg then
        local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(self.data.words_cfg.param1)
        if skill_cfg then
            local show_data = {
                icon = skill_cfg.skill_icon,
                top_text = skill_cfg.skill_name,
                body_text = skill_cfg.skill_des,
                x = 0,
                y = 0,
                set_pos2 = true,
                hide_next = true,
                is_active_skill = false,
                skill_level = skill_cfg.skill_level or 0,
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

function AlchemyAdditionalPreviewRender:OnFlush()
    if not self.data then
        return
    end

	self.node_list.operate_btn:CustomSetActive(self.data.words_type == 2)

	if self.data.words_type == 1 then
		self.node_list.additional_name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, true, false)
		self.node_list.additional_value.text.text = self.data.attr_value
	else
		local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(self.data.words_cfg.param1)
		if skill_cfg then
			local skill_color = skill_cfg.color or COMMON_CONSTS.NUMBER_ZERO
			self.node_list.additional_name.text.text = ToColorStr(skill_cfg.skill_name, ITEM_COLOR[skill_color]) 
			if skill_cfg.skill_level ~= 0 then
				self.node_list.additional_value.text.text = string.format(Language.Common.Level1, skill_cfg.skill_level)
			else
				self.node_list.additional_value.text.text = ""
			end
		end
	end
end

----------------------------------- 内丹套装(物品格子) ----------------------------------------
AlchemySuitPreviewRender = AlchemySuitPreviewRender or BaseClass(BaseRender)
function AlchemySuitPreviewRender:LoadCallBack()
	if not self.attr_list then
		self.attr_list = {}

		for i = 1, 6 do
			local desc_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
			local cell = CommonAttrRender.New(desc_obj)
			cell:SetAttrNameNeedSpace(true)
            cell:SetOperateBtnClickCallBack(BindTool.Bind(self.OnOperateBtnClick, self))
			self.attr_list[i] = cell
		end
	end
end

function AlchemySuitPreviewRender:ReleaseCallBack()
    if self.attr_list and #self.attr_list > 0 then
		for _, render_cell in ipairs(self.attr_list) do
			render_cell:DeleteMe()
			render_cell = nil
		end

		self.attr_list = nil
	end
end

function AlchemySuitPreviewRender:OnOperateBtnClick(cell)
    if (not cell) or (not cell.data) or (not cell.data.skill_id) then
        return
    end

    local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(cell.data.skill_id)
    if skill_cfg then
        local show_data = {
            icon = skill_cfg.skill_icon,
            top_text = skill_cfg.skill_name,
            body_text = skill_cfg.skill_des,
            x = -360,
            y = 0,
            set_pos2 = true,
            hide_next = true,
            is_active_skill = false,
            skill_level = skill_cfg.skill_level or 0,
        }
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

function AlchemySuitPreviewRender:OnFlush()
	if not self.data then return end
    local now_suit_num = self.data.cur_num
	local cfg_data = self.data.cfg_data
	local title_str = string.format("%s(%d/%d)", cfg_data.suit_name, now_suit_num, cfg_data.num)
	self.node_list.title_txt.text.text = title_str
	local list = self:AssembleSelfAttr(cfg_data)
    
    local render_cell_max = 0
	for i, render_cell in ipairs(self.attr_list) do
		render_cell:SetVisible(list[i] ~= nil)

		if list[i] ~= nil then
            render_cell:SetOperateBtnStatus(false)
			render_cell:SetData(list[i])
            render_cell_max = i + 1
		end
	end

    if cfg_data.skill_id ~= nil and cfg_data.skill_id ~= 0 and self.attr_list and self.attr_list[render_cell_max] then
        local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(cfg_data.skill_id)
        if skill_cfg then
            local cell_render = self.attr_list[render_cell_max]
            cell_render:SetVisible(true)
            cell_render:SetOperateBtnStatus(true)
            cell_render:SetData({skill_id = cfg_data.skill_id})
            cell_render:ResetName(skill_cfg.skill_name)
            if skill_cfg.skill_level ~= 0 then
                cell_render:ResetAttrVlaue(Language.Common.Level1, skill_cfg.skill_level)
            else
                cell_render:ResetAttrVlaue("")
            end
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.attr_list.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

function AlchemySuitPreviewRender:AssembleSelfAttr(cfg_data)
	if not cfg_data then return {} end

	local attr_list = {}
	for i = 1, 6 do
		local key = cfg_data["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

			attr_list[key].attr_value = cfg_data["attr_value" .. i]
		end
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end
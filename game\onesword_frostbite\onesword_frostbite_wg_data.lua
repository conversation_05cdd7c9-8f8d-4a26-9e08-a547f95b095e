OneSwordFrostbiteWGData = OneSwordFrostbiteWGData or BaseClass()

function OneSwordFrostbiteWGData:__init()
	if OneSwordFrostbiteWGData.Instance then
		ErrorLog("[OneSwordFrostbiteWGData] Attempt to create singleton twice!")
		return
	end

	OneSwordFrostbiteWGData.Instance = self

    self.box_list = {}
    self.task_list = {}
    self.grade = 0
    self.round = 0
    self.dart_num = 0
    self.cur_model_index = -1
    self:InitConfig()
    -- 红点注册
	RemindManager.Instance:Register(RemindName.OneSwordFrostbiteView, BindTool.Bind(self.GetOneSwordFrostbiteRemind, self))
end

function OneSwordFrostbiteWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_sword_frostbite_auto")
    self.other_cfg = cfg.other[1]
    self.draw_num_cfg = ListToMap(cfg.draw_num, "grade", "round", "draw_num")
	self.reward_pool_cfg = ListToMap(cfg.reward_pool, "grade", "round", "seq")
	self.task_cfg = ListToMap(cfg.task, "grade", "seq")
	self.model_show_cfg = ListToMap(cfg.model_show, "grade", "round")
end

function OneSwordFrostbiteWGData:__delete()
    OneSwordFrostbiteWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.OneSwordFrostbiteView)
end

function OneSwordFrostbiteWGData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.round = protocol.round
    self.dart_num = protocol.dart_num
    self.box_list = protocol.box_list
    self.task_list = protocol.task_list
end

function OneSwordFrostbiteWGData:SetTaskInfo(protocol)
    self.task_list[protocol.task_seq] = protocol.task_item
    self.dart_num = protocol.dart_num
end

function OneSwordFrostbiteWGData:SetBoxInfo(protocol)
    self.box_list[protocol.box_seq] = protocol.reward_seq
    self.dart_num = protocol.dart_num
end

function OneSwordFrostbiteWGData:GetTaskDataBySeq(seq)
	return self.task_list[seq]
end

function OneSwordFrostbiteWGData:GetDartNum()
	return self.dart_num
end

function OneSwordFrostbiteWGData:GetBoxListData()
	return self.box_list
end

function OneSwordFrostbiteWGData:GetBoxDataBySeq(seq)
	return self.box_list[seq]
end

function OneSwordFrostbiteWGData:GetCurRoundDrawCount()
    local num = 0
    for k, v in pairs(self.box_list) do
        if v >= 0 then
            num = num + 1
        end
    end

    num = (num >= ONESWORD_FROSTBITE_TYPE.MAX_BOX_NUM) and ONESWORD_FROSTBITE_TYPE.MAX_BOX_NUM - 1 or num
    return num
end

function OneSwordFrostbiteWGData:GetCurRoundDrawCostNumCfg()
    local num = self:GetCurRoundDrawCount()
    local cfg = self:GetDrawNumCfgBySeq(num)
    return cfg
end

function OneSwordFrostbiteWGData:GetDrawNumCfgBySeq(num)
	return ((self.draw_num_cfg[self.grade] or {})[self.round] or {})[num]
end

function OneSwordFrostbiteWGData:GetOtherCfg()
	return self.other_cfg
end

function OneSwordFrostbiteWGData:GetTaskCfg()
	return self.task_cfg[self.grade]
end

function OneSwordFrostbiteWGData:GetRewardPoolCfgBySeq(seq)
	return ((self.reward_pool_cfg[self.grade] or {})[self.round] or {})[seq]
end

function OneSwordFrostbiteWGData:GetRewardPoolCfg()
	return self.reward_pool_cfg[self.grade]
end

function OneSwordFrostbiteWGData:GetRewardYulanData()
    local cfg = self:GetRewardPoolCfg()
    local reward_yulan_data_list = {}
    for k, v in pairs(cfg) do
        local reward_yulan_data = {}
        local reward_list = {}
        for k1, v1 in pairs(v) do
            table.insert(reward_list, v1.reward_item[0])
        end
        reward_yulan_data.round = k
        reward_yulan_data.reward_list = reward_list
        table.insert(reward_yulan_data_list, reward_yulan_data)
    end
	return reward_yulan_data_list
end

function OneSwordFrostbiteWGData:GetCurRound()
	return self.round
end

-- 迷影寻踪任务
function OneSwordFrostbiteWGData:GetTaskList()
	local cfg_list = self:GetTaskCfg()
	local task_data_list = {}
	for k, v in pairs(cfg_list) do
		local data = {}
		data.cfg = v
		data.seq = v.seq
		local protocol_data = self:GetTaskDataBySeq(v.seq)
		data.process = protocol_data.process
		local is_received = protocol_data.fetch_flag == 1
		data.is_received = is_received
		local can_receive = not is_received and protocol_data.process >= v.param1
		data.can_receive = can_receive
		data.sort = (can_receive and 0 or 1000) + (is_received and 100000 or 0) + v.seq
		table.insert(task_data_list, data)
	end
	table.sort(task_data_list, SortTools.KeyLowerSorter("sort"))
	return task_data_list
end

function OneSwordFrostbiteWGData:GetDrawBoxSeq()
    local no_draw_list = {}
    for k, v in pairs(self.box_list) do
        if v < 0 then
            table.insert(no_draw_list, k)
        end
    end

    if IsEmptyTable(no_draw_list) then
        return -1
    end

    local default_index = math.random(1, #no_draw_list)
	return no_draw_list[default_index]
end

function OneSwordFrostbiteWGData:GetModelCfg()
	local old_model_index = self.cur_model_index
	local cur_model_cfg = {}
	local grade = self.grade
	local cur_round = self.round

	local model_cfg = self.model_show_cfg[grade] or {}
	local list = SortTableKey(model_cfg)
	for k, v in pairs(list) do
		if cur_round <= v.round then
			self.cur_model_index = grade .. v.round
			cur_model_cfg = v
			break
		end
	end

	if IsEmptyTable(cur_model_cfg) then
		cur_model_cfg = list[#list]
		self.cur_model_index = grade .. list[#list].round
	end

	-- if old_model_index == self.cur_model_index then
	-- 	return {}
	-- end

	return cur_model_cfg
end

function OneSwordFrostbiteWGData:ResetModelIndex()
	self.cur_model_index = -1
end

--距离终极大奖还有多少轮
function OneSwordFrostbiteWGData:GetFinalRewardRound()
	local cur_round = self.round
    local round = -1
    local model_cfg = self.model_show_cfg[self.grade] or {}
    local list = SortTableKey(model_cfg)
	for k, v in pairs(list) do
		if cur_round <= v.round then
			round = v.round - cur_round
            break
		end
	end

    return round
end

--获取当前轮的大奖配置
function OneSwordFrostbiteWGData:GetBigRewardCfg()
	local cur_round = self.round
    local reward_pool_cfg = self.reward_pool_cfg[self.grade][cur_round] or {}
	for k, v in pairs(reward_pool_cfg) do
		if v.reward_type == 1 or v.reward_type == 2 then
			return v
		end
	end

    return nil
end

--是否全部抽完
function OneSwordFrostbiteWGData:GetIsAllDrawFinish()
    for i = 0, ONESWORD_FROSTBITE_TYPE.MAX_BOX_NUM - 1 do
        if self.box_list[i] < 0 then
            return false
        end
    end

	return true
end

function OneSwordFrostbiteWGData:GetOneSwordFrostbiteRemind()
    if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SWORD_FROSTBITE) then
        return 0
    end

    if self:GetIsCanDraw() or self:GetIsHaveTaskCanReceive() then
        return 1
    end

    return 0
end

--是否可以抽奖
function OneSwordFrostbiteWGData:GetIsCanDraw()
    local cfg = self:GetCurRoundDrawCostNumCfg()
    if cfg and self.dart_num >= cfg.consume_num and not self:GetIsAllDrawFinish() then
        return true
    end

    return false
end

--是否有任务可以领取
function OneSwordFrostbiteWGData:GetIsHaveTaskCanReceive()
    local cfg_list = self:GetTaskCfg()
    for k, v in pairs(cfg_list) do
		local task_data = self:GetTaskDataBySeq(v.seq)
        if task_data.process >= v.param1 and task_data.fetch_flag == 0 then
            return true
        end
	end

    return false
end
GloryCrystalExchangeShopView = GloryCrystalExchangeShopView or BaseClass(SafeBaseView)

function GloryCrystalExchangeShopView:__init()
	self.view_style = ViewStyle.Full
	self.default_index = TabIndex.glory_crystal_exchange_shop_1
	self.is_safe_area_adapter = true
	self:SetMaskBg()

	local assetbundle = "uis/view/glory_crystal_ui_prefab"

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, assetbundle, "glory_craystal_bg_panel")
	self:AddViewResource(0, assetbundle, "layout_glory_crystal_exchange_shop")
	self:AddViewResource(0, assetbundle, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function GloryCrystalExchangeShopView:LoadCallBack()
	self:InitTabbar()
	self:InitMoneyBar()

	self:ChangeViewStyle()

	self.node_list.title_view_name.text.text = Language.GloryCrystal.TitleName

	if not self.grid_list then
		local bundle = "uis/view/glory_crystal_ui_prefab"
		local asset = "glory_crystal_exchange_shop_item"
		self.grid_list = AsyncBaseGrid.New()
		self.grid_list:CreateCells({
			col = 3,
			change_cells_num = 1,
			list_view = self.node_list["fes_list"],
			assetBundle = bundle,
			assetName = asset,
			itemRender = GloryCrystalExchangeShopItemRender
		})
		self.grid_list:SetStartZeroIndex(false)
		self.grid_list:SetSelectCallBack(BindTool.Bind(self.ClickCallBack, self))
	end

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_display"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.select_item_cell then
		self.select_item_cell = ItemCell.New(self.node_list.item_cell_root)
	end

	if not self.gift_reward_list then
		self.gift_reward_list = AsyncListView.New(ItemCell, self.node_list.gift_reward_list)
		self.gift_reward_list:SetStartZeroIndex(false)
	end

	self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"level"})

	XUI.AddClickEventListener(self.node_list["fes_item_buy_btn"], BindTool.Bind(self.OnBuyBtnClick, self))
	XUI.AddClickEventListener(self.node_list["fes_item_info_btn"], BindTool.Bind(self.OnInfoBtnClick, self))
	XUI.AddClickEventListener(self.node_list["buy_add_btn"], BindTool.Bind(self.OnClickBuyAddBtn, self))
	XUI.AddClickEventListener(self.node_list["buy_sub_btn"], BindTool.Bind(self.OnClickBuySubBtn, self))
	XUI.AddClickEventListener(self.node_list["buy_count_btn"], BindTool.Bind(self.OnClickBuyCount, self))
	XUI.AddClickEventListener(self.node_list["buy_one_key_max_btn"], BindTool.Bind(self.OnClickOneKeyMaxBtn, self))

	self.select_seq = nil
	self.select_buy_count = 1
end

function GloryCrystalExchangeShopView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.grid_list then
		self.grid_list:DeleteMe()
		self.grid_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.select_item_cell then
		self.select_item_cell:DeleteMe()
		self.select_item_cell = nil
	end

	if self.gift_reward_list then
		self.gift_reward_list:DeleteMe()
		self.gift_reward_list = nil
	end

	self.select_seq = nil

	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
end

function GloryCrystalExchangeShopView:LoadIndexCallBack(index)

end

function GloryCrystalExchangeShopView:ShowIndexCallBack(index)
	local data_list = GloryCrystalWGData.Instance:GetItemDataList(index)
	if not IsEmptyTable(data_list) and data_list[1] then
		GloryCrystalWGData.Instance:SetSelectCellInfo({ seq = data_list[1].seq, cell_idx = 1 })
	end
end

function GloryCrystalExchangeShopView:OnFlush(param_t, index)
	for i, v in pairs(param_t) do
		if i == "all" then
			self:UpdateChangeTab(index)
		elseif i == "OnShopItemInfo" then
			self:UpdateChangeTab(self.show_index)
		elseif i == "OnShopBuy" then
			self:UpdateChangeTab(self.show_index)
		end
	end
end

function GloryCrystalExchangeShopView:ChangeViewStyle()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	if not cfg then
		return
	end

	local bundle, asset = ResPath.GetRawImagesJPG("a3_tcxy_ll_bg_" .. cfg.color_index)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function GloryCrystalExchangeShopView:InitTabbar()
	if not self.tabbar then
		self.tab_sub = {}
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("a3_txxy_jy_")
		self.tabbar:SetVerTabbarIconPath(ResPath.GetGloryCrystalExchangeShopImg)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		local assetbundle = "uis/view/glory_crystal_ui_prefab"
		self.tabbar:Init(Language.GloryCrystal.TabGrop, self.tab_sub, assetbundle, assetbundle)
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.GloryCrystalExchangeShopView, self.tabbar)
	end
end

function GloryCrystalExchangeShopView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local money_cfg = GloryCrystalWGData.Instance:GetMoneyCfg()

		local show_params = {
			show_gold = false,
			--自定义货币.
			show_custom_1 = money_cfg[1] ~= nil,
			custom_item_id_1 = money_cfg[1] and money_cfg[1] or 0,
			show_custom_2 = money_cfg[2] ~= nil,
			custom_item_id_2 =  money_cfg[2] and money_cfg[2] or 0,
			show_custom_3 = money_cfg[3] ~= nil,
			custom_item_id_3 =  money_cfg[3] and money_cfg[3] or 0,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function GloryCrystalExchangeShopView:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "level" then
		self:Flush()
	end
end

function GloryCrystalExchangeShopView:UpdateChangeTab(index)
	local data_list = GloryCrystalWGData.Instance:GetItemDataList(index)
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	if not IsEmptyTable(data_list) then
		self.grid_list:SetDataList(data_list)
		self.grid_list:JumpToIndexAndSelect(select_info.cell_idx)
	end

	self.node_list["fes_list"]:SetActive(0 < #data_list)
	self.node_list["right_panel"]:SetActive(0 < #data_list)
	self.node_list["model_display"]:SetActive(0 < #data_list)
	self.node_list["buy_empty_tips"]:SetActive(0 == #data_list)

	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	self.select_buy_count = 1

	if shop_cfg.model_show_type == 4 then
		self:FlushItemInfo()
	end
end

function GloryCrystalExchangeShopView:ClickCallBack(cell)
	local data = cell:GetData()
	if nil == data then
		return
	end

	GloryCrystalWGData.Instance:SetSelectCellInfo({ seq = data.seq, cell_idx = cell:GetIndex() })

	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	if self.select_seq == select_info.seq then
		return
	end

	self.select_buy_count = 1
	self.node_list["buy_count_label"].text.text = self.select_buy_count

	self.select_seq = select_info.seq
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	self.node_list["common_capability"]:SetActive(shop_cfg.model_show_type == OARenderType.RoleModel)
	self.node_list["panel_1"]:SetActive(shop_cfg.model_show_type ~= 4)
	self.node_list["panel_2"]:SetActive(shop_cfg.model_show_type == 4)

	if shop_cfg.model_show_type == OARenderType.RoleModel then
		self:FlushExchangeModel()
	elseif shop_cfg.model_show_type == 4 then
		self:FlushItemInfo()
	end
end

function GloryCrystalExchangeShopView:OnBuyBtnClick()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(shop_cfg.item.item_id)
	if not item_cfg then
		print_error("the item don't exist:::", shop_cfg.item.item_id)
		return
	end

	local is_can_buy = GloryCrystalWGData.Instance:CanBuyExchangeShopItem(select_info.seq)
	if is_can_buy then
		local expend_item_id = shop_cfg.stuff_id_1

		local buy_func = function()
			local item_count = ItemWGData.Instance:GetItemNumInBagById(expend_item_id)
			if item_count < shop_cfg.stuff_num_1 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GloryCrystal.StuffNotEnough)
				return
			end
		
			GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.CONVERT, select_info.seq, self.select_buy_count)
		end

		local tips_data = {}
		tips_data.title_view_name = Language.Common.OpenBuyItemTipsTitle
		tips_data.item_id = shop_cfg.item.item_id
		tips_data.expend_item_id = expend_item_id
		tips_data.expend_item_num = shop_cfg.stuff_num_1
		tips_data.has_checkbox = true
		tips_data.checkbox_str = self.view_name .. "_buy_tips"
		TipWGCtrl.Instance:OpenBuyItemTipsView(tips_data, buy_func)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GloryCrystal.MaxNumConvert)
	end
end

function GloryCrystalExchangeShopView:FlushExchangeModel()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(shop_cfg.item.item_id)

	--默认值.
	local img_pos_x, img_pos_y = -218, -37
	local scale = 1

	local display_data = {}
	display_data.item_id = shop_cfg.item.item_id
	display_data.model_rt_type = ModelRTSCaleType.L
	display_data.render_type = tonumber(shop_cfg.model_show_type) or 0
	if shop_cfg.model_show_type == OARenderType.Image then
		local res_str = shop_cfg.image_name
		if not res_str or "" == res_str then
			if item_cfg then
				res_str = item_cfg.resouce
			end
		end

		if nil ~= res_str and "" ~= res_str then
			display_data.bundle_name, display_data.asset_name = ResPath.GetRawImagesPNG(res_str)
		end

		if shop_cfg.model_pos and shop_cfg.model_pos ~= "" then
			local pos_list = string.split(shop_cfg.model_pos, "|")
			img_pos_x = tonumber(pos_list[1]) or img_pos_x
			img_pos_y = tonumber(pos_list[2]) or img_pos_y
		end
		
		scale = shop_cfg.model_scale
	end

	--只有图片才执行参数配置，否则复原默认值.
	RectTransform.SetAnchoredPositionXY(self.node_list.model_display.rect, img_pos_x, img_pos_y)
	Transform.SetLocalScaleXYZ(self.node_list.model_display.transform, scale, scale, scale)

	if shop_cfg.model_pos and shop_cfg.model_pos ~= "" then
		local pos_list = string.split(shop_cfg.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if shop_cfg.model_rot and shop_cfg.model_rot ~= "" then
		local rot_list = string.split(shop_cfg.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if shop_cfg.model_scale and shop_cfg.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = shop_cfg.model_scale
	end

	self.model_display:SetData(display_data)

	if item_cfg then
		local attr_desc = ""
		local capability = 0
		local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_cfg.id,
			item_cfg.sys_attr_cap_location)
		if need_get_sys then
			attr_desc, capability = ItemShowWGData.Instance:GetItemAttrDescAndCap({ item_id = shop_cfg.item.item_id },
				sys_type)
		end

		local have_cfg_cap = item_cfg.capability_show and item_cfg.capability_show > 0
		if (need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CALC and capability <= 0) or (not need_get_sys or not show_cap_type) then
			if have_cfg_cap then
				capability = item_cfg.capability_show
			end
		end

		self.node_list.cap_value.text.text = capability
		self.node_list.fes_item_info_text.text.text = item_cfg.name
	end
end

function GloryCrystalExchangeShopView:FlushItemInfo()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	local list = GloryCrystalWGData.Instance:ExplainComposeStr(shop_cfg.seq, true)
	local is_buy_empty, empyt_str = GloryCrystalWGData.Instance:ItemEmpty(shop_cfg.seq)

	if is_buy_empty then
		self.node_list["limit_buy_text"].text.text = empyt_str
	elseif IsEmptyTable(list) then
		self.node_list["limit_buy_text"].text.text = Language.FashionExchangeShop.NoLimitBuy
	else
		self.node_list["limit_buy_text"].text.text = list.str
	end

	self.node_list["flush_text"].text.text = Language.FashionExchangeShop.FlushShopItemText
	self.node_list["flush_text"]:SetActive(not IsEmptyTable(list) and list.t_type == 1)

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(shop_cfg.item.item_id)
	if not item_cfg then
		return
	end

	local item_id = shop_cfg.stuff_id_1
	local bundel, asset = ItemWGData.Instance:GetTipsItemIcon(item_id)
	local scale = 0.8
	local vector3 = Vector3(scale, scale, scale)
	if bundel and asset then
		self.node_list["item_price_icon"].image:LoadSprite(bundel, asset, function()
			-- self.node_list["item_price_icon"].image:SetNativeSize()
			self.node_list["item_price_icon"].transform.localScale = vector3
		end)
	end

	self.node_list.item_price_text.text.text = shop_cfg.stuff_num_1

	self.select_item_cell:SetData({item_id = item_cfg.id})
	self.node_list.item_name.text.text = item_cfg.name

	local desc_str = ItemWGData.Instance:ShowItemDescription(item_cfg)
	self.node_list.item_desc.text.text = desc_str

	self.node_list.gift_panel:SetActive(big_type == GameEnum.ITEM_BIGTYPE_GIF)
	if big_type == GameEnum.ITEM_BIGTYPE_GIF then
		local gift_reward_list =  ItemWGData.Instance:GetGiftDropList(item_cfg.id, 1)
		if gift_reward_list then
			self.gift_reward_list:SetDataList(gift_reward_list)
		end

		self.node_list.gift_title.text.text = item_cfg.biaoti2
	end

	self.node_list["buy_count_label"].text.text = self.select_buy_count
end

function GloryCrystalExchangeShopView:OnInfoBtnClick()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	TipWGCtrl.Instance:OpenItem({ item_id = shop_cfg.item.item_id })
end

function GloryCrystalExchangeShopView:OnClickBuyAddBtn()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	local max_buy_count = self:GetTrueBuyMaxCount()

	if self.select_buy_count < max_buy_count then
		self.select_buy_count = self.select_buy_count + 1
		self.node_list["buy_count_label"].text.text = self.select_buy_count
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
end

function GloryCrystalExchangeShopView:OnClickBuySubBtn()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	local min_buy_count = 1
	if self.select_buy_count > min_buy_count then
		self.select_buy_count = self.select_buy_count - 1
		self.node_list["buy_count_label"].text.text = self.select_buy_count
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
end

function GloryCrystalExchangeShopView:OnClickBuyCount()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	local max_buy_count = self:GetTrueBuyMaxCount()

	local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	pop_num_view:Open()
	pop_num_view:SetText(1)
	pop_num_view:SetMinValue(1)
	pop_num_view:SetMaxValue(max_buy_count)
	pop_num_view:SetOkCallBack(function (num)
		self.select_buy_count = num
		self.node_list["buy_count_label"].text.text = self.select_buy_count
	end)
end

function GloryCrystalExchangeShopView:OnClickOneKeyMaxBtn()
	self.select_buy_count = self:GetTrueBuyMaxCount()
	self.node_list["buy_count_label"].text.text = self.select_buy_count
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
end

--根据当前的货币类型与限购数量,得出能购买的最大上限.
function GloryCrystalExchangeShopView:GetTrueBuyMaxCount()
	local select_info = GloryCrystalWGData.Instance:GetSelectCellInfo()
	local shop_cfg = GloryCrystalWGData.Instance:GetShopCfgSeq(select_info.seq)
	if not shop_cfg then
		return
	end

	local max_buy_count = GloryCrystalWGData.Instance:GetMaxBuyNum(select_info.seq)

	local money_cfg = GloryCrystalWGData.Instance:GetMoneyCfg()
	local price_num = ItemWGData.Instance:GetItemNumInBagById(shop_cfg.stuff_id_1)

	local true_num = math.floor(price_num / shop_cfg.stuff_num_1)
	if max_buy_count < true_num then
		true_num = max_buy_count
	elseif true_num <= 0 then
		true_num = 1
	end
	return true_num
end

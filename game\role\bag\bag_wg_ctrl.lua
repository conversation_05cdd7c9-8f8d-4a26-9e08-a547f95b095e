require("game/role/bag/item_wg_data")
require("game/role/bag/bag_quick_use")
require("game/rolebag/item_multiple_use_view")
-- require("game/role/bag/openbaoxiang_itemtip")
require("game/role/equip_wg_data")
require("game/role/bag/xiaogui_wg_data")
require("game/role/bag/item_show_wg_data")
--------------------------------------------------------------
--背包相关
--------------------------------------------------------------

local ITEM_CHANGE_FLUSH_CD = 0.2

BagWGCtrl = BagWGCtrl or BaseClass(BaseWGCtrl)
function BagWGCtrl:__init()
	if BagWGCtrl.Instance then
		ErrorLog("[BagWGCtrl] Attemp to create a singleton twice !")
	end
	BagWGCtrl.Instance = self
	self.item_data = ItemWGData.New()
	self.equip_data = EquipWGData.New()
	self.xiaogui_data = XiaoGuiWGData.New()
	self.quick_use_view = BagQuickUseView.New()
	self.multiple_use_view = ItemMultipleUseView.New()
	self.item_show_data = ItemShowWGData.New()
	-- self.open_item_tip = OpenBaoXiangItemTip.New()
	self:RegisterAllProtocols()

	self.animation_item_queue = {}
	self.prve_play_animation_time = 0
	Runner.Instance:AddRunObj(self, 8)
	-- self.is_show = true
	self.item_change = false

	self.item_change_notify_remind_list = {}
	self.item_id_to_remind_name_map = {}
	self.any_item_id_to_remind_name_map = {}
	self.item_type_to_remind_name_map = {}
	self.notify_remind_time_stamp_list = {}
	self.delay_notify_remind_timer_list = {}

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	--self:BindGlobalEvent(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

function BagWGCtrl:__delete()
	BagWGCtrl.Instance = nil

	if EquipWGData.Instance and self.equip_data_change_fun then
		EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change_fun)
		self.equip_data_change_fun = nil
	end

	self:CancelBianqiangTimer()

	if RoleWGData.Instance and self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if ItemWGData.Instance and self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end

	self.item_data:DeleteMe()
	self.item_data = nil

	self.equip_data:DeleteMe()
	self.equip_data = nil

    self.xiaogui_data:DeleteMe()
	self.xiaogui_data = nil

	self.quick_use_view:DeleteMe()
	self.quick_use_view = nil

	self.multiple_use_view:DeleteMe()
	self.multiple_use_view = nil

	self.item_show_data:DeleteMe()
	self.item_show_data = nil

	self.animation_item_queue = {}

	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	self.item_change_notify_remind_list = {}
	self.item_id_to_remind_name_map = {}
	self.any_item_id_to_remind_name_map = {}
	self.item_type_to_remind_name_map = {}
	self.notify_remind_time_stamp_list = {}

	self.item_change = false
	Runner.Instance:RemoveRunObj(self)
end

function BagWGCtrl:RegisterAllProtocols()     --背包相关
	self:RegisterProtocol(SCKnapsackInfoAck, "OnKnapsackInfoAck")
	self:RegisterProtocol(SCKnapsackItemChange, "OnKnapsackItemChange")
	self:RegisterProtocol(SCKnapsackInfoParam, "OnKnapsackInfoParam")
	self:RegisterProtocol(SCKnapsackItemChangeParam, "OnKnapsackItemChangeParam")
	self:RegisterProtocol(SCUseItemSuc, "OnUseItemSuc")
	self:RegisterProtocol(SCKnapsackMaxGridNum, "OnKnapsackMaxGridNum")
	self:RegisterProtocol(SCStorageMaxGridNum, "OnStorageMaxGridNum")
	self:RegisterProtocol(SCLeckItem, "OnLeckItem")
	self:RegisterProtocol(SCRoleNorexItemUseTimes, "OnNorexItemUseTimes")
	self:RegisterProtocol(SCItemlistInfo, "onSCItemlistInfo")
	self:RegisterProtocol(SCRoleGetItemListInfo, "OnSCRoleGetItemListInfo")
	self:RegisterProtocol(SCNoticeXiaoGuiGuoQi, "OnSCNoticeXiaoGuiGuoQi")
	self:RegisterProtocol(SCEveryDayUseAddExpDanNumInfo, "OnSCEveryDayUseAddExpDanNumInfo")
	self:RegisterProtocol(CSUseItem)
	self:RegisterProtocol(CSDiscardItem)
	self:RegisterProtocol(CSKnapsackStoragePutInOrder)
	self:RegisterProtocol(CSMoveItem)
	self:RegisterProtocol(CSNPCShopSell)
	self:RegisterProtocol(CSKnapsackStorageExtendGridNum)
	self:RegisterProtocol(CSAutoSellEquip)
	self:RegisterProtocol(CSExchangeSilverTicket)
	self:RegisterProtocol(CSItemResolveReq)			--物品分解

	self:RegisterProtocol(SCPickFallingItem, "OnSCPickFallingItem") 		--从地上捡起物品 1493

	-- self:Bind(SettingEventType.GUAJI_SETTING_CHANGE, BindTool.Bind1(self.OnGuaJiSettingChange, self))
end

-- function BagWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
-- 	if fun_name == FunName.Marry and is_open == true then
-- 		local has_gift, has_flower, _, flower_item_id = ItemWGData.Instance:CheckHasScoietyGiftItem()
-- 		if has_gift or has_flower then
-- 			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 1, function()
-- 				if has_gift then
-- 					--ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
-- 				else
-- 					local t = flower_item_id ~= 0 and {item_id = flower_item_id} or nil
-- 					FlowerWGCtrl.Instance:Open(t)
-- 				end
-- 				MarryWGData.Instance:SetSocietyGiftOpened(true)
-- 				return SocietyWGData.Instance:GetIsLoverOnline() or not MarryWGData.Instance:GetIsSocietyGiftOpened()
-- 			end)
-- 		elseif MarryWGData.Instance:GetIsSocietyGiftOpened() then
-- 			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIANLV_ZENGSONG, 0)
-- 		end
-- 	end
-- end

function BagWGCtrl:MainuiOpenCreate()
	-- 装备改变监听
	if not self.equip_data_change_fun then
		self.equip_data_change_fun = BindTool.Bind(self.OnEquipDataChange, self)
		EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change_fun, false)
	end

	-- 玩家等级改变监听
	if not self.role_data_change then
		self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
		RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level", "prof"})
	end

	-- 背包物品改变监听
	if not self.item_data_event then
		self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	end

	self:AddBianqiangTimer()
end

function BagWGCtrl:OpenQuickUseView()
	self.quick_use_view:Open()
end

function BagWGCtrl:FlushQuickUseView()
	self.quick_use_view:Flush()
end

function BagWGCtrl:OnKnapsackInfoAck(protocol)
	ItemWGData.Instance.max_knapsack_valid_num = protocol.max_knapsack_valid_num
	ItemWGData.Instance.max_storage_valid_num = protocol.max_storage_valid_num

	for k, _ in pairs(self.item_change_notify_remind_list) do
		self:FireRemind(k)
	end

	ItemWGData.Instance:SetDataList(protocol.info_list)
	OfflineRestWGCtrl.Instance:FlushOfflineView()

	XiaoGuiWGData.Instance:AddAllKnapsackXiaoGuiInfo(protocol)
	NewYinJiJiChengWGData.Instance:SetBagKeYinFuList()
end

--获得物品改变
function BagWGCtrl:OnKnapsackItemChange(protocol) --协议1501
	-- print_error("-----获得物品改变 协议1501---", protocol)
	local old_item_id = ItemWGData.Instance:GetItemIdByIndex(protocol.index)
	ItemWGData.Instance:ChangeDataInGrid(protocol)
	XiaoGuiWGData.Instance:ChangeKnapsackXiaoGuiInfo(protocol)

	local t = ItemWGData.Instance:GetGridData(protocol.index)

	if t == nil and old_item_id > 0 then
		FunctionGuide.Instance:UpdateShowKeyUseView()
	end

	-- 背包、材料背包的物品改变才刷红点
	if protocol.index < COMMON_CONSTS.MAX_BAG_COUNT or protocol.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		local item_id = protocol.item_id > 0 and protocol.item_id or old_item_id
		self:ItemChangeFlushRemind(item_id)
	end
end

function BagWGCtrl:OnKnapsackInfoParam(protocol)--协议1502
	 -- print_error("--------协议1502-------------")
	local info_list = protocol.info_list
	for k,v in pairs(info_list) do
		ItemWGData.Instance:ChangeParamInGrid(v)
	end
	--xxxx
	ItemWGData.Instance:UpdateComposeEquipList(true)
	RoleBagWGCtrl.Instance:OnFluhEquipMeltView()
	NewYinJiJiChengWGData.Instance:SetBagKeYinFuList()
	EquipTargetWGCtrl.Instance:UpdateAllRemind()
end

function BagWGCtrl:OnKnapsackItemChangeParam(protocol)--协议1503
	-- print_error("-----协议1503-----",protocol)
	local old_item_id = ItemWGData.Instance:GetItemIdByIndex(protocol.index)
	ItemWGData.Instance:ChangeDataInGrid(protocol)
	GuildWGCtrl.Instance:OpenGuildDayRewardView(protocol) --领取帮派奖励面板
    XiaoGuiWGData.Instance:ChangeKnapsackXiaoGuiInfo(protocol)
	-- 背包、材料背包的物品改变才刷红点
	if protocol.index < COMMON_CONSTS.MAX_BAG_COUNT or protocol.index >= (COMMON_CONSTS.MAX_BAG_COUNT * 2) then
		local item_id = protocol.item_id > 0 and protocol.item_id or old_item_id
		self:ItemChangeFlushRemind(item_id)
	end
end

function BagWGCtrl:onSCItemlistInfo(protocol)
	-- print_error('onSCItemlistInfo----------',protocol)

	local gift_cfg = ItemWGData.Instance:GetShowBaoXiangTipCfg(protocol.item_id)
	if not gift_cfg then return end

	if gift_cfg.baoxiang_tip == 1 then
		ItemWGData.Instance:SetBaoXiangIdList(protocol)
		TipWGCtrl.Instance:ShowGetReward(protocol.item_id, protocol.item_id_list)
	elseif gift_cfg.jipin_baoxiang_tip == 1 then
		ItemWGData.Instance:SetBaoXiangIdList(protocol)
		TipWGCtrl.Instance:ShowGetReward(protocol.item_id, protocol.item_id_list,true)
	end
end

function BagWGCtrl:OnUseItemSuc(protocol)
	local itemcfg, big_type = ItemWGData.Instance:GetItemConfig(protocol.item_id)
	if not itemcfg then return end

	if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE or big_type == GameEnum.ITEM_BIGTYPE_GIF then	--消耗
		SysMsgWGCtrl.Instance:FloatingLabel(string.format(Language.Role.UseItemSucc, ToColorStr(itemcfg.name, ITEM_COLOR[itemcfg.color])))
	end

	if nil ~= itemcfg and nil ~= itemcfg.colddown_id and nil ~= itemcfg.client_colddown then
		self.item_data:SetColddownInfo(itemcfg.colddown_id, itemcfg.client_colddown + Status.NowTime)
	end
end

function BagWGCtrl:OnKnapsackMaxGridNum(protocol)
	ItemWGData.Instance.max_knapsack_valid_num = protocol.max_grid_num
	-- GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_EXTEND_BAG)
	ViewManager.Instance:FlushView(GuideModuleName.Bag, TabIndex.rolebag_bag_all, "itemdata_list_change")
	ViewManager.Instance:FlushView(GuideModuleName.Bag, TabIndex.rolebag_storge, "itemdata_list_change")
end

function BagWGCtrl:OnStorageMaxGridNum(protocol)
	ItemWGData.Instance.max_storage_valid_num = protocol.max_grid_num
	-- GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_EXTEND_STORAGE)
	ViewManager.Instance:FlushView(GuideModuleName.Bag, TabIndex.rolebag_bag_all, "itemdata_list_change")
	ViewManager.Instance:FlushView(GuideModuleName.Bag, TabIndex.rolebag_storge, "itemdata_list_change")
end

function BagWGCtrl:OnLeckItem(protocol)
	GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, protocol.item_id, protocol.item_count, protocol.seq)
end

function BagWGCtrl:OnNorexItemUseTimes(protocol)
	self.item_data:SetUseTimesList(protocol.use_times_list)
end

--使用物品
function BagWGCtrl:SendUseItem(index, num, equip_index, need_gold, select_list)
	local item_id = ItemWGData.Instance:GetItemIdByIndex(index)

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return
	end

	num = num or 1
	-- 自定义称号拦截
	local title_id = item_cfg.param1
	local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
	if diy_cfg then
		TitleWGCtrl.Instance:OpenDiyTitleNameView(title_id)
		print_error("---哪里过来的---")
		return
	end

	-- 虚拟现金使用限制
	if item_cfg.use_type == 146 then
		local virtual_type = item_cfg.param1
		-- 目前限制:充值券
		if virtual_type == VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME then
			local add_gold = num * item_cfg.param2
			local get_num = RechargeWGData.Instance:GetVirtualGoldGetNum(virtual_type)
			local max_get_num = RechargeWGData.Instance:GetVirtualGoldMaxGetNum(virtual_type)
			if max_get_num > 0 and (add_gold + get_num > max_get_num) then
				TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Recharge.VirtualGoldGetLimit[virtual_type], add_gold))
				return
			end
		end
	end

	-- 角色装备肉身处理
	local type = item_cfg.sub_type
	if type == GameEnum.EQUIP_TYPE_TOUKUI or  -- 头盔
	type == GameEnum.EQUIP_TYPE_YIFU or       -- 衣服
	type == GameEnum.EQUIP_TYPE_KUZI or       -- 裤子
	type == GameEnum.EQUIP_TYPE_XIANLIAN or   -- 仙链
	type == GameEnum.EQUIP_TYPE_XIEZI or      -- 鞋子
	type == GameEnum.EQUIP_TYPE_WUQI or       -- 武器
	type == GameEnum.EQUIP_TYPE_XIANZHUI or  -- 仙坠
	type == GameEnum.EQUIP_TYPE_XIANFU or     -- 匕首
	type == GameEnum.EQUIP_TYPE_XIANJIE or    -- 仙戒
	type == GameEnum.EQUIP_TYPE_XIANZHUO then   -- 仙镯 
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local use_limit_level = ItemWGData.Instance:GetEquipLimitLevel(item_cfg)
		if role_level < use_limit_level then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.UseLevelLimitUse, use_limit_level))
			return
		end

		local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)
		local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)

		-- 肉身未解锁
		if not EquipBodyWGData.Instance:IsEquipBodyUnLock(equip_body_seq) then
			local body_cfg = EquipBodyWGData.Instance:GetEquipBodyCfgBySeq(equip_body_seq)
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.RoleEquipBody.EquipBodyLockCanNotWear, body_cfg.equip_order))
			return
		end

		if item_cfg.order then
			local cal_equip_index = EquipBodyWGData.Instance:CalRoleEquipWearIndex(type, item_cfg.order)

			if cal_equip_index >= 0 then
				equip_index = cal_equip_index
			end
		end
	end

	local send_reqest = function ()
		local cmd = ProtocolPool.Instance:GetProtocol(CSUseItem)
		cmd.index = index or -1
		cmd.num = num
		cmd.equip_index = equip_index or 0
		if select_list then
			cmd.select_list = select_list
		end
	
		if need_gold and tonumber(need_gold) > 0 then
			if nil == self.alert_window then
				self.alert_window = Alert.New(nil, nil, nil, nil, false)
			end
			local label_str = string.format(Language.Role.SpendGoldUseItemTips, need_gold * cmd.num)
			self.alert_window:SetLableString(label_str)
			self.alert_window:SetOkFunc(function ()
				cmd:EncodeAndSend()
			end)
			self.alert_window:Open()
			return
		end
	
		--经验药水
		-- local item_id = ItemWGData.Instance:GetItemIdByIndex(index)
		-- local flag = self:CheckExpBuff(item_id)
		-- if flag then
		-- 	if nil == self.alert_window then
		-- 		self.alert_window = Alert.New(nil, nil, nil, nil, false)
		-- 	end
		-- 	local label_str = Language.Role.CheckUseExp
		-- 	self.alert_window:SetLableString(label_str)
		-- 	self.alert_window:SetOkFunc(function ()
		-- 		cmd:EncodeAndSend()
		-- 	end)
		-- 	self.alert_window:Open()
		-- 	return
		-- end
		cmd:EncodeAndSend()
	end

	-- 阵地战化怒秘药 特殊处理
	if item_cfg.use_type == 170 then
		local cur_tired = PositionalWarfareWGData.Instance:GetTired()
		if item_cfg.param1 > cur_tired then
			TipWGCtrl.Instance:OpenAlertTips(string.format(Language.PositionalWarfare.UseTiredReduceItemDesc, item_cfg.name, item_cfg.param1, cur_tired), function ()
				send_reqest()
			end)
		else
			send_reqest()
		end
	else
		send_reqest()
	end
end

-- 返回True 需要弹提示 ,返回false 不需要弹提示
function BagWGCtrl:CheckExpBuff(use_item_id)
	local fight_data = FightWGData.Instance
	local flag = fight_data:HasEffectByClientType(EXP_BUFF[use_item_id])
	if flag then
		return false
	end

	if EXP_BUFF[use_item_id] == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1 then --1.5倍
		return fight_data:HasEffectByClientType(EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2) or fight_data:HasEffectByClientType(EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3)
	end
	if EXP_BUFF[use_item_id] == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2 then --2倍
		return fight_data:HasEffectByClientType(EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1) or fight_data:HasEffectByClientType(EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3)
	end
	if EXP_BUFF[use_item_id] == EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3 then --3倍
		return fight_data:HasEffectByClientType(EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1) or fight_data:HasEffectByClientType(EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2)
	end

	return false
end

--丢弃物品
--discard_medthod 0出售 1回收
function BagWGCtrl:SendDiscardItem(index, discard_num, item_id_in_client, item_num_in_client, discard_medthod)
	local cmd = ProtocolPool.Instance:GetProtocol(CSDiscardItem)
	cmd.index = index or -1
	cmd.discard_num = discard_num or 1
	cmd.item_id_in_client = item_id_in_client or 0
	cmd.item_num_in_client = item_num_in_client or 0
	cmd.discard_medthod = discard_medthod or 0
	cmd:EncodeAndSend()
end

--合并整理物品
function BagWGCtrl:SendKnapsackStoragePutInOrder(is_storage, ignore_bind)
	local cmd = ProtocolPool.Instance:GetProtocol(CSKnapsackStoragePutInOrder)
	cmd.is_storage = is_storage			--整理的是哪个，1为仓库，0为背包
	cmd.ignore_bind = ignore_bind		--是否忽略绑定，1为是，0为否
	cmd:EncodeAndSend()
end

--移动物品
function BagWGCtrl:SendMoveItem(from_index, to_index)
	local cmd = ProtocolPool.Instance:GetProtocol(CSMoveItem)
	cmd.from_index = from_index
	cmd.to_index = to_index
	cmd:EncodeAndSend()
end

--出售物品
function BagWGCtrl:SendNPCShopSell(index_t)
	local cmd = ProtocolPool.Instance:GetProtocol(CSNPCShopSell)
	cmd.npc_id = 0
	cmd.function_index = 0
	cmd.open_type = 0
	cmd.count = #index_t
	cmd.index_t = index_t
	cmd:EncodeAndSend()
end

-- 背包、仓库扩展
function BagWGCtrl:SendKnapsackStorageExtendGridNum(bag_type, extend_num, can_use_gold)
	local cmd = ProtocolPool.Instance:GetProtocol(CSKnapsackStorageExtendGridNum)
	cmd.type = bag_type 				--1为仓库，0为背包
	cmd.extend_num = extend_num
	cmd.can_use_gold = can_use_gold or 1
	cmd:EncodeAndSend()
end

-- 兑换银票
function BagWGCtrl:CSExchangeSilverTicketReq(count_list)
	local cmd = ProtocolPool.Instance:GetProtocol(CSExchangeSilverTicket)
	cmd.count_list = count_list or {}
	cmd:EncodeAndSend()
end

-- 物品分解
function BagWGCtrl:SnedCSItemResolveReq(bag_index,item_id,resolve_count)
	local cmd = ProtocolPool.Instance:GetProtocol(CSItemResolveReq)
	cmd.bag_index = bag_index
	cmd.item_id = item_id
	cmd.resolve_count = resolve_count
	cmd:EncodeAndSend()
end

function BagWGCtrl:PlayAnimationOnGetItem(item_id)
	table.insert(self.animation_item_queue, item_id)
	if #self.animation_item_queue > 12 then
		table.remove(self.animation_item_queue, 1)
	end
end

function BagWGCtrl:Update(now_time, elapse_time)
	for k,v in pairs(self.delay_notify_remind_timer_list) do
		if now_time >= v then
			self.delay_notify_remind_timer_list[k] = nil
			self:FireRemind(k)
		end
	end

	--[[
	if 0 == #self.animation_item_queue then return end

	if now_time - self.prve_play_animation_time < 0.06 then
		return
	end

	self.prve_play_animation_time = now_time
	local item_id = table.remove(self.animation_item_queue, 1)

	local role = Scene.Instance:GetMainRole()
	if role.vo.level >= COMMON_CONSTS.XIN_SHOU_LEVEL and Scene.Instance:GetSceneType() ~= SceneType.TEAM_EQUIP_FB then
		self:StartFlyItem(item_id)
	end
	]]
end

function BagWGCtrl:StartFlyItem(item_id)

end

function BagWGCtrl:ItemFlyEnd(fly_icon)
	if fly_icon then
		fly_icon:removeFromParent()
	end
end

function BagWGCtrl:OnGuaJiSettingChange(guaji_setting_type, flag)

end

function BagWGCtrl:AddItemTips(change_item_id, old_num, new_num, put_reason, index, item_data)
	if not item_data then return end
	
	if GodGetRewardWGCtrl.Instance:ViewIsOpen() then
		return
	end

	if (index >= COMMON_CONSTS.MAX_BAG_COUNT and index < COMMON_CONSTS.MAX_BAG_COUNT * 2) or index >= COMMON_CONSTS.MAX_BAG_COUNT * 3 then
		return
	end

	-- 特殊随机礼包不弹提示
	local is_special_gift = ItemWGData.Instance:IsSpecialRandGift(change_item_id)
	if is_special_gift then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
	if item_cfg == nil then
		print_error("BagWGCtrl.AddItemTips has a nil item_cfg", change_item_id)
		return
	end

	local str = string.format(Language.Bag.GetItemTxt, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), new_num - old_num)
	SysMsgWGCtrl.Instance:ErrorRemind(str)

	if put_reason == PUT_REASON_TYPE.PUT_REASON_TASK_REWARD then -- and Scene.Instance:GetSceneType() ~= SceneType.Common --策划需求
		item_data.change_num = new_num - old_num
		TipWGCtrl.Instance:ShowGetItem(item_data)
	end
end

function BagWGCtrl:RoleBagAutoSell()
	local cmd = ProtocolPool.Instance:GetProtocol(CSAutoSellEquip)
	cmd:EncodeAndSend()
end

--OnSCPickFallingItem
--从地上捡起物品 1493
local pick_falling_count = 1
function BagWGCtrl:OnSCPickFallingItem(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	--永夜之巅不走通用捡起物品。走另外协议8977
	if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
		return
	end

	-- 仙界装备虚拟物品 搞特殊
	if protocol.item_id == COMMON_CONSTS.XiamVirtualItemId then
		return
	end

	local data = ItemWGData.Instance:GetItemConfig(protocol.item_id)
	if data and next(data) then
		local str = string.format(Language.Bag.GetItemTxt, ToColorStr(data.name, GET_TIP_ITEM_COLOR[data.color]), protocol.num)
		-- SysMsgWGCtrl.Instance:ErrorRemind(str)
		local item_data = {}
		item_data.item_id = protocol.item_id
		item_data.change_num = protocol.num
		item_data.star_level = protocol.star_level
		pick_falling_count = pick_falling_count + 1
		if pick_falling_count > 50 then
			pick_falling_count = 1
		end
		TipWGCtrl.Instance:ShowGetItem(item_data)
		-- if put_reason == PUT_REASON_TYPE.PUT_REASON_PICK then -- and Scene.Instance:GetSceneType() ~= SceneType.Common --策划需求
		-- 	TipWGCtrl.Instance:ShowGetItem(item_data)
		-- end
		local bundle, asset = ResPath.GetFallItemEffect(data.color)
		local async_loader = AllocAsyncLoader(self, "PickFalling" .. pick_falling_count)
		local mainrole = Scene.Instance:GetMainRole()
		async_loader:SetIsUseObjPool(true)
		async_loader:SetParent(mainrole.draw_obj.root_transform)
		async_loader:Load(bundle, asset, function(obj)
			if IsNil(obj) then
				return
			end
			obj:SetActive(true)
			if mainrole and mainrole.draw_obj then
				local x, y = GameMapHelper.LogicToWorld(protocol.pos_x, protocol.pos_y)
				local role_pos = mainrole.draw_obj.root_transform.position
				obj.transform.position = Vector3(x, role_pos.y, y)
				obj.transform:DOLocalMove(Vector3(0, 2, 0), 1)
			end
			GlobalTimerQuest:AddDelayTimer(function()
				async_loader:Destroy()
			end, 1)
		end)
	end
	-- 屏蔽珍稀掉落展示
	-- ItemWGData.Instance:SaveBestDropEquip(protocol)
	GlobalEventSystem:Fire(OtherEventType.PICK_ITEM_EVENT, protocol.item_id)
	RareItemDropWGCtrl.Instance:CheckPickItem(protocol.item_id, protocol.num, protocol.star_level, protocol.item_index)
end

function BagWGCtrl:OpenMultipleUseView(data, other_data)
	self.multiple_use_view:SetData(data, other_data)
end

--恭喜获得界面，显示自己获得的奖励
function BagWGCtrl:OnSCRoleGetItemListInfo(protocol)
	local is_big, no_need_sort
	local time = 0
	if protocol.type == ROLE_GET_ITEMLIST_TYPE.RA_FIERCE_FIGHTING then
		is_big = false
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.OA_TIME_LIMIT_DISCOUNT then--限时特惠物品列表服务端已经排序过了
		no_need_sort = true
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.ROLE_GET_ITEMLIST_TYPE_CHAOTIC_GIFT_RMB_BUY then
		no_need_sort = true
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.ROLE_GET_ITEMLIST_TYPE_BEAST then
		ControlBeastsWGCtrl.Instance:RewardContractListShow(protocol)
		return
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.OGA_DRAW_REWARD then
		local is_skip = ServerActivityWGData.Instance:GetIsSkipOGALotteryAnim()
		time = is_skip and 0 or 3.5
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.CROSS_BOSS_STRIKE_RANK_REWARD then
		-- Boss入侵排名奖励
		BOSSInvasionWGCtrl.Instance:OpenResultView(protocol)
		return
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.CROSS_BOSS_STRIKE_GATHER_REWARD then
		--Boss入侵采集奖励	
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.ROLE_GET_ITEMLIST_TYPE_TEAM_COMMON_TOWER_FB_LEVEL_REWARAD then
		FuBenTeamCommonTowerWGCtrl.Instance:ShowRuneTowerDrawGetReward(protocol.item_list)
		return
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.ROLE_GET_ITEMLIST_TYPE_TREASURE_PURSUIT_LUCKY_BADGE_REWARAD then
		local other_info = {show_reward_desc = Language.WorldTreasure.LuckyBadgeReward}
		AddDelayCall(self, function()
			TipWGCtrl.Instance:ShowGetCommonReward(protocol.item_list, nil, other_info)
		end, 1.4)
		return
	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.ROLE_GET_ITEMLIST_TYPE_GUILD_SIGN then
		local data = {reward_list = protocol.item_list}
		GuildWGCtrl.Instance:OpenGuildSignRewardView(data)
		return

	elseif protocol.type == ROLE_GET_ITEMLIST_TYPE.ROLE_GET_ITEMLIST_TYPE_ONESWORD_FROSTBITE_BIG_REWARAD then
		if protocol.param == 1 then
			OneSwordFrostbiteWGCtrl.Instance:PlayVideo()
			time = 1.5
		end
	end

	AddDelayCall(self, function()
		TipWGCtrl.Instance:ShowGetReward(nil, protocol.item_list, is_big, nil, nil, no_need_sort)
	end, time)
end

function BagWGCtrl:OnSCNoticeXiaoGuiGuoQi(protocol)
	--这里要设置一下，不然会被整理背包什么弹出来
	XiaoGuiWGData.Instance:SetIsLoginFirstOpenView(true)
	MainuiWGCtrl.Instance:OpenGuradInvalidTimeView()
end

function BagWGCtrl:OnSCEveryDayUseAddExpDanNumInfo(protocol)
	self.item_data:SetLimitNumByUseTimeList(protocol.use_dan_list)
end


----------------------------------------装备提升变强Begin----------------------------------------------
local cur_bag_index = 0
local last_check_time_stamp = 0
local change_time_stamp = 0
-- 分帧检查背包是否有更好的装备，有就提醒
function BagWGCtrl:CheckBianqiangBetterEquip()
	if cur_bag_index == 0 then
		last_check_time_stamp = TimeWGCtrl.Instance:GetServerTime()
		-- 新一轮检查先去掉变强提示
		self:RemoveBianqiang()
	end

	-- 一次最多检查背包的五个装备
	local budget = 5
	-- 一次最多检查30个背包格子
	for i = 1, 30 do
		if budget <= 0 then
			break
		end

		local bag_item_data = ItemWGData.Instance:GetGridData(cur_bag_index)
		cur_bag_index = cur_bag_index + 1
		RemindManager.Instance:Fire(RemindName.XiaoGui_BecomeStronger)

		if bag_item_data then
			local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(bag_item_data.item_id)
			if item_cfg and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and not ItemWGData.GetIsXiaogGui(bag_item_data.item_id) then
				budget = budget - 1
				if EquipWGData.Instance:GetIsBetterEquip(bag_item_data) then
					-- 提示变强
					self:AddBianqiang(bag_item_data)
					self:CancelBianqiangTimer()
					break
				end
			end
		end

		if cur_bag_index >= COMMON_CONSTS.MAX_BAG_COUNT then
			cur_bag_index = 0
			self:CancelBianqiangTimer()
			self:CheckNeedAddTimer()
			break
		end
	end
end

-- 取消计时器
function BagWGCtrl:CancelBianqiangTimer()
	if self.equip_check_timer then
        GlobalTimerQuest:CancelQuest(self.equip_check_timer)
        self.equip_check_timer = nil
    end
end

-- 启动计时器
function BagWGCtrl:AddBianqiangTimer()
	self:CancelBianqiangTimer()
    self.equip_check_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.CheckBianqiangBetterEquip, self), 0.1)
end

-- 检查是否需要开启一轮检查
function BagWGCtrl:CheckNeedAddTimer()
	if self.equip_check_timer == nil and change_time_stamp > last_check_time_stamp then
		self:RemoveBianqiang()
		self:AddBianqiangTimer()
	end
end

-- 装备更变
function BagWGCtrl:OnEquipDataChange(item_id, index, reason)
	change_time_stamp = TimeWGCtrl.Instance:GetServerTime()
	self:CheckNeedAddTimer()
end

-- 等级更变
function BagWGCtrl:RoleLevelChange(attr_name, value)
	if attr_name == "level" or attr_name == "prof" then
		change_time_stamp = TimeWGCtrl.Instance:GetServerTime()
		self:CheckNeedAddTimer()
	end
end

-- 背包物品更变
function BagWGCtrl:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local config, item_type = ItemWGData.Instance:GetItemConfig(change_item_id)
	if config and item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		change_time_stamp = TimeWGCtrl.Instance:GetServerTime()
		self:CheckNeedAddTimer()
	end
end

-- 提醒变强
function BagWGCtrl:AddBianqiang(bag_item_info)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_BETTER, 1, function ()
		ViewManager.Instance:Open(GuideModuleName.Bag)
		TipWGCtrl.Instance:OpenItem(bag_item_info, ItemTip.FROM_BAG)
		return true
	end)
end

-- 取消提醒变强
function BagWGCtrl:RemoveBianqiang()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_BETTER, 0)
end

----------------------------------------装备提升变强End----------------------------------------------

------------------------------------------物品变化时的红点通知---------------------------------------------------

-- 参数item_id_list为关注的物品id列表，参数item_type_list为关注的物品sub_type列表
-- 只有当背包中关注的物品变化时，才会通知红点刷新
-- item_id_list和item_type_list全部传nil时，表示任何item变化都需要通知红点刷新
function BagWGCtrl:RegisterRemindByItemChange(remind_name, item_id_list, item_type_list)
	if nil == remind_name or type(remind_name) ~= "string" then
		print_error("remind_name error")
		return
	end

	self.item_change_notify_remind_list[remind_name] = true

	if item_id_list and nil == item_type_list and #item_id_list == 0 then
		print_error("[BagWGCtrl]item_id_list长度为0，性能会很差，请检查", remind_name)
	end

	if (nil == item_id_list or #item_id_list == 0) and (nil == item_type_list or #item_type_list == 0) then
		self.any_item_id_to_remind_name_map[remind_name] = true
		return
	end

	if item_id_list then
		for _, item_id in ipairs(item_id_list) do
			if item_id ~= 0 then
				self.item_id_to_remind_name_map[item_id] = self.item_id_to_remind_name_map[item_id] or {}
				self.item_id_to_remind_name_map[item_id][remind_name] = true
			end
		end
	end

	if item_type_list then
		for _, item_type in ipairs(item_type_list) do
			self.item_type_to_remind_name_map[item_type] = self.item_type_to_remind_name_map[item_type] or {}
			self.item_type_to_remind_name_map[item_type][remind_name] = true
		end
	end
end

function BagWGCtrl:ItemChangeFlushRemind(item_id)
	local need_remind_list = {}
	for remind_name, _ in pairs(self.any_item_id_to_remind_name_map) do
		need_remind_list[remind_name] = true
	end

	local list = self.item_id_to_remind_name_map[item_id]
	if list then
		for remind_name, _ in pairs(list) do
			need_remind_list[remind_name] = true
		end
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if nil ~= item_cfg then
		local list = self.item_type_to_remind_name_map[item_cfg.sub_type]
		if list then
			for remind_name, _ in pairs(list) do
				need_remind_list[remind_name] = true
			end
		end
	end

	for remind_name, _ in pairs(need_remind_list) do
		self:FireRemind(remind_name)
	end
end

function BagWGCtrl:FireRemind(remind_name)
	local time_stamp = self.notify_remind_time_stamp_list[remind_name] or 0
	local now_time = Status.NowTime

	if now_time - time_stamp >= ITEM_CHANGE_FLUSH_CD then
		self.notify_remind_time_stamp_list[remind_name] = now_time
		RemindManager.Instance:Fire(remind_name)
	else
		if nil == self.delay_notify_remind_timer_list[remind_name] then
			self.delay_notify_remind_timer_list[remind_name] = time_stamp + ITEM_CHANGE_FLUSH_CD + 0.01
		end
	end
end

--猎魔赏金格子
LieMoRankRender = LieMoRankRender or BaseClass(BaseRender)

function LieMoRankRender:LoadCallBack()
    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    self.is_load_complete = true
    self.item_list:SetIsDelayFlush(false)
	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_list)
	if self.parent_scroll_rect then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end

function LieMoRankRender:__delete()
    if self.nested_scroll_rect then
		self.nested_scroll_rect:DeleteMe()
		self.nested_scroll_rect = nil
	end
    self.is_load_complete = nil
    self.parent_scroll_rect = nil

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function LieMoRankRender:OnFlush()
    if not self.data then
        return
    end

    --奖励格子列表
    self.item_list:SetDataList(self.data.item_list)

    local bg_indx = self.index > 3 and 4 or self.index
    local bundle, asset = ResPath.GetNoPackPNG("a3_hfhd_lmzq_zt"..bg_indx)
    self.node_list["bg"].image:LoadSprite(bundle, asset, function()
        -- self.node_list["bg"].image:SetNativeSize()
    end)

    if self.index <= 3 then
        --local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset, function()
            self.node_list["rank_img"].image:SetNativeSize() end)
    else
        self.node_list["rank_text"].text.text = self.index
    end
    self.node_list["rank_img"]:SetActive(self.index <= 3)
    self.node_list["rank_text"]:SetActive(self.index > 3)

    --虚位以待逻辑
    if self.data.is_empty_value then
        self.node_list.name.text.text = Language.LieMoDaRen.XuWeiYiDai
        self.node_list.guild_name.text.text = Language.LieMoDaRen.XuWeiYiDai
        self.node_list.score.text.text = string.format(Language.LieMoDaRen.NeedValue, self.data.reach_value)
        return
    end

    self.node_list.name.text.text = self.data.user_name
    self.node_list.guild_name.text.text = self.data.flexible_name
    self.node_list.score.text.text = self.data.rank_value

end

function LieMoRankRender:SetParentScrollRect(scroll_rect)
	self.parent_scroll_rect = scroll_rect

	if self.is_load_complete then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end

-- LieMoRankItem = LieMoRankItem or BaseClass(BaseRender)
-- function LieMoRankItem:LoadCallBack()
--     self.item = ItemCell.New(self.node_list.item_pos)
-- end
-- function LieMoRankItem:__delete()
--     if self.item then
--         self.item:DeleteMe()
--         self.item = nil
--     end
-- end
-- function LieMoRankItem:OnFlush()
--     if not self.data then
--         return
--     end
--     self.item:SetData(self.data)
-- end
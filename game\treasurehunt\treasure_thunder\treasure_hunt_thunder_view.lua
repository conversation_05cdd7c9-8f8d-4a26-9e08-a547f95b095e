function TreasureHuntView:InitThunderView()
    if not self.thunder_show_reward_item_list then
        self.thunder_show_reward_item_list = {}
        for i = 1, 5 do
            local item = ItemCell.New(self.node_list["thunder_show_reward_cell_" .. i])
            item:SetIsUseRoundQualityBg(true)
            item:SetCellBgEnabled(false)
            self.thunder_show_reward_item_list[i] = item
        end
    end

    if not self.thunder_draw_count_reward_list then
        self.thunder_draw_count_reward_list = AsyncListView.New(TreasureHuntThunderCountRewardItem, self.node_list.thunder_draw_count_reward_list) 
        self.thunder_draw_count_reward_list:SetStartZeroIndex(true)
    end

    self.node_list.thunder_record_btn.button:AddClickListener(BindTool.Bind(self.OpenThunderRecord, self))
    self.node_list.thunder_gailv.button:AddClickListener(BindTool.Bind(self.OnClickThunderGaiLvBtn, self))

    for i = 1, 2 do
        self.node_list["thunder_key_bg_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickThunderKey, self))
        self.node_list["thunder_draw_btn_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickThunderDraw, self, i - 1))
    end
    self.node_list.btn_thunder_bag.button:AddClickListener(BindTool.Bind(self.OnClickThunderStorage, self))           --仓库
    self.node_list.btn_thunder_convert.button:AddClickListener(BindTool.Bind(self.OnClickThunderConvert, self))           --仓库
end

function TreasureHuntView:ReleaseThunder()
    if self.thunder_show_reward_item_list then
        for k, v in ipairs(self.thunder_show_reward_item_list) do
            if v then
                v:DeleteMe()
            end
        end

        self.thunder_show_reward_item_list = nil
    end

    if self.thunder_draw_count_reward_list then
        self.thunder_draw_count_reward_list:DeleteMe()
        self.thunder_draw_count_reward_list = nil
    end
end

function TreasureHuntView:FlushThunderView()
    self:FlushThunderShowRewardInfo()
    self:FlushThunderDrawBtnInfo()
    self:FlushThunderOtherInfo()
    self:FlushThunderCountRewardInfo()
end

function TreasureHuntView:FlushThunderShowRewardInfo()
    local show_reward_info = TreasureHuntThunderWGData.Instance:GetShowRewardInfo()
    if IsEmptyTable(show_reward_info) then
        return
    end

    local show_item_id_list = {}
    
    local split_list = string.split(show_reward_info.item_id_list, "|")
    for i = 1, 5 do
        if split_list[i] and self.thunder_show_reward_item_list[i] then
            self.thunder_show_reward_item_list[i]:SetData({item_id = tonumber(split_list[i])})
        end
    end

    self.node_list.thunder_spe_name.text.text = show_reward_info.name
end

function TreasureHuntView:FlushThunderDrawBtnInfo()
    local draw_num_cfg = TreasureHuntThunderWGData.Instance:GetAllDrawNumCfg()
    local other_cfg = TreasureHuntThunderWGData.Instance:GetOtherInfo()
    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.draw_item_id)
    for i = 1, 2 do
        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
        self.node_list["thunder_key_icon_"..i].image:LoadSprite(bundle, asset, function()
            self.node_list["thunder_key_icon_"..i].image:SetNativeSize()
        end)

        local draw_num = draw_num_cfg[i - 1] and draw_num_cfg[i - 1].draw_num or 0
        local consume_num = draw_num_cfg[i - 1] and draw_num_cfg[i - 1].consume_num or 0

        local has_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.draw_item_id)
        local color = has_num >= consume_num and COLOR3B.DEFAULT_NUM or "#ff9393"
        self.node_list["thunder_consume_" .. i].text.text = ToColorStr(has_num .. "/" .. consume_num, color)
        self.node_list["thunder_draw_text_" .. i].text.text = string.format(Language.TreasureHunt.ThunderTreasure,  draw_num)
        self.node_list["thunder_draw_remind_" .. i]:SetActive(has_num >= consume_num)
    end
end

function TreasureHuntView:FlushThunderOtherInfo()
    local level = TreasureHuntThunderWGData.Instance:GetCurLevel()
    local draw_times = TreasureHuntThunderWGData.Instance:GetCurDrawTimes()

    self.node_list.thunder_pool_level.text.text = string.format(Language.TreasureHunt.ThunderLevel, level)
    local cur_reward_pool_cfg = TreasureHuntThunderWGData.Instance:GetRewardPoolcfgByLevel(level)
    local next_reward_pool_cfg = TreasureHuntThunderWGData.Instance:GetRewardPoolcfgByLevel(level + 1)
    if next_reward_pool_cfg then
        self.node_list.thunder_exp_num.text.text = string.format("%s/%s", draw_times, cur_reward_pool_cfg.next_level_draw_num)
        self.node_list.thunder_exp_slider.slider.value = draw_times/cur_reward_pool_cfg.next_level_draw_num
    else
        self.node_list.thunder_exp_num.text.text = "max"
        self.node_list.thunder_exp_slider.slider.value = 1
    end

    local storage_grid_list = TreasureHuntThunderWGData.Instance:GetStorageItemList()
    self.node_list.thunder_bag_remind:SetActive(not IsEmptyTable(storage_grid_list)) 
end

function TreasureHuntView:FlushThunderCountRewardInfo()
    local level = TreasureHuntThunderWGData.Instance:GetCurLevel()
    local draw_times = TreasureHuntThunderWGData.Instance:GetCurDrawTimes()
    local count_reward_list = TreasureHuntThunderWGData.Instance:GetCountRewardCfgByLevel(level)
    if IsEmptyTable(count_reward_list) then
        return
    end

    self.thunder_draw_count_reward_list:SetDataList(count_reward_list)
    local jump_index = 0
    for k, v in ipairs(count_reward_list) do
        local is_get = TreasureHuntThunderWGData.Instance:GetCountRewardFlagBySeq(v.seq) == 1
        if not is_get then
            jump_index = v.seq
            break
        end
    end

    self.thunder_draw_count_reward_list:JumpToIndex(jump_index, 4)
    self.node_list.thunder_draw_times.text.text = draw_times
end

function TreasureHuntView:OpenThunderRecord()
    TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.RECORD_INFO)
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    TreasureHuntWGCtrl.Instance:OpenTreasureRecordTip(treasure_type)
end

function TreasureHuntView:OnClickThunderGaiLvBtn()
    --TreasureHuntWGCtrl.Instance:OpenProbabilityView()
    local data_list = TreasureHuntThunderWGData.Instance:GetDrawProbabilityInfo()
    TipWGCtrl.Instance:OpenTipsRewardProGroupView(data_list)
end

function TreasureHuntView:OnClickThunderKey()
    local other_cfg = TreasureHuntThunderWGData.Instance:GetOtherInfo()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other_cfg.draw_item_id})
end

function TreasureHuntView:OnClickThunderDraw(mode_type)
	TreasureHuntThunderWGCtrl.Instance:ClickUse(mode_type)
end

function TreasureHuntView:OnClickThunderStorage()
    TreasureHuntThunderWGCtrl.Instance:OpenStroageView()
end

function TreasureHuntView:OnClickThunderConvert()
    TreasureHuntThunderWGCtrl.Instance:OpenStoreView()
end

---------------------TreasureHuntThunderCountRewardItem------------
TreasureHuntThunderCountRewardItem = TreasureHuntThunderCountRewardItem or BaseClass(BaseRender)
function TreasureHuntThunderCountRewardItem:LoadCallBack()
    if not self.item_reward then
        self.item_reward = ItemCell.New(self.node_list.cell_pos)
        self.item_reward:SetIsShowTips(false)
    end

    self.node_list.button.button:AddClickListener(BindTool.Bind1(self.OnClickItem, self))
end

function TreasureHuntThunderCountRewardItem:ReleaseCallBack()
    if self.item_reward then
        self.item_reward:DeleteMe()
        self.item_reward = nil
    end
end

function TreasureHuntThunderCountRewardItem:OnFlush()
	if not self.data then
		return
    end
   
    self.item_reward:SetData(self.data.reward_item[0])
    self.item_reward:SetBindIconVisible(false)

    local is_get = TreasureHuntThunderWGData.Instance:GetCountRewardFlagBySeq(self.data.seq) == 1
    self.item_reward:SetDefaultEff(not is_get)

    local draw_times = TreasureHuntThunderWGData.Instance:GetCurDrawTimes()
    self.node_list.times.text.text = string.format(Language.TreasureHunt.CiShu, self.data.draw_times)
    self.node_list.had_get:SetActive(is_get)
    self.node_list.red:SetActive(not is_get and draw_times >= self.data.draw_times)

    self.node_list.jindu_img:SetActive(self.data.seq ~= 0)
    self.node_list.jindu_hl_img:SetActive(self.data.seq ~= 0 and draw_times >= self.data.draw_times)
end

function TreasureHuntThunderCountRewardItem:OnClickItem()
    if not self.data then
		return
    end

    local is_get = TreasureHuntThunderWGData.Instance:GetCountRewardFlagBySeq(self.data.seq) == 1
    local draw_times = TreasureHuntThunderWGData.Instance:GetCurDrawTimes()
    if not is_get then
        if draw_times >= self.data.draw_times then
            TreasureHuntThunderWGCtrl.Instance:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.COUNT_REWARD, self.data.seq)
        else
            TipWGCtrl.Instance:OpenItem({item_id = tonumber(self.data.reward_item[0].item_id)})
        end
    else
        TipWGCtrl.Instance:OpenItem({item_id = tonumber(self.data.reward_item[0].item_id)})
    end
end
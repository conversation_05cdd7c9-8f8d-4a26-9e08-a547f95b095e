NewGloryCrystalView = NewGloryCrystalView or BaseClass(SafeBaseView)

local PROGRESS_REWARD_MAX = 7				--次数奖励最大数量.
local BIG_REWARD_CAN_DRAW_NUM = 4			--大奖可滑动列表数量.

function NewGloryCrystalView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self.is_first_open = true

	self.show_fashion_seq = nil

	-- 和界面样式的color_index对应
	self.tip_color = {
		[1] = RewardShowViewColor.Green,
		[2] = RewardShowViewColor.Green
	}

	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/glory_crystal_ui_prefab", "layout_new_glory_crystal")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:AddViewResource(0, "uis/view/glory_crystal_ui_prefab", "glory_crystal_fashion_dye_tips")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.GLORY_CRYSTAL})
end

function NewGloryCrystalView:__delete()

end

function NewGloryCrystalView:ReleaseCallBack()
	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.item_list then
		for i = 1, #self.item_list do
			self.item_list[i]:DeleteMe()
			self.item_list[i] = nil
		end
		self.item_list = nil
	end

	if self.big_reward_list then
		for k, v in pairs(self.big_reward_list) do
			v:DeleteMe()
			v = nil
		end
		self.big_reward_list = nil
	end

	if self.normal_reward_list then
		self.normal_reward_list:DeleteMe()
		self.normal_reward_list = nil
	end


	self.fashion_part_type = nil
	self.fashion_index = nil

	self.sub_color_selector = nil
	self.show_fashion_seq = nil

	if CountDownManager.Instance:HasCountDown("glory_crystal_free_time") then
		CountDownManager.Instance:RemoveCountDown("glory_crystal_free_time")
	end

	self:RemoveActivityCountDown()
end

function NewGloryCrystalView:OpenCallBack()
	GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.INFO)

	if self.role_model then
		ReDelayCall(self, function ()
			self.role_model:PlayLastAction()
		end, 0.02, "replay_role_model")
	end
end

function NewGloryCrystalView:LoadCallBack()
	self:ChangeBgByGrade()

	self:InitMoneyBar()
	self.node_list.title_view_name.text.text = Language.GloryCrystal.TitleName
	self.node_list.open_display_view_btn_text.text.text = Language.GloryCrystal.DsiplayBtnText

	self:InitColorSelector()

	XUI.AddClickEventListener(self.node_list.open_display_view_btn, BindTool.Bind(self.OpenSpDisplayItemTips, self))
	XUI.AddClickEventListener(self.node_list.open_reward_view_btn, BindTool.Bind(self.OpenCollectReward, self))
	XUI.AddClickEventListener(self.node_list.open_gc_exchange_shop_btn, BindTool.Bind(self.OpenGloryCrystalExchangeShop, self))
	XUI.AddClickEventListener(self.node_list.open_reward_preview_btn, BindTool.Bind(self.OpenRewardPreview, self))
	XUI.AddClickEventListener(self.node_list.open_dye_view_btn, BindTool.Bind(self.OpenFashionDyeTips, self, true))
	XUI.AddClickEventListener(self.node_list.btn_close_tips, BindTool.Bind(self.OpenFashionDyeTips, self))
	XUI.AddClickEventListener(self.node_list.open_haoli_btn, BindTool.Bind(self.OpenHaoli, self))
	XUI.AddClickEventListener(self.node_list.skip_spine_check, BindTool.Bind(self.OnClickSkipSpine, self))

	for i = 1, 3 do
		self.node_list["btn_buy_model_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickDraw, self, i))
		self.node_list["cost_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowCostItemTips, self))
	end

	if not self.big_reward_list then
		self.big_reward_list = {}
	end

	for i = 0, 1 do
		self.node_list["big_reward_btn_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickBigRewardBtn, self, i))

		self.big_reward_list[i] = AsyncListView.New(NewGloryCrystalItemCell, self.node_list["big_reward_list_" .. i])
	end

	if not self.normal_reward_list then
		self.normal_reward_list = AsyncListView.New(NewGloryCrystalItemCell, self.node_list.reward_list)
	end

	for i = 1, 3 do
		-- XUI.AddClickEventListener(self.node_list["dye_btn_" .. i], BindTool.Bind(self.ChangeFalshionDyeColor, self, i))
		self.node_list["dye_btn_" .. i].toggle:AddClickListener(BindTool.Bind(self.ChangeFalshionDyeColor, self, i))
	end
	XUI.AddClickEventListener(self.node_list.go_btn, BindTool.Bind(self.OpenNewAppearanceDyeView, self))

	--人物模型
	if self.role_model == nil then
		self.role_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["role_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = true,
		-- }
		-- self.role_model:SetRenderTexUI3DModel(display_data)

		self.role_model:SetUISceneModel(self.node_list["role_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
	end

	self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)

	self:InitProgressRewardList()

	self.select_big_reward_type = -1						--当前选择的展示类型.
	self.select_falshion_dye_index = nil
	self.fashion_part_type = nil
	self.fashion_index = nil

	self:OnClickBigRewardBtn(0)

	self:SetActivityCountDown()
end

function NewGloryCrystalView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.GloryCrystalView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL)
end

function NewGloryCrystalView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushBigRewardPanel()
			self:FlushBtnInfoShow()
			self:FlushTimesReward()
			self:FlushFreeTimeCount()
			self:FlushSkipSpine()
		elseif k == "flush_model" then
			if self.role_model then
				self.role_model:PlayLastAction()
			end
		end
	end
end

function NewGloryCrystalView:OnActivityChange()
	self:SetActivityCountDown() -- 活动变化也刷新
	self:ChangeFalshionDyeColor(3, true)
	self:OpenFashionDyeTips(false)
	self.node_list["dye_btn_3"].toggle.isOn = true
end

function NewGloryCrystalView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	if change_item_id == open_day_cfg.cost_item_id or GloryCrystalWGData.Instance:GetIsStuffItem(change_item_id) then
		self:FlushBtnInfoShow()
	end
end

function NewGloryCrystalView:InitMoneyBar()
	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")

		local show_params = {
			show_gold = false,
			show_cangjin_score = true,
			--自定义货币.
			show_custom_1 = open_day_cfg and open_day_cfg.cost_item_id ~= nil,
			custom_item_id_1 = open_day_cfg.cost_item_id and open_day_cfg.cost_item_id or 0,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	-- 抽奖消耗
	local item_id = open_day_cfg.cost_item_id and open_day_cfg.cost_item_id or 0
	-- print_error("item_id = ", item_id)
	local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(item_id))
	for i = 1, 3 do
		if self.node_list["cost_icon_" .. i] then
			self.node_list["cost_icon_" .. i].image:LoadSprite(bundle, asset)
		end
	end
end

function NewGloryCrystalView:InitProgressRewardList()
	if not self.item_list then
		self.item_list = {}
		local res_async_loader = AllocResAsyncLoader(self, "task_cell")
		res_async_loader:Load("uis/view/glory_crystal_ui_prefab", "times_reward_item_cell", nil, function(obj)
			for i = 1, PROGRESS_REWARD_MAX do
				local root = ResMgr:Instantiate(obj)
				self.item_list[i] = NewGloryCrystalTimesRewardCell.New(root)
				self.item_list[i]:SetIndex(i)
				self.item_list[i]:SetInstanceParent(self.node_list.root_pos.transform)
			end

			self:FlushTimesReward()
		end)
	end
end

--刷新按钮信息
function NewGloryCrystalView:FlushBtnInfoShow()
	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	local item_cfg = ItemWGData.Instance:GetItemConfig(open_day_cfg.cost_item_id)
	if IsEmptyTable(item_cfg) then
		return
	end

	local free_draw_times = GloryCrystalWGData.Instance:GetFreeDrawTimes()

	for i = 1, 3 do
		local mode_cfg = GloryCrystalWGData.Instance:GetModeCfgByMode(i)
		if mode_cfg then
			self.node_list["buy_num_text_" .. i].text.text = string.format(Language.GloryCrystal.DarwBtnDes, mode_cfg.times)
			local item_count = ItemWGData.Instance:GetItemNumInBagById(item_cfg.id)
			-- local color = free_draw_times + item_count >= mode_cfg.cost_item_num and COLOR3B.C8 or COLOR3B.C10
			-- local num_text = free_draw_times + item_count .. "/" .. mode_cfg.cost_item_num
			-- self.node_list["cost_num_" .. i].text.text = ToColorStr(num_text, color)
			-- self.node_list["buy_red_img_" .. i]:SetActive(free_draw_times + item_count >= mode_cfg.cost_item_num)
			-- 策划要求，改成免费独立，连抽只算道具
			local color = item_count >= mode_cfg.cost_item_num and COLOR3B.C8 or COLOR3B.C10
			local num_text = item_count .. "/" .. mode_cfg.cost_item_num
			self.node_list["cost_num_" .. i].text.text = ToColorStr(num_text, color)
			item_count = i == 1 and item_count + free_draw_times or item_count
			self.node_list["buy_red_img_" .. i]:SetActive(item_count >= mode_cfg.cost_item_num)
		end
	end

	-- self.node_list["cost_icon_bg_1"]:SetActive(free_draw_times <= 0)
	if free_draw_times > 0 then
		self.node_list["buy_num_text_1"].text.text = string.format(Language.GloryCrystal.FreeDrawNum, free_draw_times)
	end

	local task_red = GloryCrystalWGData.Instance:ShowCollectTaskRemind()
	self.node_list.open_reward_view_red:SetActive(task_red)

	self.node_list.open_haoli_red:CustomSetActive(GloryCrystalWGData.Instance:GetHaoLiRed() == 1)
end

function NewGloryCrystalView:RemoveActivityCountDown()
	if self.activity_count_down then
		CountDown.Instance:RemoveCountDown(self.activity_count_down)
		self.activity_count_down = nil
	end
end

function NewGloryCrystalView:SetActivityCountDown()
	self:RemoveActivityCountDown()
	
	-- 活动结束时间
	local all_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL)
	local timer_func = function(elapse_time, total_time)
		self:FlushActivityTime(total_time - elapse_time)
	end
	
	timer_func(0, all_time)
	self.activity_count_down = CountDown.Instance:AddCountDown(all_time, 1, timer_func, end_call_back)
end

function NewGloryCrystalView:FlushActivityTime(left_time)
	if self.node_list and self.node_list.end_time then
		local time = TimeUtil.FormatSecondDHM9(left_time)
		self.node_list.end_time.text.text = string.format(Language.GloryCrystal.ActivityTime, time)
	end
end

--刷新免费抽奖倒计时.
function NewGloryCrystalView:FlushFreeTimeCount()
	local free_draw_times = GloryCrystalWGData.Instance:GetFreeDrawTimes()
	if free_draw_times > 0 then
		if CountDownManager.Instance:HasCountDown("glory_crystal_free_time") then
			CountDownManager.Instance:RemoveCountDown("glory_crystal_free_time")
		end
		self.node_list.free_draw_count_down.text.text = ""
	else
		-- 获取当天剩余时间
		local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
		if time > 0 then
			if not CountDownManager.Instance:HasCountDown("glory_crystal_free_time") then
				CountDownManager.Instance:AddCountDown("glory_crystal_free_time",
					BindTool.Bind(self.FinalUpdateTimeCallBack, self),
					BindTool.Bind(self.OnComplete, self),
					nil, time, 1)
			end
		else
			self:OnComplete()
		end
	end
end

function NewGloryCrystalView:FinalUpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list.free_draw_count_down.text.text = string.format(Language.GloryCrystal.FreeDrawCountDown, time_str)
end

function NewGloryCrystalView:OnComplete()
	self.node_list.free_draw_count_down.text.text = ""
end

function NewGloryCrystalView:FlushTimesReward()
	local need_num = GloryCrystalWGData.Instance:GetBaoDiNeedDrawNum()
	local need_str = ""
	if need_num > 0 then
		need_str = string.format(Language.GloryCrystal.NeedDrawNum, need_num)
	else
		need_str = Language.GloryCrystal.NeedDrawNumMax
	end
	self.node_list.draw_num_tips_text.text.text = need_str

	local draw_times = GloryCrystalWGData.Instance:GetDrawTimes()
	self.node_list.cur_draw_times.text.text = draw_times

	local data_list = GloryCrystalWGData.Instance:GetAllTimesRewardInfo()
	local slider_value = GloryCrystalWGData.Instance:GetCurSliderProgress()
	self.node_list.pro_reward.slider.value = slider_value

	for i = 1, PROGRESS_REWARD_MAX do
		if self.item_list[i] and data_list[i] then
			self.item_list[i]:SetData(data_list[i])
		end
	end
end

function NewGloryCrystalView:FlushBigRewardPanel()
	for i = 0, 1 do
		self.node_list["big_reward_quality_text_" .. i].text.text = Language.GloryCrystal.BigRewardQualityText[i]

		local model_cfg = GloryCrystalWGData.Instance:GetModelCfgByType(i)
		if model_cfg then
			local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(model_cfg.part_type, model_cfg.index)
			if fashion_cfg then
				self.node_list["big_reward_name_text_" .. i].text.text = fashion_cfg.name
			end
		end

		local show_list = GloryCrystalWGData.Instance:GetShowRewardListByType(i)
		if not IsEmptyTable(show_list) then
			self.big_reward_list[i]:SetDataList(show_list)
		end

		self.node_list["big_reward_list_" .. i].scroll_rect.enabled = #show_list > BIG_REWARD_CAN_DRAW_NUM

		local sex = RoleWGData.Instance:GetRoleSex()
		local grade = GloryCrystalWGData.Instance:GetCurGrade()
		-- print_error("grade = ", grade)
		local bundle, asset = ResPath.GetGloryCrystalExchangeShopImg(string.format("a3_txxy_js_%s_%s_%s", grade, i, sex))
		self.node_list["big_reward_image_" .. i].image:LoadSprite(bundle, asset, function()
			self.node_list["big_reward_image_" .. i].image:SetNativeSize()
		end)
	end

	local show_list = GloryCrystalWGData.Instance:GetShowRewardListByType(GloryCrystalWGData.BIG_REWARD_SHOW_ITEM_TYPE.Normal)
	if not IsEmptyTable(show_list) then
		self.normal_reward_list:SetDataList(show_list)
	end
end

--刷新角色模型.
function NewGloryCrystalView:FlushFishionModel()
	local model_cfg = GloryCrystalWGData.Instance:GetModelCfgByType(self.select_big_reward_type)
	if not model_cfg then
		return
	end

	local is_show_dye = false
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(model_cfg.part_type, model_cfg.index)
	if fashion_cfg then
		local res_id = fashion_cfg.resouce
		local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
		role_res_id = AppearanceWGData.GetFashionBodyResIdByResViewId(res_id)

		if tonumber(model_cfg.show_weapon_id) then
			weapon_res_id = NewAppearanceWGData.Instance:GetFashionResByItemId(tonumber(model_cfg.show_weapon_id))
			weapon_res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(weapon_res_id)
		end

		local extra_role_model_data = {
			weapon_res_id = weapon_res_id,
		}

		-- 后调用ChangeFalshionDyeColor，select_falshion_dye_index会被重置为nil
		-- self:ChangeFalshionDyeColor(self.select_falshion_dye_index, true)
		self.role_model:SetRoleResid(role_res_id, BindTool.Bind(self.ChangeFalshionDyeColor, self, self.select_falshion_dye_index, true), extra_role_model_data)
		self:ChangeFalshionDyeColor(self.select_falshion_dye_index, true)

		local halo_res_id = NewAppearanceWGData.Instance:GetFashionResByItemId(tonumber(model_cfg.show_halo_id))
		if halo_res_id then
			self.role_model:SetHaloResid(halo_res_id)
		else
			self.role_model:RemoveHalo(true)
		end

		is_show_dye = fashion_cfg.is_open_dyeing == 1
	end

	-- self.node_list.open_dye_view_btn:CustomSetActive(is_show_dye)
	self.node_list.btn_group_2:CustomSetActive(is_show_dye)

	-- local pos_x , pos_y, pos_z = 0, 0, 0
	-- if model_cfg.model_pos and model_cfg.model_pos ~= "" then
	-- 	local pos_list = Split(model_cfg.model_pos, "|")
	-- 	pos_x = tonumber(pos_list[1]) or pos_x
	-- 	pos_y = tonumber(pos_list[2]) or pos_y
	-- 	pos_z = tonumber(pos_list[3]) or pos_z
	-- 	self.role_model:SetRTAdjustmentRootLocalPosition(pos_x, pos_y, pos_z)
	-- end

	-- local rot_x , rot_y, rot_z = 0, 0, 0
	-- if model_cfg.model_rot and model_cfg.model_rot ~= "" then
	-- 	local pos_list = Split(model_cfg.model_rot, "|")
	-- 	rot_x = tonumber(pos_list[1]) or rot_x
	-- 	rot_y = tonumber(pos_list[2]) or rot_y
	-- 	rot_z = tonumber(pos_list[3]) or rot_z
	-- 	self.role_model:SetRTAdjustmentRootLocalRotation(rot_x, rot_y, rot_z)
	-- end

	-- local scale = model_cfg.model_scale
	-- if scale and scale ~= "" then
	-- 	self.role_model:SetRTAdjustmentRootLocalScale(scale)
	-- end
end

--战力.
function NewGloryCrystalView:FlushCapability()
	local model_cfg = GloryCrystalWGData.Instance:GetModelCfgByType(self.select_big_reward_type)
	if not model_cfg then
		return
	end

	local capability = 0

	local item_cfg = ItemWGData.Instance:GetItemConfig(model_cfg.show_item_id)
	if item_cfg then
		local fashion_part_type = self.fashion_part_type
		local fashion_index = self.fashion_index

		local show_lv = 1

		local cur_attr_data = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(fashion_part_type, fashion_index, show_lv)

		-- 战力
		local attr_cap = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(cur_attr_data))
		local add_per_val = NewAppearanceWGData.Instance:GetCurFashionPartAddPerValue(fashion_part_type)
		local extra_cap =  NewAppearanceWGData.Instance:CalcFashionAttrCapWithAddPer(cur_attr_data, add_per_val)
		capability = attr_cap + extra_cap
	end

	self.node_list.cap_value.text.text = capability
end

function NewGloryCrystalView:OnClickBigRewardBtn(type)
	if self.select_big_reward_type ~= type then
		self.select_big_reward_type = type

		for i = 0, 1 do
			self.node_list["big_reward_select_image_" .. i]:SetActive(type == i)
		end

		local model_cfg = GloryCrystalWGData.Instance:GetModelCfgByType(self.select_big_reward_type)
		if not model_cfg then
			return
		end
 
		self.fashion_part_type = model_cfg.part_type
		self.fashion_index = model_cfg.index

		self:FlushFishionModel()
		self:FlushCapability()

		--切换模型强制隐藏节点.
		self:OpenFashionDyeTips(false)
		self.node_list["dye_btn_3"].toggle.isOn = true
	else
		local model_cfg = GloryCrystalWGData.Instance:GetModelCfgByType(self.select_big_reward_type)
		if not model_cfg then
			return
		end
 
		TipWGCtrl.Instance:OpenItem({item_id = model_cfg.show_item_id}, ItemTip.FROM_NORMAL)
	end
end

function NewGloryCrystalView:OnClickDraw(mode_type)
	GloryCrystalWGCtrl.Instance:ClickUse(mode_type)
end

function NewGloryCrystalView:ShowCostItemTips()
	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	local item_id = open_day_cfg.cost_item_id
	TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function NewGloryCrystalView:OpenSpDisplayItemTips()
	local open_day_cfg = GloryCrystalWGData.Instance:GetOpenDayCfg()
	local item_id = open_day_cfg.sp_model_id
	SPDisplayItemTip.Instance:SetData(item_id)
end

function NewGloryCrystalView:OpenCollectReward()
	GloryCrystalWGCtrl.Instance:OpenCollectRewardView()
end

function NewGloryCrystalView:OpenGloryCrystalExchangeShop()
	ViewManager.Instance:Open(GuideModuleName.GloryCrystalExchangeShopView)
end

function NewGloryCrystalView:OpenRewardPreview()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	local data_list =
	{
		view_type = RewardShowViewType.Title_Probability,
		view_color = self.tip_color[cfg and cfg.color_index or 1],
		title_probability_reward_item_data = GloryCrystalWGData.Instance:GetShowRewardPerviewList()
	}

	RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end

function NewGloryCrystalView:OpenFashionDyeTips(is_show)
	local show_flag = false
	if nil ~= is_show then
		show_flag = is_show
	else
		show_flag = not self.node_list.dye_root.activeSelf
	end
	self.node_list.dye_root.activeSelf = show_flag
	self.node_list.dye_root:SetActive(show_flag)
	self.node_list.btn_close_tips:CustomSetActive(show_flag)

	if is_show ~= nil then
		self.node_list.open_dye_view_select:CustomSetActive(is_show)
		if is_show then
			self.node_list.temp_btn.toggle.isOn = true
		end
	end
end

function NewGloryCrystalView:OpenNewAppearanceDyeView()
	NewAppearanceDyeWGCtrl.Instance:OpenAppearanceDyeView(self.fashion_part_type, self.fashion_index)
end

function NewGloryCrystalView:OpenHaoli()
	ViewManager.Instance:Open(GuideModuleName.GloryCrystalHaoLiView)
end

function NewGloryCrystalView:OnClickSkipSpine()
    local is_skip = GloryCrystalWGData.Instance:GetSkipSpineStatus()
    is_skip = not is_skip
    GloryCrystalWGData.Instance:SetSkipSpineStatus(is_skip)
    self:FlushSkipSpine()
end

function NewGloryCrystalView:FlushSkipSpine()
	local is_skip = GloryCrystalWGData.Instance:GetSkipSpineStatus()
	self.node_list.skip_spine_yes:SetActive(is_skip)
end

function NewGloryCrystalView:ChangeFalshionDyeColor(index, is_flush)
	if self.select_falshion_dye_index then
		if not is_flush and self.select_falshion_dye_index == index then
			return
		end
		self.select_falshion_dye_index = index
	else
		self.select_falshion_dye_index = 3	--默认选择原色.
	end

	self.role_model:ResetPartDyeColor()
	self:OpenFashionDyeTips(false)

	local dye_index = GloryCrystalWGData.Instance:GetDyeIndex(self.select_big_reward_type, self.select_falshion_dye_index)

	-- 获取染色配置
	local list = NewAppearanceDyeWGData.Instance:GetDyeingSchemeListByPartIndex(self.fashion_index, self.fashion_part_type)
	if not list or not list[dye_index] then
		return
	end

	local cur_scheme_data = list[dye_index]
	local seq, part_color = cur_scheme_data.seq, cur_scheme_data.dyeing_info.part_color
	if (not part_color) or (not seq) then
		return
	end

	if self.show_fashion_seq == nil then
		self.show_fashion_seq = seq
	end

	local change_body_dye_color_fun = function()
		local dye_color_table = {}
		for show_part, color_data in ipairs(part_color) do
			local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(seq, show_part)
			local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
	
			local color = nil
			if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
				color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
			else
				local defult_color = NewAppearanceDyeWGData.Instance:GetDefultColorCfgBySeqPart(seq, dye_index, show_part, true)
				if defult_color ~= nil then
					color = UtilU3d.ConvertHexToColor(defult_color)
				end
			end
	
			if show_part ~= HAIR_PART then
				if index_list and color then
					for _, dye_index in ipairs(index_list) do
						local dye_color_data = {}
						dye_color_data.dye_index = dye_index
						dye_color_data.r = color.r
						dye_color_data.g = color.g
						dye_color_data.b = color.b
						dye_color_data.a = color.a
						table.insert(dye_color_table, dye_color_data)
					end
				end
			end
		end
	
		if self.role_model and (not IsEmptyTable(dye_color_table))  then
			self.role_model:ChangePartDyeColor(dye_color_table)
		end
	end

	local change_hair_dye_color_fun = function()
		local color_data = part_color[HAIR_PART]
		local color = nil

		if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
			color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
		else
			local defult_color = NewAppearanceDyeWGData.Instance:GetDefultColorCfgBySeqPart(seq, dye_index, HAIR_PART)
			if defult_color ~= nil then
				color = UtilU3d.ConvertHexToColor(defult_color)
			end
		end

		local new_color = {r = math.floor(color.r * 255), g = math.floor(color.g * 255), b = math.floor(color.b * 255), a = math.floor(color.a * 255)}
		self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, new_color)
	end

	local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(seq, dye_index)
	local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(seq, dye_index)
	local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(seq, dye_index)
	self.role_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.BODY, body_material_id, change_body_dye_color_fun)
	self.role_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.HAIR, hair_material_id, change_hair_dye_color_fun)
	self.role_model:ChangeRoleMaterialsByProjectId(ROLE_SKIN_TYPE.FACE, face_material_id)
end

-- 着色器调色 ----------------------------
function NewGloryCrystalView:InitColorSelector()
	self.sub_color_selector = self.node_list.sub_color:GetComponent(typeof(SubColorSelector))
	-- 选择完颜色回调
	if self.sub_color_selector then
		self.sub_color_selector:SetSelectColorCB(BindTool.Bind(self.SelectSubColorCallBack, self))
	end
end

function NewGloryCrystalView:SelectSubColorCallBack(final_color, hex_color)
	if not self.show_fashion_seq or self.is_first_open then
		self.is_first_open = false
		return
	end

	local dye_index_list = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeq(self.show_fashion_seq)
	if dye_index_list then
		for part, dye_index_list_data in pairs(dye_index_list) do
			local dye_color_table = {}
			if part == HAIR_PART then
				local new_color = {r = math.floor(final_color.r * 255), g = math.floor(final_color.g * 255), b = math.floor(final_color.b * 255), a = math.floor(final_color.a * 255)}
				self.role_model:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, new_color)
			else
				local index_list = dye_index_list_data.dye_index_list
				if index_list and final_color then
					for _, dye_index in ipairs(index_list) do
						local dye_color_data = {}
						dye_color_data.dye_index = dye_index
						dye_color_data.r = final_color.r
						dye_color_data.g = final_color.g
						dye_color_data.b = final_color.b
						dye_color_data.a = final_color.a
		
						table.insert(dye_color_table, dye_color_data)
					end
				end
			end
		
			if self.role_model then
				if not IsEmptyTable(dye_color_table) then
					self.role_model:ChangePartDyeColor(dye_color_table)
				end
			end
		end
	end
end
-- 着色器调色end -------------------------

-- 根据档次换界面
function NewGloryCrystalView:ChangeBgByGrade()
	local cfg = GloryCrystalWGData.Instance:GetCurViewStyle()
	if not cfg then
		return
	end
	
	local bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_txxy_yq2_" .. cfg.color_index)
	if self.node_list.open_reward_view_btn then
		self.node_list.open_reward_view_btn.image:LoadSprite(bundle, asset)
	end
	
	if self.node_list.open_gc_exchange_shop_btn then
		self.node_list.open_gc_exchange_shop_btn.image:LoadSprite(bundle, asset)
	end

	if self.node_list.open_haoli_btn then
		self.node_list.open_haoli_btn.image:LoadSprite(bundle, asset)
	end

	if self.node_list.reward_pool_bg then
		bundle, asset = ResPath.GetRawImagesPNG("a3_txxy_ll_bqjc_di_" .. cfg.color_index)
		self.node_list.reward_pool_bg.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.reward_pool_bg.raw_image:SetNativeSize()
		end)
	end

	if self.node_list.open_display_view_bg then
		local bg_name = cfg.zuoqi_btn_res
		if not bg_name or bg_name == "" then
			bg_name = "a3_txxy_yq1_" .. cfg.color_index
		end
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg(bg_name)
		self.node_list.open_display_view_bg.image:LoadSprite(bundle, asset)
	end

	if self.node_list.times_bg then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_txxy_di19_" .. cfg.color_index)
		self.node_list.times_bg.image:LoadSprite(bundle, asset)
	end

	if self.node_list.dye_btn_3 then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_ysxy_lltz_ts_btn1_1_" .. cfg.color_index)
		self.node_list.dye_btn_3.image:LoadSprite(bundle, asset)
	end

	if self.node_list.dye_select_3 then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_ysxy_lltz_ts_btn1_2_" .. cfg.color_index)
		self.node_list.dye_select_3.image:LoadSprite(bundle, asset)
	end

	if self.node_list.dye_btn_1 then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_ysxy_lltz_ts_btn2_1_" .. cfg.color_index)
		self.node_list.dye_btn_1.image:LoadSprite(bundle, asset)
		self.node_list.dye_btn_2.image:LoadSprite(bundle, asset)
	end

	if self.node_list.dye_select_1 then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_ysxy_lltz_ts_btn2_2_" .. cfg.color_index)
		self.node_list.dye_select_1.image:LoadSprite(bundle, asset)
		self.node_list.dye_select_2.image:LoadSprite(bundle, asset)
	end
	
	if self.node_list.open_dye_view_btn then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_ysxy_lltz_ts_btn3_1_" .. cfg.color_index)
		self.node_list.open_dye_view_btn.image:LoadSprite(bundle, asset)
	end

	if self.node_list.open_dye_view_select then
		bundle, asset = ResPath.GetGloryCrystalExchangeShopImg("a3_ysxy_lltz_ts_btn3_2_" .. cfg.color_index)
		self.node_list.open_dye_view_select.image:LoadSprite(bundle, asset)
	end
	
	if self.node_list.btn_group_bg_2 then
		bundle, asset = ResPath.GetRawImagesPNG("a3_ysxy_lltz_tszs_" .. cfg.color_index)
		self.node_list.btn_group_bg_2.raw_image:LoadSprite(bundle, asset, function()
			self.node_list.btn_group_bg_2.raw_image:SetNativeSize()
		end)
	end

	if cfg.ui_scene_config_index and cfg.ui_scene_config_index ~= "" then
		self.ui_scene_change_config_index = cfg.ui_scene_config_index
		Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.GLORY_CRYSTAL, cfg.ui_scene_config_index)
	end
end

-----------------------------------NewGloryCrystalTimesRewardCell-----------------------------------
NewGloryCrystalTimesRewardCell = NewGloryCrystalTimesRewardCell or BaseClass(BaseRender)

function NewGloryCrystalTimesRewardCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGet, self))

	if not self.item_cell then
		self.item_cell = DiamondItemCell.New(self.node_list.item_cell)
		self.item_cell:SetShowCualityBg(false)
		self.item_cell:SetQualityIconVisible(false)
	end
end

function NewGloryCrystalTimesRewardCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function NewGloryCrystalTimesRewardCell:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data.item)
	self.item_cell:SetRightBottomTextVisible(false)
	self.item_cell:SetBindIconVisible(false)
	self.item_cell:SetQualityIconVisible(false)
	self.item_cell:SetEffectRootEnable(false)
	self.item_cell:SetCellBgEnabled(false)

	self.node_list.num.text.text = string.format(Language.GloryCrystal.NumText, self.data.item.num)
	self.node_list.text_prestige.text.text = self.data.need_draw_times

	local is_can = self.data.state == REWARD_STATE_TYPE.CAN_FETCH
	self.node_list.btn_get:SetActive(is_can)
	self.node_list.img_can_get:SetActive(is_can)
	self.node_list.img_red:SetActive(is_can)
	self.node_list.mask:SetActive(self.data.state == REWARD_STATE_TYPE.FINISH)
end

function NewGloryCrystalTimesRewardCell:OnClickGet()
	if not self.data then
		return
	end

	if self.data.state == REWARD_STATE_TYPE.CAN_FETCH then
		GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.FETCH_TIMES_REWARD, self.data.seq)
	end
end

-----------------------------------NewGloryCrystalItemCell-----------------------------------
NewGloryCrystalItemCell = NewGloryCrystalItemCell or BaseClass(BaseRender)

function NewGloryCrystalItemCell:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
		self.item_cell:SetShowCualityBg(false)
		self.item_cell:SetQualityIconVisible(false)
	end
end

function NewGloryCrystalItemCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function NewGloryCrystalItemCell:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data)
	self.item_cell:SetEffectRootEnable(false)
	self.item_cell:SetCellBgEnabled(false)
end
-- L-离线经验.xls

return {
rest_reward={
{hang_monster_id=10101,pos_x=101,pos_y=155,pre_scene_id=1001,pre_pos_x=101,pre_pos_y=155,hang_monster_id_scene_id=1001,hang_monster_id_pos_x=199,hang_monster_id_pos_y=131,},
{level=2,},
{level=3,},
{level=4,},
{level=5,},
{level=6,},
{level=7,},
{level=8,},
{level=9,},
{level=10,},
{level=11,},
{level=12,},
{level=13,},
{level=14,},
{level=15,},
{level=16,},
{level=17,},
{level=18,},
{level=19,},
{level=20,},
{level=21,},
{level=22,},
{level=23,},
{level=24,},
{level=25,},
{level=26,},
{level=27,},
{level=28,},
{level=29,},
{level=30,},
{level=31,},
{level=32,},
{level=33,},
{level=34,},
{level=35,},
{level=36,},
{level=37,},
{level=38,},
{level=39,},
{level=40,},
{level=41,},
{level=42,},
{level=43,},
{level=44,},
{level=45,},
{level=46,},
{level=47,},
{level=48,},
{level=49,},
{level=50,pos_x=250,pos_y=314,pre_pos_x=250,pre_pos_y=314,},
{level=51,},
{level=52,},
{level=53,},
{level=54,},
{level=55,},
{level=56,},
{level=57,},
{level=58,},
{level=59,},
{level=60,pos_x=118,pos_y=258,pre_pos_x=118,pre_pos_y=258,},
{level=61,},
{level=62,},
{level=63,},
{level=64,},
{level=65,},
{level=66,},
{level=67,},
{level=68,pos_x=371,pos_y=147,pre_scene_id=1002,pre_pos_x=371,pre_pos_y=147,},
{level=69,},
{level=70,},
{level=71,},
{level=72,},
{level=73,},
{level=74,},
{level=75,},
{level=76,},
{level=77,},
{level=78,},
{level=79,},
{level=80,},
{level=81,},
{level=82,},
{level=83,},
{level=84,},
{level=85,},
{level=86,},
{level=87,},
{level=88,},
{level=89,},
{level=90,},
{level=91,},
{level=92,},
{level=93,},
{level=94,},
{level=95,pos_x=57,pos_y=334,pre_pos_x=57,pre_pos_y=334,},
{level=96,},
{level=97,},
{level=98,},
{level=99,},
{level=100,pos_x=59,pos_y=249,pre_pos_x=59,pre_pos_y=249,},
{level=101,},
{level=102,},
{level=103,pos_x=74,pos_y=167,pre_pos_x=74,pre_pos_y=167,},
{level=104,},
{level=105,},
{level=106,pos_x=184,pos_y=178,pre_pos_x=184,pre_pos_y=178,},
{level=107,},
{level=108,},
{level=109,},
{level=110,},
{level=111,},
{level=112,},
{level=113,},
{level=114,},
{level=115,pos_x=252,pos_y=238,pre_pos_x=252,pre_pos_y=238,},
{level=116,},
{level=117,},
{level=118,},
{level=119,},
{level=120,pos_x=246,pos_y=132,pre_pos_x=246,pre_pos_y=132,},
{level=121,},
{level=122,},
{level=123,},
{level=124,},
{level=125,pos_x=212,pos_y=55,pre_pos_x=212,pre_pos_y=55,},
{level=126,},
{level=127,},
{level=128,pos_x=248,pos_y=112,pre_scene_id=1008,pre_pos_x=248,pre_pos_y=112,},
{level=129,},
{level=130,},
{level=131,pos_x=285,pos_y=352,pre_pos_x=285,pre_pos_y=352,},
{level=132,},
{level=133,},
{level=134,},
{level=135,},
{level=136,},
{level=137,},
{level=138,},
{level=139,},
{level=140,pos_x=243,pre_scene_id=1008,pre_pos_x=243,},
{level=141,pos_x=334,pos_y=431,pre_pos_x=334,pre_pos_y=431,},
{level=142,},
{level=143,},
{level=144,},
{level=145,},
{level=146,},
{level=147,},
{level=148,},
{level=149,},
{level=150,},
{level=151,},
{level=152,},
{level=153,},
{level=154,},
{level=155,},
{level=156,},
{level=157,},
{level=158,},
{level=159,},
{level=160,pos_x=201,pos_y=386,pre_pos_x=201,pre_pos_y=386,},
{level=161,},
{level=162,},
{level=163,},
{level=164,pos_x=106,pos_y=375,pre_pos_x=106,pre_pos_y=375,},
{level=165,},
{level=166,},
{level=167,},
{level=168,},
{level=169,},
{level=170,},
{level=171,hang_monster_id=10101,pos_x=119,pre_scene_id=1005,pre_pos_x=119,hang_monster_id_scene_id=1001,hang_monster_id_pos_x=199,hang_monster_id_pos_y=131,},
{level=172,},
{level=173,},
{level=174,},
{level=175,},
{level=176,},
{level=177,},
{level=178,},
{level=179,},
{level=180,},
{level=181,},
{level=182,},
{level=183,},
{level=184,pos_x=181,pos_y=257,pre_pos_x=181,pre_pos_y=257,},
{level=185,},
{level=186,},
{level=187,},
{level=188,},
{level=189,},
{level=190,},
{level=191,pos_x=229,pos_y=190,pre_pos_x=229,pre_pos_y=190,},
{level=192,},
{level=193,},
{level=194,},
{level=195,},
{level=196,},
{level=197,},
{level=198,},
{level=199,},
{level=200,pos_x=229,pos_y=190,pre_pos_x=229,pre_pos_y=190,},
{level=201,},
{level=202,},
{level=203,},
{level=204,},
{level=205,},
{level=206,},
{level=207,},
{level=208,},
{level=209,pos_x=269,pos_y=270,pre_scene_id=1005,pre_pos_x=269,pre_pos_y=270,},
{level=210,},
{level=211,},
{level=212,},
{level=213,},
{level=214,},
{level=215,},
{level=216,},
{level=217,},
{level=218,},
{level=219,hang_monster_id=10501,hang_monster_id_pos_x=204,hang_monster_id_pos_y=152,},
{level=220,pos_x=341,pos_y=331,pre_scene_id=1005,pre_pos_x=341,pre_pos_y=331,},
{level=221,},
{level=222,pos_x=450,pos_y=246,pre_pos_x=450,pre_pos_y=246,},
{level=223,},
{level=224,},
{level=225,},
{level=226,},
{level=227,},
{level=228,},
{level=229,},
{level=230,},
{level=231,},
{level=232,},
{level=233,},
{level=234,},
{level=235,},
{level=236,},
{level=237,pos_x=439,pos_y=415,pre_scene_id=1006,pre_pos_x=439,pre_pos_y=415,},
{level=238,},
{level=239,},
{level=240,hang_monster_id=10505,hang_monster_id_pos_x=61,hang_monster_id_pos_y=242,},
{level=241,},
{level=242,},
{level=243,},
{level=244,},
{level=245,},
{level=246,},
{level=247,},
{level=248,pos_x=401,pos_y=424,pre_scene_id=1006,pre_pos_x=401,pre_pos_y=424,},
{level=249,},
{level=250,},
{level=251,pos_x=349,pos_y=454,pre_pos_x=349,pre_pos_y=454,},
{level=252,},
{level=253,},
{level=254,},
{level=255,},
{level=256,},
{level=257,},
{level=258,},
{level=259,},
{level=260,pos_x=349,pos_y=454,radius=115,pre_scene_id=1006,pre_pos_x=349,pre_pos_y=454,pre_radius=115,},
{level=261,},
{level=262,},
{level=263,},
{level=264,},
{level=265,},
{level=266,},
{level=267,},
{level=268,radius=115,pre_radius=115,},
{level=269,},
{level=270,pos_x=335,pos_y=451,pre_scene_id=1006,pre_pos_x=335,pre_pos_y=451,},
{level=271,},
{level=272,},
{level=273,},
{level=274,pos_x=290,pos_y=425,pre_scene_id=1006,pre_pos_x=290,pre_pos_y=425,},
{level=275,},
{level=276,},
{level=277,},
{level=278,},
{level=279,},
{level=280,},
{level=281,pos_x=273,pos_y=360,pre_scene_id=1006,pre_pos_x=273,pre_pos_y=360,},
{level=282,},
{level=283,},
{level=284,},
{level=285,},
{level=286,},
{level=287,},
{level=288,},
{level=289,},
{level=290,},
{level=291,},
{level=292,},
{level=293,},
{level=294,},
{level=295,},
{level=296,},
{level=297,},
{level=298,},
{level=299,},
{level=300,pos_x=274,pre_scene_id=1006,pre_pos_x=274,},
{level=301,pos_y=159,pre_pos_y=159,},
{level=302,},
{level=303,},
{level=304,},
{level=305,},
{level=306,},
{level=307,},
{level=308,},
{level=309,},
{level=310,},
{level=311,},
{level=312,},
{level=313,},
{level=314,},
{level=315,},
{level=316,},
{level=317,},
{level=318,},
{level=319,},
{level=320,pos_x=198,pos_y=152,pre_scene_id=1006,pre_pos_x=198,pre_pos_y=152,},
{level=321,},
{level=322,},
{level=323,},
{level=324,},
{level=325,},
{level=326,},
{level=327,pos_x=192,pos_y=215,pre_scene_id=1006,pre_pos_x=192,pre_pos_y=215,},
{level=328,},
{level=329,},
{level=330,},
{level=331,},
{level=332,},
{level=333,},
{level=334,},
{level=335,},
{level=336,},
{level=337,},
{level=338,pos_x=132,pos_y=228,pre_scene_id=1006,pre_pos_x=132,pre_pos_y=228,},
{level=339,},
{level=340,},
{level=341,},
{level=342,},
{level=343,},
{level=344,},
{level=345,},
{level=346,},
{level=347,},
{level=348,},
{level=349,},
{level=350,pos_x=188,pos_y=125,pre_scene_id=1006,pre_pos_x=188,pre_pos_y=125,},
{level=351,},
{level=352,},
{level=353,},
{level=354,},
{level=355,},
{level=356,},
{level=357,pos_x=187,pos_y=289,pre_scene_id=1006,pre_pos_x=187,pre_pos_y=289,},
{level=358,},
{level=359,},
{level=360,},
{level=361,pos_x=141,pos_y=300,pre_scene_id=1006,pre_pos_x=141,pre_pos_y=300,},
{level=362,},
{level=363,},
{level=364,},
{level=365,},
{level=366,},
{level=367,},
{level=368,},
{level=369,},
{level=370,},
{level=371,pos_x=134,pos_y=355,pre_scene_id=1006,pre_pos_x=134,pre_pos_y=355,},
{level=372,},
{level=373,},
{level=374,},
{level=375,},
{level=376,},
{level=377,},
{level=378,},
{level=379,},
{level=380,},
{level=381,},
{level=382,},
{level=383,},
{level=384,},
{level=385,},
{level=386,},
{level=387,},
{level=388,},
{level=389,},
{level=390,},
{level=391,},
{level=392,},
{level=393,},
{level=394,pos_x=200,pos_y=366,pre_scene_id=1006,pre_pos_x=200,pre_pos_y=366,},
{level=395,},
{level=396,},
{level=397,},
{level=398,},
{level=399,},
{level=400,hang_monster_id=10501,hang_monster_id_pos_x=204,hang_monster_id_pos_y=152,},
{level=401,pos_x=373,pos_y=429,pre_scene_id=1007,pre_pos_x=373,pre_pos_y=429,},
{level=402,},
{level=403,},
{level=404,},
{level=405,},
{level=406,},
{level=407,},
{level=408,},
{level=409,},
{level=410,},
{level=411,pos_x=378,pos_y=363,pre_pos_x=378,pre_pos_y=363,},
{level=412,},
{level=413,},
{level=414,},
{level=415,},
{level=416,},
{level=417,},
{level=418,},
{level=419,},
{level=420,hang_monster_id=10502,hang_monster_id_pos_x=196,hang_monster_id_pos_y=100,},
{level=421,},
{level=422,},
{level=423,},
{level=424,},
{level=425,},
{level=426,},
{level=427,},
{level=428,},
{level=429,},
{level=430,pos_x=455,pos_y=352,pre_pos_x=455,pre_pos_y=352,},
{level=431,},
{level=432,},
{level=433,},
{level=434,},
{level=435,pos_x=458,pos_y=317,pre_scene_id=1007,pre_pos_x=458,pre_pos_y=317,},
{level=436,},
{level=437,},
{level=438,},
{level=439,},
{level=440,hang_monster_id=10504,hang_monster_id_pos_x=22,hang_monster_id_pos_y=111,},
{level=441,pos_x=421,pos_y=245,pre_pos_x=421,pre_pos_y=245,},
{level=442,},
{level=443,},
{level=444,},
{level=445,},
{level=446,},
{level=447,},
{level=448,},
{level=449,},
{level=450,},
{level=451,pos_x=385,pos_y=213,pre_pos_x=385,pre_pos_y=213,},
{level=452,},
{level=453,},
{level=454,},
{level=455,},
{level=456,},
{level=457,},
{level=458,},
{level=459,},
{level=460,hang_monster_id=10508,hang_monster_id_pos_x=122,hang_monster_id_pos_y=330,},
{level=461,},
{level=462,},
{level=463,},
{level=464,pos_x=370,pos_y=241,pre_scene_id=1007,pre_pos_x=370,pre_pos_y=241,},
{level=465,},
{level=466,},
{level=467,},
{level=468,},
{level=469,},
{level=470,},
{level=471,},
{level=472,},
{level=473,},
{level=474,},
{level=475,},
{level=476,},
{level=477,},
{level=478,},
{level=479,hang_monster_id=10508,hang_monster_id_pos_x=122,hang_monster_id_pos_y=330,},
{level=480,pos_x=288,pos_y=290,pre_scene_id=1007,pre_pos_x=288,pre_pos_y=290,},
{level=481,pos_x=270,pos_y=220,pre_scene_id=1007,pre_pos_x=270,pre_pos_y=220,},
{level=482,},
{level=483,},
{level=484,},
{level=485,},
{level=486,},
{level=487,},
{level=488,},
{level=489,},
{level=490,},
{level=491,pos_x=275,pos_y=182,pre_scene_id=1007,pre_pos_x=275,pre_pos_y=182,},
{level=492,},
{level=493,},
{level=494,},
{level=495,},
{level=496,},
{level=497,},
{level=498,},
{level=499,},
{level=500,},
{level=501,pos_x=227,pos_y=186,pre_scene_id=1007,pre_pos_x=227,pre_pos_y=186,},
{level=502,},
{level=503,},
{level=504,},
{level=505,},
{level=506,},
{level=507,},
{level=508,},
{level=509,},
{level=510,},
{level=511,},
{level=512,},
{level=513,},
{level=514,},
{level=515,},
{level=516,pos_x=225,pos_y=203,pre_scene_id=1007,pre_pos_x=225,pre_pos_y=203,},
{level=517,},
{level=518,},
{level=519,},
{level=520,},
{level=521,},
{level=522,},
{level=523,},
{level=524,},
{level=525,},
{level=526,},
{level=527,},
{level=528,},
{level=529,},
{level=530,pos_x=152,pos_y=224,pre_scene_id=1007,pre_pos_x=152,pre_pos_y=224,},
{level=531,},
{level=532,},
{level=533,},
{level=534,},
{level=535,},
{level=536,},
{level=537,},
{level=538,},
{level=539,pos_x=160,pos_y=286,pre_scene_id=1007,pre_pos_x=160,pre_pos_y=286,},
{level=540,},
{level=541,pos_x=153,pos_y=321,pre_scene_id=1007,pre_pos_x=153,pre_pos_y=321,},
{level=542,},
{level=543,},
{level=544,},
{level=545,},
{level=546,},
{level=547,},
{level=548,},
{level=549,},
{level=550,},
{level=551,},
{level=552,},
{level=553,},
{level=554,},
{level=555,},
{level=556,},
{level=557,},
{level=558,},
{level=559,pos_x=167,pos_y=343,pre_scene_id=1007,pre_pos_x=167,pre_pos_y=343,},
{level=560,},
{level=561,},
{level=562,},
{level=563,pos_x=169,pos_y=377,pre_scene_id=1007,pre_pos_x=169,pre_pos_y=377,},
{level=564,},
{level=565,},
{level=566,},
{level=567,},
{level=568,},
{level=569,},
{level=570,},
{level=571,pos_x=238,pos_y=388,pre_scene_id=1007,pre_pos_x=238,pre_pos_y=388,},
{level=572,},
{level=573,},
{level=574,},
{level=575,},
{level=576,},
{level=577,},
{level=578,},
{level=579,},
{level=580,},
{level=581,pos_x=57,pos_y=332,pre_pos_x=57,pre_pos_y=332,},
{level=582,},
{level=583,},
{level=584,},
{level=585,},
{level=586,},
{level=587,},
{level=588,},
{level=589,},
{level=590,},
{level=591,pos_x=61,pos_y=263,pre_pos_x=61,pre_pos_y=263,},
{level=592,},
{level=593,},
{level=594,},
{level=595,},
{level=596,},
{level=597,},
{level=598,},
{level=599,},
{level=600,hang_monster_id=10501,hang_monster_id_pos_x=204,hang_monster_id_pos_y=152,},
{level=601,},
{level=602,},
{level=603,},
{level=604,},
{level=605,},
{level=606,},
{level=607,},
{level=608,},
{level=609,},
{level=610,hang_monster_id=10501,pos_x=83,pos_y=167,pre_pos_x=83,pre_pos_y=167,hang_monster_id_pos_x=204,hang_monster_id_pos_y=152,},
{level=611,},
{level=612,},
{level=613,},
{level=614,},
{level=615,},
{level=616,hang_monster_id=10501,pos_x=101,pos_y=159,pre_pos_x=101,pre_pos_y=159,hang_monster_id_pos_x=204,hang_monster_id_pos_y=152,},
{level=617,},
{level=618,},
{level=619,},
{level=620,hang_monster_id=10502,hang_monster_id_pos_x=196,hang_monster_id_pos_y=100,},
{level=621,hang_monster_id=10502,pos_x=188,pos_y=154,pre_pos_x=188,pre_pos_y=154,hang_monster_id_pos_x=196,hang_monster_id_pos_y=100,},
{level=622,},
{level=623,},
{level=624,},
{level=625,},
{level=626,},
{level=627,},
{level=628,},
{level=629,},
{level=630,},
{level=631,},
{level=632,},
{level=633,},
{level=634,},
{level=635,},
{level=636,hang_monster_id=10502,pos_x=182,pos_y=187,pre_pos_x=182,pre_pos_y=187,hang_monster_id_pos_x=196,hang_monster_id_pos_y=100,},
{level=637,},
{level=638,},
{level=639,},
{level=640,hang_monster_id=10504,hang_monster_id_pos_x=22,hang_monster_id_pos_y=111,},
{level=641,},
{level=642,},
{level=643,},
{level=644,},
{level=645,},
{level=646,hang_monster_id=10504,pos_x=188,pos_y=208,pre_pos_x=188,pre_pos_y=208,hang_monster_id_pos_x=22,hang_monster_id_pos_y=111,},
{level=647,},
{level=648,},
{level=649,},
{level=650,},
{level=651,},
{level=652,},
{level=653,},
{level=654,},
{level=655,},
{level=656,},
{level=657,},
{level=658,},
{level=659,hang_monster_id=10504,pos_x=232,pos_y=235,pre_pos_x=232,pre_pos_y=235,hang_monster_id_pos_x=22,hang_monster_id_pos_y=111,},
{level=660,hang_monster_id=10508,hang_monster_id_pos_x=122,hang_monster_id_pos_y=330,},
{level=661,},
{level=662,hang_monster_id=10508,pos_x=282,pos_y=198,pre_pos_x=282,pre_pos_y=198,hang_monster_id_pos_x=122,hang_monster_id_pos_y=330,},
{level=663,},
{level=664,},
{level=665,},
{level=666,},
{level=667,},
{level=668,},
{level=669,},
{level=670,},
{level=671,hang_monster_id=10508,hang_monster_id_pos_x=122,hang_monster_id_pos_y=330,},
{level=672,},
{level=673,},
{level=674,},
{level=675,},
{level=676,},
{level=677,},
{level=678,},
{level=679,},
{level=680,pos_x=267,pos_y=144,pre_pos_x=267,pre_pos_y=144,},
{level=681,},
{level=682,},
{level=683,},
{level=684,},
{level=685,},
{level=686,},
{level=687,},
{level=688,},
{level=689,pos_x=204,pos_y=55,pre_pos_x=204,pre_pos_y=55,},
{level=690,},
{level=691,},
{level=692,},
{level=693,},
{level=694,},
{level=695,},
{level=696,},
{level=697,},
{level=698,},
{level=699,},
{level=700,pos_x=332,pos_y=57,pre_pos_x=332,pre_pos_y=57,},
{level=701,},
{level=702,},
{level=703,},
{level=704,},
{level=705,},
{level=706,},
{level=707,},
{level=708,},
{level=709,},
{level=710,pos_x=354,pos_y=123,pre_pos_x=354,pre_pos_y=123,},
{level=711,pos_x=363,pos_y=163,pre_pos_x=363,pre_pos_y=163,},
{level=712,},
{level=713,},
{level=714,},
{level=715,},
{level=716,},
{level=717,},
{level=718,},
{level=719,},
{level=720,},
{level=721,},
{level=722,},
{level=723,},
{level=724,},
{level=725,},
{level=726,},
{level=727,},
{level=728,},
{level=729,},
{level=730,pos_x=364,pos_y=227,pre_pos_x=364,pre_pos_y=227,},
{level=731,},
{level=732,},
{level=733,},
{level=734,},
{level=735,},
{level=736,},
{level=737,},
{level=738,},
{level=739,},
{level=740,pos_x=390,pos_y=313,pre_pos_x=390,pre_pos_y=313,},
{level=741,},
{level=742,},
{level=743,},
{level=744,},
{level=745,},
{level=746,},
{level=747,},
{level=748,},
{level=749,},
{level=750,pos_x=297,pos_y=348,pre_pos_x=297,pre_pos_y=348,},
{level=751,},
{level=752,},
{level=753,},
{level=754,},
{level=755,},
{level=756,},
{level=757,},
{level=758,},
{level=759,},
{level=760,},
{level=761,},
{level=762,},
{level=763,},
{level=764,},
{level=765,},
{level=766,},
{level=767,},
{level=768,},
{level=769,},
{level=770,},
{level=771,},
{level=772,},
{level=773,},
{level=774,},
{level=775,},
{level=776,},
{level=777,},
{level=778,},
{level=779,},
{level=780,},
{level=781,},
{level=782,},
{level=783,},
{level=784,},
{level=785,},
{level=786,},
{level=787,},
{level=788,},
{level=789,},
{level=790,},
{level=791,},
{level=792,},
{level=793,},
{level=794,},
{level=795,},
{level=796,},
{level=797,},
{level=798,},
{level=799,},
{level=800,},
{level=801,},
{level=802,},
{level=803,},
{level=804,},
{level=805,},
{level=806,},
{level=807,},
{level=808,},
{level=809,},
{level=810,},
{level=811,},
{level=812,},
{level=813,},
{level=814,},
{level=815,},
{level=816,},
{level=817,},
{level=818,},
{level=819,},
{level=820,},
{level=821,},
{level=822,},
{level=823,},
{level=824,},
{level=825,},
{level=826,},
{level=827,},
{level=828,},
{level=829,},
{level=830,},
{level=831,},
{level=832,},
{level=833,},
{level=834,},
{level=835,},
{level=836,},
{level=837,},
{level=838,},
{level=839,},
{level=840,},
{level=841,},
{level=842,},
{level=843,},
{level=844,},
{level=845,},
{level=846,},
{level=847,},
{level=848,},
{level=849,},
{level=850,},
{level=851,},
{level=852,},
{level=853,},
{level=854,},
{level=855,},
{level=856,},
{level=857,},
{level=858,},
{level=859,},
{level=860,},
{level=861,},
{level=862,},
{level=863,},
{level=864,},
{level=865,},
{level=866,},
{level=867,},
{level=868,},
{level=869,},
{level=870,},
{level=871,},
{level=872,},
{level=873,},
{level=874,},
{level=875,},
{level=876,},
{level=877,},
{level=878,},
{level=879,},
{level=880,},
{level=881,},
{level=882,},
{level=883,},
{level=884,},
{level=885,},
{level=886,},
{level=887,},
{level=888,},
{level=889,},
{level=890,},
{level=891,},
{level=892,},
{level=893,},
{level=894,},
{level=895,},
{level=896,},
{level=897,},
{level=898,},
{level=899,},
{level=900,},
{level=901,},
{level=902,},
{level=903,},
{level=904,},
{level=905,},
{level=906,},
{level=907,},
{level=908,},
{level=909,},
{level=910,},
{level=911,},
{level=912,},
{level=913,},
{level=914,},
{level=915,},
{level=916,},
{level=917,},
{level=918,},
{level=919,},
{level=920,},
{level=921,},
{level=922,},
{level=923,},
{level=924,},
{level=925,},
{level=926,},
{level=927,},
{level=928,},
{level=929,},
{level=930,},
{level=931,},
{level=932,},
{level=933,},
{level=934,},
{level=935,},
{level=936,},
{level=937,},
{level=938,},
{level=939,},
{level=940,},
{level=941,},
{level=942,},
{level=943,},
{level=944,},
{level=945,},
{level=946,},
{level=947,},
{level=948,},
{level=949,},
{level=950,},
{level=951,},
{level=952,},
{level=953,},
{level=954,},
{level=955,},
{level=956,},
{level=957,},
{level=958,},
{level=959,},
{level=960,},
{level=961,},
{level=962,},
{level=963,},
{level=964,},
{level=965,},
{level=966,},
{level=967,},
{level=968,},
{level=969,},
{level=970,},
{level=971,},
{level=972,},
{level=973,},
{level=974,},
{level=975,},
{level=976,},
{level=977,},
{level=978,},
{level=979,},
{level=980,},
{level=981,},
{level=982,},
{level=983,},
{level=984,},
{level=985,},
{level=986,},
{level=987,},
{level=988,},
{level=989,},
{level=990,},
{level=991,},
{level=992,},
{level=993,},
{level=994,},
{level=995,},
{level=996,},
{level=997,},
{level=998,},
{level=999,},
{level=1000,},
{level=1001,},
{level=1002,},
{level=1003,},
{level=1004,},
{level=1005,},
{level=1006,},
{level=1007,},
{level=1008,},
{level=1009,},
{level=1010,},
{level=1011,},
{level=1012,},
{level=1013,},
{level=1014,},
{level=1015,},
{level=1016,},
{level=1017,},
{level=1018,},
{level=1019,},
{level=1020,},
{level=1021,},
{level=1022,},
{level=1023,},
{level=1024,},
{level=1025,},
{level=1026,},
{level=1027,},
{level=1028,},
{level=1029,},
{level=1030,},
{level=1031,},
{level=1032,},
{level=1033,},
{level=1034,},
{level=1035,},
{level=1036,},
{level=1037,},
{level=1038,},
{level=1039,},
{level=1040,},
{level=1041,},
{level=1042,},
{level=1043,},
{level=1044,},
{level=1045,},
{level=1046,},
{level=1047,},
{level=1048,},
{level=1049,},
{level=1050,},
{level=1051,},
{level=1052,},
{level=1053,},
{level=1054,},
{level=1055,},
{level=1056,},
{level=1057,},
{level=1058,},
{level=1059,},
{level=1060,},
{level=1061,},
{level=1062,},
{level=1063,},
{level=1064,},
{level=1065,},
{level=1066,},
{level=1067,},
{level=1068,},
{level=1069,},
{level=1070,},
{level=1071,},
{level=1072,},
{level=1073,},
{level=1074,},
{level=1075,},
{level=1076,},
{level=1077,},
{level=1078,},
{level=1079,},
{level=1080,},
{level=1081,},
{level=1082,},
{level=1083,},
{level=1084,},
{level=1085,},
{level=1086,},
{level=1087,},
{level=1088,},
{level=1089,},
{level=1090,},
{level=1091,},
{level=1092,},
{level=1093,},
{level=1094,},
{level=1095,hang_monster_id=10508,hang_monster_id_pos_x=122,hang_monster_id_pos_y=330,},
{level=1096,},
{level=1097,},
{level=1098,},
{level=1099,},
{level=1100,},
{level=1101,},
{level=1102,},
{level=1103,},
{level=1104,},
{level=1105,},
{level=1106,},
{level=1107,},
{level=1108,},
{level=1109,},
{level=1110,},
{level=1111,},
{level=1112,},
{level=1113,},
{level=1114,},
{level=1115,},
{level=1116,},
{level=1117,},
{level=1118,},
{level=1119,},
{level=1120,},
{level=1121,},
{level=1122,},
{level=1123,},
{level=1124,},
{level=1125,},
{level=1126,},
{level=1127,},
{level=1128,},
{level=1129,},
{level=1130,},
{level=1131,},
{level=1132,},
{level=1133,},
{level=1134,},
{level=1135,},
{level=1136,},
{level=1137,},
{level=1138,},
{level=1139,},
{level=1140,},
{level=1141,},
{level=1142,},
{level=1143,},
{level=1144,},
{level=1145,},
{level=1146,},
{level=1147,},
{level=1148,},
{level=1149,},
{level=1150,},
{level=1151,},
{level=1152,},
{level=1153,},
{level=1154,},
{level=1155,},
{level=1156,},
{level=1157,},
{level=1158,},
{level=1159,},
{level=1160,},
{level=1161,},
{level=1162,},
{level=1163,},
{level=1164,},
{level=1165,},
{level=1166,},
{level=1167,},
{level=1168,},
{level=1169,},
{level=1170,},
{level=1171,},
{level=1172,},
{level=1173,},
{level=1174,},
{level=1175,},
{level=1176,},
{level=1177,},
{level=1178,},
{level=1179,},
{level=1180,},
{level=1181,},
{level=1182,},
{level=1183,},
{level=1184,},
{level=1185,},
{level=1186,},
{level=1187,},
{level=1188,},
{level=1189,},
{level=1190,},
{level=1191,},
{level=1192,},
{level=1193,},
{level=1194,},
{level=1195,},
{level=1196,},
{level=1197,},
{level=1198,},
{level=1199,},
{level=1200,},
{level=1201,},
{level=1202,},
{level=1203,},
{level=1204,},
{level=1205,},
{level=1206,},
{level=1207,},
{level=1208,},
{level=1209,},
{level=1210,},
{level=1211,},
{level=1212,},
{level=1213,},
{level=1214,},
{level=1215,},
{level=1216,},
{level=1217,},
{level=1218,},
{level=1219,},
{level=1220,},
{level=1221,},
{level=1222,},
{level=1223,},
{level=1224,},
{level=1225,},
{level=1226,},
{level=1227,},
{level=1228,},
{level=1229,},
{level=1230,},
{level=1231,},
{level=1232,},
{level=1233,},
{level=1234,},
{level=1235,},
{level=1236,},
{level=1237,},
{level=1238,},
{level=1239,},
{level=1240,},
{level=1241,},
{level=1242,},
{level=1243,},
{level=1244,},
{level=1245,},
{level=1246,},
{level=1247,},
{level=1248,},
{level=1249,},
{level=1250,},
{level=1251,},
{level=1252,},
{level=1253,},
{level=1254,},
{level=1255,},
{level=1256,},
{level=1257,},
{level=1258,},
{level=1259,},
{level=1260,},
{level=1261,},
{level=1262,},
{level=1263,},
{level=1264,},
{level=1265,},
{level=1266,},
{level=1267,},
{level=1268,},
{level=1269,},
{level=1270,},
{level=1271,},
{level=1272,},
{level=1273,},
{level=1274,},
{level=1275,},
{level=1276,},
{level=1277,},
{level=1278,},
{level=1279,},
{level=1280,},
{level=1281,},
{level=1282,},
{level=1283,},
{level=1284,},
{level=1285,},
{level=1286,},
{level=1287,},
{level=1288,},
{level=1289,},
{level=1290,},
{level=1291,},
{level=1292,},
{level=1293,},
{level=1294,},
{level=1295,},
{level=1296,},
{level=1297,},
{level=1298,},
{level=1299,},
{level=1300,},
{level=1301,},
{level=1302,},
{level=1303,},
{level=1304,},
{level=1305,},
{level=1306,},
{level=1307,},
{level=1308,},
{level=1309,},
{level=1310,},
{level=1311,},
{level=1312,},
{level=1313,},
{level=1314,},
{level=1315,},
{level=1316,},
{level=1317,},
{level=1318,},
{level=1319,},
{level=1320,},
{level=1321,},
{level=1322,},
{level=1323,},
{level=1324,},
{level=1325,},
{level=1326,},
{level=1327,},
{level=1328,},
{level=1329,hang_monster_id=10907,hang_monster_id_scene_id=0,hang_monster_id_pos_x=0,hang_monster_id_pos_y=0,},
{level=1330,},
{level=1331,},
{level=1332,},
{level=1333,},
{level=1334,},
{level=1335,},
{level=1336,},
{level=1337,},
{level=1338,},
{level=1339,},
{level=1340,},
{level=1341,},
{level=1342,},
{level=1343,},
{level=1344,},
{level=1345,},
{level=1346,},
{level=1347,},
{level=1348,},
{level=1349,},
{level=1350,},
{level=1351,},
{level=1352,},
{level=1353,},
{level=1354,},
{level=1355,},
{level=1356,},
{level=1357,},
{level=1358,},
{level=1359,},
{level=1360,},
{level=1361,},
{level=1362,},
{level=1363,},
{level=1364,},
{level=1365,},
{level=1366,},
{level=1367,},
{level=1368,},
{level=1369,},
{level=1370,},
{level=1371,},
{level=1372,},
{level=1373,},
{level=1374,},
{level=1375,},
{level=1376,},
{level=1377,},
{level=1378,},
{level=1379,},
{level=1380,},
{level=1381,},
{level=1382,},
{level=1383,},
{level=1384,},
{level=1385,},
{level=1386,},
{level=1387,},
{level=1388,},
{level=1389,},
{level=1390,},
{level=1391,},
{level=1392,},
{level=1393,},
{level=1394,},
{level=1395,},
{level=1396,},
{level=1397,},
{level=1398,},
{level=1399,},
{level=1400,},
{level=1401,},
{level=1402,},
{level=1403,},
{level=1404,},
{level=1405,},
{level=1406,},
{level=1407,},
{level=1408,},
{level=1409,},
{level=1410,},
{level=1411,},
{level=1412,},
{level=1413,},
{level=1414,},
{level=1415,},
{level=1416,},
{level=1417,},
{level=1418,},
{level=1419,},
{level=1420,},
{level=1421,},
{level=1422,},
{level=1423,},
{level=1424,},
{level=1425,},
{level=1426,},
{level=1427,},
{level=1428,},
{level=1429,},
{level=1430,},
{level=1431,},
{level=1432,},
{level=1433,},
{level=1434,},
{level=1435,},
{level=1436,},
{level=1437,},
{level=1438,},
{level=1439,},
{level=1440,},
{level=1441,},
{level=1442,},
{level=1443,},
{level=1444,},
{level=1445,},
{level=1446,},
{level=1447,hang_monster_id=10501,hang_monster_id_pos_x=204,hang_monster_id_pos_y=152,},
{level=1448,},
{level=1449,},
{level=1450,},
{level=1451,},
{level=1452,},
{level=1453,},
{level=1454,},
{level=1455,},
{level=1456,},
{level=1457,},
{level=1458,},
{level=1459,},
{level=1460,},
{level=1461,},
{level=1462,},
{level=1463,},
{level=1464,},
{level=1465,},
{level=1466,},
{level=1467,},
{level=1468,hang_monster_id=10504,hang_monster_id_pos_x=22,hang_monster_id_pos_y=111,},
{level=1469,},
{level=1470,},
{level=1471,},
{level=1472,},
{level=1473,},
{level=1474,},
{level=1475,},
{level=1476,},
{level=1477,},
{level=1478,},
{level=1479,},
{level=1480,},
{level=1481,},
{level=1482,hang_monster_id=10505,hang_monster_id_pos_x=61,hang_monster_id_pos_y=242,},
{level=1483,},
{level=1484,},
{level=1485,},
{level=1486,},
{level=1487,},
{level=1488,},
{level=1489,},
{level=1490,},
{level=1491,},
{level=1492,},
{level=1493,},
{level=1494,},
{level=1495,},
{level=1496,},
{level=1497,},
{level=1498,},
{level=1499,},
{level=1500,},
{level=1501,},
{level=1502,},
{level=1503,},
{level=1504,},
{level=1505,},
{level=1506,},
{level=1507,},
{level=1508,},
{level=1509,},
{level=1510,},
{level=1511,},
{level=1512,},
{level=1513,},
{level=1514,},
{level=1515,},
{level=1516,},
{level=1517,},
{level=1518,},
{level=1519,},
{level=1520,},
{level=1521,},
{level=1522,},
{level=1523,},
{level=1524,},
{level=1525,},
{level=1526,},
{level=1527,},
{level=1528,},
{level=1529,},
{level=1530,},
{level=1531,},
{level=1532,},
{level=1533,},
{level=1534,},
{level=1535,},
{level=1536,},
{level=1537,},
{level=1538,},
{level=1539,},
{level=1540,},
{level=1541,},
{level=1542,},
{level=1543,},
{level=1544,},
{level=1545,},
{level=1546,},
{level=1547,},
{level=1548,},
{level=1549,},
{level=1550,},
{level=1551,},
{level=1552,},
{level=1553,},
{level=1554,},
{level=1555,},
{level=1556,},
{level=1557,},
{level=1558,},
{level=1559,},
{level=1560,},
{level=1561,},
{level=1562,},
{level=1563,},
{level=1564,},
{level=1565,},
{level=1566,},
{level=1567,},
{level=1568,},
{level=1569,},
{level=1570,},
{level=1571,},
{level=1572,},
{level=1573,},
{level=1574,},
{level=1575,},
{level=1576,},
{level=1577,},
{level=1578,},
{level=1579,},
{level=1580,},
{level=1581,},
{level=1582,},
{level=1583,},
{level=1584,},
{level=1585,},
{level=1586,},
{level=1587,},
{level=1588,},
{level=1589,},
{level=1590,},
{level=1591,},
{level=1592,},
{level=1593,},
{level=1594,},
{level=1595,},
{level=1596,},
{level=1597,},
{level=1598,},
{level=1599,},
{level=1600,},
{level=1601,},
{level=1602,},
{level=1603,},
{level=1604,},
{level=1605,},
{level=1606,},
{level=1607,},
{level=1608,},
{level=1609,},
{level=1610,},
{level=1611,},
{level=1612,},
{level=1613,},
{level=1614,},
{level=1615,},
{level=1616,},
{level=1617,},
{level=1618,},
{level=1619,},
{level=1620,},
{level=1621,},
{level=1622,},
{level=1623,},
{level=1624,},
{level=1625,},
{level=1626,},
{level=1627,},
{level=1628,},
{level=1629,},
{level=1630,},
{level=1631,},
{level=1632,},
{level=1633,},
{level=1634,},
{level=1635,},
{level=1636,},
{level=1637,},
{level=1638,},
{level=1639,},
{level=1640,},
{level=1641,},
{level=1642,},
{level=1643,},
{level=1644,},
{level=1645,},
{level=1646,},
{level=1647,},
{level=1648,},
{level=1649,},
{level=1650,},
{level=1651,},
{level=1652,},
{level=1653,},
{level=1654,},
{level=1655,},
{level=1656,},
{level=1657,},
{level=1658,},
{level=1659,},
{level=1660,hang_monster_id=10502,hang_monster_id_pos_x=196,hang_monster_id_pos_y=100,},
{level=1661,},
{level=1662,},
{level=1663,},
{level=1664,},
{level=1665,},
{level=1666,},
{level=1667,},
{level=1668,},
{level=1669,},
{level=1670,},
{level=1671,},
{level=1672,},
{level=1673,},
{level=1674,},
{level=1675,},
{level=1676,},
{level=1677,},
{level=1678,},
{level=1679,},
{level=1680,},
{level=1681,},
{level=1682,},
{level=1683,},
{level=1684,},
{level=1685,},
{level=1686,},
{level=1687,},
{level=1688,},
{level=1689,},
{level=1690,},
{level=1691,},
{level=1692,},
{level=1693,},
{level=1694,},
{level=1695,},
{level=1696,},
{level=1697,},
{level=1698,},
{level=1699,},
{level=1700,},
{level=1701,},
{level=1702,},
{level=1703,},
{level=1704,},
{level=1705,},
{level=1706,},
{level=1707,},
{level=1708,},
{level=1709,},
{level=1710,},
{level=1711,},
{level=1712,},
{level=1713,},
{level=1714,},
{level=1715,},
{level=1716,},
{level=1717,},
{level=1718,},
{level=1719,},
{level=1720,},
{level=1721,},
{level=1722,},
{level=1723,},
{level=1724,},
{level=1725,},
{level=1726,},
{level=1727,},
{level=1728,},
{level=1729,},
{level=1730,},
{level=1731,},
{level=1732,},
{level=1733,},
{level=1734,},
{level=1735,},
{level=1736,},
{level=1737,},
{level=1738,},
{level=1739,},
{level=1740,},
{level=1741,},
{level=1742,},
{level=1743,},
{level=1744,},
{level=1745,},
{level=1746,},
{level=1747,},
{level=1748,},
{level=1749,},
{level=1750,},
{level=1751,},
{level=1752,},
{level=1753,},
{level=1754,},
{level=1755,},
{level=1756,},
{level=1757,},
{level=1758,},
{level=1759,},
{level=1760,},
{level=1761,},
{level=1762,},
{level=1763,},
{level=1764,},
{level=1765,},
{level=1766,},
{level=1767,},
{level=1768,},
{level=1769,},
{level=1770,},
{level=1771,},
{level=1772,},
{level=1773,},
{level=1774,},
{level=1775,},
{level=1776,},
{level=1777,},
{level=1778,},
{level=1779,},
{level=1780,},
{level=1781,},
{level=1782,},
{level=1783,},
{level=1784,},
{level=1785,},
{level=1786,},
{level=1787,},
{level=1788,},
{level=1789,},
{level=1790,},
{level=1791,},
{level=1792,},
{level=1793,},
{level=1794,},
{level=1795,},
{level=1796,},
{level=1797,},
{level=1798,},
{level=1799,},
{level=1800,},
{level=1801,},
{level=1802,},
{level=1803,},
{level=1804,},
{level=1805,},
{level=1806,},
{level=1807,},
{level=1808,},
{level=1809,},
{level=1810,},
{level=1811,},
{level=1812,},
{level=1813,},
{level=1814,},
{level=1815,},
{level=1816,},
{level=1817,},
{level=1818,},
{level=1819,},
{level=1820,},
{level=1821,},
{level=1822,},
{level=1823,},
{level=1824,},
{level=1825,},
{level=1826,},
{level=1827,},
{level=1828,},
{level=1829,},
{level=1830,},
{level=1831,},
{level=1832,},
{level=1833,},
{level=1834,},
{level=1835,},
{level=1836,},
{level=1837,},
{level=1838,},
{level=1839,},
{level=1840,},
{level=1841,},
{level=1842,},
{level=1843,},
{level=1844,},
{level=1845,},
{level=1846,},
{level=1847,},
{level=1848,},
{level=1849,},
{level=1850,},
{level=1851,},
{level=1852,},
{level=1853,},
{level=1854,},
{level=1855,},
{level=1856,},
{level=1857,},
{level=1858,},
{level=1859,},
{level=1860,},
{level=1861,},
{level=1862,},
{level=1863,},
{level=1864,},
{level=1865,},
{level=1866,},
{level=1867,},
{level=1868,},
{level=1869,},
{level=1870,},
{level=1871,},
{level=1872,},
{level=1873,},
{level=1874,},
{level=1875,},
{level=1876,},
{level=1877,},
{level=1878,},
{level=1879,},
{level=1880,},
{level=1881,},
{level=1882,},
{level=1883,},
{level=1884,},
{level=1885,},
{level=1886,},
{level=1887,},
{level=1888,},
{level=1889,},
{level=1890,},
{level=1891,},
{level=1892,},
{level=1893,},
{level=1894,},
{level=1895,},
{level=1896,},
{level=1897,},
{level=1898,},
{level=1899,},
{level=1900,},
{level=1901,},
{level=1902,},
{level=1903,},
{level=1904,},
{level=1905,},
{level=1906,},
{level=1907,},
{level=1908,},
{level=1909,},
{level=1910,},
{level=1911,},
{level=1912,},
{level=1913,},
{level=1914,},
{level=1915,},
{level=1916,},
{level=1917,},
{level=1918,},
{level=1919,},
{level=1920,},
{level=1921,},
{level=1922,},
{level=1923,},
{level=1924,},
{level=1925,},
{level=1926,},
{level=1927,},
{level=1928,},
{level=1929,},
{level=1930,},
{level=1931,},
{level=1932,},
{level=1933,},
{level=1934,},
{level=1935,},
{level=1936,},
{level=1937,},
{level=1938,},
{level=1939,},
{level=1940,},
{level=1941,},
{level=1942,},
{level=1943,},
{level=1944,},
{level=1945,},
{level=1946,},
{level=1947,},
{level=1948,},
{level=1949,},
{level=1950,},
{level=1951,},
{level=1952,},
{level=1953,},
{level=1954,},
{level=1955,},
{level=1956,},
{level=1957,},
{level=1958,},
{level=1959,},
{level=1960,},
{level=1961,},
{level=1962,},
{level=1963,},
{level=1964,},
{level=1965,},
{level=1966,},
{level=1967,},
{level=1968,},
{level=1969,},
{level=1970,},
{level=1971,},
{level=1972,},
{level=1973,},
{level=1974,},
{level=1975,},
{level=1976,},
{level=1977,},
{level=1978,},
{level=1979,},
{level=1980,},
{level=1981,},
{level=1982,},
{level=1983,},
{level=1984,},
{level=1985,},
{level=1986,},
{level=1987,},
{level=1988,},
{level=1989,},
{level=1990,},
{level=1991,},
{level=1992,},
{level=1993,},
{level=1994,},
{level=1995,},
{level=1996,},
{level=1997,},
{level=1998,},
{level=1999,},
{level=2000,}
},

rest_reward_meta_table_map={
[1469]=1468,	-- depth:1
[1470]=1469,	-- depth:2
[1471]=1470,	-- depth:3
[1472]=1471,	-- depth:4
[1473]=1472,	-- depth:5
[1474]=1473,	-- depth:6
[1475]=1474,	-- depth:7
[1476]=1475,	-- depth:8
[1477]=1476,	-- depth:9
[1478]=1477,	-- depth:10
[1479]=1478,	-- depth:11
[1480]=1482,	-- depth:1
[1481]=1480,	-- depth:2
[1483]=1481,	-- depth:3
[1484]=1483,	-- depth:4
[1485]=1484,	-- depth:5
[1467]=1479,	-- depth:12
[1486]=1485,	-- depth:6
[1466]=1467,	-- depth:13
[1464]=1466,	-- depth:14
[1448]=1447,	-- depth:1
[1449]=1448,	-- depth:2
[1450]=1449,	-- depth:3
[1451]=1450,	-- depth:4
[1452]=1451,	-- depth:5
[1453]=1452,	-- depth:6
[1454]=1453,	-- depth:7
[1455]=1454,	-- depth:8
[1456]=1455,	-- depth:9
[1457]=1456,	-- depth:10
[1458]=1457,	-- depth:11
[1459]=1458,	-- depth:12
[1460]=1464,	-- depth:15
[1461]=1460,	-- depth:16
[1462]=1461,	-- depth:17
[1463]=1462,	-- depth:18
[1465]=1463,	-- depth:19
[1487]=1486,	-- depth:7
[1488]=1487,	-- depth:8
[1489]=1488,	-- depth:9
[1652]=1459,	-- depth:13
[1653]=1652,	-- depth:14
[1654]=1653,	-- depth:15
[1655]=1654,	-- depth:16
[1656]=1655,	-- depth:17
[1657]=1656,	-- depth:18
[1658]=1657,	-- depth:19
[1659]=1658,	-- depth:20
[1661]=1660,	-- depth:1
[1662]=1661,	-- depth:2
[1663]=1662,	-- depth:3
[1664]=1663,	-- depth:4
[1665]=1664,	-- depth:5
[1666]=1665,	-- depth:6
[1667]=1666,	-- depth:7
[1668]=1667,	-- depth:8
[1651]=1659,	-- depth:21
[1650]=1651,	-- depth:22
[1649]=1650,	-- depth:23
[1490]=1489,	-- depth:10
[1491]=1490,	-- depth:11
[1492]=1491,	-- depth:12
[1493]=1492,	-- depth:13
[1494]=1493,	-- depth:14
[1495]=1494,	-- depth:15
[1496]=1495,	-- depth:16
[1497]=1496,	-- depth:17
[1446]=1649,	-- depth:24
[1498]=1497,	-- depth:18
[1640]=1446,	-- depth:25
[1641]=1640,	-- depth:26
[1642]=1641,	-- depth:27
[1643]=1642,	-- depth:28
[1644]=1643,	-- depth:29
[1645]=1644,	-- depth:30
[1646]=1645,	-- depth:31
[1647]=1646,	-- depth:32
[1499]=1498,	-- depth:19
[1669]=1668,	-- depth:9
[1445]=1647,	-- depth:33
[1443]=1445,	-- depth:34
[1073]=1465,	-- depth:20
[1072]=1073,	-- depth:21
[1071]=1072,	-- depth:22
[1070]=1071,	-- depth:23
[1069]=1070,	-- depth:24
[1068]=1069,	-- depth:25
[1067]=1068,	-- depth:26
[1066]=1067,	-- depth:27
[1065]=1066,	-- depth:28
[1064]=1065,	-- depth:29
[1063]=1064,	-- depth:30
[1062]=1063,	-- depth:31
[1061]=1062,	-- depth:32
[1060]=1061,	-- depth:33
[1059]=1669,	-- depth:10
[1058]=1059,	-- depth:11
[1057]=1058,	-- depth:12
[1074]=1060,	-- depth:34
[1056]=1057,	-- depth:13
[1075]=1074,	-- depth:35
[1077]=1075,	-- depth:36
[1094]=1095,	-- depth:1
[1093]=1094,	-- depth:2
[1092]=1093,	-- depth:3
[1091]=1092,	-- depth:4
[1090]=1091,	-- depth:5
[1089]=1090,	-- depth:6
[1088]=1089,	-- depth:7
[1087]=1088,	-- depth:8
[1086]=1087,	-- depth:9
[1085]=1086,	-- depth:10
[1084]=1085,	-- depth:11
[1083]=1084,	-- depth:12
[1082]=1083,	-- depth:13
[1081]=1082,	-- depth:14
[1080]=1081,	-- depth:15
[1079]=1077,	-- depth:37
[1078]=1079,	-- depth:38
[1076]=1078,	-- depth:39
[1055]=1056,	-- depth:14
[1054]=1055,	-- depth:15
[1053]=1054,	-- depth:16
[1030]=1443,	-- depth:35
[1029]=1030,	-- depth:36
[1028]=1029,	-- depth:37
[1027]=1028,	-- depth:38
[1026]=1027,	-- depth:39
[1025]=1026,	-- depth:40
[1024]=1025,	-- depth:41
[1023]=1024,	-- depth:42
[1022]=1023,	-- depth:43
[1021]=1022,	-- depth:44
[1020]=1021,	-- depth:45
[1097]=1080,	-- depth:16
[1098]=1097,	-- depth:17
[1099]=1098,	-- depth:18
[1440]=1020,	-- depth:46
[1441]=1440,	-- depth:47
[1442]=1441,	-- depth:48
[1031]=1442,	-- depth:49
[1032]=1031,	-- depth:50
[1033]=1032,	-- depth:51
[1034]=1033,	-- depth:52
[1052]=1053,	-- depth:17
[1051]=1052,	-- depth:18
[1050]=1051,	-- depth:19
[1049]=1050,	-- depth:20
[1048]=1049,	-- depth:21
[1047]=1048,	-- depth:22
[1046]=1047,	-- depth:23
[1045]=1046,	-- depth:24
[1444]=1034,	-- depth:53
[1044]=1045,	-- depth:25
[1042]=1044,	-- depth:26
[1041]=1042,	-- depth:27
[1040]=1041,	-- depth:28
[1039]=1444,	-- depth:54
[1038]=1039,	-- depth:55
[1037]=1038,	-- depth:56
[1036]=1037,	-- depth:57
[1035]=1036,	-- depth:58
[1043]=1040,	-- depth:29
[1670]=1043,	-- depth:30
[1648]=1035,	-- depth:59
[879]=1499,	-- depth:20
[1860]=1648,	-- depth:60
[1861]=1860,	-- depth:61
[1862]=1861,	-- depth:62
[1863]=1862,	-- depth:63
[1864]=1863,	-- depth:64
[1865]=1864,	-- depth:65
[1866]=1865,	-- depth:66
[1867]=1866,	-- depth:67
[1868]=1867,	-- depth:68
[1869]=1868,	-- depth:69
[1870]=1869,	-- depth:70
[1871]=1870,	-- depth:71
[1872]=1871,	-- depth:72
[1873]=1872,	-- depth:73
[1874]=1873,	-- depth:74
[1875]=1874,	-- depth:75
[1876]=1875,	-- depth:76
[1719]=1099,	-- depth:19
[1718]=1719,	-- depth:20
[1717]=1718,	-- depth:21
[1716]=1717,	-- depth:22
[1699]=1076,	-- depth:40
[1700]=1716,	-- depth:23
[1701]=1700,	-- depth:24
[1671]=1670,	-- depth:31
[1702]=1701,	-- depth:25
[1703]=1702,	-- depth:26
[1704]=1703,	-- depth:27
[1705]=1704,	-- depth:28
[1877]=1876,	-- depth:77
[1706]=1705,	-- depth:29
[1708]=1706,	-- depth:30
[1709]=1708,	-- depth:31
[1710]=1709,	-- depth:32
[1711]=1710,	-- depth:33
[1712]=1711,	-- depth:34
[1713]=1712,	-- depth:35
[1714]=1713,	-- depth:36
[1715]=1714,	-- depth:37
[1707]=1715,	-- depth:38
[1698]=1699,	-- depth:41
[1878]=1877,	-- depth:78
[1880]=1698,	-- depth:42
[1903]=879,	-- depth:21
[1904]=1903,	-- depth:22
[1905]=1904,	-- depth:23
[1906]=1905,	-- depth:24
[1907]=1906,	-- depth:25
[1908]=1907,	-- depth:26
[1909]=1908,	-- depth:27
[1910]=1909,	-- depth:28
[1911]=1910,	-- depth:29
[1912]=1911,	-- depth:30
[1913]=1912,	-- depth:31
[1914]=1913,	-- depth:32
[1915]=1914,	-- depth:33
[1916]=1915,	-- depth:34
[1917]=1916,	-- depth:35
[1918]=1917,	-- depth:36
[1919]=1918,	-- depth:37
[1902]=1919,	-- depth:38
[1901]=1902,	-- depth:39
[1900]=1901,	-- depth:40
[1899]=1880,	-- depth:43
[1881]=1899,	-- depth:44
[1882]=1881,	-- depth:45
[1883]=1882,	-- depth:46
[1884]=1883,	-- depth:47
[1885]=1884,	-- depth:48
[1886]=1885,	-- depth:49
[1887]=1886,	-- depth:50
[1888]=1887,	-- depth:51
[1879]=1878,	-- depth:79
[1889]=1888,	-- depth:52
[1891]=1889,	-- depth:53
[1892]=1891,	-- depth:54
[1893]=1892,	-- depth:55
[1894]=1893,	-- depth:56
[1895]=1894,	-- depth:57
[1896]=1895,	-- depth:58
[1897]=1896,	-- depth:59
[1898]=1897,	-- depth:60
[1890]=1898,	-- depth:61
[1697]=1890,	-- depth:62
[1096]=1707,	-- depth:39
[1695]=1697,	-- depth:63
[845]=1695,	-- depth:64
[844]=845,	-- depth:65
[843]=844,	-- depth:66
[842]=843,	-- depth:67
[841]=842,	-- depth:68
[840]=841,	-- depth:69
[839]=1879,	-- depth:80
[838]=839,	-- depth:81
[837]=838,	-- depth:82
[836]=837,	-- depth:83
[835]=836,	-- depth:84
[834]=835,	-- depth:85
[833]=834,	-- depth:86
[846]=840,	-- depth:70
[832]=833,	-- depth:87
[830]=832,	-- depth:88
[829]=830,	-- depth:89
[828]=829,	-- depth:90
[827]=828,	-- depth:91
[826]=827,	-- depth:92
[825]=826,	-- depth:93
[824]=825,	-- depth:94
[823]=824,	-- depth:95
[822]=823,	-- depth:96
[821]=822,	-- depth:97
[820]=821,	-- depth:98
[1672]=1671,	-- depth:32
[1673]=1672,	-- depth:33
[831]=820,	-- depth:99
[847]=846,	-- depth:71
[848]=847,	-- depth:72
[849]=848,	-- depth:73
[878]=1900,	-- depth:41
[877]=878,	-- depth:42
[876]=877,	-- depth:43
[875]=876,	-- depth:44
[874]=875,	-- depth:45
[873]=874,	-- depth:46
[872]=873,	-- depth:47
[871]=872,	-- depth:48
[870]=871,	-- depth:49
[869]=870,	-- depth:50
[868]=869,	-- depth:51
[867]=868,	-- depth:52
[866]=867,	-- depth:53
[865]=866,	-- depth:54
[864]=865,	-- depth:55
[863]=864,	-- depth:56
[862]=863,	-- depth:57
[861]=862,	-- depth:58
[860]=861,	-- depth:59
[859]=849,	-- depth:74
[858]=859,	-- depth:75
[857]=858,	-- depth:76
[856]=857,	-- depth:77
[855]=856,	-- depth:78
[854]=855,	-- depth:79
[853]=854,	-- depth:80
[852]=853,	-- depth:81
[851]=852,	-- depth:82
[850]=851,	-- depth:83
[1674]=1673,	-- depth:34
[1696]=850,	-- depth:84
[1675]=1674,	-- depth:35
[1677]=1675,	-- depth:36
[1676]=1677,	-- depth:37
[1690]=1696,	-- depth:85
[1691]=1690,	-- depth:86
[1692]=1691,	-- depth:87
[1693]=1692,	-- depth:88
[1694]=1693,	-- depth:89
[1688]=1694,	-- depth:90
[1687]=1688,	-- depth:91
[1689]=1687,	-- depth:92
[1685]=1689,	-- depth:93
[1684]=1685,	-- depth:94
[1683]=1684,	-- depth:95
[1682]=1683,	-- depth:96
[1681]=1682,	-- depth:97
[1680]=1681,	-- depth:98
[1679]=1676,	-- depth:38
[1678]=1679,	-- depth:39
[1686]=1680,	-- depth:99
[1323]=1329,	-- depth:1
[1325]=1323,	-- depth:2
[1326]=1325,	-- depth:3
[1327]=1326,	-- depth:4
[1328]=1327,	-- depth:5
[1330]=1328,	-- depth:6
[1339]=1330,	-- depth:7
[1332]=1339,	-- depth:8
[1333]=1332,	-- depth:9
[1334]=1333,	-- depth:10
[1335]=1334,	-- depth:11
[1336]=1335,	-- depth:12
[1337]=1336,	-- depth:13
[1338]=1337,	-- depth:14
[1322]=1338,	-- depth:15
[1340]=1322,	-- depth:16
[1331]=1340,	-- depth:17
[1321]=1331,	-- depth:18
[1314]=1321,	-- depth:19
[1319]=1314,	-- depth:20
[1296]=1319,	-- depth:21
[1297]=1296,	-- depth:22
[1341]=1297,	-- depth:23
[1298]=1341,	-- depth:24
[1299]=1298,	-- depth:25
[1300]=1299,	-- depth:26
[1301]=1300,	-- depth:27
[1302]=1301,	-- depth:28
[1303]=1302,	-- depth:29
[1304]=1303,	-- depth:30
[1305]=1304,	-- depth:31
[1306]=1305,	-- depth:32
[1307]=1306,	-- depth:33
[1308]=1307,	-- depth:34
[1309]=1308,	-- depth:35
[1310]=1309,	-- depth:36
[1311]=1310,	-- depth:37
[1312]=1311,	-- depth:38
[1313]=1312,	-- depth:39
[1315]=1313,	-- depth:40
[1316]=1315,	-- depth:41
[1317]=1316,	-- depth:42
[1318]=1317,	-- depth:43
[1320]=1318,	-- depth:44
[1324]=1320,	-- depth:45
[1294]=1324,	-- depth:46
[1267]=1294,	-- depth:47
[1266]=1267,	-- depth:48
[1265]=1266,	-- depth:49
[1264]=1265,	-- depth:50
[1263]=1264,	-- depth:51
[1262]=1263,	-- depth:52
[1261]=1262,	-- depth:53
[1260]=1261,	-- depth:54
[1259]=1260,	-- depth:55
[1258]=1259,	-- depth:56
[1257]=1258,	-- depth:57
[1256]=1257,	-- depth:58
[1255]=1256,	-- depth:59
[1254]=1255,	-- depth:60
[1253]=1254,	-- depth:61
[1252]=1253,	-- depth:62
[1251]=1252,	-- depth:63
[1250]=1251,	-- depth:64
[1249]=1250,	-- depth:65
[1248]=1249,	-- depth:66
[1342]=1248,	-- depth:67
[1268]=1342,	-- depth:68
[1269]=1268,	-- depth:69
[1270]=1269,	-- depth:70
[1271]=1270,	-- depth:71
[1293]=1271,	-- depth:72
[1292]=1293,	-- depth:73
[1291]=1292,	-- depth:74
[1290]=1291,	-- depth:75
[1289]=1290,	-- depth:76
[1288]=1289,	-- depth:77
[1287]=1288,	-- depth:78
[1286]=1287,	-- depth:79
[1285]=1286,	-- depth:80
[1284]=1285,	-- depth:81
[1295]=1284,	-- depth:82
[1283]=1295,	-- depth:83
[1281]=1283,	-- depth:84
[1280]=1281,	-- depth:85
[1279]=1280,	-- depth:86
[1278]=1279,	-- depth:87
[1277]=1278,	-- depth:88
[1276]=1277,	-- depth:89
[1275]=1276,	-- depth:90
[1274]=1275,	-- depth:91
[1273]=1274,	-- depth:92
[1272]=1273,	-- depth:93
[1282]=1272,	-- depth:94
[1343]=1282,	-- depth:95
[1433]=1343,	-- depth:96
[1345]=1433,	-- depth:97
[1414]=1345,	-- depth:98
[1413]=1414,	-- depth:99
[1412]=1413,	-- depth:100
[1411]=1412,	-- depth:101
[1410]=1411,	-- depth:102
[1409]=1410,	-- depth:103
[1408]=1409,	-- depth:104
[1407]=1408,	-- depth:105
[1406]=1407,	-- depth:106
[1415]=1406,	-- depth:107
[1405]=1415,	-- depth:108
[1403]=1405,	-- depth:109
[1402]=1403,	-- depth:110
[1401]=1402,	-- depth:111
[1400]=1401,	-- depth:112
[1399]=1400,	-- depth:113
[1398]=1399,	-- depth:114
[1397]=1398,	-- depth:115
[1396]=1397,	-- depth:116
[1395]=1396,	-- depth:117
[1404]=1395,	-- depth:118
[1416]=1404,	-- depth:119
[1417]=1416,	-- depth:120
[1418]=1417,	-- depth:121
[1439]=1418,	-- depth:122
[1438]=1439,	-- depth:123
[1437]=1438,	-- depth:124
[1436]=1437,	-- depth:125
[1435]=1436,	-- depth:126
[1434]=1435,	-- depth:127
[1247]=1434,	-- depth:128
[1432]=1247,	-- depth:129
[1431]=1432,	-- depth:130
[1430]=1431,	-- depth:131
[1429]=1430,	-- depth:132
[1428]=1429,	-- depth:133
[1427]=1428,	-- depth:134
[1426]=1427,	-- depth:135
[1425]=1426,	-- depth:136
[1424]=1425,	-- depth:137
[1423]=1424,	-- depth:138
[1422]=1423,	-- depth:139
[1421]=1422,	-- depth:140
[1420]=1421,	-- depth:141
[1419]=1420,	-- depth:142
[1394]=1419,	-- depth:143
[1344]=1394,	-- depth:144
[1393]=1344,	-- depth:145
[1391]=1393,	-- depth:146
[1365]=1391,	-- depth:147
[1364]=1365,	-- depth:148
[1363]=1364,	-- depth:149
[1362]=1363,	-- depth:150
[1361]=1362,	-- depth:151
[1360]=1361,	-- depth:152
[1359]=1360,	-- depth:153
[1358]=1359,	-- depth:154
[1357]=1358,	-- depth:155
[1366]=1357,	-- depth:156
[1356]=1366,	-- depth:157
[1354]=1356,	-- depth:158
[1353]=1354,	-- depth:159
[1352]=1353,	-- depth:160
[1351]=1352,	-- depth:161
[1350]=1351,	-- depth:162
[1349]=1350,	-- depth:163
[1348]=1349,	-- depth:164
[1347]=1348,	-- depth:165
[1346]=1347,	-- depth:166
[1367]=1346,	-- depth:167
[1368]=1367,	-- depth:168
[1369]=1368,	-- depth:169
[1390]=1369,	-- depth:170
[1389]=1390,	-- depth:171
[1388]=1389,	-- depth:172
[1387]=1388,	-- depth:173
[1386]=1387,	-- depth:174
[1385]=1386,	-- depth:175
[1384]=1385,	-- depth:176
[1383]=1384,	-- depth:177
[1382]=1383,	-- depth:178
[1381]=1382,	-- depth:179
[1380]=1381,	-- depth:180
[1379]=1380,	-- depth:181
[1378]=1379,	-- depth:182
[1377]=1378,	-- depth:183
[1376]=1377,	-- depth:184
[1375]=1376,	-- depth:185
[1374]=1375,	-- depth:186
[1373]=1374,	-- depth:187
[1372]=1373,	-- depth:188
[1371]=1372,	-- depth:189
[1370]=1371,	-- depth:190
[1392]=1370,	-- depth:191
[1246]=1392,	-- depth:192
[1355]=1246,	-- depth:193
[1244]=1355,	-- depth:194
[702]=710,	-- depth:1
[703]=702,	-- depth:2
[704]=703,	-- depth:3
[705]=704,	-- depth:4
[706]=705,	-- depth:5
[707]=706,	-- depth:6
[708]=707,	-- depth:7
[709]=708,	-- depth:8
[749]=750,	-- depth:1
[748]=749,	-- depth:2
[747]=748,	-- depth:3
[746]=747,	-- depth:4
[745]=746,	-- depth:5
[744]=745,	-- depth:6
[743]=744,	-- depth:7
[742]=743,	-- depth:8
[741]=742,	-- depth:9
[739]=740,	-- depth:1
[738]=739,	-- depth:2
[737]=738,	-- depth:3
[736]=737,	-- depth:4
[735]=736,	-- depth:5
[734]=735,	-- depth:6
[733]=734,	-- depth:7
[732]=733,	-- depth:8
[701]=709,	-- depth:9
[731]=732,	-- depth:9
[698]=700,	-- depth:1
[582]=581,	-- depth:1
[583]=582,	-- depth:2
[584]=583,	-- depth:3
[585]=584,	-- depth:4
[586]=585,	-- depth:5
[587]=586,	-- depth:6
[588]=587,	-- depth:7
[589]=588,	-- depth:8
[590]=589,	-- depth:9
[592]=591,	-- depth:1
[593]=592,	-- depth:2
[594]=593,	-- depth:3
[595]=594,	-- depth:4
[596]=595,	-- depth:5
[597]=596,	-- depth:6
[598]=597,	-- depth:7
[599]=598,	-- depth:8
[690]=689,	-- depth:1
[692]=698,	-- depth:2
[693]=692,	-- depth:3
[694]=693,	-- depth:4
[695]=694,	-- depth:5
[696]=695,	-- depth:6
[697]=696,	-- depth:7
[699]=697,	-- depth:8
[691]=699,	-- depth:9
[728]=730,	-- depth:1
[729]=728,	-- depth:2
[688]=690,	-- depth:2
[687]=688,	-- depth:3
[686]=687,	-- depth:4
[685]=686,	-- depth:5
[684]=685,	-- depth:6
[683]=684,	-- depth:7
[682]=683,	-- depth:8
[681]=682,	-- depth:9
[1245]=1244,	-- depth:195
[1240]=1245,	-- depth:196
[1241]=1240,	-- depth:197
[1242]=1241,	-- depth:198
[1243]=1242,	-- depth:199
[712]=711,	-- depth:1
[726]=729,	-- depth:3
[713]=712,	-- depth:2
[725]=726,	-- depth:4
[724]=725,	-- depth:5
[723]=724,	-- depth:6
[722]=723,	-- depth:7
[721]=722,	-- depth:8
[720]=713,	-- depth:3
[719]=720,	-- depth:4
[718]=719,	-- depth:5
[717]=718,	-- depth:6
[716]=717,	-- depth:7
[714]=716,	-- depth:8
[715]=714,	-- depth:9
[727]=721,	-- depth:9
[356]=357,	-- depth:1
[355]=356,	-- depth:2
[354]=355,	-- depth:3
[339]=338,	-- depth:1
[340]=339,	-- depth:2
[341]=350,	-- depth:1
[342]=341,	-- depth:2
[343]=342,	-- depth:3
[344]=343,	-- depth:4
[353]=354,	-- depth:4
[345]=344,	-- depth:5
[346]=345,	-- depth:6
[347]=346,	-- depth:7
[358]=353,	-- depth:5
[348]=347,	-- depth:8
[352]=358,	-- depth:6
[351]=352,	-- depth:7
[349]=348,	-- depth:9
[359]=351,	-- depth:8
[564]=563,	-- depth:1
[565]=564,	-- depth:2
[566]=565,	-- depth:3
[567]=566,	-- depth:4
[568]=567,	-- depth:5
[569]=568,	-- depth:6
[570]=569,	-- depth:7
[572]=571,	-- depth:1
[573]=572,	-- depth:2
[574]=573,	-- depth:3
[575]=574,	-- depth:4
[576]=575,	-- depth:5
[577]=576,	-- depth:6
[579]=577,	-- depth:7
[580]=579,	-- depth:8
[337]=340,	-- depth:3
[271]=274,	-- depth:1
[272]=271,	-- depth:2
[273]=272,	-- depth:3
[360]=359,	-- depth:9
[336]=337,	-- depth:4
[334]=336,	-- depth:5
[300]=591,	-- depth:1
[299]=300,	-- depth:2
[275]=273,	-- depth:4
[276]=275,	-- depth:5
[562]=570,	-- depth:8
[277]=276,	-- depth:6
[278]=277,	-- depth:7
[279]=278,	-- depth:8
[280]=279,	-- depth:9
[282]=281,	-- depth:1
[283]=282,	-- depth:2
[284]=283,	-- depth:3
[285]=284,	-- depth:4
[286]=285,	-- depth:5
[287]=286,	-- depth:6
[288]=287,	-- depth:7
[289]=288,	-- depth:8
[290]=289,	-- depth:9
[291]=299,	-- depth:3
[292]=291,	-- depth:4
[293]=292,	-- depth:5
[294]=293,	-- depth:6
[295]=294,	-- depth:7
[296]=295,	-- depth:8
[297]=296,	-- depth:9
[298]=297,	-- depth:10
[301]=300,	-- depth:2
[302]=301,	-- depth:3
[303]=302,	-- depth:4
[304]=303,	-- depth:5
[333]=334,	-- depth:6
[332]=333,	-- depth:7
[331]=332,	-- depth:8
[330]=327,	-- depth:1
[329]=330,	-- depth:2
[328]=329,	-- depth:3
[326]=328,	-- depth:4
[325]=326,	-- depth:5
[324]=325,	-- depth:6
[323]=324,	-- depth:7
[322]=323,	-- depth:8
[321]=322,	-- depth:9
[335]=331,	-- depth:9
[319]=320,	-- depth:1
[317]=319,	-- depth:2
[316]=317,	-- depth:3
[315]=316,	-- depth:4
[314]=315,	-- depth:5
[313]=314,	-- depth:6
[312]=313,	-- depth:7
[311]=312,	-- depth:8
[310]=304,	-- depth:6
[309]=310,	-- depth:7
[308]=309,	-- depth:8
[307]=308,	-- depth:9
[306]=307,	-- depth:10
[305]=306,	-- depth:11
[318]=311,	-- depth:9
[561]=562,	-- depth:9
[578]=580,	-- depth:9
[395]=394,	-- depth:1
[396]=395,	-- depth:2
[397]=396,	-- depth:3
[398]=397,	-- depth:4
[399]=398,	-- depth:5
[482]=481,	-- depth:1
[483]=482,	-- depth:2
[484]=483,	-- depth:3
[485]=484,	-- depth:4
[486]=485,	-- depth:5
[487]=486,	-- depth:6
[488]=487,	-- depth:7
[489]=488,	-- depth:8
[490]=489,	-- depth:9
[492]=491,	-- depth:1
[493]=492,	-- depth:2
[494]=493,	-- depth:3
[495]=494,	-- depth:4
[496]=495,	-- depth:5
[497]=496,	-- depth:6
[498]=497,	-- depth:7
[499]=498,	-- depth:8
[500]=499,	-- depth:9
[502]=501,	-- depth:1
[393]=399,	-- depth:6
[503]=502,	-- depth:2
[392]=393,	-- depth:7
[390]=392,	-- depth:8
[362]=361,	-- depth:1
[363]=362,	-- depth:2
[364]=363,	-- depth:3
[365]=364,	-- depth:4
[366]=365,	-- depth:5
[367]=366,	-- depth:6
[368]=367,	-- depth:7
[369]=368,	-- depth:8
[370]=369,	-- depth:9
[372]=371,	-- depth:1
[373]=372,	-- depth:2
[374]=373,	-- depth:3
[375]=374,	-- depth:4
[376]=375,	-- depth:5
[377]=376,	-- depth:6
[378]=377,	-- depth:7
[379]=378,	-- depth:8
[380]=379,	-- depth:9
[381]=390,	-- depth:9
[382]=381,	-- depth:10
[383]=382,	-- depth:11
[384]=383,	-- depth:12
[385]=384,	-- depth:13
[386]=385,	-- depth:14
[387]=386,	-- depth:15
[388]=387,	-- depth:16
[560]=559,	-- depth:1
[391]=388,	-- depth:17
[504]=503,	-- depth:3
[389]=391,	-- depth:18
[506]=504,	-- depth:4
[538]=539,	-- depth:1
[537]=538,	-- depth:2
[536]=537,	-- depth:3
[535]=536,	-- depth:4
[534]=535,	-- depth:5
[532]=534,	-- depth:6
[531]=532,	-- depth:7
[529]=530,	-- depth:1
[528]=529,	-- depth:2
[527]=528,	-- depth:3
[526]=527,	-- depth:4
[525]=526,	-- depth:5
[524]=525,	-- depth:6
[540]=531,	-- depth:8
[542]=541,	-- depth:1
[543]=542,	-- depth:2
[558]=560,	-- depth:2
[557]=558,	-- depth:3
[556]=557,	-- depth:4
[555]=556,	-- depth:5
[554]=555,	-- depth:6
[553]=554,	-- depth:7
[552]=553,	-- depth:8
[523]=524,	-- depth:7
[551]=552,	-- depth:9
[549]=543,	-- depth:3
[505]=506,	-- depth:5
[548]=549,	-- depth:4
[547]=548,	-- depth:5
[546]=547,	-- depth:6
[545]=546,	-- depth:7
[544]=545,	-- depth:8
[550]=544,	-- depth:9
[522]=523,	-- depth:8
[533]=540,	-- depth:9
[510]=505,	-- depth:6
[515]=516,	-- depth:1
[514]=515,	-- depth:2
[513]=514,	-- depth:3
[512]=513,	-- depth:4
[511]=512,	-- depth:5
[509]=510,	-- depth:7
[508]=509,	-- depth:8
[517]=511,	-- depth:6
[521]=522,	-- depth:9
[507]=508,	-- depth:9
[520]=517,	-- depth:7
[519]=520,	-- depth:8
[518]=519,	-- depth:9
[622]=621,	-- depth:1
[619]=616,	-- depth:1
[618]=619,	-- depth:2
[617]=618,	-- depth:3
[620]=616,	-- depth:1
[614]=617,	-- depth:4
[613]=614,	-- depth:5
[612]=613,	-- depth:6
[611]=612,	-- depth:7
[623]=622,	-- depth:2
[609]=610,	-- depth:1
[663]=662,	-- depth:1
[664]=663,	-- depth:2
[665]=664,	-- depth:3
[666]=665,	-- depth:4
[667]=666,	-- depth:5
[615]=611,	-- depth:8
[624]=623,	-- depth:3
[627]=624,	-- depth:4
[626]=627,	-- depth:5
[645]=646,	-- depth:1
[644]=645,	-- depth:2
[643]=644,	-- depth:3
[642]=643,	-- depth:4
[641]=642,	-- depth:5
[647]=641,	-- depth:6
[640]=636,	-- depth:1
[638]=636,	-- depth:1
[631]=638,	-- depth:2
[632]=631,	-- depth:3
[633]=632,	-- depth:4
[634]=633,	-- depth:5
[635]=634,	-- depth:6
[639]=635,	-- depth:7
[625]=626,	-- depth:6
[648]=647,	-- depth:7
[649]=648,	-- depth:8
[637]=639,	-- depth:8
[628]=625,	-- depth:7
[629]=628,	-- depth:8
[658]=659,	-- depth:1
[630]=629,	-- depth:9
[668]=667,	-- depth:6
[657]=658,	-- depth:2
[655]=657,	-- depth:3
[654]=655,	-- depth:4
[653]=654,	-- depth:5
[652]=653,	-- depth:6
[651]=652,	-- depth:7
[650]=649,	-- depth:9
[656]=651,	-- depth:8
[669]=668,	-- depth:7
[661]=669,	-- depth:8
[671]=680,	-- depth:1
[268]=270,	-- depth:1
[269]=268,	-- depth:2
[660]=659,	-- depth:1
[670]=661,	-- depth:9
[172]=171,	-- depth:1
[173]=172,	-- depth:2
[174]=173,	-- depth:3
[175]=174,	-- depth:4
[176]=175,	-- depth:5
[267]=269,	-- depth:3
[177]=176,	-- depth:6
[179]=177,	-- depth:7
[180]=179,	-- depth:8
[601]=609,	-- depth:2
[602]=601,	-- depth:3
[603]=602,	-- depth:4
[604]=603,	-- depth:5
[605]=604,	-- depth:6
[606]=605,	-- depth:7
[607]=606,	-- depth:8
[608]=607,	-- depth:9
[178]=180,	-- depth:9
[266]=267,	-- depth:4
[600]=591,	-- depth:1
[264]=266,	-- depth:5
[679]=671,	-- depth:2
[678]=679,	-- depth:3
[677]=678,	-- depth:4
[265]=264,	-- depth:6
[675]=677,	-- depth:5
[674]=675,	-- depth:6
[676]=674,	-- depth:7
[672]=676,	-- depth:8
[261]=265,	-- depth:7
[262]=261,	-- depth:8
[263]=262,	-- depth:9
[673]=672,	-- depth:9
[209]=610,	-- depth:1
[208]=209,	-- depth:2
[207]=208,	-- depth:3
[206]=207,	-- depth:4
[205]=206,	-- depth:5
[202]=205,	-- depth:6
[203]=202,	-- depth:7
[201]=203,	-- depth:8
[200]=209,	-- depth:2
[220]=659,	-- depth:1
[219]=220,	-- depth:2
[218]=219,	-- depth:3
[217]=218,	-- depth:4
[216]=217,	-- depth:5
[215]=216,	-- depth:6
[213]=215,	-- depth:7
[212]=213,	-- depth:8
[211]=212,	-- depth:9
[210]=201,	-- depth:9
[204]=210,	-- depth:10
[214]=211,	-- depth:10
[237]=659,	-- depth:1
[222]=220,	-- depth:2
[435]=636,	-- depth:1
[436]=435,	-- depth:2
[437]=436,	-- depth:3
[438]=437,	-- depth:4
[439]=438,	-- depth:5
[440]=435,	-- depth:2
[441]=440,	-- depth:3
[442]=441,	-- depth:4
[443]=442,	-- depth:5
[444]=443,	-- depth:6
[445]=444,	-- depth:7
[446]=445,	-- depth:8
[447]=446,	-- depth:9
[448]=447,	-- depth:10
[449]=448,	-- depth:11
[450]=449,	-- depth:12
[451]=440,	-- depth:3
[464]=662,	-- depth:1
[463]=464,	-- depth:2
[462]=463,	-- depth:3
[461]=462,	-- depth:4
[460]=451,	-- depth:4
[221]=222,	-- depth:3
[434]=439,	-- depth:6
[459]=451,	-- depth:4
[457]=459,	-- depth:5
[456]=457,	-- depth:6
[455]=456,	-- depth:7
[454]=455,	-- depth:8
[453]=454,	-- depth:9
[452]=453,	-- depth:10
[458]=452,	-- depth:11
[465]=461,	-- depth:5
[433]=434,	-- depth:7
[431]=433,	-- depth:8
[400]=394,	-- depth:1
[401]=610,	-- depth:1
[402]=401,	-- depth:2
[403]=402,	-- depth:3
[404]=403,	-- depth:4
[405]=404,	-- depth:5
[406]=405,	-- depth:6
[407]=406,	-- depth:7
[408]=407,	-- depth:8
[409]=408,	-- depth:9
[410]=409,	-- depth:10
[411]=401,	-- depth:2
[412]=411,	-- depth:3
[413]=412,	-- depth:4
[414]=413,	-- depth:5
[415]=414,	-- depth:6
[416]=415,	-- depth:7
[430]=435,	-- depth:2
[429]=430,	-- depth:3
[428]=429,	-- depth:4
[427]=428,	-- depth:5
[426]=427,	-- depth:6
[425]=426,	-- depth:7
[432]=431,	-- depth:9
[424]=425,	-- depth:8
[422]=424,	-- depth:9
[421]=422,	-- depth:10
[420]=411,	-- depth:3
[419]=416,	-- depth:8
[418]=419,	-- depth:9
[417]=418,	-- depth:10
[423]=421,	-- depth:11
[466]=465,	-- depth:6
[468]=466,	-- depth:7
[248]=1482,	-- depth:1
[249]=248,	-- depth:2
[250]=249,	-- depth:3
[251]=248,	-- depth:2
[252]=251,	-- depth:3
[253]=252,	-- depth:4
[254]=253,	-- depth:5
[255]=254,	-- depth:6
[256]=255,	-- depth:7
[247]=250,	-- depth:4
[257]=256,	-- depth:8
[259]=257,	-- depth:9
[479]=480,	-- depth:1
[467]=468,	-- depth:8
[477]=479,	-- depth:2
[476]=477,	-- depth:3
[475]=476,	-- depth:4
[474]=475,	-- depth:5
[473]=474,	-- depth:6
[472]=473,	-- depth:7
[258]=259,	-- depth:10
[471]=472,	-- depth:8
[246]=247,	-- depth:5
[244]=246,	-- depth:6
[223]=221,	-- depth:4
[224]=223,	-- depth:5
[225]=224,	-- depth:6
[226]=225,	-- depth:7
[227]=226,	-- depth:8
[228]=227,	-- depth:9
[229]=228,	-- depth:10
[230]=229,	-- depth:11
[231]=237,	-- depth:2
[245]=244,	-- depth:7
[232]=231,	-- depth:3
[234]=232,	-- depth:4
[235]=234,	-- depth:5
[236]=235,	-- depth:6
[238]=236,	-- depth:7
[239]=238,	-- depth:8
[240]=237,	-- depth:2
[241]=245,	-- depth:8
[242]=241,	-- depth:9
[243]=242,	-- depth:10
[233]=239,	-- depth:9
[470]=467,	-- depth:9
[478]=471,	-- depth:9
[469]=470,	-- depth:10
[128]=1,	-- depth:1
[68]=1,	-- depth:1
[67]=68,	-- depth:2
[66]=67,	-- depth:3
[65]=66,	-- depth:4
[64]=65,	-- depth:5
[63]=64,	-- depth:6
[62]=63,	-- depth:7
[61]=62,	-- depth:8
[69]=61,	-- depth:9
[60]=68,	-- depth:2
[58]=60,	-- depth:3
[57]=58,	-- depth:4
[56]=57,	-- depth:5
[55]=56,	-- depth:6
[54]=55,	-- depth:7
[53]=54,	-- depth:8
[52]=53,	-- depth:9
[51]=52,	-- depth:10
[50]=68,	-- depth:2
[59]=51,	-- depth:11
[70]=69,	-- depth:10
[71]=70,	-- depth:11
[72]=71,	-- depth:12
[164]=171,	-- depth:1
[165]=164,	-- depth:2
[166]=165,	-- depth:3
[167]=166,	-- depth:4
[168]=167,	-- depth:5
[169]=168,	-- depth:6
[170]=169,	-- depth:7
[184]=171,	-- depth:1
[183]=184,	-- depth:2
[182]=183,	-- depth:3
[181]=182,	-- depth:4
[80]=72,	-- depth:13
[79]=80,	-- depth:14
[78]=79,	-- depth:15
[77]=78,	-- depth:16
[76]=77,	-- depth:17
[75]=76,	-- depth:18
[74]=75,	-- depth:19
[73]=74,	-- depth:20
[49]=50,	-- depth:3
[48]=49,	-- depth:4
[47]=48,	-- depth:5
[46]=47,	-- depth:6
[21]=1,	-- depth:1
[20]=21,	-- depth:2
[19]=20,	-- depth:3
[18]=19,	-- depth:4
[17]=18,	-- depth:5
[16]=17,	-- depth:6
[15]=16,	-- depth:7
[14]=15,	-- depth:8
[13]=14,	-- depth:9
[22]=13,	-- depth:10
[12]=22,	-- depth:11
[10]=12,	-- depth:12
[9]=10,	-- depth:13
[8]=9,	-- depth:14
[7]=8,	-- depth:15
[6]=7,	-- depth:16
[5]=6,	-- depth:17
[4]=5,	-- depth:18
[3]=4,	-- depth:19
[2]=3,	-- depth:20
[11]=2,	-- depth:21
[163]=170,	-- depth:8
[23]=11,	-- depth:22
[25]=23,	-- depth:23
[45]=46,	-- depth:7
[44]=45,	-- depth:8
[43]=44,	-- depth:9
[42]=43,	-- depth:10
[41]=42,	-- depth:11
[40]=41,	-- depth:12
[39]=40,	-- depth:13
[38]=39,	-- depth:14
[37]=38,	-- depth:15
[24]=25,	-- depth:24
[36]=37,	-- depth:16
[34]=36,	-- depth:17
[33]=34,	-- depth:18
[32]=33,	-- depth:19
[31]=32,	-- depth:20
[30]=24,	-- depth:25
[29]=30,	-- depth:26
[28]=29,	-- depth:27
[27]=28,	-- depth:28
[26]=27,	-- depth:29
[35]=31,	-- depth:21
[129]=128,	-- depth:2
[162]=163,	-- depth:9
[160]=171,	-- depth:1
[103]=128,	-- depth:2
[102]=103,	-- depth:3
[101]=102,	-- depth:4
[100]=128,	-- depth:2
[99]=100,	-- depth:3
[98]=99,	-- depth:4
[97]=98,	-- depth:5
[96]=97,	-- depth:6
[95]=128,	-- depth:2
[94]=95,	-- depth:3
[185]=181,	-- depth:5
[92]=94,	-- depth:4
[91]=92,	-- depth:5
[90]=73,	-- depth:21
[89]=90,	-- depth:22
[88]=89,	-- depth:23
[87]=88,	-- depth:24
[86]=87,	-- depth:25
[85]=86,	-- depth:26
[104]=101,	-- depth:5
[105]=104,	-- depth:6
[106]=128,	-- depth:2
[107]=106,	-- depth:3
[127]=129,	-- depth:3
[126]=127,	-- depth:4
[125]=128,	-- depth:2
[124]=125,	-- depth:3
[123]=124,	-- depth:4
[122]=123,	-- depth:5
[121]=122,	-- depth:6
[120]=128,	-- depth:2
[119]=120,	-- depth:3
[84]=85,	-- depth:27
[118]=119,	-- depth:4
[116]=118,	-- depth:5
[115]=128,	-- depth:2
[114]=115,	-- depth:3
[113]=114,	-- depth:4
[112]=113,	-- depth:5
[111]=112,	-- depth:6
[110]=107,	-- depth:4
[109]=110,	-- depth:5
[108]=109,	-- depth:6
[117]=116,	-- depth:6
[83]=84,	-- depth:28
[82]=83,	-- depth:29
[81]=82,	-- depth:30
[140]=50,	-- depth:3
[141]=171,	-- depth:1
[142]=141,	-- depth:2
[143]=142,	-- depth:3
[144]=143,	-- depth:4
[145]=144,	-- depth:5
[146]=145,	-- depth:6
[147]=146,	-- depth:7
[148]=147,	-- depth:8
[139]=140,	-- depth:4
[149]=148,	-- depth:9
[151]=160,	-- depth:2
[152]=151,	-- depth:3
[153]=152,	-- depth:4
[154]=153,	-- depth:5
[155]=154,	-- depth:6
[156]=155,	-- depth:7
[157]=156,	-- depth:8
[158]=157,	-- depth:9
[159]=158,	-- depth:10
[150]=149,	-- depth:10
[161]=162,	-- depth:10
[138]=139,	-- depth:5
[136]=138,	-- depth:6
[186]=185,	-- depth:6
[187]=186,	-- depth:7
[188]=187,	-- depth:8
[189]=188,	-- depth:9
[190]=189,	-- depth:10
[191]=171,	-- depth:1
[192]=191,	-- depth:2
[193]=192,	-- depth:3
[194]=193,	-- depth:4
[137]=136,	-- depth:7
[195]=194,	-- depth:5
[197]=195,	-- depth:6
[198]=197,	-- depth:7
[199]=198,	-- depth:8
[130]=126,	-- depth:5
[131]=128,	-- depth:2
[132]=131,	-- depth:3
[133]=132,	-- depth:4
[134]=133,	-- depth:5
[135]=134,	-- depth:6
[196]=199,	-- depth:9
[93]=91,	-- depth:6
},
other={
{}
},

other_meta_table_map={
},
exp_efficiency={
{exp_times=0.5,},
{level=2,std_exp=348,},
{level=3,std_exp=356,},
{level=4,std_exp=364,},
{level=5,std_exp=372,},
{level=6,std_exp=380,},
{level=7,std_exp=389,},
{level=8,std_exp=397,},
{level=9,std_exp=406,},
{level=10,std_exp=415,},
{level=11,std_exp=424,},
{level=12,std_exp=434,},
{level=13,std_exp=444,},
{level=14,std_exp=453,},
{level=15,std_exp=463,},
{level=16,std_exp=474,},
{level=17,std_exp=484,},
{level=18,std_exp=495,},
{level=19,std_exp=506,},
{level=20,std_exp=517,},
{level=21,std_exp=529,},
{level=22,std_exp=541,},
{level=23,std_exp=553,},
{level=24,std_exp=565,},
{level=25,std_exp=577,},
{level=26,std_exp=590,},
{level=27,std_exp=603,},
{level=28,std_exp=617,},
{level=29,std_exp=630,},
{level=30,std_exp=644,},
{level=31,std_exp=659,},
{level=32,std_exp=673,},
{level=33,std_exp=688,},
{level=34,std_exp=704,},
{level=35,std_exp=719,},
{level=36,std_exp=735,},
{level=37,std_exp=752,},
{level=38,std_exp=768,},
{level=39,std_exp=785,},
{level=40,std_exp=803,},
{level=41,std_exp=821,},
{level=42,std_exp=839,},
{level=43,std_exp=857,},
{level=44,std_exp=876,},
{level=45,std_exp=896,},
{level=46,std_exp=916,},
{level=47,std_exp=936,},
{level=48,std_exp=957,},
{level=49,std_exp=978,},
{level=50,std_exp=1000,},
{level=51,std_exp=1022,},
{level=52,std_exp=1045,},
{level=53,std_exp=1068,},
{level=54,std_exp=1092,},
{level=55,std_exp=1116,},
{level=56,std_exp=1141,},
{level=57,std_exp=1166,},
{level=58,std_exp=1192,},
{level=59,std_exp=1219,},
{level=60,std_exp=1246,},
{level=61,std_exp=1273,},
{level=62,std_exp=1302,},
{level=63,std_exp=1331,},
{level=64,std_exp=1360,},
{level=65,std_exp=1390,},
{level=66,std_exp=1421,},
{level=67,std_exp=1453,},
{level=68,std_exp=1485,},
{level=69,std_exp=1518,},
{level=70,std_exp=1552,},
{level=71,std_exp=1586,},
{level=72,std_exp=1622,},
{level=73,std_exp=1658,},
{level=74,std_exp=1694,},
{level=75,std_exp=1732,},
{level=76,std_exp=1771,},
{level=77,std_exp=1810,},
{level=78,std_exp=1850,},
{level=79,std_exp=1891,},
{level=80,std_exp=1933,},
{level=81,std_exp=1976,},
{level=82,std_exp=2020,},
{level=83,std_exp=2065,},
{level=84,std_exp=2111,},
{level=85,std_exp=2158,},
{level=86,std_exp=2206,},
{level=87,std_exp=2255,},
{level=88,std_exp=2305,},
{level=89,std_exp=2356,},
{level=90,std_exp=2408,},
{level=91,std_exp=2462,},
{level=92,std_exp=2516,},
{level=93,std_exp=2572,},
{level=94,std_exp=2629,},
{level=95,std_exp=2688,},
{level=96,std_exp=2748,},
{level=97,std_exp=2809,},
{level=98,std_exp=2871,},
{level=99,std_exp=2935,},
{level=100,std_exp=3000,},
{level=101,std_exp=3067,},
{level=102,std_exp=3135,},
{level=103,std_exp=3204,},
{level=104,std_exp=3276,},
{level=105,std_exp=3348,},
{level=106,std_exp=3423,},
{level=107,std_exp=3499,},
{level=108,std_exp=3577,},
{level=109,std_exp=3656,},
{level=110,std_exp=3737,},
{level=111,std_exp=3820,},
{level=112,std_exp=3905,},
{level=113,std_exp=3992,},
{level=114,std_exp=4081,},
{level=115,std_exp=4171,},
{level=116,std_exp=4264,},
{level=117,std_exp=4359,},
{level=118,std_exp=4455,},
{level=119,std_exp=4554,},
{level=120,std_exp=4656,},
{level=121,std_exp=4759,},
{level=122,std_exp=4865,},
{level=123,std_exp=4973,},
{level=124,std_exp=5083,},
{level=125,std_exp=5196,},
{level=126,std_exp=5312,},
{level=127,std_exp=5430,},
{level=128,std_exp=5550,},
{level=129,std_exp=5674,},
{level=130,std_exp=5800,},
{level=131,std_exp=5928,},
{level=132,std_exp=6060,},
{level=133,std_exp=6195,},
{level=134,std_exp=6332,},
{level=135,std_exp=6473,},
{level=136,std_exp=6617,},
{level=137,std_exp=6764,},
{level=138,std_exp=6914,},
{level=139,std_exp=7068,},
{level=140,std_exp=7225,},
{level=141,std_exp=7385,},
{level=142,std_exp=7549,},
{level=143,std_exp=7717,},
{level=144,std_exp=7888,},
{level=145,std_exp=8064,},
{level=146,std_exp=8243,},
{level=147,std_exp=8426,},
{level=148,std_exp=8613,},
{level=149,std_exp=8804,},
{level=150,std_exp=9000,},
{level=151,std_exp=9200,},
{level=152,std_exp=9404,},
{level=153,std_exp=9613,},
{level=154,std_exp=9827,},
{level=155,std_exp=10045,},
{level=156,std_exp=10268,},
{level=157,std_exp=10496,},
{level=158,std_exp=10730,},
{level=159,std_exp=10968,},
{level=160,std_exp=11212,},
{level=161,std_exp=11461,},
{level=162,std_exp=11715,},
{level=163,std_exp=11976,},
{level=164,std_exp=12242,},
{level=165,std_exp=12514,},
{level=166,std_exp=12791,},
{level=167,std_exp=13076,},
{level=168,std_exp=13366,},
{level=169,std_exp=13663,},
{level=170,std_exp=13967,},
{level=171,std_exp=14277,},
{level=172,std_exp=14594,},
{level=173,std_exp=14918,},
{level=174,std_exp=15250,},
{level=175,std_exp=15588,},
{level=176,std_exp=15935,},
{level=177,std_exp=16289,},
{level=178,std_exp=16651,},
{level=179,std_exp=17021,},
{level=180,std_exp=17399,},
{level=181,std_exp=17785,},
{level=182,std_exp=18180,},
{level=183,std_exp=18584,},
{level=184,std_exp=18997,},
{level=185,std_exp=19419,},
{level=186,std_exp=19850,},
{level=187,std_exp=20291,},
{level=188,std_exp=20742,},
{level=189,std_exp=21203,},
{level=190,std_exp=21674,},
{level=191,std_exp=22156,},
{level=192,std_exp=22648,},
{level=193,std_exp=23151,},
{level=194,std_exp=23665,},
{level=195,std_exp=24191,},
{level=196,std_exp=24728,},
{level=197,std_exp=25278,},
{level=198,std_exp=25839,},
{level=199,std_exp=26413,},
{level=200,std_exp=27000,},
{level=201,std_exp=27600,},
{level=202,std_exp=28213,},
{level=203,std_exp=28840,},
{level=204,std_exp=29480,},
{level=205,std_exp=30135,},
{level=206,std_exp=30805,},
{level=207,std_exp=31489,},
{level=208,std_exp=32189,},
{level=209,std_exp=32904,},
{level=210,std_exp=33635,},
{level=211,std_exp=34382,},
{level=212,std_exp=35146,},
{level=213,std_exp=35927,},
{level=214,std_exp=36725,},
{level=215,std_exp=37541,},
{level=216,std_exp=38374,},
{level=217,std_exp=39227,},
{level=218,std_exp=40098,},
{level=219,std_exp=40989,},
{level=220,std_exp=41900,},
{level=221,std_exp=42831,},
{level=222,std_exp=43782,},
{level=223,std_exp=44755,},
{level=224,std_exp=45749,},
{level=225,std_exp=46765,},
{level=226,std_exp=47804,},
{level=227,std_exp=48866,},
{level=228,std_exp=49952,},
{level=229,std_exp=51062,},
{level=230,std_exp=52196,},
{level=231,std_exp=53355,},
{level=232,std_exp=54541,},
{level=233,std_exp=55752,},
{level=234,std_exp=56991,},
{level=235,std_exp=58257,},
{level=236,std_exp=59551,},
{level=237,std_exp=60874,},
{level=238,std_exp=62227,},
{level=239,std_exp=63609,},
{level=240,std_exp=65022,},
{level=241,std_exp=66467,},
{level=242,std_exp=67943,},
{level=243,std_exp=69453,},
{level=244,std_exp=70995,},
{level=245,std_exp=72573,},
{level=246,std_exp=74185,},
{level=247,std_exp=75833,},
{level=248,std_exp=77518,},
{level=249,std_exp=79240,},
{level=250,std_exp=81000,},
{level=251,std_exp=82799,},
{level=252,std_exp=84639,},
{level=253,std_exp=86519,},
{level=254,std_exp=88441,},
{level=255,std_exp=90406,},
{level=256,std_exp=92414,},
{level=257,std_exp=94467,},
{level=258,std_exp=96566,},
{level=259,std_exp=98711,},
{level=260,std_exp=100904,},
{level=261,std_exp=103146,},
{level=262,std_exp=105437,},
{level=263,std_exp=107780,},
{level=264,std_exp=110174,},
{level=265,std_exp=112622,},
{level=266,std_exp=115123,},
{level=267,std_exp=117681,},
{level=268,std_exp=120295,},
{level=269,std_exp=122968,},
{level=270,std_exp=125699,},
{level=271,std_exp=128492,},
{level=272,std_exp=131346,},
{level=273,std_exp=134264,},
{level=274,std_exp=137247,},
{level=275,std_exp=140296,},
{level=276,std_exp=143413,},
{level=277,std_exp=146599,},
{level=278,std_exp=149856,},
{level=279,std_exp=153185,},
{level=280,std_exp=156588,exp_times=0.8,},
{level=281,std_exp=160066,},
{level=282,std_exp=163622,},
{level=283,std_exp=167257,},
{level=284,std_exp=170973,},
{level=285,std_exp=174771,},
{level=286,std_exp=178654,},
{level=287,std_exp=182623,},
{level=288,std_exp=186680,},
{level=289,std_exp=190827,},
{level=290,std_exp=195066,},
{level=291,std_exp=199400,},
{level=292,std_exp=203829,},
{level=293,std_exp=208358,},
{level=294,std_exp=212986,},
{level=295,std_exp=217718,},
{level=296,std_exp=222555,},
{level=297,std_exp=227499,},
{level=298,std_exp=232553,},
{level=299,std_exp=237719,},
{level=300,std_exp=243000,},
{level=301,std_exp=248398,},
{level=302,std_exp=253917,},
{level=303,std_exp=259557,},
{level=304,std_exp=265324,},
{level=305,std_exp=271218,},
{level=306,std_exp=277243,},
{level=307,std_exp=283402,},
{level=308,std_exp=289698,},
{level=309,std_exp=296134,},
{level=310,std_exp=302713,},
{level=311,std_exp=309438,},
{level=312,std_exp=316312,},
{level=313,std_exp=323339,},
{level=314,std_exp=330522,},
{level=315,std_exp=337865,},
{level=316,std_exp=345370,},
{level=317,std_exp=353043,},
{level=318,std_exp=360886,},
{level=319,std_exp=368903,},
{level=320,std_exp=377098,},
{level=321,std_exp=385476,},
{level=322,std_exp=394039,},
{level=323,std_exp=402793,},
{level=324,std_exp=411741,},
{level=325,std_exp=420888,},
{level=326,std_exp=430239,},
{level=327,std_exp=439796,},
{level=328,std_exp=449567,},
{level=329,std_exp=459554,},
{level=330,std_exp=469763,},
{level=331,std_exp=480199,},
{level=332,std_exp=490867,},
{level=333,std_exp=501772,},
{level=334,std_exp=512919,},
{level=335,std_exp=524314,},
{level=336,std_exp=535961,},
{level=337,std_exp=547868,},
{level=338,std_exp=560039,},
{level=339,std_exp=572481,},
{level=340,std_exp=585199,},
{level=341,std_exp=598199,},
{level=342,std_exp=611488,},
{level=343,std_exp=625073,},
{level=344,std_exp=638959,},
{level=345,std_exp=653154,},
{level=346,std_exp=667664,},
{level=347,std_exp=682496,},
{level=348,std_exp=697658,},
{level=349,std_exp=713157,},
{level=350,std_exp=729000,},
{level=351,std_exp=745195,},
{level=352,std_exp=761750,},
{level=353,std_exp=778672,},
{level=354,std_exp=795971,},
{level=355,std_exp=813654,},
{level=356,std_exp=831729,},
{level=357,std_exp=850207,},
{level=358,std_exp=869094,},
{level=359,std_exp=888402,},
{level=360,std_exp=908138,},
{level=361,std_exp=928313,},
{level=362,std_exp=948935,},
{level=363,std_exp=970016,},
{level=364,std_exp=991566,},
{level=365,std_exp=1013594,},
{level=366,std_exp=1036111,},
{level=367,std_exp=1059129,},
{level=368,std_exp=1082658,},
{level=369,std_exp=1106709,},
{level=370,std_exp=1131295,},
{level=371,std_exp=1156428,},
{level=372,std_exp=1182118,},
{level=373,std_exp=1208379,},
{level=374,std_exp=1235224,},
{level=375,std_exp=1262665,},
{level=376,std_exp=1290716,},
{level=377,std_exp=1319389,},
{level=378,std_exp=1348700,},
{level=379,std_exp=1378662,},
{level=380,std_exp=1409290,},
{level=381,std_exp=1440598,},
{level=382,std_exp=1472601,},
{level=383,std_exp=1505316,},
{level=384,std_exp=1538757,},
{level=385,std_exp=1572941,},
{level=386,std_exp=1607884,},
{level=387,std_exp=1643604,},
{level=388,std_exp=1680118,},
{level=389,std_exp=1717442,},
{level=390,std_exp=1755596,},
{level=391,std_exp=1794597,},
{level=392,std_exp=1834465,},
{level=393,std_exp=1875218,},
{level=394,std_exp=1916877,},
{level=395,std_exp=1959461,},
{level=396,std_exp=2002991,},
{level=397,std_exp=2047489,},
{level=398,std_exp=2092974,},
{level=399,std_exp=2139471,},
{level=400,std_exp=2187000,},
{level=401,std_exp=2235585,},
{level=402,std_exp=2285250,},
{level=403,std_exp=2336017,},
{level=404,std_exp=2387913,},
{level=405,std_exp=2440961,},
{level=406,std_exp=2495188,},
{level=407,std_exp=2550620,},
{level=408,std_exp=2607283,},
{level=409,std_exp=2665205,},
{level=410,std_exp=2724414,},
{level=411,std_exp=2784938,},
{level=412,std_exp=2846806,},
{level=413,std_exp=2910049,},
{level=414,std_exp=2974697,},
{level=415,std_exp=3040781,},
{level=416,std_exp=3108333,},
{level=417,std_exp=3177386,},
{level=418,std_exp=3247973,},
{level=419,std_exp=3320128,},
{level=420,std_exp=3393886,},
{level=421,std_exp=3469283,},
{level=422,std_exp=3546354,},
{level=423,std_exp=3625138,},
{level=424,std_exp=3705672,},
{level=425,std_exp=3787995,},
{level=426,std_exp=3872147,},
{level=427,std_exp=3958168,},
{level=428,std_exp=4046101,},
{level=429,std_exp=4135986,},
{level=430,std_exp=4227869,},
{level=431,std_exp=4321793,},
{level=432,std_exp=4417803,},
{level=433,std_exp=4515947,},
{level=434,std_exp=4616270,},
{level=435,std_exp=4718823,},
{level=436,std_exp=4823653,},
{level=437,std_exp=4930813,},
{level=438,std_exp=5040353,},
{level=439,std_exp=5152326,},
{level=440,std_exp=5266787,},
{level=441,std_exp=5383791,},
{level=442,std_exp=5503394,},
{level=443,std_exp=5625655,},
{level=444,std_exp=5750631,},
{level=445,std_exp=5878383,},
{level=446,std_exp=6008974,},
{level=447,std_exp=6142466,},
{level=448,std_exp=6278923,},
{level=449,std_exp=6418412,},
{level=450,std_exp=6561000,},
{level=451,std_exp=6706755,},
{level=452,std_exp=6855749,},
{level=453,std_exp=7008052,},
{level=454,std_exp=7163739,},
{level=455,std_exp=7322884,},
{level=456,std_exp=7485565,},
{level=457,std_exp=7651860,},
{level=458,std_exp=7821849,},
{level=459,std_exp=7995615,},
{level=460,std_exp=8173241,},
{level=461,std_exp=8354813,},
{level=462,std_exp=8540418,},
{level=463,std_exp=8730147,},
{level=464,std_exp=8924091,},
{level=465,std_exp=9122343,},
{level=466,std_exp=9325000,},
{level=467,std_exp=9532159,},
{level=468,std_exp=9743920,},
{level=469,std_exp=9960385,},
{level=470,std_exp=10181659,},
{level=471,std_exp=10407849,},
{level=472,std_exp=10639063,},
{level=473,std_exp=10875414,},
{level=474,std_exp=11117016,},
{level=475,std_exp=11363985,},
{level=476,std_exp=11616441,},
{level=477,std_exp=11874505,},
{level=478,std_exp=12138302,},
{level=479,std_exp=12407959,},
{level=480,std_exp=12683607,},
{level=481,std_exp=12965379,},
{level=482,std_exp=13253410,},
{level=483,std_exp=13547840,},
{level=484,std_exp=13848811,},
{level=485,std_exp=14156468,},
{level=486,std_exp=14470960,},
{level=487,std_exp=14792438,},
{level=488,std_exp=15121058,},
{level=489,std_exp=15456979,},
{level=490,std_exp=15800362,},
{level=491,std_exp=16151374,},
{level=492,std_exp=16510183,},
{level=493,std_exp=16876964,},
{level=494,std_exp=17251892,},
{level=495,std_exp=17635150,},
{level=496,std_exp=18026923,},
{level=497,std_exp=18427398,},
{level=498,std_exp=18836770,},
{level=499,std_exp=19255237,},
{level=500,std_exp=19683000,},
{level=501,std_exp=20120266,},
{level=502,std_exp=20567246,},
{level=503,std_exp=21024156,},
{level=504,std_exp=21491216,},
{level=505,std_exp=21968652,},
{level=506,std_exp=22456695,},
{level=507,std_exp=22955580,},
{level=508,std_exp=23465548,},
{level=509,std_exp=23986844,},
{level=510,std_exp=24519722,},
{level=511,std_exp=25064438,},
{level=512,std_exp=25621255,},
{level=513,std_exp=26190441,},
{level=514,std_exp=26772273,},
{level=515,std_exp=27367030,},
{level=516,std_exp=27975000,},
{level=517,std_exp=28596476,},
{level=518,std_exp=29231759,},
{level=519,std_exp=29881154,},
{level=520,std_exp=30544976,},
{level=521,std_exp=31223546,},
{level=522,std_exp=31917190,},
{level=523,std_exp=32626243,},
{level=524,std_exp=33351049,},
{level=525,std_exp=34091956,},
{level=526,std_exp=34849323,},
{level=527,std_exp=35623515,},
{level=528,std_exp=36414906,},
{level=529,std_exp=37223878,},
{level=530,std_exp=38050822,},
{level=531,std_exp=38896137,},
{level=532,std_exp=39760231,},
{level=533,std_exp=40643521,},
{level=534,std_exp=41546433,},
{level=535,std_exp=42469404,},
{level=536,std_exp=43412880,},
{level=537,std_exp=44377315,},
{level=538,std_exp=45363175,},
{level=539,std_exp=46370937,},
{level=540,std_exp=47401086,},
{level=541,std_exp=48454121,},
{level=542,std_exp=49530550,},
{level=543,std_exp=50630891,},
{level=544,std_exp=51755677,},
{level=545,std_exp=52905451,},
{level=546,std_exp=54080768,},
{level=547,std_exp=55282194,},
{level=548,std_exp=56510311,},
{level=549,std_exp=57765711,},
{level=550,std_exp=59049000,},
{level=551,std_exp=60360798,},
{level=552,std_exp=61701738,},
{level=553,std_exp=63072468,},
{level=554,std_exp=64473649,},
{level=555,std_exp=65905957,},
{level=556,std_exp=67370085,},
{level=557,std_exp=68866740,},
{level=558,std_exp=70396643,},
{level=559,std_exp=71960533,},
{level=560,std_exp=73559166,},
{level=561,std_exp=75193314,},
{level=562,std_exp=76863764,},
{level=563,std_exp=78571324,},
{level=564,std_exp=80316819,},
{level=565,std_exp=82101090,},
{level=566,std_exp=83925000,},
{level=567,std_exp=85789428,},
{level=568,std_exp=87695276,},
{level=569,std_exp=89643463,},
{level=570,std_exp=91634929,},
{level=571,std_exp=93670637,},
{level=572,std_exp=95751569,},
{level=573,std_exp=97878730,},
{level=574,std_exp=100053146,},
{level=575,std_exp=102275868,},
{level=576,std_exp=104547969,},
{level=577,std_exp=106870545,},
{level=578,std_exp=109244718,},
{level=579,std_exp=111671635,},
{level=580,std_exp=114152467,},
{level=581,std_exp=116688411,},
{level=582,std_exp=119280692,},
{level=583,std_exp=121930562,},
{level=584,std_exp=124639300,},
{level=585,std_exp=127408213,},
{level=586,std_exp=130238639,},
{level=587,std_exp=133131945,},
{level=588,std_exp=136089526,},
{level=589,std_exp=139112811,},
{level=590,std_exp=142203259,},
{level=591,std_exp=145362364,},
{level=592,std_exp=148591649,},
{level=593,std_exp=151892673,},
{level=594,std_exp=155267032,},
{level=595,std_exp=158716353,},
{level=596,std_exp=162242303,},
{level=597,std_exp=165846582,},
{level=598,std_exp=169530933,},
{level=599,std_exp=173297133,},
{level=600,std_exp=177147000,},
{level=601,std_exp=181082394,},
{level=602,std_exp=185105214,},
{level=603,std_exp=189217403,},
{level=604,std_exp=193420946,},
{level=605,std_exp=197717872,},
{level=606,std_exp=202110256,},
{level=607,std_exp=206600219,},
{level=608,std_exp=211189928,},
{level=609,std_exp=215881600,},
{level=610,std_exp=220677499,},
{level=611,std_exp=225579941,},
{level=612,std_exp=230591292,},
{level=613,std_exp=235713973,},
{level=614,std_exp=240950456,},
{level=615,std_exp=246303270,},
{level=616,std_exp=251774999,},
{level=617,std_exp=257368285,},
{level=618,std_exp=263085828,},
{level=619,std_exp=268930388,},
{level=620,std_exp=274904788,},
{level=621,std_exp=281011911,},
{level=622,std_exp=287254707,},
{level=623,std_exp=293636189,},
{level=624,std_exp=300159438,},
{level=625,std_exp=306827604,},
{level=626,std_exp=313643906,},
{level=627,std_exp=320611635,},
{level=628,std_exp=327734155,},
{level=629,std_exp=335014905,},
{level=630,std_exp=342457400,},
{level=631,std_exp=350065232,},
{level=632,std_exp=357842076,},
{level=633,std_exp=365791686,},
{level=634,std_exp=373917899,},
{level=635,std_exp=382224640,},
{level=636,std_exp=390715918,},
{level=637,std_exp=399395834,},
{level=638,std_exp=408268577,},
{level=639,std_exp=417338433,},
{level=640,std_exp=426609778,},
{level=641,std_exp=436087091,},
{level=642,std_exp=445774946,},
{level=643,std_exp=455678020,},
{level=644,std_exp=465801096,},
{level=645,std_exp=476149060,},
{level=646,std_exp=486726908,},
{level=647,std_exp=497539747,},
{level=648,std_exp=508592799,},
{level=649,std_exp=519891398,},
{level=650,std_exp=531441000,},
{level=651,std_exp=543247181,},
{level=652,std_exp=555315642,},
{level=653,std_exp=567652209,},
{level=654,std_exp=580262837,},
{level=655,std_exp=593153616,},
{level=656,std_exp=606330768,},
{level=657,std_exp=619800657,},
{level=658,std_exp=633569785,},
{level=659,std_exp=647644799,},
{level=660,std_exp=662032496,},
{level=661,std_exp=676739822,},
{level=662,std_exp=691773877,},
{level=663,std_exp=707141919,},
{level=664,std_exp=722851369,},
{level=665,std_exp=738909811,},
{level=666,std_exp=755324998,},
{level=667,std_exp=772104855,},
{level=668,std_exp=789257483,},
{level=669,std_exp=806791164,},
{level=670,std_exp=824714364,},
{level=671,std_exp=843035734,},
{level=672,std_exp=861764121,},
{level=673,std_exp=880908567,},
{level=674,std_exp=900478315,},
{level=675,std_exp=920482813,},
{level=676,std_exp=940931719,},
{level=677,std_exp=961834906,},
{level=678,std_exp=983202466,},
{level=679,std_exp=1005044715,},
{level=680,std_exp=1027372199,},
{level=681,std_exp=1050195697,},
{level=682,std_exp=1073526229,},
{level=683,std_exp=1097375057,},
{level=684,std_exp=1121753698,},
{level=685,std_exp=1146673920,},
{level=686,std_exp=1172147755,},
{level=687,std_exp=1198187502,},
{level=688,std_exp=1224805732,},
{level=689,std_exp=1252015298,},
{level=690,std_exp=1279829335,},
{level=691,std_exp=1308261273,},
{level=692,std_exp=1337324837,},
{level=693,std_exp=1367034061,},
{level=694,std_exp=1397403288,},
{level=695,std_exp=1428447180,},
{level=696,std_exp=1460180724,},
{level=697,std_exp=1492619242,},
{level=698,std_exp=1525778396,},
{level=699,std_exp=1559674193,},
{level=700,std_exp=1594323000,},
{level=701,std_exp=1629741544,},
{level=702,std_exp=1665946926,},
{level=703,std_exp=1702956626,},
{level=704,std_exp=1740788511,},
{level=705,std_exp=1779460847,},
{level=706,std_exp=1818992305,},
{level=707,std_exp=1859401971,},
{level=708,std_exp=1900709354,},
{level=709,std_exp=1942934398,},
{level=710,std_exp=1986097489,},
{level=711,std_exp=2030219465,},
{level=712,std_exp=2075321630,},
{level=713,std_exp=2121425758,},
{level=714,std_exp=2168554107,},
{level=715,std_exp=2216729433,},
{level=716,std_exp=2265974994,},
{level=717,std_exp=2316314565,},
{level=718,std_exp=2367772450,},
{level=719,std_exp=2420373493,},
{level=720,std_exp=2474143091,},
{level=721,std_exp=2529107202,},
{level=722,std_exp=2585292364,},
{level=723,std_exp=2642725702,},
{level=724,std_exp=2701434946,},
{level=725,std_exp=2761448440,},
{level=726,std_exp=2822795158,},
{level=727,std_exp=2885504719,},
{level=728,std_exp=2949607399,},
{level=729,std_exp=3015134146,},
{level=730,std_exp=3082116597,},
{level=731,std_exp=3150587091,},
{level=732,std_exp=3220578686,},
{level=733,std_exp=3292125172,},
{level=734,std_exp=3365261093,},
{level=735,std_exp=3440021759,},
{level=736,std_exp=3516443265,},
{level=737,std_exp=3594562505,},
{level=738,std_exp=3674417197,},
{level=739,std_exp=3756045893,},
{level=740,std_exp=3839488005,},
{level=741,std_exp=3924783818,},
{level=742,std_exp=4011974512,},
{level=743,std_exp=4101102184,},
{level=744,std_exp=4192209864,},
{level=745,std_exp=4285341539,},
{level=746,std_exp=4380542172,},
{level=747,std_exp=4477857727,},
{level=748,std_exp=4577335187,},
{level=749,std_exp=4679022580,},
{level=750,std_exp=4782969000,},
{level=751,std_exp=4889224633,},
{level=752,std_exp=4997840779,},
{level=753,std_exp=5108869877,},
{level=754,std_exp=5222365533,},
{level=755,std_exp=5338382542,},
{level=756,std_exp=5456976916,},
{level=757,std_exp=5578205913,},
{level=758,std_exp=5702128063,},
{level=759,std_exp=5828803195,},
{level=760,std_exp=5958292467,},
{level=761,std_exp=6090658396,},
{level=762,std_exp=6225964890,},
{level=763,std_exp=6364277273,},
{level=764,std_exp=6505662322,},
{level=765,std_exp=6650188300,},
{level=766,std_exp=6797924981,},
{level=767,std_exp=6948943694,},
{level=768,std_exp=7103317350,},
{level=769,std_exp=7261120480,},
{level=770,std_exp=7422429273,},
{level=771,std_exp=7587321607,},
{level=772,std_exp=7755877092,},
{level=773,std_exp=7928177107,},
{level=774,std_exp=8104304838,},
{level=775,std_exp=8284345319,},
{level=776,std_exp=8468385474,},
{level=777,std_exp=8656514157,},
{level=778,std_exp=8848822197,},
{level=779,std_exp=9045402439,},
{level=780,std_exp=9246349792,},
{level=781,std_exp=9451761274,},
{level=782,std_exp=9661736057,},
{level=783,std_exp=9876375517,},
{level=784,std_exp=10095783280,},
{level=785,std_exp=10320065278,},
{level=786,std_exp=10549329794,},
{level=787,std_exp=10783687515,},
{level=788,std_exp=11023251590,},
{level=789,std_exp=11268137679,},
{level=790,std_exp=11518464015,},
{level=791,std_exp=11774351453,},
{level=792,std_exp=12035923537,},
{level=793,std_exp=12303306552,},
{level=794,std_exp=12576629592,},
{level=795,std_exp=12856024616,},
{level=796,std_exp=13141626516,},
{level=797,std_exp=13433573180,},
{level=798,std_exp=13732005560,},
{level=799,std_exp=14037067739,},
{level=800,std_exp=14348907000,},
{level=801,std_exp=14667673899,},
{level=802,std_exp=14993522337,},
{level=803,std_exp=15326609632,},
{level=804,std_exp=15667096599,},
{level=805,std_exp=16015147625,},
{level=806,std_exp=16370930748,},
{level=807,std_exp=16734617740,},
{level=808,std_exp=17106384189,},
{level=809,std_exp=17486409584,},
{level=810,std_exp=17874877400,},
{level=811,std_exp=18271975188,},
{level=812,std_exp=18677894669,},
{level=813,std_exp=19092831818,},
{level=814,std_exp=19516986967,},
{level=815,std_exp=19950564899,},
{level=816,std_exp=20393774943,},
{level=817,std_exp=20846831082,},
{level=818,std_exp=21309952050,},
{level=819,std_exp=21783361441,},
{level=820,std_exp=22267287818,},
{level=821,std_exp=22761964820,},
{level=822,std_exp=23267631275,},
{level=823,std_exp=23784531320,},
{level=824,std_exp=24312914513,},
{level=825,std_exp=24853035957,},
{level=826,std_exp=25405156422,},
{level=827,std_exp=25969542471,},
{level=828,std_exp=26546466590,},
{level=829,std_exp=27136207316,},
{level=830,std_exp=27739049377,},
{level=831,std_exp=28355283823,},
{level=832,std_exp=28985208171,},
{level=833,std_exp=29629126550,},
{level=834,std_exp=30287349841,},
{level=835,std_exp=30960195835,},
{level=836,std_exp=31647989381,},
{level=837,std_exp=32351062544,},
{level=838,std_exp=33069754769,},
{level=839,std_exp=33804413038,},
{level=840,std_exp=34555392044,},
{level=841,std_exp=35323054359,},
{level=842,std_exp=36107770610,},
{level=843,std_exp=36909919657,},
{level=844,std_exp=37729888776,},
{level=845,std_exp=38568073848,},
{level=846,std_exp=39424879548,},
{level=847,std_exp=40300719541,},
{level=848,std_exp=41196016681,},
{level=849,std_exp=42111203217,},
{level=850,std_exp=43046721000,},
{level=851,std_exp=44003021697,},
{level=852,std_exp=44980567010,},
{level=853,std_exp=45979828895,},
{level=854,std_exp=47001289797,},
{level=855,std_exp=48045442874,},
{level=856,std_exp=49112792244,},
{level=857,std_exp=50203853221,},
{level=858,std_exp=51319152568,},
{level=859,std_exp=52459228752,},
{level=860,std_exp=53624632199,},
{level=861,std_exp=54815925565,},
{level=862,std_exp=56033684006,},
{level=863,std_exp=57278495453,},
{level=864,std_exp=58550960902,},
{level=865,std_exp=59851694696,},
{level=866,std_exp=61181324829,},
{level=867,std_exp=62540493245,},
{level=868,std_exp=63929856149,},
{level=869,std_exp=65350084324,},
{level=870,std_exp=66801863455,},
{level=871,std_exp=68285894460,},
{level=872,std_exp=69802893826,},
{level=873,std_exp=71353593961,},
{level=874,std_exp=72938743539,},
{level=875,std_exp=74559107871,},
{level=876,std_exp=76215469266,},
{level=877,std_exp=77908627414,},
{level=878,std_exp=79639399770,},
{level=879,std_exp=81408621949,},
{level=880,std_exp=83217148130,},
{level=881,std_exp=85065851468,},
{level=882,std_exp=86955624514,},
{level=883,std_exp=88887379650,},
{level=884,std_exp=90862049524,},
{level=885,std_exp=92880587505,},
{level=886,std_exp=94943968142,},
{level=887,std_exp=97053187633,},
{level=888,std_exp=99209264306,},
{level=889,std_exp=101413239114,},
{level=890,std_exp=103666176133,},
{level=891,std_exp=105969163078,},
{level=892,std_exp=108323311831,},
{level=893,std_exp=110729758971,},
{level=894,std_exp=113189666329,},
{level=895,std_exp=115704221545,},
{level=896,std_exp=118274638645,},
{level=897,std_exp=120902158623,},
{level=898,std_exp=123588050043,},
{level=899,std_exp=126333609651,},
{level=900,std_exp=129140163000,},
{level=901,std_exp=132009065092,},
{level=902,std_exp=134941701029,},
{level=903,std_exp=137939486686,},
{level=904,std_exp=141003869390,},
{level=905,std_exp=144136328623,},
{level=906,std_exp=147338376732,},
{level=907,std_exp=150611559662,},
{level=908,std_exp=153957457705,},
{level=909,std_exp=157377686255,},
{level=910,std_exp=160873896596,},
{level=911,std_exp=164447776695,},
{level=912,std_exp=168101052017,},
{level=913,std_exp=171835486360,},
{level=914,std_exp=175652882705,},
{level=915,std_exp=179555084088,},
{level=916,std_exp=183543974487,},
{level=917,std_exp=187621479735,},
{level=918,std_exp=191789568448,},
{level=919,std_exp=196050252973,},
{level=920,std_exp=200405590366,},
{level=921,std_exp=204857683380,},
{level=922,std_exp=209408681479,},
{level=923,std_exp=214060781882,},
{level=924,std_exp=218816230618,},
{level=925,std_exp=223677323614,},
{level=926,std_exp=228646407799,},
{level=927,std_exp=233725882243,},
{level=928,std_exp=238918199311,},
{level=929,std_exp=244225865848,},
{level=930,std_exp=249651444391,},
{level=931,std_exp=255197554404,},
{level=932,std_exp=260866873543,},
{level=933,std_exp=266662138949,},
{level=934,std_exp=272586148572,},
{level=935,std_exp=278641762516,},
{level=936,std_exp=284831904427,},
{level=937,std_exp=291159562899,},
{level=938,std_exp=297627792918,},
{level=939,std_exp=304239717341,},
{level=940,std_exp=310998528398,},
{level=941,std_exp=317907489235,},
{level=942,std_exp=324969935493,},
{level=943,std_exp=332189276913,},
{level=944,std_exp=339568998986,},
{level=945,std_exp=347112664635,},
{level=946,std_exp=354823915934,},
{level=947,std_exp=362706475868,},
{level=948,std_exp=370764150128,},
{level=949,std_exp=379000828952,},
{level=950,std_exp=387420489000,},
{level=951,std_exp=396027195276,},
{level=952,std_exp=404825103088,},
{level=953,std_exp=413818460059,},
{level=954,std_exp=423011608171,},
{level=955,std_exp=432408985868,},
{level=956,std_exp=442015130195,},
{level=957,std_exp=451834678987,},
{level=958,std_exp=461872373115,},
{level=959,std_exp=472133058765,},
{level=960,std_exp=482621689788,},
{level=961,std_exp=493343330084,},
{level=962,std_exp=504303156050,},
{level=963,std_exp=515506459079,},
{level=964,std_exp=526958648116,},
{level=965,std_exp=538665252264,},
{level=966,std_exp=550631923462,},
{level=967,std_exp=562864439206,},
{level=968,std_exp=575368705343,},
{level=969,std_exp=588150758919,},
{level=970,std_exp=601216771099,},
{level=971,std_exp=614573050139,},
{level=972,std_exp=628226044437,},
{level=973,std_exp=642182345647,},
{level=974,std_exp=656448691855,},
{level=975,std_exp=671031970841,},
{level=976,std_exp=685939223396,},
{level=977,std_exp=701177646728,},
{level=978,std_exp=716754597932,},
{level=979,std_exp=732677597544,},
{level=980,std_exp=748954333173,},
{level=981,std_exp=765592663212,},
{level=982,std_exp=782600620628,},
{level=983,std_exp=799986416847,},
{level=984,std_exp=817758445715,},
{level=985,std_exp=835925287548,},
{level=986,std_exp=854495713281,},
{level=987,std_exp=873478688696,},
{level=988,std_exp=892883378755,},
{level=989,std_exp=912719152024,},
{level=990,std_exp=932995585193,},
{level=991,std_exp=953722467705,},
{level=992,std_exp=974909806478,},
{level=993,std_exp=996567830739,},
{level=994,std_exp=1018706996958,},
{level=995,std_exp=1041337993906,},
{level=996,std_exp=1064471747803,},
{level=997,std_exp=1088119427603,},
{level=998,std_exp=1112292450384,},
{level=999,std_exp=1137002486856,},
{level=1000,std_exp=1162261467000,},
{level=1001,std_exp=1172261467000,},
{level=1002,std_exp=1182261467000,},
{level=1003,std_exp=1192261467000,},
{level=1004,std_exp=1202261467000,},
{level=1005,std_exp=1212261467000,},
{level=1006,std_exp=1222261467000,},
{level=1007,std_exp=1232261467000,},
{level=1008,std_exp=1242261467000,},
{level=1009,std_exp=1252261467000,},
{level=1010,std_exp=1262261467000,},
{level=1011,std_exp=1272261467000,},
{level=1012,std_exp=1282261467000,},
{level=1013,std_exp=1292261467000,},
{level=1014,std_exp=1302261467000,},
{level=1015,std_exp=1312261467000,},
{level=1016,std_exp=1322261467000,},
{level=1017,std_exp=1332261467000,},
{level=1018,std_exp=1342261467000,},
{level=1019,std_exp=1352261467000,},
{level=1020,std_exp=1362261467000,},
{level=1021,std_exp=1372261467000,},
{level=1022,std_exp=1382261467000,},
{level=1023,std_exp=1392261467000,},
{level=1024,std_exp=1402261467000,},
{level=1025,std_exp=1412261467000,},
{level=1026,std_exp=1422261467000,},
{level=1027,std_exp=1432261467000,},
{level=1028,std_exp=1442261467000,},
{level=1029,std_exp=1452261467000,},
{level=1030,std_exp=1462261467000,},
{level=1031,std_exp=1472261467000,},
{level=1032,std_exp=1482261467000,},
{level=1033,std_exp=1492261467000,},
{level=1034,std_exp=1502261467000,},
{level=1035,std_exp=1512261467000,},
{level=1036,std_exp=1522261467000,},
{level=1037,std_exp=1532261467000,},
{level=1038,std_exp=1542261467000,},
{level=1039,std_exp=1552261467000,},
{level=1040,std_exp=1562261467000,},
{level=1041,std_exp=1572261467000,},
{level=1042,std_exp=1582261467000,},
{level=1043,std_exp=1592261467000,},
{level=1044,std_exp=1602261467000,},
{level=1045,std_exp=1612261467000,},
{level=1046,std_exp=1622261467000,},
{level=1047,std_exp=1632261467000,},
{level=1048,std_exp=1642261467000,},
{level=1049,std_exp=1652261467000,},
{level=1050,std_exp=1662261467000,},
{level=1051,std_exp=1672261467000,},
{level=1052,std_exp=1682261467000,},
{level=1053,std_exp=1692261467000,},
{level=1054,std_exp=1702261467000,},
{level=1055,std_exp=1712261467000,},
{level=1056,std_exp=1722261467000,},
{level=1057,std_exp=1732261467000,},
{level=1058,std_exp=1742261467000,},
{level=1059,std_exp=1752261467000,},
{level=1060,std_exp=1762261467000,},
{level=1061,std_exp=1772261467000,},
{level=1062,std_exp=1782261467000,},
{level=1063,std_exp=1792261467000,},
{level=1064,std_exp=1802261467000,},
{level=1065,std_exp=1812261467000,},
{level=1066,std_exp=1822261467000,},
{level=1067,std_exp=1832261467000,},
{level=1068,std_exp=1842261467000,},
{level=1069,std_exp=1852261467000,},
{level=1070,std_exp=1862261467000,},
{level=1071,std_exp=1872261467000,},
{level=1072,std_exp=1882261467000,},
{level=1073,std_exp=1892261467000,},
{level=1074,std_exp=1902261467000,},
{level=1075,std_exp=1912261467000,},
{level=1076,std_exp=1922261467000,},
{level=1077,std_exp=1932261467000,},
{level=1078,std_exp=1942261467000,},
{level=1079,std_exp=1952261467000,},
{level=1080,std_exp=1962261467000,},
{level=1081,std_exp=1972261467000,},
{level=1082,std_exp=1982261467000,},
{level=1083,std_exp=1992261467000,},
{level=1084,std_exp=2002261467000,},
{level=1085,std_exp=2012261467000,},
{level=1086,std_exp=2022261467000,},
{level=1087,std_exp=2032261467000,},
{level=1088,std_exp=2042261467000,},
{level=1089,std_exp=2052261467000,},
{level=1090,std_exp=2062261467000,},
{level=1091,std_exp=2072261467000,},
{level=1092,std_exp=2082261467000,},
{level=1093,std_exp=2092261467000,},
{level=1094,std_exp=2102261467000,},
{level=1095,std_exp=2112261467000,},
{level=1096,std_exp=2122261467000,},
{level=1097,std_exp=2132261467000,},
{level=1098,std_exp=2142261467000,},
{level=1099,std_exp=2152261467000,},
{level=1100,std_exp=2162261467000,},
{level=1101,std_exp=2172261467000,},
{level=1102,std_exp=2182261467000,},
{level=1103,std_exp=2192261467000,},
{level=1104,std_exp=2202261467000,},
{level=1105,std_exp=2212261467000,},
{level=1106,std_exp=2222261467000,},
{level=1107,std_exp=2232261467000,},
{level=1108,std_exp=2242261467000,},
{level=1109,std_exp=2252261467000,},
{level=1110,std_exp=2262261467000,},
{level=1111,std_exp=2272261467000,},
{level=1112,std_exp=2282261467000,},
{level=1113,std_exp=2292261467000,},
{level=1114,std_exp=2302261467000,},
{level=1115,std_exp=2312261467000,},
{level=1116,std_exp=2322261467000,},
{level=1117,std_exp=2332261467000,},
{level=1118,std_exp=2342261467000,},
{level=1119,std_exp=2352261467000,},
{level=1120,std_exp=2362261467000,},
{level=1121,std_exp=2372261467000,},
{level=1122,std_exp=2382261467000,},
{level=1123,std_exp=2392261467000,},
{level=1124,std_exp=2402261467000,},
{level=1125,std_exp=2412261467000,},
{level=1126,std_exp=2422261467000,},
{level=1127,std_exp=2432261467000,},
{level=1128,std_exp=2442261467000,},
{level=1129,std_exp=2452261467000,},
{level=1130,std_exp=2462261467000,},
{level=1131,std_exp=2472261467000,},
{level=1132,std_exp=2482261467000,},
{level=1133,std_exp=2492261467000,},
{level=1134,std_exp=2502261467000,},
{level=1135,std_exp=2512261467000,},
{level=1136,std_exp=2522261467000,},
{level=1137,std_exp=2532261467000,},
{level=1138,std_exp=2542261467000,},
{level=1139,std_exp=2552261467000,},
{level=1140,std_exp=2562261467000,},
{level=1141,std_exp=2572261467000,},
{level=1142,std_exp=2582261467000,},
{level=1143,std_exp=2592261467000,},
{level=1144,std_exp=2602261467000,},
{level=1145,std_exp=2612261467000,},
{level=1146,std_exp=2622261467000,},
{level=1147,std_exp=2632261467000,},
{level=1148,std_exp=2642261467000,},
{level=1149,std_exp=2652261467000,},
{level=1150,std_exp=2662261467000,},
{level=1151,std_exp=2672261467000,},
{level=1152,std_exp=2682261467000,},
{level=1153,std_exp=2692261467000,},
{level=1154,std_exp=2702261467000,},
{level=1155,std_exp=2712261467000,},
{level=1156,std_exp=2722261467000,},
{level=1157,std_exp=2732261467000,},
{level=1158,std_exp=2742261467000,},
{level=1159,std_exp=2752261467000,},
{level=1160,std_exp=2762261467000,},
{level=1161,std_exp=2772261467000,},
{level=1162,std_exp=2782261467000,},
{level=1163,std_exp=2792261467000,},
{level=1164,std_exp=2802261467000,},
{level=1165,std_exp=2812261467000,},
{level=1166,std_exp=2822261467000,},
{level=1167,std_exp=2832261467000,},
{level=1168,std_exp=2842261467000,},
{level=1169,std_exp=2852261467000,},
{level=1170,std_exp=2862261467000,},
{level=1171,std_exp=2872261467000,},
{level=1172,std_exp=2882261467000,},
{level=1173,std_exp=2892261467000,},
{level=1174,std_exp=2902261467000,},
{level=1175,std_exp=2912261467000,},
{level=1176,std_exp=2922261467000,},
{level=1177,std_exp=2932261467000,},
{level=1178,std_exp=2942261467000,},
{level=1179,std_exp=2952261467000,},
{level=1180,std_exp=2962261467000,},
{level=1181,std_exp=2972261467000,},
{level=1182,std_exp=2982261467000,},
{level=1183,std_exp=2992261467000,},
{level=1184,std_exp=3002261467000,},
{level=1185,std_exp=3012261467000,},
{level=1186,std_exp=3022261467000,},
{level=1187,std_exp=3032261467000,},
{level=1188,std_exp=3042261467000,},
{level=1189,std_exp=3052261467000,},
{level=1190,std_exp=3062261467000,},
{level=1191,std_exp=3072261467000,},
{level=1192,std_exp=3082261467000,},
{level=1193,std_exp=3092261467000,},
{level=1194,std_exp=3102261467000,},
{level=1195,std_exp=3112261467000,},
{level=1196,std_exp=3122261467000,},
{level=1197,std_exp=3132261467000,},
{level=1198,std_exp=3142261467000,},
{level=1199,std_exp=3152261467000,},
{level=1200,std_exp=3162261467000,},
{level=1201,std_exp=3172261467000,},
{level=1202,std_exp=3182261467000,},
{level=1203,std_exp=3192261467000,},
{level=1204,std_exp=3202261467000,},
{level=1205,std_exp=3212261467000,},
{level=1206,std_exp=3222261467000,},
{level=1207,std_exp=3232261467000,},
{level=1208,std_exp=3242261467000,},
{level=1209,std_exp=3252261467000,},
{level=1210,std_exp=3262261467000,},
{level=1211,std_exp=3272261467000,},
{level=1212,std_exp=3282261467000,},
{level=1213,std_exp=3292261467000,},
{level=1214,std_exp=3302261467000,},
{level=1215,std_exp=3312261467000,},
{level=1216,std_exp=3322261467000,},
{level=1217,std_exp=3332261467000,},
{level=1218,std_exp=3342261467000,},
{level=1219,std_exp=3352261467000,},
{level=1220,std_exp=3362261467000,},
{level=1221,std_exp=3372261467000,},
{level=1222,std_exp=3382261467000,},
{level=1223,std_exp=3392261467000,},
{level=1224,std_exp=3402261467000,},
{level=1225,std_exp=3412261467000,},
{level=1226,std_exp=3422261467000,},
{level=1227,std_exp=3432261467000,},
{level=1228,std_exp=3442261467000,},
{level=1229,std_exp=3452261467000,},
{level=1230,std_exp=3462261467000,},
{level=1231,std_exp=3472261467000,},
{level=1232,std_exp=3482261467000,},
{level=1233,std_exp=3492261467000,},
{level=1234,std_exp=3502261467000,},
{level=1235,std_exp=3512261467000,},
{level=1236,std_exp=3522261467000,},
{level=1237,std_exp=3532261467000,},
{level=1238,std_exp=3542261467000,},
{level=1239,std_exp=3552261467000,},
{level=1240,std_exp=3562261467000,},
{level=1241,std_exp=3572261467000,},
{level=1242,std_exp=3582261467000,},
{level=1243,std_exp=3592261467000,},
{level=1244,std_exp=3602261467000,},
{level=1245,std_exp=3612261467000,},
{level=1246,std_exp=3622261467000,},
{level=1247,std_exp=3632261467000,},
{level=1248,std_exp=3642261467000,},
{level=1249,std_exp=3652261467000,},
{level=1250,std_exp=3662261467000,},
{level=1251,std_exp=3672261467000,},
{level=1252,std_exp=3682261467000,},
{level=1253,std_exp=3692261467000,},
{level=1254,std_exp=3702261467000,},
{level=1255,std_exp=3712261467000,},
{level=1256,std_exp=3722261467000,},
{level=1257,std_exp=3732261467000,},
{level=1258,std_exp=3742261467000,},
{level=1259,std_exp=3752261467000,},
{level=1260,std_exp=3762261467000,},
{level=1261,std_exp=3772261467000,},
{level=1262,std_exp=3782261467000,},
{level=1263,std_exp=3792261467000,},
{level=1264,std_exp=3802261467000,},
{level=1265,std_exp=3812261467000,},
{level=1266,std_exp=3822261467000,},
{level=1267,std_exp=3832261467000,},
{level=1268,std_exp=3842261467000,},
{level=1269,std_exp=3852261467000,},
{level=1270,std_exp=3862261467000,},
{level=1271,std_exp=3872261467000,},
{level=1272,std_exp=3882261467000,},
{level=1273,std_exp=3892261467000,},
{level=1274,std_exp=3902261467000,},
{level=1275,std_exp=3912261467000,},
{level=1276,std_exp=3922261467000,},
{level=1277,std_exp=3932261467000,},
{level=1278,std_exp=3942261467000,},
{level=1279,std_exp=3952261467000,},
{level=1280,std_exp=3962261467000,},
{level=1281,std_exp=3972261467000,},
{level=1282,std_exp=3982261467000,},
{level=1283,std_exp=3992261467000,},
{level=1284,std_exp=4002261467000,},
{level=1285,std_exp=4012261467000,},
{level=1286,std_exp=4022261467000,},
{level=1287,std_exp=4032261467000,},
{level=1288,std_exp=4042261467000,},
{level=1289,std_exp=4052261467000,},
{level=1290,std_exp=4062261467000,},
{level=1291,std_exp=4072261467000,},
{level=1292,std_exp=4082261467000,},
{level=1293,std_exp=4092261467000,},
{level=1294,std_exp=4102261467000,},
{level=1295,std_exp=4112261467000,},
{level=1296,std_exp=4122261467000,},
{level=1297,std_exp=4132261467000,},
{level=1298,std_exp=4142261467000,},
{level=1299,std_exp=4152261467000,},
{level=1300,std_exp=4162261467000,},
{level=1301,std_exp=4172261467000,},
{level=1302,std_exp=4182261467000,},
{level=1303,std_exp=4192261467000,},
{level=1304,std_exp=4202261467000,},
{level=1305,std_exp=4212261467000,},
{level=1306,std_exp=4222261467000,},
{level=1307,std_exp=4232261467000,},
{level=1308,std_exp=4242261467000,},
{level=1309,std_exp=4252261467000,},
{level=1310,std_exp=4262261467000,},
{level=1311,std_exp=4272261467000,},
{level=1312,std_exp=4282261467000,},
{level=1313,std_exp=4292261467000,},
{level=1314,std_exp=4302261467000,},
{level=1315,std_exp=4312261467000,},
{level=1316,std_exp=4322261467000,},
{level=1317,std_exp=4332261467000,},
{level=1318,std_exp=4342261467000,},
{level=1319,std_exp=4352261467000,},
{level=1320,std_exp=4362261467000,},
{level=1321,std_exp=4372261467000,},
{level=1322,std_exp=4382261467000,},
{level=1323,std_exp=4392261467000,},
{level=1324,std_exp=4402261467000,},
{level=1325,std_exp=4412261467000,},
{level=1326,std_exp=4422261467000,},
{level=1327,std_exp=4432261467000,},
{level=1328,std_exp=4442261467000,},
{level=1329,std_exp=4452261467000,},
{level=1330,std_exp=4462261467000,},
{level=1331,std_exp=4472261467000,},
{level=1332,std_exp=4482261467000,},
{level=1333,std_exp=4492261467000,},
{level=1334,std_exp=4502261467000,},
{level=1335,std_exp=4512261467000,},
{level=1336,std_exp=4522261467000,},
{level=1337,std_exp=4532261467000,},
{level=1338,std_exp=4542261467000,},
{level=1339,std_exp=4552261467000,},
{level=1340,std_exp=4562261467000,},
{level=1341,std_exp=4572261467000,},
{level=1342,std_exp=4582261467000,},
{level=1343,std_exp=4592261467000,},
{level=1344,std_exp=4602261467000,},
{level=1345,std_exp=4612261467000,},
{level=1346,std_exp=4622261467000,},
{level=1347,std_exp=4632261467000,},
{level=1348,std_exp=4642261467000,},
{level=1349,std_exp=4652261467000,},
{level=1350,std_exp=4662261467000,},
{level=1351,std_exp=4672261467000,},
{level=1352,std_exp=4682261467000,},
{level=1353,std_exp=4692261467000,},
{level=1354,std_exp=4702261467000,},
{level=1355,std_exp=4712261467000,},
{level=1356,std_exp=4722261467000,},
{level=1357,std_exp=4732261467000,},
{level=1358,std_exp=4742261467000,},
{level=1359,std_exp=4752261467000,},
{level=1360,std_exp=4762261467000,},
{level=1361,std_exp=4772261467000,},
{level=1362,std_exp=4782261467000,},
{level=1363,std_exp=4792261467000,},
{level=1364,std_exp=4802261467000,},
{level=1365,std_exp=4812261467000,},
{level=1366,std_exp=4822261467000,},
{level=1367,std_exp=4832261467000,},
{level=1368,std_exp=4842261467000,},
{level=1369,std_exp=4852261467000,},
{level=1370,std_exp=4862261467000,},
{level=1371,std_exp=4872261467000,},
{level=1372,std_exp=4882261467000,},
{level=1373,std_exp=4892261467000,},
{level=1374,std_exp=4902261467000,},
{level=1375,std_exp=4912261467000,},
{level=1376,std_exp=4922261467000,},
{level=1377,std_exp=4932261467000,},
{level=1378,std_exp=4942261467000,},
{level=1379,std_exp=4952261467000,},
{level=1380,std_exp=4962261467000,},
{level=1381,std_exp=4972261467000,},
{level=1382,std_exp=4982261467000,},
{level=1383,std_exp=4992261467000,},
{level=1384,std_exp=5002261467000,},
{level=1385,std_exp=5012261467000,},
{level=1386,std_exp=5022261467000,},
{level=1387,std_exp=5032261467000,},
{level=1388,std_exp=5042261467000,},
{level=1389,std_exp=5052261467000,},
{level=1390,std_exp=5062261467000,},
{level=1391,std_exp=5072261467000,},
{level=1392,std_exp=5082261467000,},
{level=1393,std_exp=5092261467000,},
{level=1394,std_exp=5102261467000,},
{level=1395,std_exp=5112261467000,},
{level=1396,std_exp=5122261467000,},
{level=1397,std_exp=5132261467000,},
{level=1398,std_exp=5142261467000,},
{level=1399,std_exp=5152261467000,},
{level=1400,std_exp=5162261467000,},
{level=1401,std_exp=5172261467000,},
{level=1402,std_exp=5182261467000,},
{level=1403,std_exp=5192261467000,},
{level=1404,std_exp=5202261467000,},
{level=1405,std_exp=5212261467000,},
{level=1406,std_exp=5222261467000,},
{level=1407,std_exp=5232261467000,},
{level=1408,std_exp=5242261467000,},
{level=1409,std_exp=5252261467000,},
{level=1410,std_exp=5262261467000,},
{level=1411,std_exp=5272261467000,},
{level=1412,std_exp=5282261467000,},
{level=1413,std_exp=5292261467000,},
{level=1414,std_exp=5302261467000,},
{level=1415,std_exp=5312261467000,},
{level=1416,std_exp=5322261467000,},
{level=1417,std_exp=5332261467000,},
{level=1418,std_exp=5342261467000,},
{level=1419,std_exp=5352261467000,},
{level=1420,std_exp=5362261467000,},
{level=1421,std_exp=5372261467000,},
{level=1422,std_exp=5382261467000,},
{level=1423,std_exp=5392261467000,},
{level=1424,std_exp=5402261467000,},
{level=1425,std_exp=5412261467000,},
{level=1426,std_exp=5422261467000,},
{level=1427,std_exp=5432261467000,},
{level=1428,std_exp=5442261467000,},
{level=1429,std_exp=5452261467000,},
{level=1430,std_exp=5462261467000,},
{level=1431,std_exp=5472261467000,},
{level=1432,std_exp=5482261467000,},
{level=1433,std_exp=5492261467000,},
{level=1434,std_exp=5502261467000,},
{level=1435,std_exp=5512261467000,},
{level=1436,std_exp=5522261467000,},
{level=1437,std_exp=5532261467000,},
{level=1438,std_exp=5542261467000,},
{level=1439,std_exp=5552261467000,},
{level=1440,std_exp=5562261467000,},
{level=1441,std_exp=5572261467000,},
{level=1442,std_exp=5582261467000,},
{level=1443,std_exp=5592261467000,},
{level=1444,std_exp=5602261467000,},
{level=1445,std_exp=5612261467000,},
{level=1446,std_exp=5622261467000,},
{level=1447,std_exp=5632261467000,},
{level=1448,std_exp=5642261467000,},
{level=1449,std_exp=5652261467000,},
{level=1450,std_exp=5662261467000,},
{level=1451,std_exp=5672261467000,},
{level=1452,std_exp=5682261467000,},
{level=1453,std_exp=5692261467000,},
{level=1454,std_exp=5702261467000,},
{level=1455,std_exp=5712261467000,},
{level=1456,std_exp=5722261467000,},
{level=1457,std_exp=5732261467000,},
{level=1458,std_exp=5742261467000,},
{level=1459,std_exp=5752261467000,},
{level=1460,std_exp=5762261467000,},
{level=1461,std_exp=5772261467000,},
{level=1462,std_exp=5782261467000,},
{level=1463,std_exp=5792261467000,},
{level=1464,std_exp=5802261467000,},
{level=1465,std_exp=5812261467000,},
{level=1466,std_exp=5822261467000,},
{level=1467,std_exp=5832261467000,},
{level=1468,std_exp=5842261467000,},
{level=1469,std_exp=5852261467000,},
{level=1470,std_exp=5862261467000,},
{level=1471,std_exp=5872261467000,},
{level=1472,std_exp=5882261467000,},
{level=1473,std_exp=5892261467000,},
{level=1474,std_exp=5902261467000,},
{level=1475,std_exp=5912261467000,},
{level=1476,std_exp=5922261467000,},
{level=1477,std_exp=5932261467000,},
{level=1478,std_exp=5942261467000,},
{level=1479,std_exp=5952261467000,},
{level=1480,std_exp=5962261467000,},
{level=1481,std_exp=5972261467000,},
{level=1482,std_exp=5982261467000,},
{level=1483,std_exp=5992261467000,},
{level=1484,std_exp=6002261467000,},
{level=1485,std_exp=6012261467000,},
{level=1486,std_exp=6022261467000,},
{level=1487,std_exp=6032261467000,},
{level=1488,std_exp=6042261467000,},
{level=1489,std_exp=6052261467000,},
{level=1490,std_exp=6062261467000,},
{level=1491,std_exp=6072261467000,},
{level=1492,std_exp=6082261467000,},
{level=1493,std_exp=6092261467000,},
{level=1494,std_exp=6102261467000,},
{level=1495,std_exp=6112261467000,},
{level=1496,std_exp=6122261467000,},
{level=1497,std_exp=6132261467000,},
{level=1498,std_exp=6142261467000,},
{level=1499,std_exp=6152261467000,},
{level=1500,std_exp=6162261467000,},
{level=1501,std_exp=6172261467000,},
{level=1502,std_exp=6182261467000,},
{level=1503,std_exp=6192261467000,},
{level=1504,std_exp=6202261467000,},
{level=1505,std_exp=6212261467000,},
{level=1506,std_exp=6222261467000,},
{level=1507,std_exp=6232261467000,},
{level=1508,std_exp=6242261467000,},
{level=1509,std_exp=6252261467000,},
{level=1510,std_exp=6262261467000,},
{level=1511,std_exp=6272261467000,},
{level=1512,std_exp=6282261467000,},
{level=1513,std_exp=6292261467000,},
{level=1514,std_exp=6302261467000,},
{level=1515,std_exp=6312261467000,},
{level=1516,std_exp=6322261467000,},
{level=1517,std_exp=6332261467000,},
{level=1518,std_exp=6342261467000,},
{level=1519,std_exp=6352261467000,},
{level=1520,std_exp=6362261467000,},
{level=1521,std_exp=6372261467000,},
{level=1522,std_exp=6382261467000,},
{level=1523,std_exp=6392261467000,},
{level=1524,std_exp=6402261467000,},
{level=1525,std_exp=6412261467000,},
{level=1526,std_exp=6422261467000,},
{level=1527,std_exp=6432261467000,},
{level=1528,std_exp=6442261467000,},
{level=1529,std_exp=6452261467000,},
{level=1530,std_exp=6462261467000,},
{level=1531,std_exp=6472261467000,},
{level=1532,std_exp=6482261467000,},
{level=1533,std_exp=6492261467000,},
{level=1534,std_exp=6502261467000,},
{level=1535,std_exp=6512261467000,},
{level=1536,std_exp=6522261467000,},
{level=1537,std_exp=6532261467000,},
{level=1538,std_exp=6542261467000,},
{level=1539,std_exp=6552261467000,},
{level=1540,std_exp=6562261467000,},
{level=1541,std_exp=6572261467000,},
{level=1542,std_exp=6582261467000,},
{level=1543,std_exp=6592261467000,},
{level=1544,std_exp=6602261467000,},
{level=1545,std_exp=6612261467000,},
{level=1546,std_exp=6622261467000,},
{level=1547,std_exp=6632261467000,},
{level=1548,std_exp=6642261467000,},
{level=1549,std_exp=6652261467000,},
{level=1550,std_exp=6662261467000,},
{level=1551,std_exp=6672261467000,},
{level=1552,std_exp=6682261467000,},
{level=1553,std_exp=6692261467000,},
{level=1554,std_exp=6702261467000,},
{level=1555,std_exp=6712261467000,},
{level=1556,std_exp=6722261467000,},
{level=1557,std_exp=6732261467000,},
{level=1558,std_exp=6742261467000,},
{level=1559,std_exp=6752261467000,},
{level=1560,std_exp=6762261467000,},
{level=1561,std_exp=6772261467000,},
{level=1562,std_exp=6782261467000,},
{level=1563,std_exp=6792261467000,},
{level=1564,std_exp=6802261467000,},
{level=1565,std_exp=6812261467000,},
{level=1566,std_exp=6822261467000,},
{level=1567,std_exp=6832261467000,},
{level=1568,std_exp=6842261467000,},
{level=1569,std_exp=6852261467000,},
{level=1570,std_exp=6862261467000,},
{level=1571,std_exp=6872261467000,},
{level=1572,std_exp=6882261467000,},
{level=1573,std_exp=6892261467000,},
{level=1574,std_exp=6902261467000,},
{level=1575,std_exp=6912261467000,},
{level=1576,std_exp=6922261467000,},
{level=1577,std_exp=6932261467000,},
{level=1578,std_exp=6942261467000,},
{level=1579,std_exp=6952261467000,},
{level=1580,std_exp=6962261467000,},
{level=1581,std_exp=6972261467000,},
{level=1582,std_exp=6982261467000,},
{level=1583,std_exp=6992261467000,},
{level=1584,std_exp=7002261467000,},
{level=1585,std_exp=7012261467000,},
{level=1586,std_exp=7022261467000,},
{level=1587,std_exp=7032261467000,},
{level=1588,std_exp=7042261467000,},
{level=1589,std_exp=7052261467000,},
{level=1590,std_exp=7062261467000,},
{level=1591,std_exp=7072261467000,},
{level=1592,std_exp=7082261467000,},
{level=1593,std_exp=7092261467000,},
{level=1594,std_exp=7102261467000,},
{level=1595,std_exp=7112261467000,},
{level=1596,std_exp=7122261467000,},
{level=1597,std_exp=7132261467000,},
{level=1598,std_exp=7142261467000,},
{level=1599,std_exp=7152261467000,},
{level=1600,std_exp=7162261467000,},
{level=1601,std_exp=7172261467000,},
{level=1602,std_exp=7182261467000,},
{level=1603,std_exp=7192261467000,},
{level=1604,std_exp=7202261467000,},
{level=1605,std_exp=7212261467000,},
{level=1606,std_exp=7222261467000,},
{level=1607,std_exp=7232261467000,},
{level=1608,std_exp=7242261467000,},
{level=1609,std_exp=7252261467000,},
{level=1610,std_exp=7262261467000,},
{level=1611,std_exp=7272261467000,},
{level=1612,std_exp=7282261467000,},
{level=1613,std_exp=7292261467000,},
{level=1614,std_exp=7302261467000,},
{level=1615,std_exp=7312261467000,},
{level=1616,std_exp=7322261467000,},
{level=1617,std_exp=7332261467000,},
{level=1618,std_exp=7342261467000,},
{level=1619,std_exp=7352261467000,},
{level=1620,std_exp=7362261467000,},
{level=1621,std_exp=7372261467000,},
{level=1622,std_exp=7382261467000,},
{level=1623,std_exp=7392261467000,},
{level=1624,std_exp=7402261467000,},
{level=1625,std_exp=7412261467000,},
{level=1626,std_exp=7422261467000,},
{level=1627,std_exp=7432261467000,},
{level=1628,std_exp=7442261467000,},
{level=1629,std_exp=7452261467000,},
{level=1630,std_exp=7462261467000,},
{level=1631,std_exp=7472261467000,},
{level=1632,std_exp=7482261467000,},
{level=1633,std_exp=7492261467000,},
{level=1634,std_exp=7502261467000,},
{level=1635,std_exp=7512261467000,},
{level=1636,std_exp=7522261467000,},
{level=1637,std_exp=7532261467000,},
{level=1638,std_exp=7542261467000,},
{level=1639,std_exp=7552261467000,},
{level=1640,std_exp=7562261467000,},
{level=1641,std_exp=7572261467000,},
{level=1642,std_exp=7582261467000,},
{level=1643,std_exp=7592261467000,},
{level=1644,std_exp=7602261467000,},
{level=1645,std_exp=7612261467000,},
{level=1646,std_exp=7622261467000,},
{level=1647,std_exp=7632261467000,},
{level=1648,std_exp=7642261467000,},
{level=1649,std_exp=7652261467000,},
{level=1650,std_exp=7662261467000,},
{level=1651,std_exp=7672261467000,},
{level=1652,std_exp=7682261467000,},
{level=1653,std_exp=7692261467000,},
{level=1654,std_exp=7702261467000,},
{level=1655,std_exp=7712261467000,},
{level=1656,std_exp=7722261467000,},
{level=1657,std_exp=7732261467000,},
{level=1658,std_exp=7742261467000,},
{level=1659,std_exp=7752261467000,},
{level=1660,std_exp=7762261467000,},
{level=1661,std_exp=7772261467000,},
{level=1662,std_exp=7782261467000,},
{level=1663,std_exp=7792261467000,},
{level=1664,std_exp=7802261467000,},
{level=1665,std_exp=7812261467000,},
{level=1666,std_exp=7822261467000,},
{level=1667,std_exp=7832261467000,},
{level=1668,std_exp=7842261467000,},
{level=1669,std_exp=7852261467000,},
{level=1670,std_exp=7862261467000,},
{level=1671,std_exp=7872261467000,},
{level=1672,std_exp=7882261467000,},
{level=1673,std_exp=7892261467000,},
{level=1674,std_exp=7902261467000,},
{level=1675,std_exp=7912261467000,},
{level=1676,std_exp=7922261467000,},
{level=1677,std_exp=7932261467000,},
{level=1678,std_exp=7942261467000,},
{level=1679,std_exp=7952261467000,},
{level=1680,std_exp=7962261467000,},
{level=1681,std_exp=7972261467000,},
{level=1682,std_exp=7982261467000,},
{level=1683,std_exp=7992261467000,},
{level=1684,std_exp=8002261467000,},
{level=1685,std_exp=8012261467000,},
{level=1686,std_exp=8022261467000,},
{level=1687,std_exp=8032261467000,},
{level=1688,std_exp=8042261467000,},
{level=1689,std_exp=8052261467000,},
{level=1690,std_exp=8062261467000,},
{level=1691,std_exp=8072261467000,},
{level=1692,std_exp=8082261467000,},
{level=1693,std_exp=8092261467000,},
{level=1694,std_exp=8102261467000,},
{level=1695,std_exp=8112261467000,},
{level=1696,std_exp=8122261467000,},
{level=1697,std_exp=8132261467000,},
{level=1698,std_exp=8142261467000,},
{level=1699,std_exp=8152261467000,},
{level=1700,std_exp=8162261467000,},
{level=1701,std_exp=8172261467000,},
{level=1702,std_exp=8182261467000,},
{level=1703,std_exp=8192261467000,},
{level=1704,std_exp=8202261467000,},
{level=1705,std_exp=8212261467000,},
{level=1706,std_exp=8222261467000,},
{level=1707,std_exp=8232261467000,},
{level=1708,std_exp=8242261467000,},
{level=1709,std_exp=8252261467000,},
{level=1710,std_exp=8262261467000,},
{level=1711,std_exp=8272261467000,},
{level=1712,std_exp=8282261467000,},
{level=1713,std_exp=8292261467000,},
{level=1714,std_exp=8302261467000,},
{level=1715,std_exp=8312261467000,},
{level=1716,std_exp=8322261467000,},
{level=1717,std_exp=8332261467000,},
{level=1718,std_exp=8342261467000,},
{level=1719,std_exp=8352261467000,},
{level=1720,std_exp=8362261467000,},
{level=1721,std_exp=8372261467000,},
{level=1722,std_exp=8382261467000,},
{level=1723,std_exp=8392261467000,},
{level=1724,std_exp=8402261467000,},
{level=1725,std_exp=8412261467000,},
{level=1726,std_exp=8422261467000,},
{level=1727,std_exp=8432261467000,},
{level=1728,std_exp=8442261467000,},
{level=1729,std_exp=8452261467000,},
{level=1730,std_exp=8462261467000,},
{level=1731,std_exp=8472261467000,},
{level=1732,std_exp=8482261467000,},
{level=1733,std_exp=8492261467000,},
{level=1734,std_exp=8502261467000,},
{level=1735,std_exp=8512261467000,},
{level=1736,std_exp=8522261467000,},
{level=1737,std_exp=8532261467000,},
{level=1738,std_exp=8542261467000,},
{level=1739,std_exp=8552261467000,},
{level=1740,std_exp=8562261467000,},
{level=1741,std_exp=8572261467000,},
{level=1742,std_exp=8582261467000,},
{level=1743,std_exp=8592261467000,},
{level=1744,std_exp=8602261467000,},
{level=1745,std_exp=8612261467000,},
{level=1746,std_exp=8622261467000,},
{level=1747,std_exp=8632261467000,},
{level=1748,std_exp=8642261467000,},
{level=1749,std_exp=8652261467000,},
{level=1750,std_exp=8662261467000,},
{level=1751,std_exp=8672261467000,},
{level=1752,std_exp=8682261467000,},
{level=1753,std_exp=8692261467000,},
{level=1754,std_exp=8702261467000,},
{level=1755,std_exp=8712261467000,},
{level=1756,std_exp=8722261467000,},
{level=1757,std_exp=8732261467000,},
{level=1758,std_exp=8742261467000,},
{level=1759,std_exp=8752261467000,},
{level=1760,std_exp=8762261467000,},
{level=1761,std_exp=8772261467000,},
{level=1762,std_exp=8782261467000,},
{level=1763,std_exp=8792261467000,},
{level=1764,std_exp=8802261467000,},
{level=1765,std_exp=8812261467000,},
{level=1766,std_exp=8822261467000,},
{level=1767,std_exp=8832261467000,},
{level=1768,std_exp=8842261467000,},
{level=1769,std_exp=8852261467000,},
{level=1770,std_exp=8862261467000,},
{level=1771,std_exp=8872261467000,},
{level=1772,std_exp=8882261467000,},
{level=1773,std_exp=8892261467000,},
{level=1774,std_exp=8902261467000,},
{level=1775,std_exp=8912261467000,},
{level=1776,std_exp=8922261467000,},
{level=1777,std_exp=8932261467000,},
{level=1778,std_exp=8942261467000,},
{level=1779,std_exp=8952261467000,},
{level=1780,std_exp=8962261467000,},
{level=1781,std_exp=8972261467000,},
{level=1782,std_exp=8982261467000,},
{level=1783,std_exp=8992261467000,},
{level=1784,std_exp=9002261467000,},
{level=1785,std_exp=9012261467000,},
{level=1786,std_exp=9022261467000,},
{level=1787,std_exp=9032261467000,},
{level=1788,std_exp=9042261467000,},
{level=1789,std_exp=9052261467000,},
{level=1790,std_exp=9062261467000,},
{level=1791,std_exp=9072261467000,},
{level=1792,std_exp=9082261467000,},
{level=1793,std_exp=9092261467000,},
{level=1794,std_exp=9102261467000,},
{level=1795,std_exp=9112261467000,},
{level=1796,std_exp=9122261467000,},
{level=1797,std_exp=9132261467000,},
{level=1798,std_exp=9142261467000,},
{level=1799,std_exp=9152261467000,},
{level=1800,std_exp=9162261467000,},
{level=1801,std_exp=9172261467000,},
{level=1802,std_exp=9182261467000,},
{level=1803,std_exp=9192261467000,},
{level=1804,std_exp=9202261467000,},
{level=1805,std_exp=9212261467000,},
{level=1806,std_exp=9222261467000,},
{level=1807,std_exp=9232261467000,},
{level=1808,std_exp=9242261467000,},
{level=1809,std_exp=9252261467000,},
{level=1810,std_exp=9262261467000,},
{level=1811,std_exp=9272261467000,},
{level=1812,std_exp=9282261467000,},
{level=1813,std_exp=9292261467000,},
{level=1814,std_exp=9302261467000,},
{level=1815,std_exp=9312261467000,},
{level=1816,std_exp=9322261467000,},
{level=1817,std_exp=9332261467000,},
{level=1818,std_exp=9342261467000,},
{level=1819,std_exp=9352261467000,},
{level=1820,std_exp=9362261467000,},
{level=1821,std_exp=9372261467000,},
{level=1822,std_exp=9382261467000,},
{level=1823,std_exp=9392261467000,},
{level=1824,std_exp=9402261467000,},
{level=1825,std_exp=9412261467000,},
{level=1826,std_exp=9422261467000,},
{level=1827,std_exp=9432261467000,},
{level=1828,std_exp=9442261467000,},
{level=1829,std_exp=9452261467000,},
{level=1830,std_exp=9462261467000,},
{level=1831,std_exp=9472261467000,},
{level=1832,std_exp=9482261467000,},
{level=1833,std_exp=9492261467000,},
{level=1834,std_exp=9502261467000,},
{level=1835,std_exp=9512261467000,},
{level=1836,std_exp=9522261467000,},
{level=1837,std_exp=9532261467000,},
{level=1838,std_exp=9542261467000,},
{level=1839,std_exp=9552261467000,},
{level=1840,std_exp=9562261467000,},
{level=1841,std_exp=9572261467000,},
{level=1842,std_exp=9582261467000,},
{level=1843,std_exp=9592261467000,},
{level=1844,std_exp=9602261467000,},
{level=1845,std_exp=9612261467000,},
{level=1846,std_exp=9622261467000,},
{level=1847,std_exp=9632261467000,},
{level=1848,std_exp=9642261467000,},
{level=1849,std_exp=9652261467000,},
{level=1850,std_exp=9662261467000,},
{level=1851,std_exp=9672261467000,},
{level=1852,std_exp=9682261467000,},
{level=1853,std_exp=9692261467000,},
{level=1854,std_exp=9702261467000,},
{level=1855,std_exp=9712261467000,},
{level=1856,std_exp=9722261467000,},
{level=1857,std_exp=9732261467000,},
{level=1858,std_exp=9742261467000,},
{level=1859,std_exp=9752261467000,},
{level=1860,std_exp=9762261467000,},
{level=1861,std_exp=9772261467000,},
{level=1862,std_exp=9782261467000,},
{level=1863,std_exp=9792261467000,},
{level=1864,std_exp=9802261467000,},
{level=1865,std_exp=9812261467000,},
{level=1866,std_exp=9822261467000,},
{level=1867,std_exp=9832261467000,},
{level=1868,std_exp=9842261467000,},
{level=1869,std_exp=9852261467000,},
{level=1870,std_exp=9862261467000,},
{level=1871,std_exp=9872261467000,},
{level=1872,std_exp=9882261467000,},
{level=1873,std_exp=9892261467000,},
{level=1874,std_exp=9902261467000,},
{level=1875,std_exp=9912261467000,},
{level=1876,std_exp=9922261467000,},
{level=1877,std_exp=9932261467000,},
{level=1878,std_exp=9942261467000,},
{level=1879,std_exp=9952261467000,},
{level=1880,std_exp=9962261467000,},
{level=1881,std_exp=9972261467000,},
{level=1882,std_exp=9982261467000,},
{level=1883,std_exp=9992261467000,},
{level=1884,std_exp=10002261467000,},
{level=1885,std_exp=10012261467000,},
{level=1886,std_exp=10022261467000,},
{level=1887,std_exp=10032261467000,},
{level=1888,std_exp=10042261467000,},
{level=1889,std_exp=10052261467000,},
{level=1890,std_exp=10062261467000,},
{level=1891,std_exp=10072261467000,},
{level=1892,std_exp=10082261467000,},
{level=1893,std_exp=10092261467000,},
{level=1894,std_exp=10102261467000,},
{level=1895,std_exp=10112261467000,},
{level=1896,std_exp=10122261467000,},
{level=1897,std_exp=10132261467000,},
{level=1898,std_exp=10142261467000,},
{level=1899,std_exp=10152261467000,},
{level=1900,std_exp=10162261467000,},
{level=1901,std_exp=10172261467000,},
{level=1902,std_exp=10182261467000,},
{level=1903,std_exp=10192261467000,},
{level=1904,std_exp=10202261467000,},
{level=1905,std_exp=10212261467000,},
{level=1906,std_exp=10222261467000,},
{level=1907,std_exp=10232261467000,},
{level=1908,std_exp=10242261467000,},
{level=1909,std_exp=10252261467000,},
{level=1910,std_exp=10262261467000,},
{level=1911,std_exp=10272261467000,},
{level=1912,std_exp=10282261467000,},
{level=1913,std_exp=10292261467000,},
{level=1914,std_exp=10302261467000,},
{level=1915,std_exp=10312261467000,},
{level=1916,std_exp=10322261467000,},
{level=1917,std_exp=10332261467000,},
{level=1918,std_exp=10342261467000,},
{level=1919,std_exp=10352261467000,},
{level=1920,std_exp=10362261467000,},
{level=1921,std_exp=10372261467000,},
{level=1922,std_exp=10382261467000,},
{level=1923,std_exp=10392261467000,},
{level=1924,std_exp=10402261467000,},
{level=1925,std_exp=10412261467000,},
{level=1926,std_exp=10422261467000,},
{level=1927,std_exp=10432261467000,},
{level=1928,std_exp=10442261467000,},
{level=1929,std_exp=10452261467000,},
{level=1930,std_exp=10462261467000,},
{level=1931,std_exp=10472261467000,},
{level=1932,std_exp=10482261467000,},
{level=1933,std_exp=10492261467000,},
{level=1934,std_exp=10502261467000,},
{level=1935,std_exp=10512261467000,},
{level=1936,std_exp=10522261467000,},
{level=1937,std_exp=10532261467000,},
{level=1938,std_exp=10542261467000,},
{level=1939,std_exp=10552261467000,},
{level=1940,std_exp=10562261467000,},
{level=1941,std_exp=10572261467000,},
{level=1942,std_exp=10582261467000,},
{level=1943,std_exp=10592261467000,},
{level=1944,std_exp=10602261467000,},
{level=1945,std_exp=10612261467000,},
{level=1946,std_exp=10622261467000,},
{level=1947,std_exp=10632261467000,},
{level=1948,std_exp=10642261467000,},
{level=1949,std_exp=10652261467000,},
{level=1950,std_exp=10662261467000,},
{level=1951,std_exp=10672261467000,},
{level=1952,std_exp=10682261467000,},
{level=1953,std_exp=10692261467000,},
{level=1954,std_exp=10702261467000,},
{level=1955,std_exp=10712261467000,},
{level=1956,std_exp=10722261467000,},
{level=1957,std_exp=10732261467000,},
{level=1958,std_exp=10742261467000,},
{level=1959,std_exp=10752261467000,},
{level=1960,std_exp=10762261467000,},
{level=1961,std_exp=10772261467000,},
{level=1962,std_exp=10782261467000,},
{level=1963,std_exp=10792261467000,},
{level=1964,std_exp=10802261467000,},
{level=1965,std_exp=10812261467000,},
{level=1966,std_exp=10822261467000,},
{level=1967,std_exp=10832261467000,},
{level=1968,std_exp=10842261467000,},
{level=1969,std_exp=10852261467000,},
{level=1970,std_exp=10862261467000,},
{level=1971,std_exp=10872261467000,},
{level=1972,std_exp=10882261467000,},
{level=1973,std_exp=10892261467000,},
{level=1974,std_exp=10902261467000,},
{level=1975,std_exp=10912261467000,},
{level=1976,std_exp=10922261467000,},
{level=1977,std_exp=10932261467000,},
{level=1978,std_exp=10942261467000,},
{level=1979,std_exp=10952261467000,},
{level=1980,std_exp=10962261467000,},
{level=1981,std_exp=10972261467000,},
{level=1982,std_exp=10982261467000,},
{level=1983,std_exp=10992261467000,},
{level=1984,std_exp=11002261467000,},
{level=1985,std_exp=11012261467000,},
{level=1986,std_exp=11022261467000,},
{level=1987,std_exp=11032261467000,},
{level=1988,std_exp=11042261467000,},
{level=1989,std_exp=11052261467000,},
{level=1990,std_exp=11062261467000,},
{level=1991,std_exp=11072261467000,},
{level=1992,std_exp=11082261467000,},
{level=1993,std_exp=11092261467000,},
{level=1994,std_exp=11102261467000,},
{level=1995,std_exp=11112261467000,},
{level=1996,std_exp=11122261467000,},
{level=1997,std_exp=11132261467000,},
{level=1998,std_exp=11142261467000,},
{level=1999,std_exp=11152261467000,},
{level=2000,std_exp=11162261467000,}
},

exp_efficiency_meta_table_map={
[202]=1,	-- depth:1
[191]=202,	-- depth:2
[201]=202,	-- depth:2
[200]=202,	-- depth:2
[199]=202,	-- depth:2
[197]=202,	-- depth:2
[196]=202,	-- depth:2
[195]=202,	-- depth:2
[194]=202,	-- depth:2
[203]=202,	-- depth:2
[192]=202,	-- depth:2
[193]=202,	-- depth:2
[198]=202,	-- depth:2
[204]=202,	-- depth:2
[213]=202,	-- depth:2
[206]=202,	-- depth:2
[221]=202,	-- depth:2
[220]=202,	-- depth:2
[190]=202,	-- depth:2
[219]=202,	-- depth:2
[218]=202,	-- depth:2
[217]=202,	-- depth:2
[216]=202,	-- depth:2
[215]=202,	-- depth:2
[214]=202,	-- depth:2
[212]=202,	-- depth:2
[211]=202,	-- depth:2
[210]=202,	-- depth:2
[209]=202,	-- depth:2
[208]=202,	-- depth:2
[207]=202,	-- depth:2
[205]=202,	-- depth:2
[189]=202,	-- depth:2
[166]=202,	-- depth:2
[187]=202,	-- depth:2
[167]=202,	-- depth:2
[165]=202,	-- depth:2
[164]=202,	-- depth:2
[163]=202,	-- depth:2
[162]=202,	-- depth:2
[161]=202,	-- depth:2
[160]=202,	-- depth:2
[159]=202,	-- depth:2
[158]=202,	-- depth:2
[157]=202,	-- depth:2
[156]=202,	-- depth:2
[155]=202,	-- depth:2
[154]=202,	-- depth:2
[222]=202,	-- depth:2
[153]=202,	-- depth:2
[168]=202,	-- depth:2
[188]=202,	-- depth:2
[169]=202,	-- depth:2
[171]=202,	-- depth:2
[186]=202,	-- depth:2
[185]=202,	-- depth:2
[184]=202,	-- depth:2
[183]=202,	-- depth:2
[182]=202,	-- depth:2
[181]=202,	-- depth:2
[180]=202,	-- depth:2
[179]=202,	-- depth:2
[178]=202,	-- depth:2
[177]=202,	-- depth:2
[176]=202,	-- depth:2
[175]=202,	-- depth:2
[174]=202,	-- depth:2
[173]=202,	-- depth:2
[172]=202,	-- depth:2
[170]=202,	-- depth:2
[223]=202,	-- depth:2
[272]=202,	-- depth:2
[225]=202,	-- depth:2
[279]=202,	-- depth:2
[278]=202,	-- depth:2
[277]=202,	-- depth:2
[276]=202,	-- depth:2
[275]=202,	-- depth:2
[274]=202,	-- depth:2
[273]=202,	-- depth:2
[271]=202,	-- depth:2
[270]=202,	-- depth:2
[269]=202,	-- depth:2
[268]=202,	-- depth:2
[267]=202,	-- depth:2
[266]=202,	-- depth:2
[265]=202,	-- depth:2
[264]=202,	-- depth:2
[263]=202,	-- depth:2
[281]=280,	-- depth:1
[283]=280,	-- depth:1
[152]=202,	-- depth:2
[297]=280,	-- depth:1
[296]=280,	-- depth:1
[295]=280,	-- depth:1
[294]=280,	-- depth:1
[293]=280,	-- depth:1
[292]=280,	-- depth:1
[291]=280,	-- depth:1
[290]=280,	-- depth:1
[289]=280,	-- depth:1
[288]=280,	-- depth:1
[287]=280,	-- depth:1
[286]=280,	-- depth:1
[285]=280,	-- depth:1
[284]=280,	-- depth:1
[282]=280,	-- depth:1
[224]=202,	-- depth:2
[262]=202,	-- depth:2
[260]=202,	-- depth:2
[240]=202,	-- depth:2
[239]=202,	-- depth:2
[238]=202,	-- depth:2
[237]=202,	-- depth:2
[236]=202,	-- depth:2
[235]=202,	-- depth:2
[234]=202,	-- depth:2
[233]=202,	-- depth:2
[232]=202,	-- depth:2
[231]=202,	-- depth:2
[230]=202,	-- depth:2
[229]=202,	-- depth:2
[228]=202,	-- depth:2
[227]=202,	-- depth:2
[226]=202,	-- depth:2
[241]=202,	-- depth:2
[261]=202,	-- depth:2
[242]=202,	-- depth:2
[244]=202,	-- depth:2
[259]=202,	-- depth:2
[258]=202,	-- depth:2
[257]=202,	-- depth:2
[256]=202,	-- depth:2
[255]=202,	-- depth:2
[254]=202,	-- depth:2
[253]=202,	-- depth:2
[252]=202,	-- depth:2
[251]=202,	-- depth:2
[250]=202,	-- depth:2
[249]=202,	-- depth:2
[248]=202,	-- depth:2
[247]=202,	-- depth:2
[246]=202,	-- depth:2
[245]=202,	-- depth:2
[243]=202,	-- depth:2
[151]=202,	-- depth:2
[52]=202,	-- depth:2
[149]=202,	-- depth:2
[53]=202,	-- depth:2
[298]=280,	-- depth:1
[51]=202,	-- depth:2
[50]=202,	-- depth:2
[49]=202,	-- depth:2
[48]=202,	-- depth:2
[54]=202,	-- depth:2
[47]=202,	-- depth:2
[45]=202,	-- depth:2
[44]=202,	-- depth:2
[43]=202,	-- depth:2
[42]=202,	-- depth:2
[41]=202,	-- depth:2
[40]=202,	-- depth:2
[46]=202,	-- depth:2
[55]=202,	-- depth:2
[56]=202,	-- depth:2
[57]=202,	-- depth:2
[72]=202,	-- depth:2
[71]=202,	-- depth:2
[70]=202,	-- depth:2
[69]=202,	-- depth:2
[68]=202,	-- depth:2
[67]=202,	-- depth:2
[66]=202,	-- depth:2
[65]=202,	-- depth:2
[64]=202,	-- depth:2
[63]=202,	-- depth:2
[62]=202,	-- depth:2
[61]=202,	-- depth:2
[60]=202,	-- depth:2
[59]=202,	-- depth:2
[58]=202,	-- depth:2
[39]=202,	-- depth:2
[38]=202,	-- depth:2
[37]=202,	-- depth:2
[36]=202,	-- depth:2
[16]=202,	-- depth:2
[15]=202,	-- depth:2
[14]=202,	-- depth:2
[13]=202,	-- depth:2
[12]=202,	-- depth:2
[11]=202,	-- depth:2
[10]=202,	-- depth:2
[9]=202,	-- depth:2
[8]=202,	-- depth:2
[7]=202,	-- depth:2
[6]=202,	-- depth:2
[5]=202,	-- depth:2
[4]=202,	-- depth:2
[3]=202,	-- depth:2
[2]=202,	-- depth:2
[17]=202,	-- depth:2
[73]=202,	-- depth:2
[18]=202,	-- depth:2
[20]=202,	-- depth:2
[35]=202,	-- depth:2
[34]=202,	-- depth:2
[33]=202,	-- depth:2
[32]=202,	-- depth:2
[31]=202,	-- depth:2
[30]=202,	-- depth:2
[29]=202,	-- depth:2
[28]=202,	-- depth:2
[27]=202,	-- depth:2
[26]=202,	-- depth:2
[25]=202,	-- depth:2
[24]=202,	-- depth:2
[23]=202,	-- depth:2
[22]=202,	-- depth:2
[21]=202,	-- depth:2
[19]=202,	-- depth:2
[74]=202,	-- depth:2
[75]=202,	-- depth:2
[76]=202,	-- depth:2
[129]=202,	-- depth:2
[128]=202,	-- depth:2
[127]=202,	-- depth:2
[126]=202,	-- depth:2
[125]=202,	-- depth:2
[124]=202,	-- depth:2
[123]=202,	-- depth:2
[122]=202,	-- depth:2
[121]=202,	-- depth:2
[120]=202,	-- depth:2
[119]=202,	-- depth:2
[118]=202,	-- depth:2
[117]=202,	-- depth:2
[116]=202,	-- depth:2
[115]=202,	-- depth:2
[130]=202,	-- depth:2
[114]=202,	-- depth:2
[131]=202,	-- depth:2
[133]=202,	-- depth:2
[148]=202,	-- depth:2
[147]=202,	-- depth:2
[146]=202,	-- depth:2
[145]=202,	-- depth:2
[144]=202,	-- depth:2
[143]=202,	-- depth:2
[142]=202,	-- depth:2
[141]=202,	-- depth:2
[140]=202,	-- depth:2
[139]=202,	-- depth:2
[138]=202,	-- depth:2
[137]=202,	-- depth:2
[136]=202,	-- depth:2
[135]=202,	-- depth:2
[134]=202,	-- depth:2
[132]=202,	-- depth:2
[150]=202,	-- depth:2
[113]=202,	-- depth:2
[111]=202,	-- depth:2
[91]=202,	-- depth:2
[90]=202,	-- depth:2
[89]=202,	-- depth:2
[88]=202,	-- depth:2
[87]=202,	-- depth:2
[86]=202,	-- depth:2
[85]=202,	-- depth:2
[84]=202,	-- depth:2
[83]=202,	-- depth:2
[82]=202,	-- depth:2
[81]=202,	-- depth:2
[80]=202,	-- depth:2
[79]=202,	-- depth:2
[78]=202,	-- depth:2
[77]=202,	-- depth:2
[92]=202,	-- depth:2
[112]=202,	-- depth:2
[93]=202,	-- depth:2
[95]=202,	-- depth:2
[110]=202,	-- depth:2
[109]=202,	-- depth:2
[108]=202,	-- depth:2
[107]=202,	-- depth:2
[106]=202,	-- depth:2
[105]=202,	-- depth:2
[104]=202,	-- depth:2
[103]=202,	-- depth:2
[102]=202,	-- depth:2
[101]=202,	-- depth:2
[100]=202,	-- depth:2
[99]=202,	-- depth:2
[98]=202,	-- depth:2
[97]=202,	-- depth:2
[96]=202,	-- depth:2
[94]=202,	-- depth:2
[299]=280,	-- depth:1
[399]=280,	-- depth:1
[301]=280,	-- depth:1
[505]=202,	-- depth:2
[504]=202,	-- depth:2
[503]=202,	-- depth:2
[502]=202,	-- depth:2
[501]=202,	-- depth:2
[499]=202,	-- depth:2
[506]=202,	-- depth:2
[498]=202,	-- depth:2
[496]=202,	-- depth:2
[495]=202,	-- depth:2
[494]=202,	-- depth:2
[493]=202,	-- depth:2
[492]=202,	-- depth:2
[491]=202,	-- depth:2
[497]=202,	-- depth:2
[507]=202,	-- depth:2
[508]=202,	-- depth:2
[509]=202,	-- depth:2
[524]=202,	-- depth:2
[523]=202,	-- depth:2
[522]=202,	-- depth:2
[521]=202,	-- depth:2
[520]=202,	-- depth:2
[519]=202,	-- depth:2
[518]=202,	-- depth:2
[517]=202,	-- depth:2
[516]=202,	-- depth:2
[515]=202,	-- depth:2
[514]=202,	-- depth:2
[513]=202,	-- depth:2
[512]=202,	-- depth:2
[511]=202,	-- depth:2
[510]=202,	-- depth:2
[490]=202,	-- depth:2
[489]=202,	-- depth:2
[488]=202,	-- depth:2
[487]=202,	-- depth:2
[467]=202,	-- depth:2
[466]=202,	-- depth:2
[465]=202,	-- depth:2
[464]=202,	-- depth:2
[463]=202,	-- depth:2
[462]=202,	-- depth:2
[461]=202,	-- depth:2
[460]=202,	-- depth:2
[459]=202,	-- depth:2
[458]=202,	-- depth:2
[457]=202,	-- depth:2
[456]=202,	-- depth:2
[455]=202,	-- depth:2
[454]=202,	-- depth:2
[453]=202,	-- depth:2
[468]=202,	-- depth:2
[525]=202,	-- depth:2
[469]=202,	-- depth:2
[471]=202,	-- depth:2
[486]=202,	-- depth:2
[485]=202,	-- depth:2
[484]=202,	-- depth:2
[483]=202,	-- depth:2
[482]=202,	-- depth:2
[481]=202,	-- depth:2
[480]=202,	-- depth:2
[479]=202,	-- depth:2
[478]=202,	-- depth:2
[477]=202,	-- depth:2
[476]=202,	-- depth:2
[475]=202,	-- depth:2
[474]=202,	-- depth:2
[473]=202,	-- depth:2
[472]=202,	-- depth:2
[470]=202,	-- depth:2
[452]=202,	-- depth:2
[526]=202,	-- depth:2
[528]=202,	-- depth:2
[580]=202,	-- depth:2
[579]=202,	-- depth:2
[578]=202,	-- depth:2
[577]=202,	-- depth:2
[576]=202,	-- depth:2
[575]=202,	-- depth:2
[581]=202,	-- depth:2
[574]=202,	-- depth:2
[572]=202,	-- depth:2
[571]=202,	-- depth:2
[570]=202,	-- depth:2
[569]=202,	-- depth:2
[568]=202,	-- depth:2
[567]=202,	-- depth:2
[573]=202,	-- depth:2
[582]=202,	-- depth:2
[583]=202,	-- depth:2
[584]=202,	-- depth:2
[599]=202,	-- depth:2
[598]=202,	-- depth:2
[597]=202,	-- depth:2
[596]=202,	-- depth:2
[595]=202,	-- depth:2
[594]=202,	-- depth:2
[593]=202,	-- depth:2
[592]=202,	-- depth:2
[591]=202,	-- depth:2
[590]=202,	-- depth:2
[589]=202,	-- depth:2
[588]=202,	-- depth:2
[587]=202,	-- depth:2
[586]=202,	-- depth:2
[585]=202,	-- depth:2
[566]=202,	-- depth:2
[565]=202,	-- depth:2
[564]=202,	-- depth:2
[563]=202,	-- depth:2
[543]=202,	-- depth:2
[542]=202,	-- depth:2
[541]=202,	-- depth:2
[540]=202,	-- depth:2
[539]=202,	-- depth:2
[538]=202,	-- depth:2
[537]=202,	-- depth:2
[536]=202,	-- depth:2
[535]=202,	-- depth:2
[534]=202,	-- depth:2
[533]=202,	-- depth:2
[532]=202,	-- depth:2
[531]=202,	-- depth:2
[530]=202,	-- depth:2
[529]=202,	-- depth:2
[544]=202,	-- depth:2
[527]=202,	-- depth:2
[545]=202,	-- depth:2
[547]=202,	-- depth:2
[562]=202,	-- depth:2
[561]=202,	-- depth:2
[560]=202,	-- depth:2
[559]=202,	-- depth:2
[558]=202,	-- depth:2
[557]=202,	-- depth:2
[556]=202,	-- depth:2
[555]=202,	-- depth:2
[554]=202,	-- depth:2
[553]=202,	-- depth:2
[552]=202,	-- depth:2
[551]=202,	-- depth:2
[550]=202,	-- depth:2
[549]=202,	-- depth:2
[548]=202,	-- depth:2
[546]=202,	-- depth:2
[300]=280,	-- depth:1
[451]=202,	-- depth:2
[449]=280,	-- depth:1
[353]=280,	-- depth:1
[352]=280,	-- depth:1
[351]=280,	-- depth:1
[350]=280,	-- depth:1
[349]=280,	-- depth:1
[348]=280,	-- depth:1
[354]=280,	-- depth:1
[347]=280,	-- depth:1
[345]=280,	-- depth:1
[344]=280,	-- depth:1
[343]=280,	-- depth:1
[342]=280,	-- depth:1
[341]=280,	-- depth:1
[340]=280,	-- depth:1
[346]=280,	-- depth:1
[355]=280,	-- depth:1
[356]=280,	-- depth:1
[357]=280,	-- depth:1
[372]=280,	-- depth:1
[371]=280,	-- depth:1
[370]=280,	-- depth:1
[369]=280,	-- depth:1
[368]=280,	-- depth:1
[367]=280,	-- depth:1
[366]=280,	-- depth:1
[365]=280,	-- depth:1
[364]=280,	-- depth:1
[363]=280,	-- depth:1
[362]=280,	-- depth:1
[361]=280,	-- depth:1
[360]=280,	-- depth:1
[359]=280,	-- depth:1
[358]=280,	-- depth:1
[339]=280,	-- depth:1
[338]=280,	-- depth:1
[337]=280,	-- depth:1
[336]=280,	-- depth:1
[316]=280,	-- depth:1
[315]=280,	-- depth:1
[314]=280,	-- depth:1
[313]=280,	-- depth:1
[312]=280,	-- depth:1
[311]=280,	-- depth:1
[310]=280,	-- depth:1
[309]=280,	-- depth:1
[308]=280,	-- depth:1
[307]=280,	-- depth:1
[306]=280,	-- depth:1
[305]=280,	-- depth:1
[304]=280,	-- depth:1
[303]=280,	-- depth:1
[302]=280,	-- depth:1
[317]=280,	-- depth:1
[373]=280,	-- depth:1
[318]=280,	-- depth:1
[320]=280,	-- depth:1
[335]=280,	-- depth:1
[334]=280,	-- depth:1
[333]=280,	-- depth:1
[332]=280,	-- depth:1
[331]=280,	-- depth:1
[330]=280,	-- depth:1
[329]=280,	-- depth:1
[328]=280,	-- depth:1
[327]=280,	-- depth:1
[326]=280,	-- depth:1
[325]=280,	-- depth:1
[324]=280,	-- depth:1
[323]=280,	-- depth:1
[322]=280,	-- depth:1
[321]=280,	-- depth:1
[319]=280,	-- depth:1
[450]=202,	-- depth:2
[374]=280,	-- depth:1
[376]=280,	-- depth:1
[429]=280,	-- depth:1
[428]=280,	-- depth:1
[427]=280,	-- depth:1
[426]=280,	-- depth:1
[425]=280,	-- depth:1
[424]=280,	-- depth:1
[430]=280,	-- depth:1
[423]=280,	-- depth:1
[421]=280,	-- depth:1
[420]=280,	-- depth:1
[419]=280,	-- depth:1
[418]=280,	-- depth:1
[417]=280,	-- depth:1
[416]=280,	-- depth:1
[422]=280,	-- depth:1
[431]=280,	-- depth:1
[432]=280,	-- depth:1
[433]=280,	-- depth:1
[448]=280,	-- depth:1
[447]=280,	-- depth:1
[446]=280,	-- depth:1
[445]=280,	-- depth:1
[444]=280,	-- depth:1
[443]=280,	-- depth:1
[442]=280,	-- depth:1
[441]=280,	-- depth:1
[440]=280,	-- depth:1
[439]=280,	-- depth:1
[438]=280,	-- depth:1
[437]=280,	-- depth:1
[436]=280,	-- depth:1
[435]=280,	-- depth:1
[434]=280,	-- depth:1
[415]=280,	-- depth:1
[414]=280,	-- depth:1
[413]=280,	-- depth:1
[412]=280,	-- depth:1
[391]=280,	-- depth:1
[390]=280,	-- depth:1
[389]=280,	-- depth:1
[388]=280,	-- depth:1
[387]=280,	-- depth:1
[386]=280,	-- depth:1
[385]=280,	-- depth:1
[384]=280,	-- depth:1
[383]=280,	-- depth:1
[382]=280,	-- depth:1
[381]=280,	-- depth:1
[380]=280,	-- depth:1
[379]=280,	-- depth:1
[378]=280,	-- depth:1
[377]=280,	-- depth:1
[392]=280,	-- depth:1
[375]=280,	-- depth:1
[393]=280,	-- depth:1
[395]=280,	-- depth:1
[411]=280,	-- depth:1
[410]=280,	-- depth:1
[409]=280,	-- depth:1
[408]=280,	-- depth:1
[407]=280,	-- depth:1
[406]=280,	-- depth:1
[405]=280,	-- depth:1
[404]=280,	-- depth:1
[403]=280,	-- depth:1
[402]=280,	-- depth:1
[401]=280,	-- depth:1
[400]=280,	-- depth:1
[398]=280,	-- depth:1
[397]=280,	-- depth:1
[396]=280,	-- depth:1
[394]=280,	-- depth:1
[500]=202,	-- depth:2
},
offline_guaji_card={
{},
{is_hang_up_card=22537,time=6,},
{is_hang_up_card=22538,time=5,}
},

offline_guaji_card_meta_table_map={
},
meditation_pos={
{},
{pos_x=252,pos_y=229,},
{pos_x=250,},
{pos_x=245,pos_y=222,},
{pos_y=217,},
{pos_y=216,},
{pos_x=223,pos_y=219,},
{pos_x=220,pos_y=223,},
{pos_x=217,pos_y=226,},
{pos_x=212,},
{pos_y=241,},
{pos_x=215,pos_y=246,},
{pos_x=218,pos_y=250,},
{pos_x=221,pos_y=253,},
{pos_x=227,pos_y=256,},
{pos_x=239,pos_y=257,},
{pos_x=242,pos_y=255,},
{pos_x=247,pos_y=252,},
{pos_x=250,pos_y=247,},
{pos_y=243,}
},

meditation_pos_meta_table_map={
[11]=10,	-- depth:1
[6]=15,	-- depth:1
[5]=16,	-- depth:1
[3]=9,	-- depth:1
},
lilian_show={
{},
{min_level=151,max_level=200,dropid1=21255,min_exp_west_level=1,max_exp_west_level=2,now_level_order=4,},
{min_level=201,max_level=250,dropid1=21256,min_exp_west_level=2,max_exp_west_level=3,now_level_order=5,},
{min_level=251,max_level=300,dropid1=21257,min_exp_west_level=3,max_exp_west_level=4,now_level_order=6,},
{min_level=301,max_level=350,dropid1=21258,min_exp_west_level=4,max_exp_west_level=5,now_level_order=7,},
{min_level=351,max_level=420,dropid1=21259,min_exp_west_level=5,max_exp_west_level=6,now_level_order=8,},
{min_level=421,max_level=520,dropid1=21260,min_exp_west_level=6,max_exp_west_level=7,now_level_order=9,},
{min_level=521,max_level=620,dropid1=21261,min_exp_west_level=7,max_exp_west_level=8,now_level_order=10,},
{min_level=621,max_level=720,dropid1=21262,min_exp_west_level=8,max_exp_west_level=9,now_level_order=11,},
{min_level=721,max_level=820,dropid1=21263,min_exp_west_level=9,max_exp_west_level=10,now_level_order=12,},
{min_level=821,max_level=920,dropid1=21264,min_exp_west_level=10,max_exp_west_level=11,now_level_order=13,},
{min_level=921,max_level=1020,dropid1=21265,min_exp_west_level=11,max_exp_west_level=12,now_level_order=14,},
{min_level=1021,max_level=1120,dropid1=21266,min_exp_west_level=12,max_exp_west_level=13,now_level_order=15,},
{min_level=1121,max_level=1220,dropid1=21267,min_exp_west_level=13,max_exp_west_level=14,now_level_order=16,},
{min_level=1221,max_level=1320,dropid1=21268,min_exp_west_level=14,max_exp_west_level=15,now_level_order=17,},
{min_level=1321,max_level=2000,dropid1=21269,min_exp_west_level=15,max_exp_west_level=16,now_level_order=18,}
},

lilian_show_meta_table_map={
},
rest_reward_default_table={level=1,hang_monster_id=10509,exp=400,scene_id=1003,pos_x=265,pos_y=323,radius=10,pre_scene_id=1004,pre_pos_x=265,pre_pos_y=323,pre_radius=10,hang_monster_id_scene_id=1005,hang_monster_id_pos_x=166,hang_monster_id_pos_y=65,},

other_default_table={hang_limit_level=70,hang_begin_calc_time=300,hang_max_offlie_time=28800,check_obj_interval=5,exp_interval=10,sit_area="1003#212,215#212,257#253,257#253,215",exp_item=22011,xiuwei_interval=5,second_add_xiuwei_lingli=30,},

exp_efficiency_default_table={level=1,std_exp=341,std_cap=1,exp_times=0.3,},

offline_guaji_card_default_table={is_hang_up_card=36024,time=2,},

meditation_pos_default_table={scene_id=1003,pos_x=253,pos_y=231,radius=3,},

lilian_show_default_table={min_level=1,max_level=150,dropid1=21254,min_exp_west_level=0,max_exp_west_level=1,now_level_order=3,}

}


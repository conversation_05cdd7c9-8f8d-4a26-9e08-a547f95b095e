XuYuanFreshPoolWGData = XuYuanFreshPoolWGData or BaseClass()
XuYuanFreshPoolWGData.REWARD_TYPE = {
	BIG = 1,
	NORMAL = 2,
	LOW = 3
}

function XuYuanFreshPoolWGData:__init()
	if XuYuanFreshPoolWGData.Instance then
		ErrorLog("[XuYuanFreshPoolWGData] Attemp to create a singleton twice !")
	end
	XuYuanFreshPoolWGData.Instance = self

	self.fws_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_yanhua_shengdian3_auto")
    self.param_cfg = ListToMap(self.fws_cfg.config_param, "grade")
    self.grade_cfg = ListToMap(self.fws_cfg.grade, "grade")
    self.consume_cfg = ListToMapList(self.fws_cfg.consume, "consume")
    self.reward_cfg = ListToMap(self.fws_cfg.reward, "reward", "reward_id")
    self.rebate_cfg = ListToMap(self.fws_cfg.rebate, "rebate", "index")
    self.yanhua_cfg = ListToMap(self.fws_cfg.yanhua_show, "grade")
    self.role_sp_guarantee_cfg = ListToMapList(self.fws_cfg.role_sp_guarantee, "reward")
    self.item_random_desc_cfg = ListToMapList(self.fws_cfg.item_random_desc, "grade")
    self.show_model_cfg = ListToMapList(self.fws_cfg.show_model_map, "rebate", "index")
    self.model_detail_cfg = ListToMapList(self.fws_cfg.exchange_model_detail, "rebate", "index")
    self.other_cfg = self.fws_cfg.other[1]
    self.activity_day_cfg = ListToMap(self.fws_cfg.activity_day, "grade", "activity_day")
    self.exchange_shop_cfg = ListToMapList(self.fws_cfg.exchange_shop, "grade")

    self.grade = 0
    self.show_model_suit_index = 0
    self.cur_pool_index = -1
    self.cur_pool_index_prob = -1
    self.cur_grade = -1
    self.shop_big_award = {}
    self.shop_data_list = {}
    self.exchange_shop_list = {}

	RemindManager.Instance:Register(RemindName.XuYuanFreshPool, BindTool.Bind(self.ShowRed, self))
	self:RemindInBag(RemindName.XuYuanFreshPool)

	self.baoxia_is_skipanim = false
end

function XuYuanFreshPoolWGData:__delete()
    self.cur_grade = nil
    self.shop_big_award = nil
    self.shop_data_list = nil
    self.exchange_shop_list = nil
    RemindManager.Instance:UnRegister(RemindName.XuYuanFreshPool)
	XuYuanFreshPoolWGData.Instance = nil
end

function XuYuanFreshPoolWGData:ShowRed()
	if not self:GetActIsOpen() then
        return 0
    end

    --背包有道具
    if self:GetEnoughItem() then
        return 1
    end

    --返利
    if self:GetFanliRed() then
        return 1
    end

    return 0
end

function XuYuanFreshPoolWGData:GetEnoughItem()
    local item_list = self:GetItemDataChangeList()
    for i, v in pairs(item_list) do
        if ItemWGData.Instance:GetItemNumInBagById(v) > 0 then
            return true
        end
    end
    return false
end

function XuYuanFreshPoolWGData:GetFanliRed()
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1
    local draw_time = self:GetCurDrawTimes()
    for i, v in ipairs(self.rebate_cfg[rebate]) do
        if draw_time >= v.lotto_num then
            if not self:GetFanliHasGet(v.index) then
                return true
            end
        else
            return false
        end
    end
end

function XuYuanFreshPoolWGData:GetActIsOpen()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
    if not is_open then
        return false
    end

    local cur_cfg = self:GetCurCfg()
    if not cur_cfg then
        return false
    end

    return RoleWGData.Instance:GetRoleLevel() >= cur_cfg.open_level
end

function XuYuanFreshPoolWGData:GetCurCfg()
    return self.param_cfg[self.grade or 1]
end

function XuYuanFreshPoolWGData:GetGradeCfg()
    local grade_cfg = self.grade_cfg[self.grade or 0]
    if grade_cfg then
        return grade_cfg
    end
    print_error("活动配置没有对应档次：" .. self.grade .. ", 请检查")
end

function XuYuanFreshPoolWGData:GetConsumeCfg()
    local cfg = self:GetGradeCfg()
    if cfg then
        return self.consume_cfg[cfg.consume]
    end
    return {}
end

function XuYuanFreshPoolWGData:GetRewardById(reward_pool_id, reward_id)
    return self.reward_cfg[reward_pool_id or 1][reward_id]
end

function XuYuanFreshPoolWGData:GetItemDataChangeList()
    if not self.item_data_change_list then
        self.item_data_change_list = {}
        local cfg = XuYuanFreshPoolWGData.Instance:GetConsumeCfg()
        for i, v in pairs(cfg) do
            table.insert(self.item_data_change_list, v.yanhua_item.item_id)
        end
    end
    return self.item_data_change_list
end

function XuYuanFreshPoolWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.draw_btn_index = btn_index
	end

	return self.draw_btn_index
end

--协议信息
function XuYuanFreshPoolWGData:SetInfo(protocol)
    self.grade = protocol.grade
    if self.grade == -1 then
        --print_error("档次出错: ", self.grade)
        self.grade = 0
    end
    self.cur_draw_times = protocol.person_draw_count
    self.fetch_flag = protocol.leiji_reward_fetch_flag
    self.is_skip_comic = protocol.is_skip_comic			--跳过动画？
    self.sp_guarantee_x = protocol.sp_guarantee_x		--特殊保底次数？ 弃用
    self.sp_guarantee_n = protocol.sp_guarantee_n		--特殊保底轮数？
    self.sp_enter_num = protocol.sp_enter_num			--进入保底库次数？

    self.gather_small_count = protocol.gather_small_count
    self.gather_big_count = protocol.gather_big_count

	self:SetModelSuiIndex()
end

function XuYuanFreshPoolWGData:GetCurDrawTimes()
    return self.cur_draw_times or 0
end

function XuYuanFreshPoolWGData:RemindInBag(remind_name)
    local check_list = self:GetItemDataChangeList()
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, check_list, nil)
end

function XuYuanFreshPoolWGData:GetFanliHasGet(index)
    return bit:_and(self.fetch_flag or 0, bit:_lshift(1, index - 1)) ~= 0
end

function XuYuanFreshPoolWGData:GetFanliList(len)
    local cfg = self:GetGradeCfg()
    local rebate = cfg and cfg.rebate or 1

    local rebate_list = self.rebate_cfg[rebate] or {}
    local list = {}
    local max = #rebate_list
    local len = len or max
    local t = {}
    for i = 0, len-1 do
        if rebate_list[i] and rebate_list[i].display_reward == 0 then
            t = {
                data = rebate_list[max - i],
                has_get = self:GetFanliHasGet(max - i) and 1 or 0
            }
            table.insert(list, 1, t)
        end
    end

    -- 改成全部显示，不需要再筛选
    -- local flag
    -- for i = max - len, 1, -1 do
    --     flag = self:GetFanliHasGet(i)
    --     if not flag and self.rebate_cfg[rebate][i].display_reward == 0 then
    --         t = {
    --             data = self.rebate_cfg[rebate][i],
    --             has_get = 0
    --         }
    --         table.insert(list, 1, t)
    --         table.remove(list, len + 1)
    --     end
    -- end

    local max_num = #list
    for index, value in ipairs(list) do
        value.is_last = index == max_num
        if not value.is_last then
            value.next_target = list[index + 1].data.lotto_num
        end
    end
    return list
end

function XuYuanFreshPoolWGData:SortDataList(data_list)
	local list = {}
    if data_list and not IsEmptyTable(data_list) then    
        for k,v in pairs(data_list) do
            local temp = {}
            temp.reward_item = v.reward_item
			if temp.reward_item and temp.reward_item.item_id and temp.reward_item.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(temp.reward_item.item_id)       
                temp.color = item_cfg and item_cfg.color or 1
			else
                temp.color = 0
            end
            list[#list+1] = temp
		end
		table.sort(list, SortTools.KeyUpperSorters("color"))
    end
    return list
end

--已经抽到的奖励
function XuYuanFreshPoolWGData:SaveDrawInfo(protocol)
    self.had_draw_list = protocol.reward_info
end

--绑定奖池和非绑奖池的展示奖励
function XuYuanFreshPoolWGData:GetShowCellList()
    local activity_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
    local pool_cfg = self:GetAwardPoolByDay(activity_day)
    local reward_pool = pool_cfg and pool_cfg.reward_pool or 1
    if self.show_prob_list and self.cur_pool_index_prob == reward_pool then
        return self.show_prob_list
    end
    self.cur_pool_index_prob = reward_pool
    self.show_prob_list = {}
    -- local grade_cfg = self:GetGradeCfg()
    -- local reward_pool = grade_cfg and grade_cfg.reward_bind or 1--reward_bind已废弃，改读活动天数奖池
    local activity_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
    local pool_cfg = self:GetAwardPoolByDay(activity_day)
    local reward_pool = pool_cfg and pool_cfg.reward_pool or 1
    local cfg = self.reward_cfg[reward_pool] or {}  --做展示用的奖励配置
    for i, v in ipairs(cfg) do
        local data = self.show_prob_list[v.reward_type]
        if not data then
            data = {}
            data.title_text = Language.XuYuanFreshPool.RewardPreviewTitle[v.reward_type]
            data.reward_item_list = {}
            self.show_prob_list[v.reward_type] = data
        end
        local str = string.format(Language.XuYuanFreshPool.Probability, v.rewrad_rare_show)
        table.insert(data.reward_item_list, {item = v.reward_item, probability_text = str, prob = v.rewrad_rare_show})
    end

    -- 排序
    for _, value in pairs(self.show_prob_list) do
        table.sort(value.reward_item_list, SortTools.KeyLowerSorters("prob"))
    end

    return self.show_prob_list
end

--绑定奖池和非绑奖池的展示奖励 分页保存
function XuYuanFreshPoolWGData:GetShowRewardPageList(one_page_num)
    local activity_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3)
    local pool_cfg = self:GetAwardPoolByDay(activity_day)
    local reward_pool = pool_cfg and pool_cfg.reward_pool or 1
    if self.show_reward_page_list and self.cur_pool_index == reward_pool then
        return self.show_reward_page_list
    end
    
    self.cur_pool_index = reward_pool
    one_page_num = one_page_num or 6
    self.show_reward_page_list = {}
    -- local grade_cfg = self:GetGradeCfg()
    -- local reward_pool = grade_cfg and grade_cfg.reward_bind or 1--reward_bind已废弃，改读活动天数奖池
    local cfg = self.reward_cfg[reward_pool] or {}  --做展示用的奖励配置
    local num = 0
    for i, v in ipairs(cfg) do
        if v.reward_show == 1 then -- 配置展示的才展示
            num = num + 1
            local page = math.ceil(num / one_page_num)
            local data = self.show_reward_page_list[page]
            if not data then
                data = {}
                data.list = {}
                self.show_reward_page_list[page] = data
            end
            if v.reward_item then
                table.insert(data.list, v.reward_item)
            end
        end
    end

    return self.show_reward_page_list
end

--日志协议
function XuYuanFreshPoolWGData:SetRecord(record_list)
    self.record_list = record_list
    self.new_record_time = self.record_list[1] and self.record_list[1].draw_time or 0
end

function XuYuanFreshPoolWGData:GetRecordInfo()
    local list = {}
    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            list[k] = v
        end
    end
    
    if not IsEmptyTable(list) then
        table.sort(list, SortTools.KeyUpperSorter("draw_time"))
    end
    --获取日志
    return list
end

function XuYuanFreshPoolWGData:GetDrawRecord()
    local record_data = {}

    if not IsEmptyTable(self.record_list) then
        for k, v in pairs(self.record_list) do
            record_data[k] = {}
            record_data[k].item_data = {}
            record_data[k].item_data.item_id = v.item_id
            record_data[k].item_data.num = v.num
            record_data[k].consume_time = v.draw_time
            record_data[k].role_name = v.role_name
        end
    end

    if not IsEmptyTable(record_data) then
        table.sort(record_data, SortTools.KeyUpperSorter("consume_time"))
    end

    return record_data
end


--获取最新日志的时间
function XuYuanFreshPoolWGData:GetRecordTime()
    return self.new_record_time or 1
end

--获取奖励对应的配置
function XuYuanFreshPoolWGData:CalDrawRewardList(protocol)
    local data_list = {}
    local item_ids = {}
	if not protocol or not protocol.count or protocol.count <= 0 then
		return data_list, item_ids
	end

    local zhenxi_item = nil
	for i,v in ipairs(protocol.reward_list) do
		local cfg = self:GetRewardById(v.reward_pool_id, v.reward_id)
        if cfg then
            local temp = {}
            temp.is_zhenxi = v.is_zhenxi
            temp.reward = cfg.reward
            temp.reward_item = cfg.reward_item
            temp.reward_type = cfg.reward_type
            temp.rewrad_rare_show = cfg.rewrad_rare_show
            if temp.is_zhenxi and protocol.count == 50 then
                zhenxi_item = temp
            else
                if cfg.reward_type == XuYuanFreshPoolWGData.REWARD_TYPE.BIG then
                    table.insert(item_ids, temp.reward_item)
                else
                    table.insert(data_list, temp.reward_item)
                end
            end
		else
			print_error("错误数据 请检查奖励配置 reward_pool_id,reward_id: ", v.reward_pool_id, v.reward_id)
		end
    end

    local temp_data_list
    if zhenxi_item then
        local zhenxi_index
        temp_data_list = {}
        if protocol.count == 50 then
            zhenxi_index = math.random(22, 28)
        end
        for k, v in ipairs(data_list) do
            if k < zhenxi_index then
                temp_data_list[k] = v
            else
                if k == zhenxi_index then
                    temp_data_list[zhenxi_index] = zhenxi_item
                    temp_data_list[k+1] = v
                else
                    temp_data_list[k+1] = v
                end
            end
        end
    end

	return temp_data_list or data_list, item_ids
end


function XuYuanFreshPoolWGData:GetRandomGaiLvinfo()
	return self.item_random_desc_cfg[self.grade or 0]
end

--获取当前抽奖次数的index.
function XuYuanFreshPoolWGData:GetCurDrawIndex()
	local cfg = self:GetGradeCfg()
	local rebate = cfg and cfg.rebate or 1
	local draw_time = self:GetCurDrawTimes()
	local index = 0
	for i, v in ipairs(self.rebate_cfg[rebate]) do
		if draw_time >= v.lotto_num then
			index = v.index
		else
			break
		end
	end

	return index
end

function XuYuanFreshPoolWGData:SetModelSuiIndex()
	local cfg = self:GetGradeCfg()
	local rebate = cfg and cfg.rebate or 1
	local draw_index = self:GetCurDrawIndex()
    local index = 0
    local list = (self.show_model_cfg or {})[rebate] or {}
	for i, v in pairs(list) do
		if draw_index >= i then
			index = i
		end
	end
	self.show_model_suit_index = index
end

function XuYuanFreshPoolWGData:GetModelSuiIndex()
	return self.show_model_suit_index
end

function XuYuanFreshPoolWGData:GetShowModelCfg()
	local cfg = self:GetGradeCfg()
	local rebate = cfg and cfg.rebate or 1
	return ((self.show_model_cfg or {})[rebate] or {})[self.show_model_suit_index] or {}
end

function XuYuanFreshPoolWGData:GetModelDetailData()
	local cfg = self:GetGradeCfg()
	local rebate = cfg and cfg.rebate or 1
	return (((self.model_detail_cfg or {})[rebate] or {})[self.show_model_suit_index] or {})[1] or {}
end

function XuYuanFreshPoolWGData:GetBigRewardData()
	local cfg = self:GetGradeCfg()
	local rebate = cfg and cfg.rebate or 1
	local list_data = self.rebate_cfg[rebate]
	local item_data

	for i, value in ipairs(list_data) do
		local get_flag = self:GetFanliHasGet(value.index)
		if value.display_reward == 1 and not get_flag then
			item_data = value
			break
		end

		--都领取过了，显示最后一个大奖.
		if not item_data and i == #list_data then
			item_data = value
		end
	end

	return item_data
end

--设置宝匣是否跳过动画
function XuYuanFreshPoolWGData:SetBoxSkipAnimFalg(falg)
	self.baoxia_is_skipanim = falg
end

function XuYuanFreshPoolWGData:GetBoxIsSkipAnim()
	return self.baoxia_is_skipanim == true
end

function XuYuanFreshPoolWGData:GetSkipComic()
    return self.is_skip_comic == 1
end

-- 兑换商店配置
function XuYuanFreshPoolWGData:GetShopData()
    if self.cur_grade == self.grade then
        return self.shop_big_award, self.shop_data_list
    end

    self.cur_grade = self.grade
    local list = self.exchange_shop_cfg and self.exchange_shop_cfg[self.grade or 0]
    for i = 1, #list do
        local data = list[i]
        if data and data.big_reward ~= "" then
            self.shop_big_award = data
        elseif data then
            table.insert(self.shop_data_list, data)
        end
    end
    return self.shop_big_award, self.shop_data_list
end

-- 兑换商店购买次数
function XuYuanFreshPoolWGData:GetGoodsBuyNum(seq)
    return self.exchange_shop_list[seq] or 0
end
function XuYuanFreshPoolWGData:SetGoodsBuyNum(protocol)
    self.exchange_shop_list = protocol.exchange_shop_list
end

-- 兑换商店货币
function XuYuanFreshPoolWGData:GetShopAssets()
    if self.shop_asset_id then
        return self.shop_asset_id
    end

    local big_award, _ = self:GetShopData()
    self.shop_asset_id = big_award and big_award.stuff_id or 59687

    return self.shop_asset_id
end

-- 获取当前展示奖池
function XuYuanFreshPoolWGData:GetAwardPoolByDay(day)
    local cfg = self.activity_day_cfg and self.activity_day_cfg[self.grade]
    cfg = cfg and cfg[day]
    return cfg
end

-- 根据档次获取大奖展示
function XuYuanFreshPoolWGData:GetBigAward()
    local cur_cfg = self:GetCurCfg()
    local item_id = cur_cfg and cur_cfg.item_id or 59680

    return item_id
end

-- 根据档次获取资源
function XuYuanFreshPoolWGData:GetResByName(res_name)
    local cur_cfg = self:GetCurCfg()
    local color_index = cur_cfg and cur_cfg.ui_color_index or 1
    -- TODO
end

-- 根据档次获取商店界面
function XuYuanFreshPoolWGData:GetShopPanel()
    local cur_cfg = self:GetCurCfg()
    local default_panel = "WorldTreasureView#tcdb_premium_shop"
    local panel_name = cur_cfg and cur_cfg.open_panel or default_panel
    return panel_name, panel_name == default_panel
end

-- 根据档次获取拍脸图界面
function XuYuanFreshPoolWGData:GetPopPanel()
    local cur_cfg = self:GetCurCfg()
    local default_panel = GuideModuleName.XunYuanFreshPoolPopView
    local panel_name = cur_cfg and cur_cfg.open_panel_pop or default_panel
    return panel_name
end
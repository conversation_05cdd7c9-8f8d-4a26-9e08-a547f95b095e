require("game/control_beasts/control_beasts_wg_view")
require("game/control_beasts/control_beasts_wg_data")
require("game/control_beasts/control_beasts_battle_view")
require("game/control_beasts/control_beasts_culture_view")
require("game/control_beasts/control_beasts_culture_sxd_attr_view")
require("game/control_beasts/control_beasts_refining_view")
require("game/control_beasts/control_beasts_stable_view")
require("game/control_beasts/control_beasts_compose_view")
require("game/control_beasts/control_beasts_hand_book_view")
require("game/control_beasts/control_beasts_king_view")
require("game/control_beasts/control_beasts_view_render")
require("game/control_beasts/control_beasts_hole_tip")
require("game/control_beasts/control_beasts_release_tip")
require("game/control_beasts/control_beasts_learn_skill_tip")
require("game/control_beasts/control_beasts_quick_sp")
require("game/control_beasts/control_beasts_battle_select_view")
require("game/control_beasts/control_beasts_batch_select")
require("game/control_beasts/control_beasts_evolve_preview")
require("game/control_beasts/control_beasts_assist_battle_select_view")
require("game/control_beasts/control_beasts_group_view")
require("game/control_beasts/control_beasts_refining_reset")
-- require("game/control_beasts/control_beasts_culture_swallow")
-- require("game/control_beasts/control_beasts_swallow_select")
-- require("game/control_beasts/control_beasts_culture_change")
require("game/control_beasts/control_beasts_battle_attr_view")
require("game/control_beasts/control_beasts_contract_view")
require("game/control_beasts/control_beasts_stable_reset_tip")
require("game/control_beasts/control_beasts_despose_view")
require("game/control_beasts/control_beasts_battle_element_view")
require("game/control_beasts/control_beasts_element_active_view")
require("game/control_beasts/control_beasts_refining_level_view")
require("game/control_beasts/control_beasts_skill_show_view")
require("game/control_beasts/control_beasts_hand_book_reward_view")
require("game/control_beasts/control_beasts_skin_view")

require("game/control_beasts/holy_beasts/holy_beasts_view")
require("game/control_beasts/holy_beasts/holy_beasts_contract_view")
require("game/control_beasts/holy_beasts/holy_beasts_spirit_view")
require("game/control_beasts/holy_beasts/holy_beasts_contract_select_view")

-- 幻兽抽奖(新)
require("game/control_beasts/control_beasts_prize_draw/control_beasts_prize_draw_wg_data")
--require("game/control_beasts/control_beasts_prize_draw/control_beasts_prize_draw_wg_view")
require("game/control_beasts/control_beasts_prize_draw/control_beasts_draw_view")
require("game/control_beasts/control_beasts_prize_draw/control_beasts_draw_prepare_view")
require("game/control_beasts/control_beasts_prize_draw/control_beasts_draw_result_view")

-- 幻兽内丹
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_wg_ctrl")
require("game/control_beasts/control_beasts_alchemy_view")

ControlBeastsWGCtrl = ControlBeastsWGCtrl or BaseClass(BaseWGCtrl)


local OPERATE_RESULT = {
	TRANSFER_SUCCESS = 1,				--转化成功
	SWALLOW_SUCCESS = 2,				--吞噬成功
	SWALLOW_FAILURE = 3,				--吞噬失败
	ACQUIRE_BEAST_SUCCESS = 4,			--认主或单个升星成功
	ADD_SKILL = 5,						--打书
	COMPOSE_BEAST_SUCCESS = 6,			-- 驭兽合成成功
	COMPOSE_BEAST_EGG_SUCCESS = 7,		-- 驭兽蛋合成成功
}

local ADD_SKILL_TYPE = {
	ADD_SKILL_FAILURE = 0,				--打书失败
	ADD_SKILL_NEW_SKILL = 1,			--获得新技能
	ADD_SKILL_REPLACE_SKILL = 2,		--替换技能
}
local BAG_DELAY_TIME = 0.1

function ControlBeastsWGCtrl:__init()
	if ControlBeastsWGCtrl.Instance ~= nil then
		ErrorLog("[ControlBeastsWGCtrl] attempt to create singleton twice!")
		return
	end

	ControlBeastsWGCtrl.Instance = self

	self.data = ControlBeastsWGData.New()
	self.view = ControlBeastsWGView.New(GuideModuleName.ControlBeastsView)
	self.prize_draw_data = ControlBeastsPrizeDrawWGData.New()
	self.prize_draw_view = ControlBeastsDrawView.New(GuideModuleName.ControlBeastsPrizeDrawWGView)
	self.prize_draw_prepare_view = ControlBeastsDrawPrepareView.New(GuideModuleName.ControlBeastsDrawPrepareView)

	self.control_beasts_hole_tips = ControlBeastsHoleTip.New()
	self.control_beasts_battle_select = ControlBeastsBattleSelectView.New(GuideModuleName.ControlBeastsBattleSelectView)
	self.control_beasts_batch_select = ControlBeastsBatchSelect.New()
	self.control_beasts_evolve_preview = ControlBeastsEvolvePreview.New()
	self.control_beasts_release_tip = ControlBeastsReleaseTip.New()
	self.control_beasts_learn_skill_tip = ControlBeastsLearnSkillTip.New()
	self.control_beasts_assist_battle_select_view = ControlBeastsBattleAssistSelectView.New()
	self.control_beasts_group_view = ControlBeastsGroupView.New()
	self.control_beasts_quick_sp = ControlBeastsQuickSp.New()
	self.control_beasts_refining_reset = ControlBeastsRefiningReset.New()
	-- self.control_beasts_culture_swallow = ControlBeastsCultureSwallow.New()
	-- self.control_beasts_culture_change = ControlBeastsCultureChange.New()
	-- self.control_beasts_swallow_select = ControlBeastsSwallowSelect.New()
	self.control_beasts_battle_attr_view = ControlBeastsBattleAttrView.New()
	self.control_beasts_king_view = ControlBeastsKingView.New(GuideModuleName.ControlBeastsKingView)
	self.control_beasts_stable_reset_tip = ControlBeastsStableResetTip.New()
	self.control_beasts_battle_element_view = ControlBeastsBattleElementView.New()
	self.control_beasts_refining_level_view = ControlBeastsRefiningLevelView.New()
	self.control_beasts_skill_show_view = ControlBeastsSkillShowView.New()
	self.control_beasts_despose_view = ControlBeastsDesposeView.New(GuideModuleName.ControlBeastsDesposeView)
	self.control_beasts_element_active_view = ControlBeastsElementActiveView.New()
	self.control_beasts_hand_book_reward_view = ControlBeastsHandBookRewardView.New()
	self.control_beasts_skin_view = ControlBeastsSkinView.New(GuideModuleName.ControlBeastsSkinView)
	self.control_beasts_draw_result_view = ControlBeastsDrawResultView.New(GuideModuleName.ControlBeastsDrawResultView)
	self.holy_beasts_view = HolyBeastsView.New(GuideModuleName.HolyBeastsView)
	self.holy_beasts_contract_select_view = HolyBeastsContractSelectView.New(GuideModuleName.HolyBeastsContractSelectView)
	self.control_beasts_culture_sxd_attr_view = ControlBeastsCultureSXDAttrView.New()

	-- 新增幻兽内丹
	self.control_beasts_cultivate_wg_ctrl = ControlBeastsCultivateWGCtrl.New()

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)

	if not self.listen_fun_open_event then
		self.listen_fun_open_event = BindTool.Bind(self.OnControlBeastsFunOpen, self)
		FunOpen.Instance:NotifyFunOpen(self.listen_fun_open_event)
	end

	-- 角色屬性改變
	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level", "prof", "tianxiange_level"})

	if not self.day_pass then
        self.day_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPass, self))
    end

	self:RegisterAllProtocols()
	Runner.Instance:AddRunObj(self, 8)
	self.item_add_str_list = {}
end

function ControlBeastsWGCtrl:__delete()
	ControlBeastsWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.data:DeleteMe()

	self.prize_draw_data:DeleteMe()
	self.prize_draw_view:DeleteMe()
	self.prize_draw_prepare_view:DeleteMe()

	self.control_beasts_hole_tips:DeleteMe()
	self.control_beasts_battle_select:DeleteMe()
	self.control_beasts_batch_select:DeleteMe()
	self.control_beasts_evolve_preview:DeleteMe()
	self.control_beasts_release_tip:DeleteMe()
	self.control_beasts_learn_skill_tip:DeleteMe()
	self.control_beasts_assist_battle_select_view:DeleteMe()
	self.control_beasts_group_view:DeleteMe()
	self.control_beasts_quick_sp:DeleteMe()
	self.control_beasts_refining_reset:DeleteMe()
	-- self.control_beasts_culture_swallow:DeleteMe()
	-- self.control_beasts_culture_change:DeleteMe()
	-- self.control_beasts_swallow_select:DeleteMe()
	self.control_beasts_battle_attr_view:DeleteMe()
	self.control_beasts_king_view:DeleteMe()
	self.control_beasts_stable_reset_tip:DeleteMe()
	self.control_beasts_battle_element_view:DeleteMe()
	self.control_beasts_refining_level_view:DeleteMe()
	self.control_beasts_skill_show_view:DeleteMe()
	self.control_beasts_despose_view:DeleteMe()
	self.control_beasts_element_active_view:DeleteMe()
	self.control_beasts_hand_book_reward_view:DeleteMe()
	self.control_beasts_draw_result_view:DeleteMe()
	self.control_beasts_draw_result_view = nil
	self.holy_beasts_view:DeleteMe()
	self.holy_beasts_contract_select_view:DeleteMe()
	self.control_beasts_culture_sxd_attr_view:DeleteMe()

	--新增幻兽内丹
	if self.control_beasts_cultivate_wg_ctrl then
		self.control_beasts_cultivate_wg_ctrl:DeleteMe()
		self.control_beasts_cultivate_wg_ctrl = nil
	end

    ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	self.item_data_change_callback = nil

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if self.day_pass then
		GlobalEventSystem:UnBind(self.day_pass)
		self.day_pass = nil
	end

	if self.listen_fun_open_event then
        FunOpen.Instance:UnNotifyFunOpen(self.listen_fun_open_event)
        self.listen_fun_open_event = nil
    end
	
	Runner.Instance:RemoveRunObj(self)
	self.item_add_str_list = nil
	self.need_delay = nil
end

function ControlBeastsWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSRoleBeastOperate)
	self:RegisterProtocol(CSRoleBeastCompose)
	self:RegisterProtocol(CSBeastBatchCompose)
	self:RegisterProtocol(CSRoleBeastChangeFlair)
	self:RegisterProtocol(SCAllBeastEggBagInfo, "OnSCAllBeastEggBagInfo")
	self:RegisterProtocol(SCSingleBeastEggBagInfo, "OnSCSingleBeastEggBagInfo")
	self:RegisterProtocol(SCAllBreedingInfo, "OnSCAllBreedingInfo")
	self:RegisterProtocol(SCSingleBreedingInfo, "OnSCSingleBreedingInfo")
	self:RegisterProtocol(SCAllBeastBagInfo, "OnSCAllBeastBagInfo")
	self:RegisterProtocol(SCSingleBeastBagInfo, "OnSCSingleBeastBagInfo")
	self:RegisterProtocol(SCAllStandBySlotInfo, "OnSCAllStandBySlotInfo")
	self:RegisterProtocol(SCSingleStandBySlotInfo, "OnSCSingleStandBySlotInfo")
	self:RegisterProtocol(SCBeastBaseInfo, "OnSCBeastBaseInfo")
	self:RegisterProtocol(SCAllTreasureInfo, "OnSCAllTreasureInfo")
	self:RegisterProtocol(SCAllStarCircleInfo, "OnSCAllStarCircleInfo")
	self:RegisterProtocol(SCSingleStarCircleInfo, "OnSCSingleStarCircleInfo")
	self:RegisterProtocol(SCBeastTriggerSkill, "OnSCBeastTriggerSkill")
	self:RegisterProtocol(CSBeastBreakListReq)
	self:RegisterProtocol(CSBeastResetListReq)
	self:RegisterProtocol(CSBeastBackListReq)
	self:RegisterProtocol(CSBeastUseSkill)
	self:RegisterProtocol(CSBeastFightChange)
	self:RegisterProtocol(SCBeastFightInfo, "OnSCBeastFightInfo")
	self:RegisterProtocol(SCBeastDrawRecordInfo, "OnSCBeastDrawRecordInfo")
	self:RegisterProtocol(SCBeastDrawRecordAdd, "OnSCBeastDrawRecordAdd")

	----------------------------幻兽抽奖（新）---------------------------
	self:RegisterProtocol(SCBeastDrawItemInfo, "OnSCBeastDrawItemInfo")
	self:RegisterProtocol(SCBeastDrawItemUpdate, "OnSCBeastDrawItemUpdate")
	self:RegisterProtocol(SCBeastDrawResult, "OnSCBeastDrawResult")

	----------------------------幻兽图鉴（新）---------------------------
	self:RegisterProtocol(SCBeastHoodbookInfo, "OnSCBeastHoodbookInfo")
	self:RegisterProtocol(SCBeastHoodbookUpdate, "OnSCBeastHoodbookUpdate")

	-- 元素反应
	self:RegisterProtocol(SCEffectElementReaction, "OnSCEffectElementReaction")
	-- 皮肤信息
	self:RegisterProtocol(SCBeastSkinInfo, "OnSCBeastSkinInfo")
	-- 属性丹信息
	self:RegisterProtocol(SCBeastPelletInfo, "OnSCBeastPelletInfo")
end

--物品变化(这里需要更新红点)
function ControlBeastsWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self.control_beasts_cultivate_wg_ctrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE or change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		if ControlBeastsWGData.Instance:CheckisBookItemId(change_item_id) then	---技能书物品变化刷新学习技能书红点
			ControlBeastsWGData.Instance:RefreshAllBeastLearnSkill()
			self:FlushBeastLearnSkillView()
			RemindManager.Instance:Fire(RemindName.BeastsCulture)
			self:FlushCurShowView()
		end
	
		-- if ControlBeastsWGData.Instance:CheckisQuickIncubateItemId(change_item_id) then	-- 一键孵化物品变化刷新学习技能书红点
		-- 	ControlBeastsWGData.Instance:RefreshAllQuickBreedingInfo()
		-- 	RemindManager.Instance:Fire(RemindName.BeastsStable)
		-- 	self:FlushBeastStableResetTipsView("item_id")
		-- 	ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsView, TabIndex.beasts_stable, "all")
		-- end
	
		-- if ControlBeastsWGData.Instance:CheckisCircleILeveltemId(change_item_id) then	-- 星阵物品变化刷新学习技能书红点
		-- 	ControlBeastsWGData.Instance:RefreshAllStarCircleRed()
		-- 	RemindManager.Instance:Fire(RemindName.BeastsRefining)
		-- 	self:FlushBeastsRefiningResetView()
		-- 	ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsView, TabIndex.beasts_refining, "all")
		-- end
	
		-- if ControlBeastsContractWGData.Instance:GetCurBeastDrawItem(change_item_id) then
		-- 	RemindManager.Instance:Fire(RemindName.BeastsContract)
		-- 	ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsView, TabIndex.beasts_contract, "all")
		-- 	self:FlushBeastsKingView()
		-- end
		if ControlBeastsWGData.Instance:IsBeastsPelletItem(change_item_id) then
			self:FlushCurShowView(nil, "culture_beasts_sxd")
			RemindManager.Instance:Fire(RemindName.BeastsCulture)
		end

		if ControlBeastsWGData.Instance:CheckisBeastStarUpItemId(change_item_id) then
			RemindManager.Instance:Fire(RemindName.BeastsCompose)
			self:FlushCurShowView()
		end
		
		if ControlBeastsWGData.Instance:CheckisBeastChangeFlairItemId(change_item_id) then
			ControlBeastsWGData.Instance:RefreshAllBeastChangeFlair()
			RemindManager.Instance:Fire(RemindName.BeastsCulture)
			self:FlushCurShowView()
		end

		if ControlBeastsPrizeDrawWGData.Instance:IsBeastsPrizeDrawItem(change_item_id) or ControlBeastsOADrawWGData.Instance:IsBeastsPrizeDrawItem(change_item_id) then
			if self.prize_draw_view:IsOpen() then
				self.prize_draw_view:Flush()
			end
		
			RemindManager.Instance:Fire(RemindName.BeastsCulture)
			RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
			self:FlushCurShowView()
		end

		if ControlBeastsWGData.Instance:CheckIsHolyBeastUnlockItemId(change_item_id) then
			RemindManager.Instance:Fire(RemindName.HolyBeastsContract)
			ViewManager.Instance:FlushView(GuideModuleName.HolyBeastsView)
		end

		if ControlBeastsWGData.Instance:CheckIsHolySpiritUpLevelItemId(change_item_id) then
			RemindManager.Instance:Fire(RemindName.HolyBeastsSpirit)
			ViewManager.Instance:FlushView(GuideModuleName.HolyBeastsView)
		end
	end
end

-- 所有的背包id和客户端都差1
function ControlBeastsWGCtrl:GetServerBagId(client_bag_id)
	return client_bag_id - 1
end

-- 开始孵化
function ControlBeastsWGCtrl:SendOperateTypeBreed(select_egg_index, incubate_index)
	--print_error("驭兽天下操作请求, 开始孵化", select_egg_index, incubate_index)
	local server_egg_index = self:GetServerBagId(select_egg_index)
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BREED, server_egg_index, server_incubate_index)
	-- self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SORT_EGG_BGG, 0, 0)	---开始孵蛋后整理一下背包
end

-- 整理蛋背包
function ControlBeastsWGCtrl:SendOperateTypeArrangeEggBag()
	--print_error("驭兽天下操作请求, 整理蛋背包")
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SORT_EGG_BGG, 0, 0)
	-- self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SORT_EGG_BGG, 0, 0)	---开始孵蛋后整理一下背包
end

-- 孵化加速
function ControlBeastsWGCtrl:SendOperateTypeIncubateSpeed(incubate_index)
	--print_error("驭兽天下操作请求, 孵化加速", incubate_index)
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BEAST_REDUCE_BREEDING_TIME, server_incubate_index, 0)
end

-- 一键加速
function ControlBeastsWGCtrl:SendOperateTypeIncubateQuick(incubate_index)
	--print_error("驭兽天下操作请求, 一键加速", incubate_index)
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BEAST_REDUCE_ALL_BREEDING_TIME, server_incubate_index, 0)
end

-- 放生(传入字段是否为孵化槽，槽位id或者背包id)
function ControlBeastsWGCtrl:SendOperateTypeIncubateRelease(is_incubate, index)
	--print_error("驭兽天下操作请求, 孵化槽放生", is_incubate, index)
	local server_index = self:GetServerBagId(index)
	if is_incubate then
		self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BREEDING_SLOT_RELEASE, server_index, 0)
	else
		self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_RELEASE, server_index, 0)
	end
end

-- 认主
function ControlBeastsWGCtrl:SendOperateTypeIncubateOwner(incubate_index)
	--print_error("驭兽天下操作请求, 认主", incubate_index)
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_ACQUIRE_BRED_BEAST, server_incubate_index, 0)
end

-- 获取经验
function ControlBeastsWGCtrl:SendOperateTypeGetBeastExp()
	--print_error("驭兽天下操作请求, 获取经验")
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_ACQUIRE_EXP, 0, 0)
end

-- 上阵
function ControlBeastsWGCtrl:SendOperateTypeApplyBeast(bag_beast_index, stand_by_slot_index)
	-- print_error("驭兽天下操作请求, 上阵", bag_beast_index, stand_by_slot_index)
	local server_bag_beast_index = self:GetServerBagId(bag_beast_index)
	local server_stand_by_slot_index = self:GetServerBagId(stand_by_slot_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_APPLY_BEAST, server_bag_beast_index, server_stand_by_slot_index)
end

-- 下阵
function ControlBeastsWGCtrl:SendOperateTypeRetreatBeast(stand_by_slot_index)
	--print_error("驭兽天下操作请求, 上阵", bag_beast_index, stand_by_slot_index)
	-- local server_stand_by_slot_index = self:GetServerBagId(stand_by_slot_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_RETREAT_BEAST, stand_by_slot_index)
end

-- 升级 p1->已孵化背包下标(0-34)
function ControlBeastsWGCtrl:SendOperateTypeUpgrade(bag_beast_index, to_lv)
	--print_error("驭兽天下操作请求, 升级", bag_beast_index)
	local server_bag_beast_index = self:GetServerBagId(bag_beast_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_UPGRADE, server_bag_beast_index, to_lv)
end

-- 打书, p1 -> 驭兽背包下标 p2 -> 技能书id
function ControlBeastsWGCtrl:SendOperateTypeAddSkill(bag_beast_index, skill_id)
	--print_error("驭兽天下操作请求, 打书", bag_beast_index, skill_id)
	local server_bag_beast_index = self:GetServerBagId(bag_beast_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_ADD_SKILL, server_bag_beast_index, skill_id)
end

-- 星阵升星, p1 -> 星阵下标(0-8) p2 -> 属性下标(0-3)
function ControlBeastsWGCtrl:SendOperateTypeStarCircleUpgrade(circle_index, attr_index)
	--print_error("驭兽天下操作请求, 星阵升星", circle_index, attr_index)
	local server_circle_index = self:GetServerBagId(circle_index)
	local server_attr_index = self:GetServerBagId(attr_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_STAR_CIRCLE_UPGRADE, server_circle_index, server_attr_index)
end

-- 星阵刷属性, p1 -> 星阵下标(0-8) p2 -> 属性下标(0-3)
function ControlBeastsWGCtrl:SendOperateTypeStarCircleAttrFlush(circle_index, attr_index)
	-- print_error("驭兽天下操作请求, 星阵刷属性", circle_index, attr_index)
	local server_circle_index = self:GetServerBagId(circle_index)
	local server_attr_index = self:GetServerBagId(attr_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_STAR_CIRCLE_ATTR_FLUSH, server_circle_index, server_attr_index)
end

-- 星阵开新轮回, p1 -> 星阵下标(0-8)
function ControlBeastsWGCtrl:SendOperateTypeStarNewCircle(circle_index)
	-- print_error("驭兽天下操作请求, 星阵开新轮回", circle_index)
	local server_circle_index = self:GetServerBagId(circle_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_STAR_CIRCLE_START_NEW_CYCLE, server_circle_index, 0)
end

-- 兽王等级升级
function ControlBeastsWGCtrl:SendOperateTypeBeastKingUpgrade()
	--print_error("驭兽天下操作请求, 兽王等级升级")
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BEAST_KING_UPGRADE, 0, 0)
end

-- 孔位灵玉解锁 p1->孔位下标(0-n)
function ControlBeastsWGCtrl:SendOperateTypeSlotUnlock(slot_index)
	--print_error("孔位灵玉解锁", slot_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SLOT_UNLOCK, slot_index, 0)
end

-- 孔位灵玉升级 p1->孔位下标(0-n)
function ControlBeastsWGCtrl:SendOperateTypeSlotUpgrade(slot_index)
	--print_error("孔位灵玉升级", slot_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SLOT_UPGRADE, slot_index, 0)
end

-- 驭兽抽奖 - 抽奖 p1->抽奖模式mode_seq
function ControlBeastsWGCtrl:SendOperateTypeBeastDraw(mode_seq)
	--print_error("驭兽抽奖", mode_seq)
	local server_mode_seq = self:GetServerBagId(mode_seq)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BEAST_DRAW, server_mode_seq, 0)
end

-- 驭兽抽奖 - 选择额外大奖 -> 奖励下标(0-n)
function ControlBeastsWGCtrl:SendOperateTypeSelectBeastDraw(seq)
	--print_error("驭兽抽奖 选择额外大奖", seq)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BEAST_DRAW_SELECT_EXTRA_REWARD, seq, 0)
end

-- 整理孵化背包
function ControlBeastsWGCtrl:SendOperateTypeSortBeastBag()
	--print_error("整理孵化背包")
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_BEAST_SORT_BEAST_BAG, 0, 0)
end

-- 重新孵化
function ControlBeastsWGCtrl:SendOperateTypeRebreed(incubate_index)
	--print_error("整理孵化背包")
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_REBREED, server_incubate_index, 0)
end

-- 洗练 param1:index param2:洗练倍率
function ControlBeastsWGCtrl:SendOperateTypeRefine(incubate_index, multiple_value)
	-- print_error("洗练 param1:index param2:洗练倍率", incubate_index, multiple_value)
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_REFINE, server_incubate_index, multiple_value)
end

-- 洗练结果 param1:index param2:is_save
function ControlBeastsWGCtrl:SendOperateTypeRefineResult(incubate_index, is_save)
	--print_error("整理孵化背包")
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_REFINE_RESULT, server_incubate_index, is_save)
end

-- 洗练升级 param1:index
function ControlBeastsWGCtrl:SendOperateTypeRefineLevel(incubate_index)
	--print_error("整理孵化背包")
	local server_incubate_index = self:GetServerBagId(incubate_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_REFINE_LEVEL, server_incubate_index)
end

-- 驭兽抽奖 - 抽奖 p1->抽奖模式mode_seq
function ControlBeastsWGCtrl:SendOperateTypeBeastDraw(draw_type, mode_seq)
	--print_error("驭兽抽奖", mode_seq)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_DRAW, draw_type, mode_seq)
end

-- 驭兽抽奖记录
function ControlBeastsWGCtrl:SendOperateTypeBeastDrawRecordInfo(draw_type)
	--print_error("驭兽抽奖记录", draw_type)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_DRAW_RECORD_INFO, draw_type)
end

-- 驭兽幸运值领奖
function ControlBeastsWGCtrl:SendOperateTypeBeastDrawConvert(draw_type)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_DRAW_CONVERT, draw_type)
end

-- 驭兽图鉴信息
function ControlBeastsWGCtrl:SendOperateTypeBeastHandBookInfo()
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_HOODBOOK_INFO)
end

-- 驭兽领取图鉴奖励
function ControlBeastsWGCtrl:SendOperateTypeBeastHandBookReward(beast_type)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_FETCH_HOODBOOK_REWARD, beast_type)
end

-- 皮肤升级
function ControlBeastsWGCtrl:SendOperateTypeBeastSkinUpLevel(skin_seq)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SKIN_UPLEVEL, skin_seq)
end

-- 皮肤使用
function ControlBeastsWGCtrl:SendOperateTypeBeastSkinUse(bag_index, skin_seq)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_SKIN_USE, bag_index, skin_seq)
end

-- 圣兽召唤（碎片合成）
function ControlBeastsWGCtrl:SendOperateTypeHolyBeastCall(beast_type)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_HOLY_CALL, beast_type)
end

-- 圣兽链接
function ControlBeastsWGCtrl:SendOperateTypeHolyBeastLink(bag_index, link_bag_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_HOLY_LINK, bag_index, link_bag_index)
end

-- 圣魂升级
function ControlBeastsWGCtrl:SendOperateTypeHolySpiritUpLevel(bag_index, index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_HOLY_SPIRIT_UPLEVEL, bag_index, index)
end

-- 圣魂重置
function ControlBeastsWGCtrl:SendOperateTypeHolySpiritReset(bag_index)
	self:SendCSRoleBeastOperate(BEAST_OPERATE_TYPE.OPERATE_TYPE_HOLY_SPIRIT_RESET, bag_index)
end

-- 驭兽天下操作请求
function ControlBeastsWGCtrl:SendCSRoleBeastOperate(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleBeastOperate)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- 驭兽天下分解幻兽
function ControlBeastsWGCtrl:SendCSBeastBreakListReq(bag_id_list)
	if self.view:IsOpen() then
		self.view:SetBeastBreakStatus(true)
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSBeastBreakListReq)
	protocol.list_len = #bag_id_list
	protocol.operate_list = bag_id_list
	protocol:EncodeAndSend()
end

-- 驭兽天下重置幻兽
function ControlBeastsWGCtrl:SendCSBeastResetListReq(bag_id_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBeastResetListReq)
	protocol.list_len = #bag_id_list
	protocol.operate_list = bag_id_list
	protocol:EncodeAndSend()
end

-- 驭兽天下回退幻兽
function ControlBeastsWGCtrl:SendCSBeastBackListReq(bag_id_list)
	if self.view:IsOpen() then
		self.view:SetBeastBreakStatus(true)
	end
	local protocol = ProtocolPool.Instance:GetProtocol(CSBeastBackListReq)
	protocol.list_len = #bag_id_list
	protocol.operate_list = bag_id_list
	protocol:EncodeAndSend()
end

-- 创建一个狗粮对象
function ControlBeastsWGCtrl:CreateComposeMaterialData(aim_beast)
	local data = {}
	data.bag_type = 1

	if aim_beast.is_egg then
		data.bag_type = 0
	elseif aim_beast.is_card then
		data.bag_type = 2
	elseif aim_beast.is_chip then
		data.bag_type = 2
		data.item_num = aim_beast.item_num
	end

	if data.bag_type == 2 then
		data.bag_index = ItemWGData.Instance:GetItemIndex(aim_beast.item_id)
	else
		data.bag_index = self:GetServerBagId(aim_beast.bag_id)
	end

	return data
end

-- 创建一个背包材料对象扣到数量满足为止
function ControlBeastsWGCtrl:CreateComposeMaterialBagData(aim_beast)
	local list = {}

	if aim_beast.is_card then
		local data = {}
		data.bag_type = 2
		data.bag_index = ItemWGData.Instance:GetItemIndex(aim_beast.item_id)
		table.insert(list, data)
	elseif aim_beast.is_chip then
		local index_list = ItemWGData.Instance:GetItemListIndexByStuffBag(aim_beast.item_id)
		local need_num = aim_beast.item_num

		for i, bag_index in ipairs(index_list) do
			local num = ItemWGData.Instance:GetItemNumInBagByIndex(bag_index)
			if num >= need_num then
				local data = {}
				data.bag_type = 2
				data.bag_index = bag_index
				data.item_num = need_num
				table.insert(list, data)
				break
			else
				local data = {}
				data.bag_type = 2
				data.bag_index = bag_index
				data.item_num = num
				need_num = need_num - num
				table.insert(list, data)
			end
		end
	end

	return list
end

-- 创建一个合成对象
function ControlBeastsWGCtrl:CreateBeastComposeMaterial(aim_beast, compose_list)
	local data = {}
	data.aim_beast = self:CreateComposeMaterialData(aim_beast)
	local spend_count = 0
	data.stuff_list = {}

	for i, compose_data in ipairs(compose_list) do
		if compose_data.is_chip then
			local list = self:CreateComposeMaterialBagData(compose_data)
			for _, bag_data in ipairs(list) do
				spend_count = spend_count + 1
				table.insert(data.stuff_list, bag_data)
			end
		else
			spend_count = spend_count + 1
			table.insert(data.stuff_list, self:CreateComposeMaterialData(compose_data))
		end
	end

	data.spend_count = spend_count
	return data
end

-- 驭兽天下合成
function ControlBeastsWGCtrl:SendCSRoleBeastCompose(aim_beast, compose_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleBeastCompose)
	protocol.compose_data = self:CreateBeastComposeMaterial(aim_beast, compose_list)
	protocol:EncodeAndSend()
end

-- 驭兽天下批量合成
function ControlBeastsWGCtrl:SendCSRoleBatchBeastCompose(batch_compose_list)
	-- print_error("驭兽天下批量合成", batch_compose_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBeastBatchCompose)
	protocol.count = #batch_compose_list
	if protocol.count > 20 then
		protocol.count = 20
	end
	protocol.compose_batch_list = {}
	for i, compose_list_data in ipairs(batch_compose_list) do
		protocol.compose_batch_list[i] = self:CreateBeastComposeMaterial(compose_list_data.aim_beast, compose_list_data.compose_list)
	end
	-- print_error("驭兽天下批量合成", protocol)
	self.need_delay = true			-- 批量升星后后发一次整个背包信息，所以单个刷新在批量的时候不做更行
	protocol:EncodeAndSend()
end

-- 驭兽天下转换资质
function ControlBeastsWGCtrl:SendCSRoleBeastChangeFlair(aim_bag_id, beast_list, num)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleBeastChangeFlair)
	protocol.beast_index = self:GetServerBagId(aim_bag_id)
	protocol.material_list_count = #beast_list
	protocol.material_list = {}
	for i, compose_list_data in ipairs(beast_list) do
		protocol.material_list[i] = self:GetServerBagId(compose_list_data)
	end
	protocol.material_item_num = num or 0
	--print_error("驭兽天下转换资质", protocol)
	protocol:EncodeAndSend()
end

-- 数据更新（增加update，防止一直刷新数据）
function ControlBeastsWGCtrl:Update(now_time, elapse_time)
	if self.is_need_refresh_bag then
		if self.need_refresh_time == nil then
			self.need_refresh_time = 0
		end

		self.need_refresh_time = self.need_refresh_time + elapse_time
		if self.need_refresh_time >= BAG_DELAY_TIME then
			self.is_need_refresh_bag = false
			self.need_refresh_time = 0
			self:FlushSingleBeastBagInfo(true)
		end
	end
end

function ControlBeastsWGCtrl:FlushCurShowView(param_t, key)
	if self.view:IsOpen() then
		self.view:FlushCurShowView(param_t, key)
	end
end

-- 未孵化背包信息
function ControlBeastsWGCtrl:OnSCAllBeastEggBagInfo(protocol)
	--print_error("未孵化背包信息", protocol)
	-- self.data:SetAllBeastEggBagInfo(protocol)
	-- self.data:RefreshAllQuickBreedingInfo()
	-- self.data:RefreshAllBeastComposeListRedStatus()
	-- self:FlushCurShowView()
	-- self:FlushBeastsQuickSpView()
	-- RemindManager.Instance:Fire(RemindName.BeastsStable)
	-- RemindManager.Instance:Fire(RemindName.BeastsCompose)
end

-- 更新单个未孵化背包信息
function ControlBeastsWGCtrl:OnSCSingleBeastEggBagInfo(protocol)
	-- print_error("更新单个未孵化背包信息", protocol)
	self.data:RefreshSingleBeastEggData(protocol.egg_bag_index, protocol.egg_bag_info)
	self.is_need_refresh_bag = true
	self:FlushBeastsQuickSpView()
	-- self.data:RefreshAllQuickBreedingInfo()
	-- RemindManager.Instance:Fire(RemindName.BeastsStable)
end

-- 全部孵化槽信息
function ControlBeastsWGCtrl:OnSCAllBreedingInfo(protocol)
	--print_error("全部孵化槽信息", protocol)
	self.data:SetAllBreedingInfo(protocol)
	self.data:RefreshAllQuickBreedingInfo()
	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsStable)
end

-- 单个孵化槽信息
function ControlBeastsWGCtrl:OnSCSingleBreedingInfo(protocol)
	-- print_error("单个孵化槽信息", protocol)
	self.data:SetSingleBreedingInfo(protocol.breeding_index, protocol.breeding_slot)
	self:FlushCurShowView(nil, "single")
	self:FlushBeastStableResetTipsView()
	RemindManager.Instance:Fire(RemindName.BeastsStable)
end

-- 已孵化背包信息
function ControlBeastsWGCtrl:OnSCAllBeastBagInfo(protocol)
	-- print_error("已孵化背包信息", protocol, self.need_delay)
	self.data:SetAllBeastBagInfo(protocol)
	if self.prize_draw_view:IsOpen() then
		return
	end

	self.need_delay = false								-- 批量升星后后发一次整个背包信息，所以单个刷新在批量的时候不做更行
	self.data:RefreshAllHoleSingleRed()					-- 刷新上阵红点
	self.data:RefreshAllBeastComposeListRedStatus()		-- 刷新所有的合成信息
	self.view:SetMaskBtnsActive()
	self:FlushCurShowView()
	self:FlushBeastsDesposeView()
	self:FlushBeastsQuickSpView()

	--更新委托任务外形数据
	AssignmentWGData.Instance:SetMyAssignFashionData(AssignmentWGData.FASHION_TYPE.HUANSHOU, protocol)
	RemindManager.Instance:Fire(RemindName.Compose_Beast)
	self:FlushSingleBeastChange()
end

-- 更新单个已孵化背包信息
function ControlBeastsWGCtrl:OnSCSingleBeastBagInfo(protocol)
	local need_refresh_compose = self.data:SetSingleBeastBagInfo(protocol.beast_bag_index, protocol.beast_bag_info)
	-- print_error("单个更新", need_refresh_compose, protocol, self.need_delay)
	if self.prize_draw_view:IsOpen() or self.need_delay then	-- 批量升星后后发一次整个背包信息，所以单个刷新在批量的时候不做更行
		return
	end

	if need_refresh_compose then
		self.need_refresh_time = 0
		self.is_need_refresh_bag = true
	else
		self:FlushSingleBeastBagInfo(false)
	end

	--更新委托任务外形数据
	AssignmentWGData.Instance:SetMyAssignFashionData(AssignmentWGData.FASHION_TYPE.HUANSHOU, protocol)


end

-- 更新单个已孵化背包信息
function ControlBeastsWGCtrl:FlushSingleBeastBagInfo(is_refresh_compose)
	if is_refresh_compose then
		self.data:RefreshAllBeastComposeListRedStatus()
	end

	self.data:RefreshAllHoleSingleRed()
	self.view:SetMaskBtnsActive()
	self:FlushCurShowView()
	self:FlushBeastsDesposeView()
	self:FlushBeastsQuickSpView()
	self:FlushSingleBeastChange()
end

-- 单个幻兽变更刷新界面
function ControlBeastsWGCtrl:FlushSingleBeastChange()
	ViewManager.Instance:FlushView(GuideModuleName.AssignmentView, 0, "HuanShouDataChange")
	ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsSkinView)
	ViewManager.Instance:FlushView(GuideModuleName.HolyBeastsView)
	ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_beast, "beast_compose_item_change")

	RemindManager.Instance:Fire(RemindName.BeastsHandBook)
	RemindManager.Instance:Fire(RemindName.BeastsBattle)
	RemindManager.Instance:Fire(RemindName.BeastsCulture)
	RemindManager.Instance:Fire(RemindName.BeastsCompose)
	RemindManager.Instance:Fire(RemindName.HolyBeastsContract)
	RemindManager.Instance:Fire(RemindName.HolyBeastsSpirit)
end

-- 所有出战位信息
function ControlBeastsWGCtrl:OnSCAllStandBySlotInfo(protocol)
	-- print_error("所有出战位信息", protocol)
	self.data:SetStandBySlotInfo(protocol)
	self:FlushCurShowView()
	self:FlushAssistBattleSelectView()
	self:FlushBeastsKingView()
	self:FlushBeastsBattleSelectView()
	RemindManager.Instance:Fire(RemindName.BeastsBattle)
	RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
end

-- 更新单个出战位信息
function ControlBeastsWGCtrl:OnSCSingleStandBySlotInfo(protocol)
	-- print_error("更新单个出战位信息", protocol)
	self.data:RefreshAllHoleSingleRed()
	self.data:SetSingleStandBySlotInfo(protocol.stand_by_slot_index, protocol.stand_by_slot)
	local client_index = protocol.stand_by_slot_index + 1
	if client_index <= 3 then	--主战位置
		self:FlushCurShowView(nil, "main")
	else	--辅战位置
		self:FlushCurShowView(nil, "assist")
	end

	self:FlushBeastsBattleSelectView()
	self:FlushBeastsKingView()
	self:FlushAssistBattleSelectView()
	GlobalEventSystem:Fire(MainUIEventType.ROLE_BEASTS_SKILL_LIST, true)
	RemindManager.Instance:Fire(RemindName.BeastsBattle)
	RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
end

-- 基本信息
function ControlBeastsWGCtrl:OnSCBeastBaseInfo(protocol)
	-- print_error("基本信息", protocol)
	-- 获取所有基本信息
	local beast_king_level = self.data:GetBeastBaseInfoKingLevel()
	if beast_king_level and beast_king_level ~= protocol.beast_king_level then
		self:BeastOperateFinalEffect(UIEffectName.s_feisheng, true)
		self.view:ShowBeastKingLevelChangeEffect()
	end

	self.data:SetBeastBaseInfo(protocol)
	self:FlushCurShowView()

	if self.view and self.view:IsOpen() then
		GlobalEventSystem:Fire(OtherEventType.Beast_EXP_Change)
	end

	self:FlushBeastsKingView()
	RemindManager.Instance:Fire(RemindName.BeastsBattle)
	RemindManager.Instance:Fire(RemindName.BeastsKing)
	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

-- 所有宝物信息
function ControlBeastsWGCtrl:OnSCAllTreasureInfo(protocol)
	--print_error("所有宝物信息", protocol)
end

-- 所有星阵信息
function ControlBeastsWGCtrl:OnSCAllStarCircleInfo(protocol)
	-- print_error("所有星阵信息", protocol)
	self.data:SetAllStarCircleInfo(protocol)
	self:FlushBeastsRefiningResetView()
	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsRefining)
end

-- 更新单个星阵信息
function ControlBeastsWGCtrl:OnSCSingleStarCircleInfo(protocol)
	-- print_error("更新单个星阵信息", protocol)
	local beast_circle_data = self.data:GetStarCircleDataById(protocol.circle_index + 1)
	if beast_circle_data and protocol.circle_info then
		if beast_circle_data.interval_times ~= protocol.circle_info.interval_times then--突破成功
			self:BeastOperateFinalEffect(UIEffectName.s_tupo)
		end
	end

	self.data:RefreshSingleStarCircleData(protocol.circle_index, protocol.circle_info)
	self:FlushBeastsRefiningResetView()
	self.data:RefreshAllBeastUpGrade()	-- 星阵变化,等级上限变化
	self:FlushCurShowView(nil, "single")
	RemindManager.Instance:Fire(RemindName.BeastsRefining)
	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

function ControlBeastsWGCtrl:BeastOperateFinalEffect(ui_effect_type, is_center)
	if self.view:IsOpen() then
		self.view:BeastOperateFinalEffect(ui_effect_type, is_center)
	end
end

function ControlBeastsWGCtrl:BeastCultureOperateFinalEffect(ui_effect_type)
	-- if self.control_beasts_culture_swallow:IsOpen() then
	-- 	self.control_beasts_culture_swallow:BeastOperateFinalEffect(ui_effect_type)
	-- end

	-- if self.control_beasts_culture_change:IsOpen() then
	-- 	self.control_beasts_culture_change:BeastOperateFinalEffect(ui_effect_type)
	-- end
end

function ControlBeastsWGCtrl:BeastSkillOperateFinalEffect(ui_effect_type)
	if self.control_beasts_learn_skill_tip:IsOpen() then
		self.control_beasts_learn_skill_tip:BeastOperateFinalEffect(ui_effect_type)
	end
end

-- 操作结果
function ControlBeastsWGCtrl:OnOperateResult(result, param1, param2, param3)
	if result == OPERATE_RESULT.TRANSFER_SUCCESS then
		self:BeastCultureOperateFinalEffect(UIEffectName.s_jicheng)
		self:ShowBeastsCultureChangeEffect()
	elseif result == OPERATE_RESULT.SWALLOW_SUCCESS then
		self:BeastCultureOperateFinalEffect(UIEffectName.s_jinhua)
	elseif result == OPERATE_RESULT.SWALLOW_FAILURE then
		self:BeastCultureOperateFinalEffect(UIEffectName.f_jinhua)
	elseif result == OPERATE_RESULT.ACQUIRE_BEAST_SUCCESS then
		-- print_error("操作结果", result, param1, param2, param3)
		local client_bag_id = param1 + 1
		local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(client_bag_id)
	
		if beast_data and beast_data.server_data and beast_data.is_have_beast then
			-- 如果被链接，需要展示圣兽的
			local is_linked_beast = (not beast_data.is_holy_beast) and beast_data.server_data.holy_spirit_link_index ~= -1
			local real_beast_data = is_linked_beast and beast_data.link_beast_data or beast_data
			real_beast_data.pop_param = param2
			real_beast_data.is_up_star = param3 == 1
			local is_neee_show = ControlBeastsWGData.Instance:GetIsShowNewBeast(real_beast_data.server_data.beast_id)

			-- 通过副本获得幻兽  策划说不弹恭喜获得（前端特殊处理）
			local scene_type = Scene.Instance:GetSceneType()
			local fuben_other_cfg  = FuBenTeamCommonBossWGData.Instance:GetFuBenOtherCfg()
			local is_can_show_in_scene = true
			if scene_type == SceneType.TEAM_COMMON_BOSS_FB_1 and fuben_other_cfg.spe_task_reward_id_1 == real_beast_data.server_data.beast_id then
				is_can_show_in_scene = false
			end

			if is_neee_show or real_beast_data.pop_param == 1 and is_can_show_in_scene then
				local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(real_beast_data.server_data.beast_id)
				local data = {appe_image_id = res_id, appe_type = ROLE_APPE_TYPE.BEAST, index_param = real_beast_data}
				local is_playing_draw = ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw()
				if is_playing_draw == 1 then
					AddDelayCall(self, function()
						AppearanceWGCtrl.Instance:OnGetNewAppearance(data)
					end, 1) -- 防止在抽奖准备界面就弹出
				else
					AppearanceWGCtrl.Instance:OnGetNewAppearance(data)
				end
			end
		end

		if param3 == 1 then
			self:FlushCurShowView({bag_id = client_bag_id}, "star_up")
			-- 整理一下背包
			-- self:SendOperateTypeSortBeastBag()
		end
	elseif result == OPERATE_RESULT.ADD_SKILL then
		if param1 == ADD_SKILL_TYPE.ADD_SKILL_FAILURE then
			self:BeastSkillOperateFinalEffect(UIEffectName.f_xuexi)
		elseif param1 == ADD_SKILL_TYPE.ADD_SKILL_NEW_SKILL then
			self:BeastSkillOperateFinalEffect(UIEffectName.s_xuexi)
		elseif param1 == ADD_SKILL_TYPE.ADD_SKILL_REPLACE_SKILL then
			self:BeastSkillOperateFinalEffect(UIEffectName.s_xuexi)
		end
	elseif (result == OPERATE_RESULT.COMPOSE_BEAST_SUCCESS) or (result == OPERATE_RESULT.COMPOSE_BEAST_EGG_SUCCESS) then
		local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(param1)
		if aim_cfg then
			self:OnBeastsItemDataChange(aim_cfg.starup_beast_id, GameEnum.DATALIST_CHANGE_REASON_ADD)
		end

		self:OnBeastsItemDataChange(param1, GameEnum.DATALIST_CHANGE_REASON_ADD)
	end
end

-- 操作结果
function ControlBeastsWGCtrl:OnBeastDrawConvertResult(result, param1, param2)
	if result == 1 then
		local day_draw_data, last_time = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeByType(param1)
		local grade = day_draw_data and day_draw_data.grade or 1
		local cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeConvertByTypeGrade(param1, grade)
		local show_id = cfg and cfg.item_id
		TipWGCtrl.Instance:ShowGetReward(nil, {{item_id = show_id}}, nil, nil, nil, true)
		RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
	end
end

-- 物品变化
function ControlBeastsWGCtrl:OnBeastsItemDataChange(change_item_id, change_reason)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE ) then

		local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.SysRemind.AddItem, item_name, 1)
			if self.is_beast_item_change_block then
				self:AddBeastsItemChangeCache(str)
			else
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
		end
	end
end

-- 物品变化提示缓存
function ControlBeastsWGCtrl:AddBeastsItemChangeCache(str)
	table.insert(self.item_add_str_list, str)
end

function ControlBeastsWGCtrl:GetIsBlockBeastItemAddTips()
	return self.is_beast_item_change_block
end

-- 设置屏蔽物品变化提示
function ControlBeastsWGCtrl:SetBeastAddItemShow(is_show)
	self.is_beast_item_change_block = not is_show
	if is_show then
		for i, v in ipairs(self.item_add_str_list) do
			SysMsgWGCtrl.Instance:ErrorRemind(v)
		end
		self.item_add_str_list = {}
	end
end

--人物属性监听
function ControlBeastsWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" or attr_name == "prof" or attr_name == "tianxiange_level" then
		self.data:RefreshAllHoleSingleRed()
		RemindManager.Instance:Fire(RemindName.BeastsBattle)
	end
end

-- 可以释放御兽技能
function ControlBeastsWGCtrl:OnSCBeastTriggerSkill(protocol)
	-- local view = MainuiWGCtrl.Instance:GetView()
	-- if view then
	-- 	view:ShowBeastGroupSkill(true)
	-- end
end

--------------------------------------驭兽出战新改--------------------------------------
-- 驭兽用技能（不受任何东西影响，沉默也能放）
function ControlBeastsWGCtrl:SendCSBeastUseSkill(skill_id, target_id, pos_x, pos_y)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBeastUseSkill)
	protocol.skill_id = skill_id or 0
	protocol.target_id = target_id or 0
	protocol.pos_x = pos_x or 0
	protocol.pos_y = pos_y or 0
	protocol:EncodeAndSend()
end

-- 驭兽切换驭兽
function ControlBeastsWGCtrl:SendCSChangeBeast(battle_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBeastFightChange)
	protocol.battle_index = battle_index
	protocol:EncodeAndSend()
end

-- 驭兽战斗信息
function ControlBeastsWGCtrl:OnSCBeastFightInfo(protocol)
	self.data:SetBeastFightInfo(protocol)
	GlobalEventSystem:Fire(MainUIEventType.ROLE_BEASTS_SKILL_LIST, true)
end

-- 驭兽战斗信息
function ControlBeastsWGCtrl:OnControlBeastsFunOpen(fun_name)
    if fun_name == FunName.ControlBeastsView then
		GlobalEventSystem:Fire(MainUIEventType.ROLE_BEASTS_SKILL_LIST, true)
    end
end
--------------------------------------驭兽出战新改End--------------------------------------

--------------------------------------幻兽抽奖（新）--------------------------------------
--抽奖数据
function ControlBeastsWGCtrl:OnSCBeastDrawItemInfo(protocol)
	-- print_error("抽奖数据", protocol)
	self.prize_draw_data:SetDrawItemInfo(protocol)
	if self.prize_draw_view:IsOpen() then
		self.prize_draw_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

--抽奖数据更新
function ControlBeastsWGCtrl:OnSCBeastDrawItemUpdate(protocol)
	-- print_error("抽奖数据更新", protocol)
	self.prize_draw_data:DrawItemUpdate(protocol)
	if self.prize_draw_view:IsOpen() then
		self.prize_draw_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

--抽奖结果
function ControlBeastsWGCtrl:OnSCBeastDrawResult(protocol)
	if self.prize_draw_view:IsOpen() then
		self.prize_draw_view:Flush()
		self.prize_draw_view:ShowDrawResult(protocol)			-- 抽奖结果展示
	end

	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

-- 打开幻兽抽奖（新）
function ControlBeastsWGCtrl:OpenBeastsPrizeDraw()
	if not self.prize_draw_view:IsOpen() then
		-- self.prize_draw_view:Open()
		FunOpen.Instance:OpenViewByName(GuideModuleName.ControlBeastsPrizeDrawWGView)
	end
end

-- 关闭幻兽抽奖（新）
function ControlBeastsWGCtrl:CloseBeastsPrizeDraw()
	if self.prize_draw_view:IsOpen() then
		self.prize_draw_view:Close()
	end
end

-- 打开幻兽抽奖记录返回
function ControlBeastsWGCtrl:OnSCBeastDrawRecordInfo(protocol)
	-- print_error("打开幻兽抽奖记录返回", protocol)
	self.prize_draw_data:SetDrawRecordInfo(protocol)
end

-- 打开幻兽抽奖记录返回(单个增加)
function ControlBeastsWGCtrl:OnSCBeastDrawRecordAdd(protocol)
	-- print_error("打开幻兽抽奖记录返回", protocol)
	self.prize_draw_data:UpdateRecordInfo(protocol)
end

function ControlBeastsWGCtrl:OnDayPass()
	if self.prize_draw_view and self.prize_draw_view:IsOpen() then
		self.prize_draw_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.BeastsPrizeDraw)
end

--------------------------------------幻兽抽奖（新）End--------------------------------------
function ControlBeastsWGCtrl:OpenHoleTipsView(tips_data)
	self.control_beasts_hole_tips:SetTipsData(tips_data)
	if not self.control_beasts_hole_tips:IsOpen() then
		self.control_beasts_hole_tips:Open()
	else
		self.control_beasts_hole_tips:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastReleaseTipsView(tips_data)
	self.control_beasts_release_tip:SetTipsData(tips_data)
	if not self.control_beasts_release_tip:IsOpen() then
		self.control_beasts_release_tip:Open()
	else
		self.control_beasts_release_tip:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastStableResetTipsView(tips_data, incubate_index)
	self.control_beasts_stable_reset_tip:SetTipsData(tips_data)
	self.control_beasts_stable_reset_tip:SetTipsIncubateIndex(incubate_index)
	if not self.control_beasts_stable_reset_tip:IsOpen() then
		self.control_beasts_stable_reset_tip:Open()
	end
end

function ControlBeastsWGCtrl:FlushBeastStableResetTipsView(param_t)
	if self.control_beasts_stable_reset_tip:IsOpen() then
		self.control_beasts_stable_reset_tip:Flush(0, param_t)
	end
end

function ControlBeastsWGCtrl:OpenBeastsBattleSelectView(battle_data)
	self.control_beasts_battle_select:SetSelectData(battle_data)
	if not self.control_beasts_battle_select:IsOpen() then
		self.control_beasts_battle_select:Open()
	else
		self.control_beasts_battle_select:Flush()
	end
end

function ControlBeastsWGCtrl:FlushBeastsBattleSelectView()
	if self.control_beasts_battle_select:IsOpen() then
		self.control_beasts_battle_select:Flush()
	end
end

function ControlBeastsWGCtrl:CloseBeastsBattleSelectView()
	if self.control_beasts_battle_select:IsOpen() then
		self.control_beasts_battle_select:Close()
	end
end

function ControlBeastsWGCtrl:OpenBeastsBatchSelectView(battle_data)
	self.control_beasts_batch_select:SetData(battle_data)
	if not self.control_beasts_batch_select:IsOpen() then
		self.control_beasts_batch_select:Open()
	else
		self.control_beasts_batch_select:Flush()
	end
end

function ControlBeastsWGCtrl:SetSelectBeastsBatchOkCallBack(fuction)
	self.control_beasts_batch_select:SetOkBack(fuction)
end

function ControlBeastsWGCtrl:OpenBeastsEvolvePreview(battle_id)
	self.control_beasts_evolve_preview:SetCurShowBeastsId(battle_id)
	if not self.control_beasts_evolve_preview:IsOpen() then
		self.control_beasts_evolve_preview:Open()
	else
		self.control_beasts_evolve_preview:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastLearnSkillView(beasts_data)
	self.control_beasts_learn_skill_tip:SetTipsData(beasts_data)
	if not self.control_beasts_learn_skill_tip:IsOpen() then
		self.control_beasts_learn_skill_tip:Open()
	else
		self.control_beasts_learn_skill_tip:Flush()
	end
end

function ControlBeastsWGCtrl:FlushBeastLearnSkillView()
	if self.control_beasts_learn_skill_tip:IsOpen() then
		self.control_beasts_learn_skill_tip:Flush()
	end
end

function ControlBeastsWGCtrl:OpenAssistBattleSelectView(beasts_data)
	self.control_beasts_assist_battle_select_view:SetSelectData(beasts_data)
	if not self.control_beasts_assist_battle_select_view:IsOpen() then
		self.control_beasts_assist_battle_select_view:Open()
	else
		self.control_beasts_assist_battle_select_view:Flush()
	end
end

function ControlBeastsWGCtrl:FlushAssistBattleSelectView()
	if self.control_beasts_assist_battle_select_view:IsOpen() then
		self.control_beasts_assist_battle_select_view:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastsQuickSpView()
	if not self.control_beasts_quick_sp:IsOpen() then
		self.control_beasts_quick_sp:Open()
	else
		self.control_beasts_quick_sp:Flush()
	end
end

function ControlBeastsWGCtrl:FlushBeastsQuickSpView()
	if self.control_beasts_quick_sp:IsOpen() then
		self.control_beasts_quick_sp:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastsGroupView()
	if not self.control_beasts_group_view:IsOpen() then
		self.control_beasts_group_view:Open()
	else
		self.control_beasts_group_view:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastElementView(now_active_gm_type)
	self.control_beasts_battle_element_view:SetNowActiveGmType(now_active_gm_type)
	if not self.control_beasts_battle_element_view:IsOpen() then
		self.control_beasts_battle_element_view:Open()
	else
		self.control_beasts_battle_element_view:Flush()
	end
end

function ControlBeastsWGCtrl:OpenRefiningLevelView()
	if not self.control_beasts_refining_level_view:IsOpen() then
		self.control_beasts_refining_level_view:Open()
	else
		self.control_beasts_refining_level_view:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastsRefiningResetView(circle_data)
	self.control_beasts_refining_reset:SetSelectData(circle_data)
	if not self.control_beasts_refining_reset:IsOpen() then
		self.control_beasts_refining_reset:Open()
	else
		self.control_beasts_refining_reset:Flush()
	end
end

function ControlBeastsWGCtrl:FlushBeastsRefiningResetView()
	if self.control_beasts_refining_reset:IsOpen() then
		self.control_beasts_refining_reset:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastsCultureSwallowView(beast_bag_id)
	-- self.control_beasts_culture_swallow:SetSelectData(beast_bag_id)
	-- if not self.control_beasts_culture_swallow:IsOpen() then
	-- 	self.control_beasts_culture_swallow:Open()
	-- else
	-- 	self.control_beasts_culture_swallow:Flush()
	-- end
end

function ControlBeastsWGCtrl:FlushBeastsCultureSwallowView()
	-- if self.control_beasts_culture_swallow:IsOpen() then
	-- 	self.control_beasts_culture_swallow:Flush()
	-- end
end

function ControlBeastsWGCtrl:OpenBeastsCultureChangeView(beast_bag_id)
	-- self.control_beasts_culture_change:SetSelectData(beast_bag_id)
	-- if not self.control_beasts_culture_change:IsOpen() then
	-- 	self.control_beasts_culture_change:Open()
	-- else
	-- 	self.control_beasts_culture_change:Flush()
	-- end
end

function ControlBeastsWGCtrl:FlushBeastsCultureChangeView()
	-- if self.control_beasts_culture_change:IsOpen() then
	-- 	self.control_beasts_culture_change:Flush()
	-- end
end

function ControlBeastsWGCtrl:ShowBeastsCultureChangeEffect()
	-- if self.control_beasts_culture_change:IsOpen() then
	-- 	self.control_beasts_culture_change:ShowChangeSuccessEffect()
	-- end
end

function ControlBeastsWGCtrl:OpenBeastsSwallowSelectView(battle_data)
	-- self.control_beasts_swallow_select:SetData(battle_data)
	-- if not self.control_beasts_swallow_select:IsOpen() then
	-- 	self.control_beasts_swallow_select:Open()
	-- else
	-- 	self.control_beasts_swallow_select:Flush()
	-- end
end

function ControlBeastsWGCtrl:SetSelectSwallowSelectOkCallBack(fuction)
	-- self.control_beasts_swallow_select:SetOkBack(fuction)
end

function ControlBeastsWGCtrl:OpenBeastsBattleAttrView()
	if not self.control_beasts_battle_attr_view:IsOpen() then
		self.control_beasts_battle_attr_view:Open()
	else
		self.control_beasts_battle_attr_view:Flush()
	end
end

function ControlBeastsWGCtrl:ChangeToIndex(index)
	if self.view:IsOpen() then
		self.view:ChangeToIndex(index)
	end
end

-- 展示奖励列表
function ControlBeastsWGCtrl:RewardContractListShow(protocol)
	if self.view:IsOpen() then
		self.view:RewardContractListShow(protocol)
	else
		TipWGCtrl.Instance:ShowGetCommonReward(protocol.item_list, nil, nil, false)
	end
end

function ControlBeastsWGCtrl:FlushBeastsKingView()
	if self.control_beasts_king_view:IsOpen() then
		self.control_beasts_king_view:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastsKingView()
	if not self.control_beasts_king_view:IsOpen() then
		self.control_beasts_king_view:Open()
	else
		self.control_beasts_king_view:Flush()
	end
end

function ControlBeastsWGCtrl:OpenBeastsSkillShowTips(beasts_id)
	if not self.control_beasts_skill_show_view:IsOpen() then
		self.control_beasts_skill_show_view:SetBeastsId(beasts_id)
	end
end

function ControlBeastsWGCtrl:OpenBeastsDesposeView()
	if not self.control_beasts_despose_view:IsOpen() then
		self.control_beasts_despose_view:Open()
	end
end

function ControlBeastsWGCtrl:FlushBeastsDesposeView()
	if self.control_beasts_despose_view:IsOpen() then
		self.control_beasts_despose_view:Flush()
	end
end

-- 触发元素反应
function ControlBeastsWGCtrl:OnSCEffectElementReaction(protocol)
	self.data:SetElementData(protocol)
	if nil == self.control_beasts_effect_loader then
		local obj_id = self.data:GetElementObjID()
		local obj = Scene.Instance:GetObj(obj_id)
		if not obj then
			return
		end

		local draw_obj = obj:GetDrawObj()
		if not draw_obj then
			return
		end

		local point = draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
		local cfg = ControlBeastsWGData.Instance:GetElementComboCfg()
		if not point or not cfg then
			return
		end

	    EffectManager.Instance:PlayControlEffect(self, cfg.bundle, cfg.asset, point.position, nil, point)
	end

	local cfg = ControlBeastsWGData.Instance:GetElementComboCfg()
	local element_param1, element_param2 = ControlBeastsWGData.Instance:GetAllElementType()
	MainuiWGCtrl.Instance:SetBeastBuffIcon(false)
	
	AddDelayCall(self, function ()
		MainuiWGCtrl.Instance:PlayBeastBuffAni(cfg.combo_skill_img, element_param1, element_param2)
	end, 0.5)
end

function ControlBeastsWGCtrl:OpenBeastElementActiveView(now_active_gm_type, gm_count)
	self.control_beasts_element_active_view:SetNowActiveGmType(now_active_gm_type, gm_count)
	if not self.control_beasts_element_active_view:IsOpen() then
		self.control_beasts_element_active_view:Open()
	else
		self.control_beasts_element_active_view:Flush()
	end
end

----------------------------幻兽图鉴（新）---------------------------
--幻兽图鉴信息
function ControlBeastsWGCtrl:OnSCBeastHoodbookInfo(protocol)
	-- print_error("幻兽图鉴信息", protocol)
	self.data:SetBeastHoodbookInfo(protocol)
	self:FlushBeastHandBookRewardView()

	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsHandBook)
end

--幻兽图鉴信息(单个)
function ControlBeastsWGCtrl:OnSCBeastHoodbookUpdate(protocol)
	-- print_error("幻兽图鉴信息(单个)", protocol)
	self.data:UpdateBeastHoodbookInfo(protocol)
	self:FlushBeastHandBookRewardView()

	RemindManager.Instance:Fire(RemindName.BeastsHandBook)
	self:FlushCurShowView()
	ViewManager.Instance:FlushView(GuideModuleName.Compose, TabIndex.other_compose_beast)
end

function ControlBeastsWGCtrl:OpenBeastHandBookRewardView(beast_type)
	self.control_beasts_hand_book_reward_view:SetSelectBeastType(beast_type)
	if not self.control_beasts_hand_book_reward_view:IsOpen() then
		self.control_beasts_hand_book_reward_view:Open()
	else
		self.control_beasts_hand_book_reward_view:Flush()
	end
end

function ControlBeastsWGCtrl:FlushBeastHandBookRewardView()
	if self.control_beasts_hand_book_reward_view:IsOpen() then
		self.control_beasts_hand_book_reward_view:Flush()
	end
end

-- 打开抽奖准备界面
-- show_data.callback:关闭回调
function ControlBeastsWGCtrl:OpenPrizeDrawPrepareView(show_data)
	self.prize_draw_prepare_view:SetDataAndOpen(show_data)
end

function ControlBeastsWGCtrl:ClosePrizeDrawPrepareView()
	if self.prize_draw_prepare_view:IsOpen() then
		self.prize_draw_prepare_view:Close()
	end
end

----------------------------幻兽皮肤---------------------------
function ControlBeastsWGCtrl:OnSCBeastSkinInfo(protocol)
		-- print_error("幻兽皮肤信息", protocol)
	self.data:SetBeastsSkinData(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.ControlBeastsSkinView)
end

function ControlBeastsWGCtrl:OpenBeastSkinView(beast_data)
	self.control_beasts_skin_view:SetDataAndOpen(beast_data)
end

-- 幻兽皮肤升级操作结果 result(1 succ, 0 fail) param1=skin_seq param2=skin_level
function ControlBeastsWGCtrl:OnBeastSkinResult(result, param1, param2)
	if result == 1 then
		local res_id = ControlBeastsWGData.Instance:GetBeastModelSkinResId(param1)
		local data = { 
			appe_image_id = res_id,  
			appe_type = ROLE_APPE_TYPE.BEAST_SKIN,
			index_param = param1
		}
		AppearanceWGCtrl.Instance:OnGetNewAppearance(data)
	end
end

-- 打开抽奖结算面板
function ControlBeastsWGCtrl:ShowControlBeastsDrawResultView(contract_select_type, id_list, again_func, other_info, no_need_sort, sure_func)
	self.control_beasts_draw_result_view:SetData(contract_select_type, id_list, again_func, other_info, no_need_sort, sure_func)
	if self.control_beasts_draw_result_view:IsLoaded() and self.control_beasts_draw_result_view:IsOpen() then
		self.control_beasts_draw_result_view:Flush()
	else
		self.control_beasts_draw_result_view:Open()
	end
end

-- 关闭幻兽抽奖结果（新）
function ControlBeastsWGCtrl:CloseControlBeastsDrawResultView()
	if self.control_beasts_draw_result_view:IsOpen() then
		self.control_beasts_draw_result_view:Close()
	end
end
----------------------------创世圣兽---------------------------
function ControlBeastsWGCtrl:OpenHolyBeastsView(beast_type)
	self.holy_beasts_view:SetDataAndOpen(beast_type)
end


function ControlBeastsWGCtrl:GetHolyBeastViewCurIndex()
	if self.holy_beasts_view:IsOpen() then
		return self.holy_beasts_view:GetShowIndex()
	end
	return 0
end

function ControlBeastsWGCtrl:OpenHolyBeastContractView(data)
	self.holy_beasts_contract_select_view:SetDataAndOpen(data)
end

function ControlBeastsWGCtrl:OnBeastHolyLinkResult(result, param1, param2)
	if result == 1 then
		local tip_str = param2 == -1 and Language.ContralBeasts.HolyBeastDisconnectTip or Language.ContralBeasts.HolyBeastLinkedTip
		TipWGCtrl.Instance:ShowSystemMsg(tip_str)
	end
end

function ControlBeastsWGCtrl:OnBeastSpiritUpLevelResult(result, param1, param2)
	if self.holy_beasts_view:IsOpen() and result == 1 then
		self.holy_beasts_view:PlayUseEffect(UIEffectName.s_shengji)
	end
end

----------------------------幻兽属性丹---------------------------
function ControlBeastsWGCtrl:OnSCBeastPelletInfo(protocol)
	-- print_error("幻兽属性丹", protocol)
	self.data:SetBeastsPelletInfo(protocol)
	self:FlushCurShowView(nil, "culture_beasts_sxd")
	RemindManager.Instance:Fire(RemindName.BeastsCulture)
end

function ControlBeastsWGCtrl:OpenTipsAttrView(tips_data)
	self.control_beasts_culture_sxd_attr_view:SetData(tips_data)
end

function ControlBeastsCultivateWGView:LoadStrengthenViewCallBack()
    if not self.beasts_alchemy_list then
		self.beasts_alchemy_list = AsyncListView.New(BeastsCultivateSlotRender, self.node_list.alchemy_strengthen_list)
        self.beasts_alchemy_list:SetStartZeroIndex(true)
		self.beasts_alchemy_list:SetSelectCallBack(BindTool.Bind(self.BeastsAlchemySlotSelect,self))
        self.beasts_alchemy_list:SetRefreshCallback(BindTool.Bind(self.BeastsAlchemySlotRefresh,self))
		self.beasts_alchemy_list:SetLimitSelectFunc(BindTool.Bind(self.BeastsAlchemySlotSelectLimit,self))
	end

    if not self.alchemy_strengthen_item then
        self.alchemy_strengthen_item = ItemCell.New(self.node_list.alchemy_strengthen_item)
    end

    if not self.alchemy_strengthen_attrlist then
        self.alchemy_strengthen_attrlist = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.alchemy_strengthen_attrlist:FindObj(string.format("attr_%d", i))
            if cell_obj then
                local cell = CommonAddAttrRender.New(cell_obj)
                cell:SetIndex(i)
                self.alchemy_strengthen_attrlist[i] = cell
            end
        end
    end

    if not self.alchemy_full_strengthen_attrlist then
        self.alchemy_full_strengthen_attrlist = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.alchemy_full_strengthen_attrlist:FindObj(string.format("attr_%d", i))
            if cell_obj then
                local cell = CommonAddAttrRender.New(cell_obj)
                cell:SetIndex(i)
                self.alchemy_full_strengthen_attrlist[i] = cell
            end
        end
    end

    if not self.alchemy_strengthen_stuff_list then
        self.alchemy_strengthen_stuff_list = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.alchemy_strengthen_stuff_list:FindObj(string.format("stuff_cell_0%d", i))
            if cell_obj then
                local cell = AlchemyStuffSpendRender.New(cell_obj)
                cell:SetIndex(i)
                self.alchemy_strengthen_stuff_list[i] = cell
            end
        end
    end

    XUI.AddClickEventListener(self.node_list.alchemy_strengthen_quick_btn, BindTool.Bind2(self.OnClickAlchemyQuickStrengthenBtn, self))
    XUI.AddClickEventListener(self.node_list.alchemy_strengthen_once_btn, BindTool.Bind2(self.OnClickAlchemyOnceStrengthenBtn, self))
end

function ControlBeastsCultivateWGView:OpenStrengthenViewCallBack()
end

function ControlBeastsCultivateWGView:CloseStrengthenViewCallBack()
    self.quick_strengthen_status = nil
    self.can_show_lv_up_effect = nil
    self.now_old_show_level = nil
    self.now_old_show_exp = nil
    self.is_anim = nil
end

function ControlBeastsCultivateWGView:ShowStrengthenViewCallBack()
end

function ControlBeastsCultivateWGView:ReleaseStrengthenViewCallBack()
    if self.beasts_alchemy_list then
        self.beasts_alchemy_list:DeleteMe()
        self.beasts_alchemy_list = nil
    end

    if self.alchemy_strengthen_item then
        self.alchemy_strengthen_item:DeleteMe()
        self.alchemy_strengthen_item = nil
    end

    if self.alchemy_strengthen_attrlist then
        for i, v in ipairs(self.alchemy_strengthen_attrlist) do
            v:DeleteMe()
        end

        self.alchemy_strengthen_attrlist = nil
    end

    if self.alchemy_full_strengthen_attrlist then
        for i, v in ipairs(self.alchemy_full_strengthen_attrlist) do
            v:DeleteMe()
        end

        self.alchemy_full_strengthen_attrlist = nil
    end

    if self.alchemy_strengthen_stuff_list then
        for i, v in ipairs(self.alchemy_strengthen_stuff_list) do
            v:DeleteMe()
        end

        self.alchemy_strengthen_stuff_list = nil
    end

    self.quick_strengthen_status = nil
    self.can_show_lv_up_effect = nil
    self.now_old_show_level = nil
    self.now_old_show_exp = nil
    self:StrengthenClearGradeSliderTween()
end

-----------------------------------------------------------------------------
function ControlBeastsCultivateWGView:FlushStrengthenViewCallBack(param_t)
    self:ShowStrengthenViewChangeHole()
end

-- 切换上阵位置
function ControlBeastsCultivateWGView:ShowStrengthenViewChangeHole()
    if (not self.fight_slot_index) or (not self.fight_slot_data) then
        return
    end

    if self.beasts_alchemy_list == nil then
        return
    end

    local hole_id = self.fight_slot_index
    local now_slot_index = 0

    if self.beast_slot_index then
        now_slot_index = self.beast_slot_index
    end

    local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(hole_id)
    self.beasts_alchemy_list:SetDataList(list)
    self.beasts_alchemy_list:JumpToIndex(now_slot_index, 6)
end

-- 当前的空位点击
function ControlBeastsCultivateWGView:BeastsAlchemySlotSelect(beast_slot_item, cell_index, is_default, is_click)
    if beast_slot_item == nil or is_default or beast_slot_item.data == nil then
        return
    end

	if self.beast_slot_index == cell_index and is_click then
		return
	end

    self.beast_slot_index = cell_index
    self.beast_slot_data = beast_slot_item.data
    local is_need_reset_lock = false
 
    if is_click then
        self.quick_strengthen_status = nil
        self.can_show_lv_up_effect = nil
        self.now_old_show_level = nil
        self.now_old_show_exp = nil
        self.is_anim = nil
        self.num_list = nil
        is_need_reset_lock = true
    end

    if self.show_index == TabIndex.beasts_alchemy_strengthen then
		self:PlayAlchemyStrengthenlvUpSliderAnim()
	elseif self.show_index == TabIndex.beasts_alchemy_succinct then
		self:BeastsFlushAlchemySlotSuccinctMessage(is_need_reset_lock)
    end
end

-- 刷新节点
function ControlBeastsCultivateWGView:BeastsAlchemySlotRefresh(beast_slot_item, cell_index)
    if beast_slot_item == nil or beast_slot_item.data == nil then
        return
    end

    local hole_id = self.fight_slot_index
    beast_slot_item:OnRefreshCallCack(hole_id, self.show_index)
end

-- 点击按钮
function ControlBeastsCultivateWGView:BeastsAlchemySlotSelectLimit(beast_slot_item)
    if beast_slot_item == nil or beast_slot_item.data == nil then
        return
    end

    if beast_slot_item.is_final_lock then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyEquipSlotLock)
        return true
    end

    local hole_id = self.fight_slot_index
    local equip_item_id = (beast_slot_item.data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    if equip_item_id == COMMON_CONSTS.NUMBER_ZERO then
        -- 这里关闭界面直接到出战
        local best_data = ControlBeastsCultivateWGData.Instance:GetBestAlchemyBeastEquipForGrid(beast_slot_item.index, 0)
        if best_data == nil then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.AlchemyEquipError)
        else
            ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperatePut(hole_id, beast_slot_item.index, best_data.index)
        end

        return true
    end

    return false
end

-- 播放动画
function ControlBeastsCultivateWGView:PlayAlchemyStrengthenlvUpSliderAnim()
    if not self.beast_slot_data then
        return
    end

    if self.now_old_show_level == nil or self.now_old_show_exp == nil then
        self:StrengthenClearGradeSliderTween()
        self:BeastsFlushAlchemySlotStrengthenMessage(nil, true) -- 初始化的状态
        return
    end

    local data = self.beast_slot_data
    local lv_cfg = ControlBeastsCultivateWGData.Instance:GetSlotUpLvCfg(self.beast_slot_index, data.level)
    local need_exp = lv_cfg and lv_cfg.need_exp or 0
    local start_value = self.node_list.alchemy_strengthen_slider.slider.value

    if self.now_old_show_level == data.level then
        if self.now_old_show_exp == data.exp then
            self:BeastsFlushAlchemySlotStrengthenMessage(nil, true) -- 初始化的状态
        else
            self:PlayStrengthenlvProgressAni(start_value, data.exp / need_exp, false, function ()
                self:BeastsFlushAlchemySlotStrengthenMessage(nil, true) -- 初始化的状态
            end)
        end
    else
        self:PlayStrengthenlvProgressAni(start_value, 1, true, function ()
            self.node_list.alchemy_strengthen_slider.slider.value = 0
            self:BeastsFlushAlchemySlotStrengthenMessage(self.now_old_show_level + COMMON_CONSTS.NUMBER_ONE) -- 初始化的状态
            self:CultivateOperateFinalEffect(UIEffectName.hsnd_qhcg)
        end)
    end
end

function ControlBeastsCultivateWGView:PlayStrengthenlvProgressAni(start_value, aim_value, is_full, complete_func)
	self:StrengthenClearGradeSliderTween()
	local slider = self.node_list.alchemy_strengthen_slider.slider
	if is_full then
		aim_value = 1
	end

	if aim_value == 0 then
		if complete_func then
			complete_func()
		end

		return
	end

    local time = tonumber(string.format("%.2f", aim_value * 0.4))
    slider.value = start_value
    self.strengthen_slider_tween = slider:DOValue(aim_value, time)
    self.is_anim = true
	self.strengthen_slider_tween:OnComplete(function ()
		if complete_func then
			complete_func()
		end

        self.is_anim = false
	end)
end


-- 清空动画
function ControlBeastsCultivateWGView:StrengthenClearGradeSliderTween()
    if self.strengthen_slider_tween then
        self.strengthen_slider_tween:Kill()
        self.strengthen_slider_tween = nil
    end
end

-- 刷新强化数据
function ControlBeastsCultivateWGView:BeastsFlushAlchemySlotStrengthenMessage(level, is_final)
    if not self.beast_slot_data then
        return
    end

    local data = self.beast_slot_data
    local real_level = level or data.level
    local need_effect = level ~= nil
    local next_lv = real_level + COMMON_CONSTS.NUMBER_ONE
    local equip_item_id = (data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    local hole_id = self.fight_slot_index
    self.can_show_lv_up_effect = true
 
    local item_data = {
        item_id = equip_item_id, 
        equip_info = data.equip_info, 
        is_bag_equip = false, 
        is_wear = true,
        fight_slot = hole_id, 
        equip_slot = self.beast_slot_index,
        equip_slot_lv = real_level,
    }

    self.alchemy_strengthen_item:SetData(item_data)  
    -- self.alchemy_strengthen_item:SetRightTopImageTextActive(true)

    -- if real_level ~= 0 then
    --     self.alchemy_strengthen_item:SetRightTopImageText(string.format("+%d", real_level))
    -- else
    --     self.alchemy_strengthen_item:SetRightTopImageTextActive(false)
    -- end

    local item_cfg = ItemWGData.Instance:GetItemConfig(equip_item_id)
    local name_str = ToColorStr(item_cfg and item_cfg.name or "", ITEM_COLOR[item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO])
    local lv_str = ""

    if real_level ~= 0 then
        lv_str = ToColorStr(string.format("(+%s)", real_level), COLOR3B.GREEN)
    end

    local final_str = string.format("%s%s", name_str, lv_str)
    self.node_list.alchemy_strengthen_name.text.text = final_str
    local lv_cfg = ControlBeastsCultivateWGData.Instance:GetSlotUpLvCfg(self.beast_slot_index, real_level)
    local lv_next_cfg = ControlBeastsCultivateWGData.Instance:GetSlotUpLvCfg(self.beast_slot_index, next_lv)
    local is_full_lv = lv_next_cfg == nil

    self.node_list.alchemy_strengthen_full_root:CustomSetActive(is_full_lv)
    self.node_list.alchemy_strengthen_not_full_root:CustomSetActive(not is_full_lv)
    self.node_list.alchemy_strengthen_slider:CustomSetActive(not is_full_lv)
    self.node_list.alchemy_strengthen_progress:CustomSetActive(not is_full_lv)

    local need_exp = lv_cfg and lv_cfg.need_exp or 0
    local now_exp = data.exp

    if not is_full_lv then
        self.node_list.alchemy_strengthen_progress.text.text = string.format("%d/%d", now_exp, need_exp)
    end

    if is_final then
        self.node_list.alchemy_strengthen_slider.slider.value = now_exp / need_exp
    end

    self.node_list.alchemy_strengthen_lv.text.text = string.format("+%d", real_level) 
    self.node_list.alchemy_strengthen_next_lv.text.text = string.format("+%d", next_lv) 
    self.node_list.alchemy_full_strengthen_lv.text.text = string.format("+%d", real_level) 
    local list = ControlBeastsCultivateWGData.Instance:SpiltAlchemyBeastEquipAttrList(nil, lv_cfg, lv_next_cfg, true)
    local attr_cell_list = is_full_lv and self.alchemy_full_strengthen_attrlist or self.alchemy_strengthen_attrlist
  
    for i, v in ipairs(attr_cell_list) do
        v:SetVisible(list[i] ~= nil)

        if list[i] ~= nil then
            v:SetData(list[i])
        end
    end

    if not is_full_lv then
        local stuff_item_list = ControlBeastsCultivateWGData.Instance:GetSlotUplevelItemListCfg()
        for i, v in ipairs(self.alchemy_strengthen_stuff_list) do
            v:SetVisible(stuff_item_list[i] ~= nil)
    
            if stuff_item_list[i] ~= nil then
                v:SetData(stuff_item_list[i])
            end
        end
    end

    self.now_old_show_level = real_level

    if need_effect then
        self.now_old_show_exp = 0
        self:CultivateOperateFinalEffect(UIEffectName.hsnd_qhcg)
        -- 再次检测动画
        if is_full_lv then
            self:BeastsFlushAlchemySlotStrengthenMessage(nil, true)
        else
            self:PlayAlchemyStrengthenlvUpSliderAnim()
        end
    else
        local red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanStrengthen(self.beast_slot_index, data)
        self.node_list.alchemy_strengthen_remind:CustomSetActive(red)
        self.node_list.alchemy_once_strengthen_remind:CustomSetActive(red)
        self:AlchemyCheckCanQuickStrengthen(is_full_lv)   -- 再次检测升级
        self.now_old_show_exp = data.exp
    end
end

-- 检测是否能快速强化
function ControlBeastsCultivateWGView:AlchemyCheckCanQuickStrengthen(is_full_lv)
    self:FlushAlchemyQuickStrengthenStatus()
    if (not self.quick_strengthen_status) or is_full_lv then
        return
    end

    if not self.beast_slot_data then
        self.quick_strengthen_status = false
        return
    end

    local data = self.beast_slot_data
    local red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanStrengthen(self.beast_slot_index, data)
    if red then
        local hole_id = self.fight_slot_index
        ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateUpLevel(hole_id, self.beast_slot_index)
    else
        self.quick_strengthen_status = false
    end
    self:FlushAlchemyQuickStrengthenStatus()
end

-- 检测是否能快速强化
function ControlBeastsCultivateWGView:FlushAlchemyQuickStrengthenStatus()
    local str = self.quick_strengthen_status and Language.TianShen.OneKeyBtnText[2] or Language.ShiTianSuit.AutoStrengthen
    self.node_list.alchemy_quick_strengthen_text.text.text = str
end

-- 检测道具是否够用
function ControlBeastsCultivateWGView:CheckAlchemyQuickStrengthenItemSatisfy()
    local stuff_item_list = ControlBeastsCultivateWGData.Instance:GetSlotUplevelItemListCfg()
    local is_statisfy = false
    local item_id = 0

    for i, v in ipairs(stuff_item_list) do
        if v.item_id then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
            item_id = v.item_id

            if item_num > 0 then
                is_statisfy = true
                break
            end
        end
    end

    return is_statisfy, item_id
end

-----------------------------------------------------------------------------
-- 快速强化
function ControlBeastsCultivateWGView:OnClickAlchemyQuickStrengthenBtn()
    if self.is_anim then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip6)
        return
    end

    local is_enougth, item_id = self:CheckAlchemyQuickStrengthenItemSatisfy()
    if not is_enougth then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
        return
    end

    self.quick_strengthen_status = not self.quick_strengthen_status
    self:AlchemyCheckCanQuickStrengthen()
end
-- 单次强化
function ControlBeastsCultivateWGView:OnClickAlchemyOnceStrengthenBtn()
    if self.is_anim then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip6)
        return
    end

    local is_enougth, item_id = self:CheckAlchemyQuickStrengthenItemSatisfy()
    if not is_enougth then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
        return
    end

    local hole_id = self.fight_slot_index
    ControlBeastsCultivateWGCtrl.Instance:SendBeastEquipClientOperateUpLevel(hole_id, self.beast_slot_index)
end
----------------------------------------BeastsCultivateHoleRender---------------------------------
BeastsCultivateSlotRender = BeastsCultivateSlotRender or BaseClass(BaseRender)
function BeastsCultivateSlotRender:LoadCallBack()
    if not self.beast_item_cell then
        self.beast_item_cell = ItemCell.New(self.node_list.alchemy_item)
    end
end

function BeastsCultivateSlotRender:ReleaseCallBack()
    if self.beast_item_cell then
        self.beast_item_cell:DeleteMe()
        self.beast_item_cell = nil
    end

    self.is_final_lock = nil
    self.fight_slot_index = nil
end

function BeastsCultivateSlotRender:OnFlush()
    if not self.data then
        return 
    end
end

function BeastsCultivateSlotRender:OnRefreshCallCack(hole_id, show_index)
    self.fight_slot_index = hole_id
    self.is_final_lock = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipSlotIsLock(hole_id, self.index)
    local equip_item_id = (self.data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO

    self.node_list.lock:CustomSetActive(self.is_final_lock)
    self.node_list.un_lock:CustomSetActive((not self.is_final_lock) and equip_item_id ~= 0)
    self.node_list.add_root:CustomSetActive((not self.is_final_lock) and equip_item_id == 0)

    if (not self.is_final_lock) and equip_item_id ~= 0 then
        local item_data = {
            item_id = equip_item_id, 
            equip_info = self.data.equip_info, 
            is_bag_equip = false, 
            is_wear = true,
            fight_slot = hole_id, 
            equip_slot = self.index,
            equip_slot_lv = self.data.level,
        }

        self.beast_item_cell:SetData(item_data)  
        if self.data.level and self.data.level ~= 0 then
            self.beast_item_cell:SetRightTopImageTextActive(true)
            self.beast_item_cell:SetRightTopImageText(string.format("+%d", self.data.level))
        else
            self.beast_item_cell:SetRightTopImageTextActive(false)
        end

        local item_cfg = ItemWGData.Instance:GetItemConfig(equip_item_id)
        self.node_list.alchemy_name.text.text = item_cfg and item_cfg.name or "" --ToColorStr(item_cfg and item_cfg.name or "", ITEM_COLOR[item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO])
        self.node_list.alchemy_select_name.text.text = item_cfg and item_cfg.name or ""
        local score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(self.data.equip_info, false, hole_id, self.index, self.data.level)
        self.node_list.alchemy_score.text.text = score
        self.node_list.alchemy_select_score.text.text = score
    end

    local red = false
    if (not self.is_final_lock) and equip_item_id ~= 0 then
        if show_index == TabIndex.beasts_alchemy_strengthen then
            red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanStrengthen(self.index, self.data)
        elseif show_index == TabIndex.beasts_alchemy_succinct then
            red = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanSuccinctSave(self.data.equip_info)
        end
    end

	self.node_list.remind:CustomSetActive(red)
end

-- 选中改变
function BeastsCultivateSlotRender:OnSelectChange(is_select)
	self.node_list.select:CustomSetActive(is_select)
	self.node_list.normal:CustomSetActive(not is_select)
end

----------------------------------------AlchemyStuffSpendRender---------------------------------
AlchemyStuffSpendRender = AlchemyStuffSpendRender or BaseClass(BaseRender)
function AlchemyStuffSpendRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
    end
end

function AlchemyStuffSpendRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function AlchemyStuffSpendRender:OnFlush()
    if not self.data then
        return 
    end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
    self.item_cell:SetData({item_id = self.data.item_id, num = item_num})
    self.item_cell:SetRightBottomTextVisible(true)
    self.item_cell:SetRightBottomColorText(item_num)
end

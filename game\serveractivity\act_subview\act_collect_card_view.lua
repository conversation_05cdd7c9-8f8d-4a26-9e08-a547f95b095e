----------------------------------------------------
--开服活动 -- 收集卡片
----------------------------------------------------
local COLLECT_CARD_TYPE = 
{
	CARD_TYPE_TOP = 1,
	CARD_TYPE_MID = 2,
	CARD_TYPE_DOWN = 3,
}

local COLLECT_NUM_ZERO = 0
local COLLECT_NUM_ONE = 1

function ServerActivityTabView:CollectCardReleaseCallBack()
    if self.collect_card_pro_list and #self.collect_card_pro_list > 0 then
		for _, card_pro_cell in ipairs(self.collect_card_pro_list) do
			card_pro_cell:DeleteMe()
			card_pro_cell = nil
		end

		self.collect_card_pro_list = nil
	end

	if self.card_list_top then
        self.card_list_top:DeleteMe()
        self.card_list_top = nil
    end

	if self.card_list_mid then
        self.card_list_mid:DeleteMe()
        self.card_list_mid = nil
    end

	if self.card_list_down then
        self.card_list_down:DeleteMe()
        self.card_list_down = nil
    end

    if self.collect_card_reward_list and #self.collect_card_reward_list > 0 then
		for _, card_rew_cell in ipairs(self.collect_card_reward_list) do
			card_rew_cell:DeleteMe()
			card_rew_cell = nil
		end

		self.collect_card_reward_list = nil
	end

	self.now_collect_card_chip_count = nil
end

function ServerActivityTabView:CollectCardLoadCallBack()
	if not self.collect_card_pro_list then
        self.collect_card_pro_list = {}

        for i = 1, ACTIVITY_COLLECT_CARD_TYPE.MAX_PRO_REWARD_COUNT do
            local attr_obj = self.node_list.collect_card_pro_list:FindObj(string.format("card_pro_render_%d", i))
            if attr_obj then
                local cell = CardProRewardRender.New(attr_obj)
                cell:SetIndex(i)
                self.collect_card_pro_list[i] = cell
            end
        end
    end

    if not self.card_list_top then
        self.card_list_top = AsyncListView.New(CollectCardRender, self.node_list.card_list_top)
        self.card_list_top:SetSelectCallBack(BindTool.Bind(self.CollectCardSelectCallBack, self))
    end

	if not self.card_list_mid then
        self.card_list_mid = AsyncListView.New(CollectCardRender, self.node_list.card_list_mid)
        self.card_list_mid:SetSelectCallBack(BindTool.Bind(self.CollectCardSelectCallBack, self))
    end

	if not self.card_list_down then
        self.card_list_down = AsyncListView.New(CollectCardRender, self.node_list.card_list_down)
        self.card_list_down:SetSelectCallBack(BindTool.Bind(self.CollectCardSelectCallBack, self))
    end

	if not self.collect_card_reward_list then
		self.collect_card_reward_list = {}
		for i = 1, 3 do
            local attr_obj = self.node_list.collect_card_reward_list:FindObj(string.format("card_reward_render_%d", i))
            if attr_obj then
                local cell = CollectCardRewardRender.New(attr_obj)
				cell:SetClickCallBack(BindTool.Bind(self.OnClickCollectCardReward, self))
                cell:SetIndex(i)
                self.collect_card_reward_list[i] = cell
            end
        end
    end

	self:CollectCardChipItemChange()
	XUI.AddClickEventListener(self.node_list.collect_card_btn_get_card, BindTool.Bind(self.OnClickDrawCard, self, 1))
	XUI.AddClickEventListener(self.node_list.collect_card_btn_find_card, BindTool.Bind(self.OnClickGetFindCard, self))
	XUI.AddClickEventListener(self.node_list.collect_card_btn_get_record, BindTool.Bind(self.OnClickGetCardRecord, self))
end

-- 卡牌选中
function ServerActivityTabView:CollectCardSelectCallBack(item, cell_index, is_default, is_click)
	if is_default or (not item) or (not item.data) then
		return
	end

	local data = item.data
	-- 特殊需求<=1 索取， >=1 赠送
	ServerActivityWGCtrl.Instance:OpenCollectCardOperateView(data.seq)
end

-- 点击卡牌收集奖励
function ServerActivityTabView:OnClickCollectCardReward(cell)
	if (not cell) or (not cell.data) then
		return
	end

	local reward_get = ActivityCollectCardWGData.Instance:GetRewardFlagBySeq(cell.data.seq) ~= COLLECT_NUM_ZERO
	local type_num = ActivityCollectCardWGData.Instance:GetAllCollectTypeNumber(cell.data.card_type)
	local reward_data = cell.data.reward_item and cell.data.reward_item[0]
	local reward_item_id = reward_data and reward_data.item_id or 0

	if type_num < cell.data.card_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.AimConditionNoAchieved)
		if reward_item_id ~= 0 then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = reward_item_id })
		end
        return
	end

	if reward_get then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.TwLiBaoTip2)
		if reward_item_id ~= 0 then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = reward_item_id })
		end
        return
	end

	ServerActivityWGCtrl.Instance:RequestAOCollectCardFetchReward(cell.data.seq)
end

function ServerActivityTabView:CollectCardShowIndexCallBack()

end

function ServerActivityTabView:CollectCardOnFlush(param_t)
	self:CollectCardFlushView()
	self:CollectCardCountDownTime()
end

function ServerActivityTabView:CollectCardFlushView()
	local card_top_list = ActivityCollectCardWGData.Instance:GetCardListByType(COLLECT_CARD_TYPE.CARD_TYPE_TOP)
	local card_mid_list = ActivityCollectCardWGData.Instance:GetCardListByType(COLLECT_CARD_TYPE.CARD_TYPE_MID)
	local card_down_list = ActivityCollectCardWGData.Instance:GetCardListByType(COLLECT_CARD_TYPE.CARD_TYPE_DOWN)
	self.card_list_top:SetDataList(card_top_list)
	self.card_list_mid:SetDataList(card_mid_list)
	self.card_list_down:SetDataList(card_down_list)

	-- 刷新收集奖励
	local list = ActivityCollectCardWGData.Instance:GetCurGradeCollectCardRewardList()
	local server_index = COMMON_CONSTS.NUMBER_ZERO
	for _, card_rew_cell in ipairs(self.collect_card_reward_list) do
		card_rew_cell:SetVisible(list[server_index] ~= nil)

		if list[server_index] ~= nil then
			card_rew_cell:SetData(list[server_index])
		end

		server_index = server_index + 1
	end

	local pro_list = ActivityCollectCardWGData.Instance:GetCurGradeCollectCardProRewarList()
	local collect_card_pro_data = pro_list and pro_list[#pro_list]
	local all_aim_count = collect_card_pro_data and collect_card_pro_data.card_num or 1
	local now_num = ActivityCollectCardWGData.Instance:GetAllCollectNumber()
	local scroll_progress = (now_num - 1) / all_aim_count
	local is_find_first = false

	-- 算出可领取的实际位置
	local server_index = 0
	for index, card_pro_cell in ipairs(self.collect_card_pro_list) do
		card_pro_cell:SetVisible(index <= all_aim_count)
		local card_pro_data = pro_list and pro_list[server_index]

		if index <= all_aim_count then
			if card_pro_data ~= nil and card_pro_data.card_num == index then
				local is_can_get = ActivityCollectCardWGData.Instance:GetCollectRewardFlagBySeq(card_pro_data.seq) ~= 1
				local card_pro_num = card_pro_data.card_num or 1
				card_pro_cell:SetCellStatus(is_can_get, now_num >= card_pro_num)
	
				if is_can_get and now_num >= card_pro_num and (not is_find_first) then
					scroll_progress = (card_pro_num - 1) / all_aim_count
					is_find_first = true
				end
	
				card_pro_cell:SetData(card_pro_data)
				server_index = server_index + 1
			else
				card_pro_cell:SetData(nil)
			end
		end
	end

	local progress = now_num / all_aim_count
	self.node_list.collect_card_pro_txt.text.text = string.format("%d/%d", now_num, all_aim_count)
	self.node_list.collect_card_pro_slider.slider.value = progress
	-- self.node_list.collect_card_pro_scroll.scroll_rect.horizontalNormalizedPosition = scroll_progress		
	local draw_times, has_draw_times = ActivityCollectCardWGData.Instance:GetCollectCardDrawTime()
	self.node_list.collect_card_btn_get_card_red:CustomSetActive(draw_times > 0)
	self.node_list.collect_card_btn_get_effect:CustomSetActive(draw_times > 0)
	self.node_list.last_get_card_times.text.text = string.format(Language.OpenServer.SevenCollectCandraw, draw_times)
	self.node_list.collect_card_btn_find_card_red:CustomSetActive(ActivityCollectCardWGData.Instance:IsHaveCollectTaskReward())
end

function ServerActivityTabView:CollectCardCountDownTime()
	self:RemoveCollectCardCountDownTime()
	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_COLLECT_CARD)
	if count_down_time > 0 then
		self.node_list.collect_card_act_time.text.text = string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM6(count_down_time))
		CountDownManager.Instance:AddCountDown(
			"kaifujizi_countdown",
			BindTool.Bind(self.CollectCardUpdateCountDown, self),
			BindTool.Bind(self.CollectCardCountDownTime, self),
			nil,
			count_down_time,
			1
		)
	else
		self.node_list.collect_card_act_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
		self.node_list.collect_card_act_time.text.color = Str2C3b(COLOR3B.RED)
	end
end

function ServerActivityTabView:CollectCardUpdateCountDown(elapse_time, total_time)
	self.node_list.collect_card_act_time.text.text = string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM6(total_time - elapse_time))
end

function ServerActivityTabView:RemoveCollectCardCountDownTime()
	if CountDownManager.Instance:HasCountDown("act_collect_card_countdown") then
		CountDownManager.Instance:RemoveCountDown("act_collect_card_countdown")
	end
end

-- 碎片物品变化
function ServerActivityTabView:CollectCardChipItemChange()
	local base_cfg = ActivityCollectCardWGData.Instance:GetBaseCfg()
	local ship_item = base_cfg and base_cfg.ship_id or 0
	local item_num = ItemWGData.Instance:GetItemNumInBagById(ship_item)


	if self.now_collect_card_chip_count ~= nil and self.now_collect_card_chip_count > item_num then
		local consume_num = self.now_collect_card_chip_count - item_num
		local item_cfg = ItemWGData.Instance:GetItemConfig(ship_item)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.SysRemind.ConsumeItem, item_name, consume_num)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end

	self.now_collect_card_chip_count = item_num
	if self.money_bar then
		self.money_bar:CollectCardChipShow(item_num)
	end
end
-----------------------------------------------------------------------------------
-- 操作抽卡
function ServerActivityTabView:OnClickDrawCard(mode)
	local draw_times, has_draw_times = ActivityCollectCardWGData.Instance:GetCollectCardDrawTime()
	local mode_cfg = ActivityCollectCardWGData.Instance:GetModeCfgByMode(mode)
	local cost_times = mode_cfg and mode_cfg.cost_times or 1

	if draw_times < cost_times then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.SevenCollectDrawError)
        return
	end

	ServerActivityWGCtrl.Instance:RequestAOCollectCardDraw(mode)
end

-- 获取卡包
function ServerActivityTabView:OnClickGetFindCard()
	ServerActivityWGCtrl.Instance:OpenCollectCardTaskView()
end

-- 抽卡记录
function ServerActivityTabView:OnClickGetCardRecord()
	local record_list = ActivityCollectCardWGData.Instance:GetCurGradeCollectCardRecordList()
	TipWGCtrl.Instance:OpenTipsRewardRecordView(record_list, Language.OpenServer.CollectCandRecordTitle)
end
------------------------------------------- CardProRewardRender ------------------------------------------------------
CardProRewardRender = CardProRewardRender or BaseClass(BaseRender)
function CardProRewardRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.get_card_pro_btn, BindTool.Bind(self.OnClickCardProGetReward, self))
end

function CardProRewardRender:ReleaseCallBack()
	self.is_can_get = nil
	self.is_complate = nil
end

function CardProRewardRender:SetCellStatus(is_can_get, is_complate)
	self.is_can_get = is_can_get
	self.is_complate = is_complate
end

-- 种类个数选中点击
function CardProRewardRender:OnClickCardProGetReward()
	if not self.data then
		return
	end
	local is_get_ed = ActivityCollectCardWGData.Instance:GetCollectRewardFlagBySeq(self.data.seq) ~= COLLECT_NUM_ZERO
	local reward_data = self.data.reward_item and self.data.reward_item[0]
	local reward_item_id = reward_data and reward_data.item_id or 0

	if not self.is_complate then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.AimConditionNoAchieved)
		if reward_item_id ~= 0 then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = reward_item_id })
		end
        return
	end

	if is_get_ed then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.TwLiBaoTip2)
		if reward_item_id ~= 0 then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = reward_item_id })
		end
        return
	end

	ServerActivityWGCtrl.Instance:RequestAOCollectCardFetchCollectReward(self.data.seq)
end

function CardProRewardRender:OnFlush()
	self.node_list.has_dara:CustomSetActive(self.data ~= nil)
	self.node_list.no_dara:CustomSetActive(self.data == nil)

	if not self.data then
		return
	end

	local is_get_ed = ActivityCollectCardWGData.Instance:GetCollectRewardFlagBySeq(self.data.seq) ~= COLLECT_NUM_ZERO
	local reward_data = self.data.reward_item and self.data.reward_item[0]
	if reward_data ~= nil then
		local icon_id = ItemWGData.Instance:GetItemIconByItemId(reward_data.item_id)
		local _, color = ItemWGData.Instance:GetItemColor(reward_data.item_id)
		self.node_list.render_icon.image:LoadSprite(ResPath.GetItem(icon_id))
		self.node_list.render_num.text.text = reward_data.num or 1
		-- self.node_list.get_card_pro_btn.image:LoadSprite(ResPath.GetCommonImages(string.format("a3_ty_wpk_big_%d", color)))
	end

	self.node_list.geted:CustomSetActive(is_get_ed)
	self.node_list.geted_mask:CustomSetActive(is_get_ed)
	self.node_list.card_pro_red:CustomSetActive((not is_get_ed) and self.is_complate)
	self.node_list.effect_root:CustomSetActive((not is_get_ed) and self.is_complate)
	self.node_list.need_aim_txt.text.text = string.format("%d%s", self.data.card_num, Language.Common.Zhong) 
end

------------------------------------------- CollectCardRender ------------------------------------------------------
CollectCardRender = CollectCardRender or BaseClass(BaseRender)
function CollectCardRender:LoadCallBack()

end

function CollectCardRender:ReleaseCallBack()

end

function CollectCardRender:OnFlush()
	if not self.data then
		return
	end

	local card_num = ActivityCollectCardWGData.Instance:GetCollectCardInfoBySeq(self.data.seq)
	local is_act = card_num > COLLECT_NUM_ZERO

	self.node_list.normal.image:LoadSprite(ResPath.GetKaiFuHuuoDongPanel(string.format("a3_kfkh_mj_%d", self.data.seq)))
	self.node_list.not_active:CustomSetActive(not is_act)
	self.node_list.active:CustomSetActive(is_act)

	self.node_list.remind:CustomSetActive(ActivityCollectCardWGData.Instance:GetCurGradeCollectCardRequsetRedBySeq(self.data.seq) and card_num > COLLECT_NUM_ONE)

	if is_act then
		self.node_list.has_num_txt.text.text = string.format("x%d", card_num)
	end
end

------------------------------------------- CollectCardRewardRender ------------------------------------------------------
CollectCardRewardRender = CollectCardRewardRender or BaseClass(BaseRender)
function CollectCardRewardRender:OnFlush()
	if not self.data then
		return
	end

	local reward_get = ActivityCollectCardWGData.Instance:GetRewardFlagBySeq(self.data.seq) ~= COLLECT_NUM_ZERO
	local reward_data = self.data.reward_item and self.data.reward_item[0]
	local type_num = ActivityCollectCardWGData.Instance:GetAllCollectTypeNumber(self.data.card_type)
	self.node_list.remind:CustomSetActive((not reward_get) and type_num >= self.data.card_num)
	self.node_list.effect_root:CustomSetActive((not reward_get) and type_num >= self.data.card_num)
	self.node_list.geted:CustomSetActive(reward_get)
	-- if reward_data then
	-- 	local icon_id = ItemWGData.Instance:GetItemIconByItemId(reward_data.item_id)
	-- 	self.node_list.icon.image:LoadSprite(ResPath.GetItem(icon_id))
	-- end
	self.node_list.aim_txt.text.text = self.data.reward_get_str
end
-- G-功能开启.xls

return {
funopen_list={
["shen_equip"]={name="shen_equip",show_name="神装",funopen_seq=0,trigger_param=210,},
["husong_task"]={name="husong_task",show_name="护送",funopen_seq=1,trigger_param=70,open_type=1,task_level=160,},
["HmGodView"]={name="HmGodView",show_name="鸿蒙神藏",funopen_seq=2,task_level=1,},
["ExpPoolView"]={name="ExpPoolView",show_name="经验池",funopen_seq=3,task_level=100,},
["rechargeui"]={name="rechargeui",show_name="每日累充",funopen_seq=4,trigger_param=30,open_type=5,task_level=30,},
["EveryDayRechargeView"]={name="EveryDayRechargeView",show_name="每日累充",funopen_seq=5,trigger_param2=9999,open_param="EveryDayRechargeView",open_type=1,},
["everyday_recharge_leichong"]={name="everyday_recharge_leichong",show_name="每日累充",funopen_seq=6,open_param="everyday_recharge_leichong#EveryDayRechargeView",},
["everyday_recharge_lianchong"]={name="everyday_recharge_lianchong",show_name="每日连充",funopen_seq=7,open_param="everyday_recharge_lianchong#EveryDayRechargeView",},
["everyday_recharge_xiaofei"]={name="everyday_recharge_xiaofei",show_name="每日消费",funopen_seq=8,open_param="everyday_recharge_xiaofei#EveryDayRechargeView",},
["everyday_recharge_zhigou"]={name="everyday_recharge_zhigou",show_name="直购活动",funopen_seq=9,open_param="everyday_recharge_zhigou#EveryDayRechargeView",},
["everyday_recharge_dailygift"]={name="everyday_recharge_dailygift",show_name="每日礼包",funopen_seq=10,open_param="everyday_recharge_dailygift#EveryDayRechargeView",},
["everyday_recharge_rapidly"]={name="everyday_recharge_rapidly",show_name="秒杀礼包",funopen_seq=11,open_param="everyday_recharge_rapidly#EveryDayRechargeView",},
["TreasurePalace"]={name="TreasurePalace",show_name="臻宝殿",funopen_seq=12,open_param="TreasurePalaceView",},
["first_recharge"]={name="first_recharge",show_name="首充入口",funopen_seq=13,trigger_param=20,open_param="first_recharge",open_type=1,task_level=10,},
["first_recharge_shouchong"]={name="first_recharge_shouchong",show_name="首充页签",funopen_seq=14,trigger_param=20,open_param="first_recharge_shouchong#first_recharge",task_level=10,},
["first_recharge_zhigou"]={name="first_recharge_zhigou",show_name="直购页签",funopen_seq=15,open_param="first_recharge_zhigou#first_recharge",},
["recharge_sevenday"]={name="recharge_sevenday",show_name="七天累充",funopen_seq=16,trigger_param=40,open_param="recharge_sevenday#recharge",},
["recharge"]={name="recharge",show_name="充值面板",funopen_seq=17,open_param="recharge",},
["recharge_cz"]={name="recharge_cz",show_name="充值",funopen_seq=18,trigger_param=1,open_param="recharge_cz#recharge",task_level=50,},
["recharge_vip"]={name="recharge_vip",show_name="vip",funopen_seq=19,trigger_param=50,open_param="recharge_vip#recharge",task_level=50,},
["recharge_king_vip"]={name="recharge_king_vip",show_name="王者特权",funopen_seq=20,open_param="recharge_king_vip#recharge",},
["recharge_tqtz"]={name="recharge_tqtz",show_name="特权回报",funopen_seq=21,open_param="recharge_tqtz#recharge",},
["recharge_month_card"]={name="recharge_month_card",show_name="月卡回报",funopen_seq=22,trigger_param=50,open_param="recharge_month_card#recharge",task_level=120,},
["recharge_week_card"]={name="recharge_week_card",show_name="周卡回报",funopen_seq=23,trigger_type=16,trigger_param2=999,open_param="recharge_week_card#recharge",task_level=999,},
["recharge_card"]={name="recharge_card",show_name="0元贵族",funopen_seq=24,open_param="recharge_card#recharge",},
["GoldStoneView"]={name="GoldStoneView",show_name="星陨怒兵直购",funopen_seq=25,open_param="GoldStoneView",open_type=1,},
["TianxianPavilionView"]={name="TianxianPavilionView",show_name="天仙宝阁",funopen_seq=26,open_param="TianxianPavilionView",},
["JingHuaShuiYue"]={name="JingHuaShuiYue",show_name="镜花水月",funopen_seq=27,zjm_fp_btn_hide=1,trigger_type=16,trigger_param2=300,open_param="JingHuaShuiYueView",open_type=1,},
["recharge_reserve_card"]={name="recharge_reserve_card",show_name="攒福特权",funopen_seq=28,trigger_type=16,trigger_param2=300,open_param="recharge_reserve_card#recharge",task_level=300,},
["YinianMagicView"]={name="YinianMagicView",show_name="日月相辉",funopen_seq=29,open_param="YinianMagicView",},
["notice_three_firstcharge"]={name="notice_three_firstcharge",show_name="首充tips",funopen_seq=30,trigger_type=2,trigger_param=820,open_param="1#notice_three#3",open_type=5,},
["open_server_activity"]={name="open_server_activity",funopen_seq=31,open_param="open_server_activity",},
["open_server_recharge"]={name="open_server_recharge",show_name="开服累充",funopen_seq=32,open_param="open_server_recharge",},
["fl_activity"]={name="fl_activity",show_name="开服活动",funopen_seq=33,},
["open_server_compete"]={name="open_server_compete",show_name="开服比拼",funopen_seq=34,trigger_param=40,open_type=1,task_level=40,},
["worlds_no1_view"]={name="worlds_no1_view",show_name="天下第一",funopen_seq=35,open_param="worlds_no1_view",},
["XianyuTrunTableView"]={name="XianyuTrunTableView",show_name="灵玉转盘",funopen_seq=36,open_param="XianyuTrunTableView",},
["recharge_week_buy"]={name="recharge_week_buy",show_name="每周必买",funopen_seq=37,open_param="recharge_week_buy#recharge",},
["auto_on_mount"]={name="auto_on_mount",show_name="自动上坐骑",funopen_seq=38,trigger_type=2,trigger_param=90,open_type=1,task_level=23,},
["hide_task"]={name="hide_task",show_name="隐藏任务",funopen_seq=39,trigger_param=1,},
["daily_task"]={name="daily_task",show_name="悬赏任务",funopen_seq=40,trigger_type=1,trigger_param=921,task_level=90,},
["TaskShangJinView"]={name="TaskShangJinView",show_name="赏金任务",funopen_seq=41,trigger_type=2,trigger_param=850,task_level=70,},
["XiuXianShiLian"]={name="XiuXianShiLian",show_name="修仙试炼",funopen_seq=42,open_type=1,},
["NewTeamView"]={name="NewTeamView",show_name="组队",funopen_seq=43,},
["crossTeamView"]={name="crossTeamView",show_name="跨服组队",funopen_seq=44,trigger_param=80,open_type=1,task_level=80,},
["role_sit"]={name="role_sit",show_name="打坐",funopen_seq=45,task_level=150,},
["ScreenShotView"]={name="ScreenShotView",show_name="风景拍照入口",funopen_seq=46,},
["achievement"]={name="achievement",show_name="成就",funopen_seq=47,trigger_param=1,open_param="achievement",open_type=4,task_level=95,},
["CapabilityContrastView "]={name="CapabilityContrastView ",show_name="战力对比",funopen_seq=48,trigger_param=90,open_param="CapabilityContrastView",open_type=1,task_level=90,},
["change_head_view"]={name="change_head_view",show_name="头像自定义",funopen_seq=49,open_param="change_head_view",},
["feed_back_view"]={name="feed_back_view",show_name="玩家反馈",funopen_seq=50,trigger_param=60,open_param="FeedBackView",open_type=1,task_level=60,},
["shileding"]={name="shileding",show_name="屏蔽玩家",funopen_seq=51,},
["near_player"]={name="near_player",show_name="附近玩家",funopen_seq=52,},
["teamicon"]={name="teamicon",show_name="组队按钮",funopen_seq=53,},
["activity_109"]={name="activity_109",show_name="挂机打怪",funopen_seq=54,trigger_param=9,},
["JumpOpen"]={name="JumpOpen",show_name="轻功",funopen_seq=55,open_type=1,},
["role"]={name="role",show_name="角色",funopen_seq=56,open_param="role_view",},
["role_view"]={name="role_view",funopen_seq=57,},
["role_intro"]={name="role_intro",show_name="人物",funopen_seq=58,open_param="role_intro#role_view",},
["role_atr"]={name="role_atr",funopen_seq=59,open_param="role_atr#role_view",},
["role_beidong_skill"]={name="role_beidong_skill",show_name="被动技能",funopen_seq=60,open_param="role_beidong_skill#role_view",},
["custom_action"]={name="custom_action",show_name="角色动作",funopen_seq=61,open_param="custom_action#RoleBranchView",},
["CustomActionView"]={name="CustomActionView",show_name="角色动作弹窗",funopen_seq=62,open_param="CustomActionView",},
["sevenday"]={name="sevenday",show_name="7天登录",funopen_seq=63,open_param="sevenday",},
["AccumulativeLoginView"]={name="AccumulativeLoginView",show_name="累计登录",funopen_seq=64,trigger_param=100,open_param="AccumulativeLoginView",open_type=4,task_level=100,},
["GodOfWealthView"]={name="GodOfWealthView",show_name="喜迎财神",funopen_seq=65,open_param="GodOfWealthView",open_type=1,},
["god_of_wealth"]={name="god_of_wealth",show_name="多倍返玉",funopen_seq=66,trigger_type=16,trigger_param=2,trigger_param2=100,open_param="god_of_wealth#GodOfWealthView",task_level=100,},
["god_of_wealth_lucky_star"]={name="god_of_wealth_lucky_star",show_name="奇珍异宝",funopen_seq=67,trigger_type=16,trigger_param2=9999,open_param="god_of_wealth_lucky_star#GodOfWealthView",task_level=100,},
["role_change"]={name="role_change",show_name="转职业",funopen_seq=68,trigger_param=40,open_param="role_change#role_view",task_level=40,},
["CashPointView"]={name="CashPointView",show_name="现金点",funopen_seq=69,open_param="CashPointView",},
["recharge_volume"]={name="recharge_volume",show_name="充值券",funopen_seq=70,trigger_type=1,trigger_param=990,open_param="RechargeVolumeView",open_type=1,},
["welfare"]={name="welfare",show_name="福利",funopen_seq=71,trigger_param=43,open_param="welfare",open_type=1,task_level=43,},
["welfare_qiandao"]={name="welfare_qiandao",show_name="签到",funopen_seq=72,trigger_param=43,open_param="welfare_qiandao#welfare",task_level=43,},
["welfare_wekqiandao"]={name="welfare_wekqiandao",show_name="周签到",funopen_seq=73,open_param="welfare_wekqiandao#welfare",},
["welfare_upgrade"]={name="welfare_upgrade",show_name="等级礼包",funopen_seq=74,open_param="welfare_upgrade#welfare",},
["welfare_vipgift"]={name="welfare_vipgift",show_name="vip礼包",funopen_seq=75,open_param="welfare_vipgift#welfare",},
["welfare_libao"]={name="welfare_libao",show_name="兑换码",funopen_seq=76,open_param="welfare_libao#welfare",},
["wenxin_remind"]={name="wenxin_remind",show_name="温馨提示",funopen_seq=77,trigger_param=1,open_param="wenxin_remind#welfare",task_level=1,},
["WardrobeView"]={name="WardrobeView",show_name="衣橱",funopen_seq=78,trigger_param=19,},
["HuanHuaFetterView"]={name="HuanHuaFetterView",show_name="七星化形",funopen_seq=79,trigger_type=16,open_type=1,task_level=99999,},
["new_appearance_zhuangban_bubble"]={name="new_appearance_zhuangban_bubble",show_name="气泡外观",funopen_seq=80,open_param="new_appearance_zhuangban_bubble#NewAppearanceWGView",},
["new_appearance_zhuangban_photoframe"]={name="new_appearance_zhuangban_photoframe",show_name="头像框",funopen_seq=81,open_param="new_appearance_zhuangban_photoframe#NewAppearanceWGView",},
["NewAppearanceWGView"]={name="NewAppearanceWGView",show_name="时装",funopen_seq=82,open_param="NewAppearanceWGView",open_type=1,},
["new_appearance_waiguan_weapon"]={name="new_appearance_waiguan_weapon",show_name="武器",funopen_seq=83,zjm_fp_btn_name="BtnFPFashion",open_param="new_appearance_waiguan_weapon#NewAppearanceWGView",fly_pos="0|-5|0",},
["new_appearance_waiguan_body"]={name="new_appearance_waiguan_body",show_name="外观",funopen_seq=84,zjm_fp_btn_name="BtnFPFashion",trigger_type=2,trigger_param=90,open_param="new_appearance_waiguan_body#NewAppearanceWGView",task_level=19,},
["new_appearance_zhuangban_weiba"]={name="new_appearance_zhuangban_weiba",show_name="尾巴",funopen_seq=85,open_param="new_appearance_zhuangban_weiba#NewAppearanceWGView",},
["new_appearance_zhuangban_mask"]={name="new_appearance_zhuangban_mask",show_name="面具",funopen_seq=86,open_param="new_appearance_zhuangban_mask#NewAppearanceWGView",},
["new_appearance_zhuangban_shouhuan"]={name="new_appearance_zhuangban_shouhuan",show_name="手环",funopen_seq=87,open_param="new_appearance_zhuangban_shouhuan#NewAppearanceWGView",},
["new_appearance_zhuangban_belt"]={name="new_appearance_zhuangban_belt",show_name="腰饰",funopen_seq=88,zjm_fp_btn_name="BtnFPFashion",trigger_param=110,open_param="new_appearance_zhuangban_belt#NewAppearanceWGView",task_level=110,},
["new_appearance_waiguan_halo"]={name="new_appearance_waiguan_halo",show_name="魂息",funopen_seq=89,zjm_fp_btn_name="BtnFPFashion",trigger_param=19,open_param="new_appearance_waiguan_halo#NewAppearanceWGView",task_level=19,},
["new_appearance_zhuangban_foot"]={name="new_appearance_zhuangban_foot",show_name="足迹",funopen_seq=90,open_param="new_appearance_zhuangban_foot#NewAppearanceWGView",},
["sxbs_xianyi"]={name="sxbs_xianyi",show_name="仙翼属性宝石",funopen_seq=91,},
["sxbs_fabao"]={name="sxbs_fabao",show_name="法宝属性宝石",funopen_seq=92,},
["sxbs_shenwu"]={name="sxbs_shenwu",show_name="神武属性宝石",funopen_seq=93,trigger_type=16,trigger_param=3,trigger_param2=250,open_type=1,task_level=250,},
["sxbs_beishi"]={name="sxbs_beishi",show_name="背饰属性宝石",funopen_seq=94,trigger_type=16,trigger_param=2,trigger_param2=9999,open_type=1,},
["sxbs_pet"]={name="sxbs_pet",show_name="灵宠属性宝石",funopen_seq=95,trigger_type=16,trigger_param=1,trigger_param2=52,open_type=1,task_level=250,},
["sxbs_mount"]={name="sxbs_mount",show_name="坐骑属性宝石",funopen_seq=96,trigger_param2=130,},
["new_appearance_mount_upstar"]={name="new_appearance_mount_upstar",show_name="幻化坐骑升星",funopen_seq=97,zjm_fp_btn_name="BtnFPFashion",trigger_type=2,trigger_param=90,open_param="new_appearance_mount_upstar#NewAppearanceWGView",task_level=23,},
["new_appearance_mount_upgrade"]={name="new_appearance_mount_upgrade",show_name="幻化坐骑进阶",funopen_seq=98,open_param="new_appearance_mount_upgrade#NewAppearanceWGView",},
["new_appearance_lingchong_upstar"]={name="new_appearance_lingchong_upstar",show_name="幻化宠物升星",funopen_seq=99,trigger_type=1,trigger_param=820,open_param="new_appearance_lingchong_upstar#NewAppearanceWGView",open_type=4,task_level=160,},
["new_appearance_lingchong_upgrade"]={name="new_appearance_lingchong_upgrade",show_name="幻化宠物进阶",funopen_seq=100,open_param="new_appearance_lingchong_upgrade#NewAppearanceWGView",},
["new_appearance_upgrade_wing"]={name="new_appearance_upgrade_wing",show_name="进阶羽翼",funopen_seq=101,with_param=1,open_param="new_appearance_upgrade_wing#NewAppearanceWGView",},
["new_appearance_upgrade_fabao"]={name="new_appearance_upgrade_fabao",show_name="进阶法宝",funopen_seq=102,with_param=1,open_param="new_appearance_upgrade_fabao#NewAppearanceWGView",},
["new_appearance_upgrade_shenbing"]={name="new_appearance_upgrade_shenbing",show_name="进阶神兵",funopen_seq=103,zjm_fp_btn_name="BtnFPFashion",trigger_param2=250,open_param="new_appearance_upgrade_shenbing#NewAppearanceWGView",task_level=160,},
["new_appearance_upgrade_jianzhen"]={name="new_appearance_upgrade_jianzhen",show_name="进阶剑阵（剑灵/背饰）",funopen_seq=104,trigger_type=16,trigger_param=3,trigger_param2=9999,open_param="new_appearance_upgrade_jianzhen#NewAppearanceWGView",open_type=4,},
["new_appearance_waiguan_wing"]={name="new_appearance_waiguan_wing",show_name="羽翼幻化",funopen_seq=105,zjm_fp_btn_name="BtnFPFashion",trigger_type=2,trigger_param=1390,open_param="new_appearance_waiguan_wing#NewAppearanceWGView",task_level=120,},
["new_appearance_waiguan_fabao"]={name="new_appearance_waiguan_fabao",show_name="神器幻化",funopen_seq=106,zjm_fp_btn_name="BtnFPFashion",trigger_type=2,trigger_param=3040,open_param="new_appearance_waiguan_fabao#NewAppearanceWGView",task_level=110,},
["new_appearance_waiguan_shenbing"]={name="new_appearance_waiguan_shenbing",show_name="神兵幻化",funopen_seq=107,zjm_fp_btn_name="BtnFPFashion",trigger_type=16,trigger_param=6,trigger_param2=9999,open_param="new_appearance_waiguan_shenbing#NewAppearanceWGView",},
["new_appearance_waiguan_jianzhen"]={name="new_appearance_waiguan_jianzhen",show_name="剑灵幻化",funopen_seq=108,zjm_fp_btn_name="BtnFPFashion",trigger_type=16,trigger_param=3,trigger_param2=9999,open_param="new_appearance_waiguan_jianzhen#NewAppearanceWGView",},
["new_appearance_upgrade_mount"]={name="new_appearance_upgrade_mount",show_name="进阶坐骑",funopen_seq=109,with_param=1,open_param="new_appearance_upgrade_mount#NewAppearanceWGView",},
["MountEquipView"]={name="MountEquipView",show_name="坐骑图腾",funopen_seq=110,trigger_param=6,open_param="MountEquipView",},
["new_appearance_upgrade_lingchong"]={name="new_appearance_upgrade_lingchong",show_name="进阶灵剑",funopen_seq=111,trigger_type=2,trigger_param=810,with_param=1,open_param="new_appearance_upgrade_lingchong#NewAppearanceWGView",task_level=23,},
["LingChongEquipView"]={name="LingChongEquipView",show_name="灵宠图腾",funopen_seq=112,open_param="LingChongEquipView",},
["new_appearance_kun_upstar"]={name="new_appearance_kun_upstar",show_name="化鲲升星",funopen_seq=113,open_param="new_appearance_kun_upstar#NewAppearanceWGView",},
["new_appearance_kun_upgrade"]={name="new_appearance_kun_upgrade",show_name="化鲲升阶",trigger_type=16,trigger_param2=250,open_param="new_appearance_kun_upgrade#NewAppearanceWGView",open_type=4,task_level=170,},
["new_appearance_multi_mount"]={name="new_appearance_multi_mount",show_name="双人坐骑",trigger_type=16,trigger_param2=9999,open_param="new_appearance_multi_mount#NewAppearanceWGView",open_type=4,},
["HuaKunEquipView"]={name="HuaKunEquipView",show_name="化鲲图腾",funopen_seq=115,trigger_param=390,open_param="HuaKunEquipView",open_type=4,task_level=250,},
["mount_lingchong_duhua"]={name="mount_lingchong_duhua",show_name="渡化",funopen_seq=116,trigger_param=290,open_param="mount_lingchong_duhua#mount_lingchong",task_level=290,},
["mount_equip_bag"]={name="mount_equip_bag",show_name="坐骑图腾背包",funopen_seq=117,trigger_type=16,trigger_param=6,trigger_param2=250,open_param="mount_equip_bag#MountEquipView",task_level=350,},
["mount_equip_shengpin"]={name="mount_equip_shengpin",show_name="坐骑图腾升品",funopen_seq=118,open_param="mount_equip_shengpin#MountEquipView",},
["mount_equip_star"]={name="mount_equip_star",show_name="坐骑图腾升星",funopen_seq=119,open_param="mount_equip_star#MountEquipView",},
["mount_equip_strength"]={name="mount_equip_strength",show_name="坐骑图腾强化",funopen_seq=120,open_param="mount_equip_strength#MountEquipView",},
["mount_equip_suit"]={name="mount_equip_suit",show_name="坐骑图腾套装",funopen_seq=121,open_param="mount_equip_suit#MountEquipView",},
["pet_equip_bag"]={name="pet_equip_bag",show_name="灵宠图腾背包",funopen_seq=122,trigger_type=16,trigger_param=5,trigger_param2=250,open_param="pet_equip_bag#LingChongEquipView",task_level=300,},
["pet_equip_shengpin"]={name="pet_equip_shengpin",show_name="灵宠图腾升品",funopen_seq=123,open_param="pet_equip_shengpin#LingChongEquipView",},
["pet_equip_star"]={name="pet_equip_star",show_name="灵宠图腾升星",funopen_seq=124,open_param="pet_equip_star#LingChongEquipView",},
["pet_equip_strength"]={name="pet_equip_strength",show_name="灵宠图腾强化",funopen_seq=125,open_param="pet_equip_strength#LingChongEquipView",},
["pet_equip_suit"]={name="pet_equip_suit",show_name="灵宠图腾套装",funopen_seq=126,open_param="pet_equip_suit#LingChongEquipView",},
["huakun_equip_bag"]={name="huakun_equip_bag",show_name="化鲲图腾背包",funopen_seq=127,open_param="huakun_equip_bag#HuaKunEquipView",},
["huakun_equip_shengpin"]={name="huakun_equip_shengpin",show_name="化鲲图腾升品",funopen_seq=128,open_param="huakun_equip_shengpin#HuaKunEquipView",},
["huakun_equip_star"]={name="huakun_equip_star",show_name="化鲲图腾升星",funopen_seq=129,trigger_param=390,open_param="huakun_equip_star#HuaKunEquipView",task_level=390,},
["huakun_equip_strength"]={name="huakun_equip_strength",show_name="化鲲图腾强化",funopen_seq=130,open_param="huakun_equip_strength#HuaKunEquipView",},
["huakun_equip_suit"]={name="huakun_equip_suit",show_name="化鲲图腾套装",funopen_seq=131,open_param="huakun_equip_suit#HuaKunEquipView",},
["WingLinZhiSkillView"]={name="WingLinZhiSkillView",show_name="羽翼器魂",funopen_seq=132,open_param="WingLinZhiSkillView",},
["rolebag_bag_all"]={name="rolebag_bag_all",show_name="背包-背包",funopen_seq=133,open_param="rolebag_bag_all#bag_view",},
["rolebag_stuff"]={name="rolebag_stuff",show_name="背包-材料",funopen_seq=134,open_param="rolebag_stuff#bag_view",},
["rolebag_storge"]={name="rolebag_storge",show_name="背包-仓库",funopen_seq=135,open_param="rolebag_storge#bag_view",},
["rolebag_equip_transsex"]={name="rolebag_equip_transsex",show_name="背包-转性别",funopen_seq=136,open_param="rolebag_equip_transsex#bag_view",},
["rolebag_view_metling_view"]={name="rolebag_view_metling_view",show_name="背包-熔炉",funopen_seq=137,trigger_param=105,open_param="rolebag_view_metling_view#bag_view",open_type=1,task_level=105,},
["rolebag_longzhu"]={name="rolebag_longzhu",show_name="星陨怒兵",funopen_seq=138,open_param="rolebag_longzhu#bag_view",open_type=4,},
["bizuo"]={name="bizuo",show_name="日常",funopen_seq=139,trigger_type=2,trigger_param=940,open_param="bizuo",open_type=4,task_level=110,},
["welfare_dailyfind"]={name="welfare_dailyfind",show_name="找回",funopen_seq=140,open_param="welfare_dailyfind#bizuo",},
["bizuo_act_hall"]={name="bizuo_act_hall",show_name="活动大厅",funopen_seq=141,open_param="bizuo_act_hall#bizuo",},
["bizuo_time_table"]={name="bizuo_time_table",show_name="活动周历",funopen_seq=142,trigger_type=2,trigger_param=870,open_param="bizuo_time_table#bizuo",task_level=50,},
["get_stronger"]={name="get_stronger",show_name="我要变强",funopen_seq=143,open_param="get_stronger#bizuo",},
["act_jjc"]={name="act_jjc",show_name="竞技",funopen_seq=144,trigger_param=1340,open_param="act_jjc",task_level=120,fly_pos="0|8|0",},
["arena_field1v1"]={name="arena_field1v1",show_name="竞技场",funopen_seq=145,open_param="arena_field1v1#act_jjc",},
["arena_kf1v1"]={name="arena_kf1v1",funopen_seq=146,open_param="arena_kf1v1#act_jjc",open_type=1,},
["arena_1v1jfreward"]={name="arena_1v1jfreward",funopen_seq=147,open_param="arena_1v1jfreward#act_jjc",},
["arena_1v1paragraph"]={name="arena_1v1paragraph",funopen_seq=148,trigger_param=160,open_param="arena_1v1paragraph#act_jjc",task_level=160,},
["arena_1v1rank"]={name="arena_1v1rank",funopen_seq=149,open_param="arena_1v1rank#act_jjc",},
["tianshen_3v3"]={name="tianshen_3v3",show_name="诸神3v3",funopen_seq=150,open_param="tianshen_3v3#act_jjc",},
["boss"]={name="boss",show_name="魔王",funopen_seq=151,trigger_type=1,trigger_param=990,open_param="boss",open_type=1,task_level=90,},
["boss_personal"]={name="boss_personal",show_name="灵妖奇脉",funopen_seq=152,icon="a3_open_mw",trigger_type=1,trigger_param=2170,open_param="boss_personal#boss",task_level=138,},
["worldboss"]={name="worldboss",show_name="伏魔战场",funopen_seq=153,zjm_zp_btn_name="BtnWorldBossView",icon="a3_open_mw",trigger_param2=220,open_param="boss_world#boss",task_level=190,},
["boss_vip"]={name="boss_vip",show_name="仙遗洞天",funopen_seq=154,trigger_type=1,trigger_param=990,open_param="boss_vip#boss",open_type=4,task_level=90,},
["boss_dabao"]={name="boss_dabao",show_name="赤霄魔域",funopen_seq=155,zjm_zp_btn_name="BtnWorldBossView",icon="a3_open_sjf",trigger_param=999,open_param="boss_dabao#boss",task_level=999,},
["worldserver"]={name="worldserver",show_name="世界服",funopen_seq=156,open_param="WorldServer",open_type=1,},
["worerv_boss_mh"]={name="worerv_boss_mh",show_name="荒天炎窟",funopen_seq=157,trigger_param=5,trigger_param2=320,open_param="worserv_boss_mh#WorldServer",task_level=320,},
["world_new_shenyuan_boss"]={name="world_new_shenyuan_boss",show_name="谪仙之境",funopen_seq=158,trigger_param=1,trigger_param2=250,open_param="world_new_shenyuan_boss#WorldServer",},
["xianjie_boss"]={name="xianjie_boss",show_name="异域魂界",funopen_seq=159,zjm_zp_btn_name="BtnWorldServerView",icon="a3_open_sjf",trigger_param=10,trigger_param2=300,open_param="xianjie_boss#WorldServer",},
["worserv_everyday_recharge_boss"]={name="worserv_everyday_recharge_boss",show_name="如梦幻影",funopen_seq=160,zjm_fp_btn_name="BtnWorldServerView",icon="a3_open_mc",open_param="worserv_everyday_recharge_boss#WorldServer",open_type=4,},
["BossMustFallPrivilegeView"]={name="BossMustFallPrivilegeView",show_name="再爆一次",funopen_seq=161,icon="a3_open_mc",open_param="BossMustFallPrivilegeView",open_type=4,},
["ConquestWarView"]={name="ConquestWarView",show_name="征战",funopen_seq=162,trigger_param=1,open_param="ConquestWarView",open_type=8,task_level=1,},
["country_map_flag_grabbing_battlefield"]={name="country_map_flag_grabbing_battlefield",show_name="冠绝征战",funopen_seq=163,trigger_type=16,trigger_param2=120,open_param="country_map_flag_grabbing_battlefield#ConquestWarView",open_type=4,task_level=300,},
["counrty_map"]={name="counrty_map",show_name="夺仙宝图",funopen_seq=164,trigger_param=180,open_param="counrty_map_map_view",open_type=4,task_level=180,},
["counrty_map_map_view"]={name="counrty_map_map_view",show_name="仙图",funopen_seq=165,open_param="counrty_map_map_view",task_level=180,},
["counrty_map_news_view"]={name="counrty_map_news_view",show_name="仙位",funopen_seq=166,open_param="counrty_map_news_view",},
["counrty_map_officer_view"]={name="counrty_map_officer_view",show_name="总览",funopen_seq=167,open_param="counrty_map_officer_view",},
["counrty_map_task_view"]={name="counrty_map_task_view",show_name="任务",funopen_seq=168,open_param="counrty_map_task_view",},
["country_map_act_view"]={name="country_map_act_view",show_name="狩猎仙图",funopen_seq=169,open_param="country_map_act_view",},
["country_map_act_farm"]={name="country_map_act_farm",show_name="仙图狩猎",funopen_seq=170,open_param=" NewFarmView",},
["country_map_secret_area"]={name="country_map_secret_area",show_name="仙图秘境",funopen_seq=171,open_param="country_map_secret_area#country_map_act_view",},
["country_map_act_boss"]={name="country_map_act_boss",show_name="国家首领",funopen_seq=172,trigger_type=16,trigger_param2=200,open_param="NewCountryBossView",},
["cross_spy"]={name="cross_spy",show_name="仙境刺探",funopen_seq=173,zjm_zp_btn_name="BtnCountryMap",zjm_fp_btn_name="BtnFPCountryMap",open_param="NewCountryMapCrossSpyView",},
["country_map_act_ship"]={name="country_map_act_ship",show_name="登神长阶",funopen_seq=174,zjm_zp_btn_name="BtnCountryMap",zjm_fp_btn_name="BtnFPCountryMap",icon="a3_open_zj",open_param="NewShipView",open_type=4,},
["country_map_act_cross_spy"]={name="country_map_act_cross_spy",funopen_seq=175,open_param="country_map_act_cross_spy#country_map_act_view",},
["CrossLongMaiView"]={name="CrossLongMaiView",show_name="仙城龙脉",funopen_seq=176,zjm_zp_btn_name="BtnCountryMap",zjm_fp_btn_name="BtnFPCountryMap",open_param="CrossLongMaiView",open_type=1,},
["country_map_xingtu"]={name="country_map_xingtu",show_name="仙图页签",funopen_seq=177,open_param="country_map_xingtu#CountryMapMapView",},
["country_map_actshow"]={name="country_map_actshow",show_name="仙图活动",funopen_seq=178,open_param="country_map_actshow#CountryMapMapView",},
[" country_map_xianwei_view"]={name=" country_map_xianwei_view",show_name="仙图仙位",funopen_seq=179,open_param="country_map_xianwei_view#CountryMapMapView",},
["dayact_chaoswar"]={name="dayact_chaoswar",show_name="一战到底",funopen_seq=180,trigger_type=2,trigger_param=1370,open_param="dayact_chaoswar#day_activity",},
["dayact_huizhan"]={name="dayact_huizhan",show_name="王城会战",funopen_seq=181,trigger_type=2,trigger_param=50,open_param="dayact_huizhan#day_activity",},
["national_war"]={name="national_war",show_name="四国争霸",funopen_seq=182,open_param="NationalWarView",},
["national_cefeng_info"]={name="national_cefeng_info",show_name="国家册封",funopen_seq=183,open_param="national_cefeng_info#national_war",},
["imperial_city_battle_info"]={name="imperial_city_battle_info",show_name="夜战皇城",funopen_seq=184,open_param="imperial_city_battle_info#national_war",},
["dayact_zhuxie"]={name="dayact_zhuxie",show_name="诛邪战场",funopen_seq=185,trigger_param=150,open_param="dayact_zhuxie#day_activity",task_level=100,},
["equipment_baoshi_jl"]={name="equipment_baoshi_jl",show_name="玉魄精炼",funopen_seq=186,icon="a3_open_dz",open_param="equipment_baoshi_jl#equipment",fly_pos="0|-35|0",},
["fubenpanel"]={name="fubenpanel",show_name="副本",funopen_seq=187,trigger_type=2,trigger_param=840,open_param="fubenpanel",open_type=1,task_level=58,},
["fubenpanel2"]={name="fubenpanel2",show_name="副本-组队",funopen_seq=188,trigger_type=2,trigger_param=890,open_param="fubenpanel",task_level=110,},
["fubenpanel_exp"]={name="fubenpanel_exp",show_name="日月修行",funopen_seq=189,trigger_type=2,trigger_param=980,open_param="fubenpanel_exp#fubenpanel",fly_pos="0|-35|0",},
["fubenpanel_equip_high"]={name="fubenpanel_equip_high",show_name="天峰夺宝",funopen_seq=190,trigger_param=2330,open_param="fubenpanel_equip_high#fubenpanel",fly_pos="0|-35|0",},
["fubenpanel_copper"]={name="fubenpanel_copper",show_name="熔火之心",funopen_seq=191,trigger_param=170,open_param="fubenpanel_copper#fubenpanel",open_type=4,task_level=170,fly_pos="0|-35|0",},
["fubenpanel_pet"]={name="fubenpanel_pet",show_name="暗翼之巢",funopen_seq=192,trigger_param=4,trigger_param2=300,open_param="fubenpanel_pet#fubenpanel",task_level=130,},
["fubenpanel_zhuogui"]={name="fubenpanel_zhuogui",show_name="封魔禁地",funopen_seq=193,trigger_param=13,open_param="fubenpanel_zhuogui#fubenpanel",task_level=550,fly_pos="0|-35|0",},
["fubenpanel_welkin"]={name="fubenpanel_welkin",show_name="九重劫塔",funopen_seq=194,trigger_param=920,open_param="fubenpanel_welkin#fubenpanel",task_level=50,fly_pos="0|-35|0",},
["fubenpanel_bootybay"]={name="fubenpanel_bootybay",show_name="仙君遗迹",funopen_seq=195,open_param="fubenpanel_bootybay#fubenpanel",open_type=4,fly_pos="0|-35|0",},
["fubenpanel_bagua"]={name="fubenpanel_bagua",show_name="神灵仙岛",funopen_seq=196,trigger_type=16,trigger_param=8,trigger_param2=150,open_param="fubenpanel_bagua#fubenpanel",task_level=220,},
["fubenpanel_control_beasts"]={name="fubenpanel_control_beasts",show_name="昆仑玉虚",funopen_seq=197,trigger_type=2,trigger_param=940,open_param="fubenpanel_control_beasts#fubenpanel",open_type=4,task_level=100,},
["fubenpanel_beauty"]={name="fubenpanel_beauty",show_name="神女无梦",funopen_seq=198,trigger_param=7,trigger_param2=150,open_param="fubenpanel_beauty#fubenpanel",},
["fubenpanel_wuhun"]={name="fubenpanel_wuhun",show_name="山海五行",funopen_seq=199,trigger_type=16,trigger_param2=9999,open_param="fubenpanel_wuhun#fubenpanel",task_level=58,fly_pos="0|-35|0",},
["fubenpanel_rune_tower"]={name="fubenpanel_rune_tower",show_name="炼魂神殿",funopen_seq=199,open_param="fubenpanel_rune_tower#fubenpanel",fly_pos="0|-35|0",},
["fubenpanel_manhuanggudian"]={name="fubenpanel_manhuanggudian",show_name="荒古墓",funopen_seq=200,open_param="fubenpanel_manhuanggudian#fubenpanel",},
["fubenpanel_equip"]={name="fubenpanel_equip",show_name="海底废墟",funopen_seq=201,zjm_fp_btn_name="BtnFPFuBen",icon="a3_open_mc",open_param="fubenpanel_equip#fubenpanel",open_type=4,},
["fubenpanel_tianshen"]={name="fubenpanel_tianshen",show_name="封神台",funopen_seq=202,trigger_type=16,trigger_param=15,trigger_param2=400,open_param="fubenpanel_tianshen#fubenpanel",task_level=400,},
["fubenpanel_tafang"]={name="fubenpanel_tafang",show_name="塔防",funopen_seq=203,open_param="fubenpanel_tafang#fubenpanel",},
["fubenpanel_zhushen_ta"]={name="fubenpanel_zhushen_ta",show_name="九层妖塔",funopen_seq=204,open_param="fubenpanel_zhushen_ta#fubenpanel",},
["worserv_boss_sgyj"]={name="worserv_boss_sgyj",show_name="仙君遗迹",funopen_seq=205,open_param="worserv_boss_sgyj#WorldServer",open_type=4,},
["worserv_boss_hmsy"]={name="worserv_boss_hmsy",show_name="须弥副本",funopen_seq=206,open_param="worserv_boss_hmsy#WorldServer",},
["equipment"]={name="equipment",show_name="装备",funopen_seq=207,trigger_param=70,open_param="equipment_strength#equipment",open_type=1,task_level=85,},
["equipment_strength"]={name="equipment_strength",show_name="锻造",funopen_seq=208,trigger_type=2,trigger_param=920,open_param="equipment_strength#equipment",open_type=4,task_level=85,},
["equipment_yingji"]={name="equipment_yingji",show_name="占星解封",funopen_seq=209,zjm_zp_btn_name="BtnRoleBagView",zjm_fp_btn_name="BtnFPRoleBag",icon="a3_open_wz",open_param="EquipmentMarkView",},
["equipment_baoshi"]={name="equipment_baoshi",show_name="玉魄",funopen_seq=210,trigger_type=2,trigger_param=1195,open_param="equipment_baoshi#equipment",task_level=120,fly_pos="0|-35|0",},
["equipment_lingyu"]={name="equipment_lingyu",show_name="灵石",funopen_seq=211,trigger_param=7,open_param="equipment_lingyu#equipment",},
["equipment_xilian"]={name="equipment_xilian",show_name="装备洗炼",funopen_seq=212,trigger_type=16,trigger_param=1,trigger_param2=260,open_param="equipment_xilian#equipment",task_level=260,},
["equipment_suit"]={name="equipment_suit",show_name="装备套装",funopen_seq=213,trigger_param=250,open_param="equipment_suit#equipment",task_level=250,fly_pos="0|-35|0",},
["equipment_shengpin"]={name="equipment_shengpin",show_name="装备升品",funopen_seq=214,zjm_fp_btn_name="BtnEquipmentView",icon="a3_open_dz",open_param="equipment_shengpin#equipment",open_type=4,},
["equipment_zhuanhua"]={name="equipment_zhuanhua",show_name="装备回炉",funopen_seq=215,icon="a3_open_dz",open_param="equipment_zhuanhua#equipment",open_type=4,},
["equipment_shenshou"]={name="equipment_shenshou",show_name="神兽装备",funopen_seq=216,trigger_param=320,open_param="equipment_shenshou#equipment",task_level=320,},
["equipmeng_yuling_set"]={name="equipmeng_yuling_set",show_name="装备炼神",funopen_seq=217,open_param="equipment_imperial_spirit_set#equipment",},
["equipmeng_yuling_strength"]={name="equipmeng_yuling_strength",show_name="装备还虚",funopen_seq=218,open_param="equipment_imperial_spirit_strengtn#equipment",},
["everyday_recharge_yingji"]={name="everyday_recharge_yingji",show_name="占星",funopen_seq=219,open_param="everyday_recharge_yingji#EveryDayRechargeView",},
["FaBaoLinZhiSkillView"]={name="FaBaoLinZhiSkillView",show_name="神器器魂",funopen_seq=220,open_param="FaBaoLinZhiSkillView",},
["FairyLandEquipmentNoticeView"]={name="FairyLandEquipmentNoticeView",show_name="仙界装备预告",funopen_seq=221,open_param="FairyLandEquipmentNoticeView",},
["FairyLandPlaneView"]={name="FairyLandPlaneView",show_name="诸天万界",funopen_seq=222,zjm_fp_btn_name="BtnDujie",icon="a3_open_dj",zjm_fp_btn_hide=1,trigger_type=16,trigger_param=10,trigger_param2=300,open_param="FairyLandPlaneView",open_type=1,task_level=300,fly_pos="0|-35|0",},
["FightSoulView"]={name="FightSoulView",show_name="幽冥鬼蜮",funopen_seq=223,zjm_fp_btn_hide=1,open_param="FightSoulView",},
["fight_soul_bone"]={name="fight_soul_bone",show_name="幽冥鬼蜮器灵",funopen_seq=224,zjm_fp_btn_name="BtnFightSoulView",open_param="fight_soul_bone#FightSoulView",},
["fight_soul_cuizhuo"]={name="fight_soul_cuizhuo",show_name="幽冥鬼蜮淬涿",funopen_seq=225,zjm_fp_btn_name="BtnFightSoulView",trigger_type=16,trigger_param2=100,open_param="fight_soul_cuizhuo#FightSoulView",},
["SiXiangCallView"]={name="SiXiangCallView",show_name="幽冥秘宝",funopen_seq=226,trigger_type=16,trigger_param2=100,child_fun="sixiang_call_sx|sixiang_call_xqzl",open_param="SiXiangCallView",open_type=1,},
["sixiang_call_sx"]={name="sixiang_call_sx",show_name="幽冥秘宝页签",funopen_seq=227,zjm_zp_btn_name="BtnSiXiangCallView",parent_fun="SiXiangCallView",open_param="sixiang_call_sx#SiXiangCallView",},
["MainUILongZhuSkill"]={name="MainUILongZhuSkill",show_name="星陨怒兵技能",funopen_seq=228,open_param="MainUILongZhuSkill",},
["fuwen"]={name="fuwen",show_name="奇门",funopen_seq=229,open_param="fuwen",},
["fuwen_xiangqian"]={name="fuwen_xiangqian",show_name="八卦",funopen_seq=230,open_param="fuwen_xiangqian#fuwen",},
["fuwen_fenjie"]={name="fuwen_fenjie",show_name="八卦分解",funopen_seq=231,open_param="fuwen_fenjie#fuwen",},
["fuwen_duihuan"]={name="fuwen_duihuan",show_name="八卦兑换",funopen_seq=232,trigger_type=2,trigger_param=840,open_param="fuwen_duihuan#fuwen",open_type=1,task_level=50,},
["guild"]={name="guild",show_name="仙盟",funopen_seq=233,open_param="guild",},
["guild_cangku"]={name="guild_cangku",show_name="仙盟仓库",funopen_seq=234,trigger_param=100,open_param="guild_cangku#guild",task_level=65,},
["guild_info"]={name="guild_info",show_name="仙盟概况",funopen_seq=235,trigger_param=100,open_param="guild_info#guild",task_level=82,},
["guild_history"]={name="guild_history",show_name="仙盟记录",funopen_seq=236,open_param="guild_history#guild",},
["guild_member"]={name="guild_member",show_name="成员列表",funopen_seq=237,trigger_param=83,open_param="guild_member#guild",task_level=100,},
["guild_guildlist"]={name="guild_guildlist",show_name="仙盟列表",funopen_seq=238,trigger_param=100,open_param="guild_guildlist#guild",task_level=100,},
["guild_juanxian"]={name="guild_juanxian",show_name="仙盟捐献",funopen_seq=239,open_param="guild_juanxian#guild",},
["guild_redpacket"]={name="guild_redpacket",show_name="仙盟红包",funopen_seq=240,open_param="guild_redpacket#guild",},
["guild_dati"]={name="guild_dati",show_name="仙盟答题",funopen_seq=241,open_param="guild_dati#guild",},
["guild_wage"]={name="guild_wage",show_name="仙盟工资",funopen_seq=242,open_param="guild_wage#guild",},
["guild_skill"]={name="guild_skill",show_name="仙盟修炼",funopen_seq=243,open_param="guild_skill#guild",},
["guild_boss"]={name="guild_boss",show_name="仙盟神兽",funopen_seq=244,open_param="guild_boss#guild",},
["guild_activity"]={name="guild_activity",show_name="仙盟活动",funopen_seq=245,open_param="guild_activity#guild",},
["guild_shouhu"]={name="guild_shouhu",show_name="仙盟守卫",funopen_seq=246,open_param="guild_shouhu#guild",},
["guild_assign"]={name="guild_assign",show_name="仙盟-雇佣上架",funopen_seq=247,open_param="guild_assign#guild",},
["guild_my_assign"]={name="guild_my_assign",show_name="仙盟-我的雇佣",funopen_seq=248,open_param="guild_my_assign#guild",},
["guild_War"]={name="guild_War",show_name="仙盟争霸",funopen_seq=249,open_param="guild_War#guild",},
["guild_battle"]={name="guild_battle",show_name="仙盟争霸",funopen_seq=250,open_param="guild_battle#guild",},
["guild_baoxiang"]={name="guild_baoxiang",show_name="每日宝箱",funopen_seq=251,open_param="guild_baoxiang#guild",},
["JianZhenLinZhiSkillView"]={name="JianZhenLinZhiSkillView",show_name="剑灵器魂",funopen_seq=252,open_param="JianZhenLinZhiSkillView",},
["LongHunView"]={name="LongHunView",show_name="悬壶济世",funopen_seq=253,zjm_fp_btn_name="BtnLongHunView",icon="a3_open_ts",zjm_fp_btn_hide=1,open_param="LongHunView",open_type=4,},
["market"]={name="market",show_name="市场",funopen_seq=254,trigger_type=2,trigger_param=930,open_param="market",open_type=1,task_level=130,},
["market_buy"]={name="market_buy",show_name="市场-购买",funopen_seq=255,trigger_type=2,trigger_param=930,open_param="market_buy#market",task_level=130,},
["market_sell"]={name="market_sell",show_name="市场-出售",funopen_seq=256,open_param="market_sell#market",},
["market_record"]={name="market_record",show_name="市场-销售记录",funopen_seq=257,open_param="market_record#market",},
["market_want_list"]={name="market_want_list",show_name="市场-求购列表",funopen_seq=258,open_param="market_want_list#market",},
["market_my_want"]={name="market_my_want",show_name="市场-我的求购",funopen_seq=259,open_param="market_my_want#market",},
["market_country_auction"]={name="market_country_auction",show_name="市场-拍卖-国家拍卖",funopen_seq=260,open_param="market_country_auction#market",},
["market_guild_auction"]={name="market_guild_auction",show_name="市场-拍卖-仙盟拍卖",funopen_seq=261,open_param="market_guild_auction#market",},
["market_cross_server"]={name="market_cross_server",show_name="市场-拍卖-跨国拍卖",funopen_seq=262,open_param="market_cross_server#market",},
["market_my_auction"]={name="market_my_auction",show_name="市场-拍卖-我的竞拍",funopen_seq=263,open_param="market_my_auction#market",},
["market_auction_record"]={name="market_auction_record",show_name="市场-拍卖-拍卖纪录",funopen_seq=264,trigger_type=2,trigger_param=930,open_param="market_auction_record#market",task_level=160,},
["shop"]={name="shop",show_name="商城",funopen_seq=265,trigger_param=28,open_param="shop",open_type=1,task_level=1,},
["privilegeshop"]={name="privilegeshop",show_name="特权商店",funopen_seq=266,open_param="privilegeshop",},
["season_privilege_shop"]={name="season_privilege_shop",show_name="季卡特权商店",funopen_seq=267,open_param="season_privilege_shop#privilegeshop",},
["sup_privilege_shop"]={name="sup_privilege_shop",show_name="至尊特权商店",funopen_seq=268,trigger_type=16,trigger_param=999,open_param="sup_privilege_shop#privilegeshop",open_type=1,},
["shop_hot"]={name="shop_hot",show_name="商城-热销",funopen_seq=269,trigger_param=0,open_param="shop_hot#shop",},
["shop_daoju"]={name="shop_daoju",show_name="商城-功能道具",funopen_seq=270,open_param="shop_daoju#shop",},
["shop_moyu"]={name="shop_moyu",show_name="商城-绑定商店（但实际控制在商城表）",funopen_seq=271,open_param="shop_moyu#shop",},
["HomesView"]={name="HomesView",show_name="家园",funopen_seq=272,open_param="HomesView",task_level=130,},
["ProfessWallView"]={name="ProfessWallView",show_name="表白墻",funopen_seq=273,trigger_type=2,trigger_param=910,open_param="ProfessWallView",open_type=4,task_level=160,},
["marry"]={name="marry",show_name="仙侣",funopen_seq=274,open_param="marry",task_level=178,fly_pos="0|-35|0",},
["marry_jiehun"]={name="marry_jiehun",show_name="结婚",funopen_seq=275,trigger_type=2,trigger_param=910,open_param="marry_jiehun#marry",task_level=178,},
["marry_hunjie"]={name="marry_hunjie",show_name="姻缘树",funopen_seq=276,open_param="marry_hunjie#marry",},
["marry_baby"]={name="marry_baby",show_name="崽崽",funopen_seq=277,open_param="marry_baby#marry",},
["marry_baoxia"]={name="marry_baoxia",show_name="缘契",funopen_seq=278,open_param="marry_baoxia#marry",},
["marry_fb"]={name="marry_fb",show_name="仙侣副本",funopen_seq=279,open_param="marry_fb#marry",},
["marry_profess_wall"]={name="marry_profess_wall",show_name="情缘",funopen_seq=280,open_param="marry_profess_wall#marry",},
["marry_notice"]={name="marry_notice",show_name="金玉良缘",funopen_seq=281,trigger_param=180,open_param="marry_notice",open_type=4,task_level=77,},
["MingWenView"]={name="MingWenView",show_name="万魂幡",funopen_seq=282,zjm_fp_btn_name="BtnMingWenView",icon="a3_open_hy",trigger_param=200,open_param="MingWenView",open_type=1,task_level=200,fly_pos="0|-35|0",},
["other_compose"]={name="other_compose",show_name="合成",funopen_seq=283,trigger_param=100,open_param="other_compose",open_type=1,task_level=130,},
["other_compose_shengyin"]={name="other_compose_shengyin",show_name="装备材料合成",funopen_seq=284,open_param="other_compose_shengyin#other_compose",},
["other_compose_daoju"]={name="other_compose_daoju",show_name="道具合成",funopen_seq=285,open_param="other_compose_daoju#other_compose",},
["other_compose_eq_hecheng_three"]={name="other_compose_eq_hecheng_three",show_name="饰品合成",funopen_seq=286,trigger_param=110,open_param="other_compose_eq_hecheng_three#other_compose",task_level=130,},
["other_compose_stone"]={name="other_compose_stone",show_name="宝石合成",funopen_seq=287,trigger_param=110,open_param="other_compose_stone#other_compose",task_level=120,},
["other_compose_shengjie"]={name="other_compose_shengjie",show_name="仙器升阶",funopen_seq=288,open_param="other_compose_shengjie#other_compose",},
["other_xianqi_stuff"]={name="other_xianqi_stuff",show_name="仙器合成",funopen_seq=289,open_param="other_xianqi_stuff#other_compose",},
["other_compose_shengxing"]={name="other_compose_shengxing",show_name="仙器升星",funopen_seq=290,open_param="other_compose_shengxing#other_compose",},
["other_compose_eq_hecheng_one"]={name="other_compose_eq_hecheng_one",show_name="苍穹装备合成",funopen_seq=291,open_param="other_compose_eq_hecheng_one#other_compose",},
["other_compose_eq_hecheng_two"]={name="other_compose_eq_hecheng_two",show_name="离歌装备合成",funopen_seq=292,trigger_param=100,open_param="other_compose_eq_hecheng_two#other_compose",task_level=130,},
["other_compose_taozhuang"]={name="other_compose_taozhuang",show_name="套装合成",funopen_seq=293,trigger_param=240,open_param="other_compose_taozhuang#other_compose",task_level=240,},
["other_compose_eq_hecheng_shenshou"]={name="other_compose_eq_hecheng_shenshou",show_name="神兽装备合成",funopen_seq=294,trigger_type=16,trigger_param=5,open_param="other_compose_eq_hecheng_shenshou#other_compose",task_level=360,},
["other_compose_longhu"]={name="other_compose_longhu",show_name="济世合成",funopen_seq=295,trigger_type=16,trigger_param=3,trigger_param2=9999,open_param="other_compose_longhu#other_compose",},
["qifu"]={name="qifu",show_name="祈福",funopen_seq=296,trigger_type=2,trigger_param=850,open_param="qifu_qf#qifu",open_type=4,task_level=200,},
["qifu_hmwd"]={name="qifu_hmwd",show_name="鸿蒙悟道",funopen_seq=297,zjm_fp_btn_name="vip",icon="a3_open_wz",trigger_type=2,open_param="qifu_hmwd#qifu",open_type=4,},
["qifu_yunshi"]={name="qifu_yunshi",show_name="运势",funopen_seq=298,trigger_type=2,trigger_param=850,open_param="qifu_yunshi#qifu",open_type=4,},
["rank_equip"]={name="rank_equip",show_name="装备榜",funopen_seq=299,trigger_param=130,open_param="rank",task_level=100,},
["rank_yuyi"]={name="rank_yuyi",show_name="羽翼榜",funopen_seq=300,trigger_param=50,open_param="Rank",task_level=15,},
["rank_fabao"]={name="rank_fabao",show_name="神器榜",funopen_seq=301,trigger_param=130,},
["rank_shenbing"]={name="rank_shenbing",show_name="神兵榜",funopen_seq=302,trigger_type=16,trigger_param=6,trigger_param2=100,open_param="Rank",task_level=110,},
["rank_jianzhen"]={name="rank_jianzhen",show_name="剑灵榜",funopen_seq=303,open_param="Rank",},
["rank_lingchong"]={name="rank_lingchong",show_name="灵宠榜",funopen_seq=304,trigger_param=130,},
["rank_zuoqi"]={name="rank_zuoqi",show_name="坐骑榜",funopen_seq=305,trigger_param=100,open_param="Rank",task_level=10,},
["rank"]={name="rank",show_name="名人堂",funopen_seq=306,trigger_param=130,open_param="rank",open_type=1,task_level=90,},
["rank_bianshen"]={name="rank_bianshen",show_name="诸神榜",funopen_seq=307,open_param="Rank",},
["rank_jinjie"]={name="rank_jinjie",show_name="境界榜",funopen_seq=308,task_level=50,},
["rank_fanrenxiuzhen"]={name="rank_fanrenxiuzhen",show_name="凡人修真榜",funopen_seq=309,},
["rank_guaji"]={name="rank_guaji",show_name="挂机榜",funopen_seq=310,},
["rank_baby"]={name="rank_baby",show_name="崽崽榜",funopen_seq=311,},
["rank_meili"]={name="rank_meili",show_name="魅力榜",funopen_seq=312,task_level=1,},
["rank_xianmeng"]={name="rank_xianmeng",show_name="仙盟榜",funopen_seq=313,open_param="Rank",},
["rank_zhanli"]={name="rank_zhanli",show_name="战力榜",funopen_seq=314,open_type=1,},
["rank_level"]={name="rank_level",show_name="等级榜",funopen_seq=315,},
["ShanHaiJingView"]={name="ShanHaiJingView",show_name="万图谱",funopen_seq=316,trigger_param=115,open_param="ShanHaiJingView",open_type=4,task_level=70,},
["shj_tujian"]={name="shj_tujian",show_name="图鉴",funopen_seq=317,trigger_param=65,open_param="ShanHaiJingView",task_level=70,},
["ShanHaiJingLSCView"]={name="ShanHaiJingLSCView",show_name="千秋绝艳图",funopen_seq=318,open_param="ShanHaiJingLSCView",},
["strange_catalog"]={name="strange_catalog",show_name="珍宝匣",funopen_seq=319,trigger_type=16,trigger_param=7,trigger_param2=100,open_param="StrangeCatalogView",},
["shj_chongwu"]={name="shj_chongwu",show_name="宠物收录",funopen_seq=320,trigger_param=200,open_param="shj_chongwu#shenshou",task_level=70,},
["shj_tianshen"]={name="shj_tianshen",show_name="诸神收录",funopen_seq=321,open_param="shj_tianshen#ShanHaiJingView",},
["shj_tianjipu"]={name="shj_tianjipu",show_name="天机谱收录",funopen_seq=322,open_param="shj_tianjipu#ShanHaiJingView",},
["shenshou"]={name="shenshou",show_name="天兵",funopen_seq=323,zjm_fp_btn_hide="shj_shouhunzhuzhan",open_param="shenshou",},
["ShenBingLinZhiSkillView"]={name="ShenBingLinZhiSkillView",show_name="神兵器魂",funopen_seq=324,open_param="ShenBingLinZhiSkillView",},
["ShenJi"]={name="ShenJi",show_name="神机",funopen_seq=325,zjm_zp_btn_name="BtnRoleBagView",trigger_type=16,trigger_param2=100,open_param="shenji_view",open_type=1,},
["ShenJiNotice"]={name="ShenJiNotice",show_name="神机现世",funopen_seq=326,trigger_param=300,open_param="ShenJiXianShiView",open_type=4,task_level=300,},
["ShenJiTianCiView"]={name="ShenJiTianCiView",show_name="神机百炼",funopen_seq=327,zjm_zp_btn_name="BtnShenJiTianCiView",icon="a3_open_ts",open_param="ShenJiTianCiView",open_type=4,},
["skill_view"]={name="skill_view",show_name="技能",funopen_seq=328,open_param="skill_view",},
["skill_talent"]={name="skill_talent",show_name="天赋技能",funopen_seq=329,zjm_fp_btn_name="BtnRoleskill",icon="a3_open_jn",open_param="skill_talent#skill_view",fly_pos="0|-35|0",},
["skill_awake"]={name="skill_awake",show_name="技能觉醒",funopen_seq=330,zjm_fp_btn_name="BtnRoleskill",icon="a3_open_jn",trigger_param2=425,open_param="skill_awake#skill_view",task_level=425,},
["ThunderManaSelectView"]={name="ThunderManaSelectView",show_name="雷法",funopen_seq=331,zjm_fp_btn_name="BtnFPThunderView",icon="a3_open_lf",trigger_param2=220,open_param="ThunderManaSelectView",fly_pos="0|-35|0",},
["NewAppearanceHaloWGView"]={name="NewAppearanceHaloWGView",show_name="雷法-护体灵罡",funopen_seq=332,trigger_param2=100,open_param="NewAppearanceHaloWGView",task_level=999,},
["skill_zhudong"]={name="skill_zhudong",show_name="主动技能",funopen_seq=333,open_param="skill_zhudong#skill_view",},
["skill_beidong"]={name="skill_beidong",show_name="被动技能",funopen_seq=334,open_param="skill_beidong#skill_view",},
["society_team"]={name="society_team",show_name="队伍",funopen_seq=335,open_param="society_temp#society",},
["society"]={name="society",show_name="社交",funopen_seq=336,open_param="society",},
["society_mail"]={name="society_mail",show_name="邮件",funopen_seq=337,open_param="society_mail#society",},
["society_friend"]={name="society_friend",show_name="好友",funopen_seq=338,open_param="society_friend#society",},
["society_enemy"]={name="society_enemy",show_name="仇人",funopen_seq=339,open_param="society_enemy#society",},
["guild_week_task"]={name="guild_week_task",show_name="仙盟周任务",funopen_seq=340,trigger_param=150,open_param="task",open_type=1,task_level=145,},
["TianShenJuexing"]={name="TianShenJuexing",show_name="灵剑降临",funopen_seq=341,close_trigger_type=15,open_param="TianShenJuexing",open_type=1,},
["TianShenView"]={name="TianShenView",show_name="诸神",funopen_seq=342,trigger_type=25,trigger_param=0,open_param="TianShenView",fly_pos="0|-35|0",},
["tianshen_activation"]={name="tianshen_activation",show_name="诸神升级",funopen_seq=343,open_param="tianshen_activation#TianShenView",},
["TianShenActivationView"]={name="TianShenActivationView",show_name="诸神化形",funopen_seq=344,open_param="TianShenActivationView#TianShenView",},
["tianshen_shenshi"]={name="tianshen_shenshi",show_name="诸神星命",funopen_seq=345,open_param="tianshen_shenshi#TianShenView",open_type=4,},
["tianshen_shentong"]={name="tianshen_shentong",show_name="诸神灵脉",funopen_seq=346,open_param="tianshen_shentong#TianShenView",},
["tianshen_bagua"]={name="tianshen_bagua",show_name="诸神神魂",funopen_seq=347,trigger_type=16,trigger_param2=250,open_param="tianshen_bagua#TianShenView",open_type=4,task_level=250,},
["tianshen_shenQi"]={name="tianshen_shenQi",show_name="诸神灵武",funopen_seq=348,trigger_param=8,open_param="tianshen_shenQi#TianShenView",},
["TianShenLingHeDrawView"]={name="TianShenLingHeDrawView",show_name="天降神装",funopen_seq=349,trigger_param=999,open_param="TianShenLingHeDrawView",task_level=999,},
["TianShenLingHeView"]={name="TianShenLingHeView",show_name="诸神神装",funopen_seq=350,open_param="TianShenLingHeView",},
["tianshen_linghe_uplevel"]={name="tianshen_linghe_uplevel",show_name="诸神神装升级",funopen_seq=351,open_param="tianshen_linghe_uplevel#TianShenView",},
["tianshen_linghe_reslove"]={name="tianshen_linghe_reslove",show_name="诸神神装分解",funopen_seq=352,open_param="tianshen_linghe_reslove#TianShenView",},
["tianshen_linghe_compose"]={name="tianshen_linghe_compose",show_name="诸神神装融合",funopen_seq=353,open_param="tianshen_linghe_compose#TianShenView",},
["TianShenHuamoView"]={name="TianShenHuamoView",show_name="诸神坠魔",funopen_seq=354,open_param="TianshenHuamoView",},
["tianshen_heji"]={name="tianshen_heji",show_name="天神合击",funopen_seq=355,open_param="tianshen_heji#TianShenView",open_type=1,},
["TianShenShuangShengView"]={name="TianShenShuangShengView",show_name="双生诸神",funopen_seq=356,open_param="TianShenShuangShengView",},
["tianshen_huanhua_jinjie"]={name="tianshen_huanhua_jinjie",show_name="诸神幻化进阶",funopen_seq=357,trigger_type=16,trigger_param=8,trigger_param2=100,task_level=100,},
["wuhun_details"]={name="wuhun_details",show_name="武魂",funopen_seq=358,open_param="wuhun_details#WuHunView",open_type=4,task_level=250,},
["WuHunView"]={name="WuHunView",show_name="武魂真身",funopen_seq=359,trigger_type=25,trigger_param=1,open_param="WuHunView",open_type=4,task_level=250,},
["wuhun_tower"]={name="wuhun_tower",show_name="武魂塔",funopen_seq=360,trigger_type=9999,trigger_param2=9999,open_param="wuhun_tower#WuHunView",},
["wuhun_front"]={name="wuhun_front",show_name="武魂魂阵",funopen_seq=361,open_param="wuhun_front#WuHunView",},
["zhuansheng"]={name="zhuansheng",show_name="天地神躯",funopen_seq=362,trigger_type=1,trigger_param=1000,open_param="transfer",open_type=1,task_level=102,},
["TreasureHunt"]={name="TreasureHunt",show_name="新寻宝",funopen_seq=363,trigger_param2=220,open_param="TreasureHunt",task_level=51,},
["treasurehunt_fuwen"]={name="treasurehunt_fuwen",show_name="魂印寻宝",funopen_seq=364,trigger_type=16,trigger_param=4,trigger_param2=220,open_param="treasurehunt_fuwen#TreasureHunt",task_level=220,},
["treasurehunt_equip"]={name="treasurehunt_equip",show_name="装备寻宝",funopen_seq=365,open_param="treasurehunt_equip#TreasureHunt",open_type=5,},
["treasurehunt_dianfeng"]={name="treasurehunt_dianfeng",show_name="巅峰寻宝",funopen_seq=366,trigger_type=16,trigger_param2=220,open_param="treasurehunt_dianfeng#TreasureHunt",task_level=220,},
["treasurehunt_zhizun"]={name="treasurehunt_zhizun",show_name="至尊寻宝",funopen_seq=367,open_param="treasurehunt_zhizun#TreasureHunt",},
["treasurehunt_boss"]={name="treasurehunt_boss",show_name="寻宝boss",funopen_seq=368,trigger_type=16,trigger_param2=220,open_param="TreasureHunt",open_type=4,},
["XiuZhenRoadView"]={name="XiuZhenRoadView",show_name="修真之路",funopen_seq=369,trigger_param=130,open_param="XiuZhenRoadView",open_type=1,task_level=130,},
["equipxunbao_exchange"]={name="equipxunbao_exchange",show_name="兑换",funopen_seq=370,trigger_param=60,open_param="equipxunbao_exchange#xunBao",task_level=60,},
["YunbiaoView"]={name="YunbiaoView",show_name="护送任务",funopen_seq=371,trigger_param=70,open_param="YunbiaoView",open_type=1,task_level=57,},
["zhanling"]={name="zhanling",show_name="战令",funopen_seq=372,open_param="ZhanLingView",task_level=170,},
["main_ui_Target"]={name="main_ui_Target",show_name="装备收集",funopen_seq=373,task_level=90,},
["rolebag_Target"]={name="rolebag_Target",funopen_seq=374,open_param="rolebag_Target#bag_view",open_type=4,},
["EquipmentIntegration"]={name="EquipmentIntegration",show_name="装备集成",funopen_seq=375,open_param="rolebag_Integration#bag_view",},
["role_refining"]={name="role_refining",show_name="丹道",funopen_seq=376,trigger_param=110,open_param="role_refining#role_view",task_level=110,},
["AlchemyView"]={name="AlchemyView",show_name="炼丹",funopen_seq=377,open_param="AlchemyView",},
["TianShenLiLianView"]={name="TianShenLiLianView",show_name="历练",funopen_seq=378,trigger_type=2,trigger_param=1350,open_param="TianShenLiLianView",open_type=4,task_level=127,},
["HongHuangClassicView"]={name="HongHuangClassicView",show_name="洪荒特典",funopen_seq=379,trigger_param=180,open_param="HongHuangClassicView",open_type=1,task_level=130,},
["HongHuangGoodCoremonyView"]={name="HongHuangGoodCoremonyView",show_name="洪荒豪礼",funopen_seq=380,open_param="HongHuangGoodCoremonyView",},
["ChaoticPurchaseView"]={name="ChaoticPurchaseView",show_name="洪荒直购",funopen_seq=381,open_param="ChaoticPurchaseView",},
["ChaoticVipSpecialView"]={name="ChaoticVipSpecialView",show_name="V6豪礼",funopen_seq=382,trigger_param=220,open_param="ChaoticVipSpecialView",open_type=1,task_level=120,},
["FiveElementsView"]={name="FiveElementsView",show_name="天道",funopen_seq=383,zjm_fp_btn_name="BtnFiveElements",trigger_param2=100,open_param="FiveElementsView",},
["five_elements_cangming"]={name="five_elements_cangming",show_name="沧溟",funopen_seq=384,open_param="five_elements_cangming#FiveElementsView",},
["five_elements_overview"]={name="five_elements_overview",show_name="天道",funopen_seq=385,open_param="five_elements_overview#FiveElementsView",},
["five_elements_knapsack"]={name="five_elements_knapsack",show_name="天命",funopen_seq=386,open_param="five_elements_knapsack#FiveElementsView",},
["five_elements_talent"]={name="five_elements_talent",show_name="灵脉",funopen_seq=387,open_param="five_elements_talent#FiveElementsView",},
["FiveElementsTreasuryView"]={name="FiveElementsTreasuryView",show_name="寻天命",funopen_seq=388,trigger_type=16,trigger_param2=100,open_param="FiveElementsTreasuryView",open_type=1,},
["SwornView"]={name="SwornView",show_name="金兰结义",funopen_seq=389,zjm_fp_btn_name="BtnSworn",open_param="SwornView",open_type=1,},
["sworn_start"]={name="sworn_start",show_name="结义主界面",funopen_seq=390,open_param="sworn_start#SwornView",},
["sworn_apply"]={name="sworn_apply",show_name="结义申请",funopen_seq=391,open_param="sworn_apply#SwornView",},
["sworn_imprint"]={name="sworn_imprint",show_name="结义金兰",funopen_seq=392,open_param="sworn_imprint#SwornView",},
["sworn_suit"]={name="sworn_suit",show_name="结义装备-套装",funopen_seq=393,open_param=" sworn_suit#SwornView",},
["sworn_upstar"]={name="sworn_upstar",show_name="结义装备-升星",funopen_seq=394,open_param="sworn_upstar#SwornView",},
["sworn_uplevel"]={name="sworn_uplevel",show_name="结义装备-升级",funopen_seq=395,open_param="sworn_uplevel#SwornView",},
["super_dragon_seal"]={name="super_dragon_seal",show_name="龙玺",funopen_seq=396,open_param="btn_dragon_king_token#bag_view",},
["dragon_king_token"]={name="dragon_king_token",funopen_seq=397,open_param="btn_super_dragon_seal#bag_view",},
["LongXiView"]={name="LongXiView",show_name="令牌",funopen_seq=398,trigger_param=3,open_param="LongXiView#bag_view",},
["HolyDarkWeaponEnter"]={name="HolyDarkWeaponEnter",show_name="藏宝阁",funopen_seq=399,zjm_fp_btn_name="BtnFPCBG",open_param="HolyDarkWeaponEnter",},
["HolyWeapon"]={name="HolyWeapon",show_name="玄印录",funopen_seq=400,zjm_fp_btn_name="BtnFPHoly",icon="a3_open_ts",open_param="HolyWeapon",open_type=4,},
["DarkWeapon"]={name="DarkWeapon",show_name="魔剑谱",funopen_seq=401,zjm_fp_btn_name="BtnFPDark",open_param="DarkWeapon",},
["country_map_yanglongsi"]={name="country_map_yanglongsi",show_name="神秘海域",funopen_seq=402,zjm_zp_btn_name="BtnCountryMap",zjm_fp_btn_name="BtnFPCountryMap",trigger_type=16,trigger_param2=9999,open_param="country_map_yanglongsi#country_map_act_view",},
["ShiTianSuitView"]={name="ShiTianSuitView",show_name="灵皇套装",funopen_seq=403,zjm_fp_btn_name="BtnFPShiTian",open_param="ShiTianSuitView",},
["equipment_zhushen"]={name="equipment_zhushen",show_name="铸器",funopen_seq=404,zjm_fp_btn_name="BtnEquipmentView",open_param="equipment_zhushen#equipment",},
["equipment_zhushentai"]={name="equipment_zhushentai",show_name="聚灵",funopen_seq=405,open_param="equipment_zhushentai#equipment",},
["DragonTempleView"]={name="DragonTempleView",show_name="龙神",funopen_seq=406,zjm_fp_btn_name="BtnDragonTemple",zjm_fp_btn_hide=1,open_param="DragonTempleView",open_type=5,},
["dragon_temp_longhun"]={name="dragon_temp_longhun",show_name="龙神-龙魂",funopen_seq=407,open_param="dragon_temp_longhun#DragonTempleView",},
["dragon_temp_equip"]={name="dragon_temp_equip",show_name="龙神-龙装",funopen_seq=408,open_param="dragon_temp_equip#DragonTempleView",},
["dragon_temp_hatch"]={name="dragon_temp_hatch",show_name="龙神-孵化",funopen_seq=409,open_param="dragon_temp_hatch#DragonTempleView",},
["dragon_temp_draw"]={name="dragon_temp_draw",show_name="龙神-秘宝",funopen_seq=410,trigger_type=16,trigger_param2=100,open_param="dragon_temp_draw#DragonTempleView",},
["dragon_temp_rank"]={name="dragon_temp_rank",show_name="龙神-排行榜",funopen_seq=411,open_param="dragon_temp_rank#DragonTempleView",},
["NewFightMountView"]={name="NewFightMountView",show_name="御龙定乾坤",funopen_seq=412,open_param="NewFightMountView",},
["ArtifactView"]={name="ArtifactView",show_name="双修",funopen_seq=413,trigger_param=7,open_param="ArtifactView",},
["SystemForceLongShenView"]={name="SystemForceLongShenView",show_name="系统预告",funopen_seq=414,trigger_type=25,trigger_param=-1,open_param="SystemForceLongShenView",open_type=1,task_level=1,},
["MultiFunctionView"]={name="MultiFunctionView",show_name="降魔",funopen_seq=415,zjm_fp_btn_name="BtnFPCharm",open_param="MultiFunctionView",task_level=300,},
["charm_lingzhu"]={name="charm_lingzhu",show_name="灵珠",funopen_seq=416,trigger_type=16,trigger_param2=100,open_param="charm_lingzhu#MultiFunctionView",task_level=300,},
["daohang_suit"]={name="daohang_suit",show_name="百鬼夜行装备",funopen_seq=417,zjm_fp_btn_hide=1,trigger_type=16,trigger_param2=100,open_param="daohang_suit#MultiFunctionView",},
["daohang_keling"]={name="daohang_keling",show_name="百鬼夜行符石",funopen_seq=418,open_param="daohang_keling#MultiFunctionView",},
["daohang_qianghua"]={name="daohang_qianghua",show_name="百鬼夜行强化",funopen_seq=419,open_param="daohang_qianghua#MultiFunctionView",},
["daohang_jinjie"]={name="daohang_jinjie",show_name="百鬼夜行进阶",funopen_seq=420,open_param="daohang_jinjie#MultiFunctionView",},
["daohang_keyin"]={name="daohang_keyin",show_name="百鬼夜行刻印",funopen_seq=421,open_param="daohang_keyin#MultiFunctionView",},
["daohang_kaiguang"]={name="daohang_kaiguang",show_name="百鬼夜行开光",funopen_seq=422,open_param="daohang_kaiguang#MultiFunctionView",},
["new_appearance_lingchong_hualing"]={name="new_appearance_lingchong_hualing",show_name="化灵-伙伴",funopen_seq=423,trigger_type=16,trigger_param2=240,open_param="new_appearance_lingchong_hualing#NewAppearanceWGView",},
["new_appearance_mount_hualing"]={name="new_appearance_mount_hualing",show_name="化灵-珍骑",funopen_seq=424,open_param="new_appearance_mount_hualing#NewAppearanceWGView",},
["new_appearance_kun_hualing"]={name="new_appearance_kun_hualing",show_name="化灵-神鲲",funopen_seq=425,trigger_type=16,trigger_param2=300,open_param="new_appearance_kun_hualing#NewAppearanceWGView",},
["sky_curtain"]={name="sky_curtain",show_name="天幕背景",funopen_seq=426,trigger_param=2,open_param="sky_curtain#RoleBranchView",},
["other_compose_back"]={name="other_compose_back",show_name="背景合成",funopen_seq=427,trigger_type=16,trigger_param=5,trigger_param2=100,open_param="other_compose_backu#other_compose",task_level=240,},
["SupremeFieldsWGView"]={name="SupremeFieldsWGView",show_name="五字真境",funopen_seq=428,trigger_param=5,open_param="SupremeFieldsWGView",},
["GuiXuDreamView"]={name="GuiXuDreamView",show_name="须弥神域",funopen_seq=429,trigger_param=100,},
["CustomizedSuitView"]={name="CustomizedSuitView",show_name="定制套装",funopen_seq=430,open_param="CustomizedSuitView",},
["BoundlessJoyView"]={name="BoundlessJoyView",show_name="长乐未央",funopen_seq=431,zjm_zp_btn_name="BtnHappyFover",open_param="BoundlessJoyView",task_level=170,},
["HolyHeavenlyDomainView"]={name="HolyHeavenlyDomainView",show_name="圣天神域",funopen_seq=432,zjm_zp_btn_name="BtnHolyHeavenlyDomain",trigger_param2=500,open_param="HolyHeavenlyDomainView",task_level=500,},
["ControlBeastsView"]={name="ControlBeastsView",show_name="驭兽-入口",funopen_seq=433,trigger_type=2,trigger_param=60,open_param="ControlBeastsView",open_type=4,task_level=26,},
["BeastsBattle"]={name="BeastsBattle",show_name="驭兽-出战",funopen_seq=434,trigger_type=2,trigger_param=60,open_param="beasts_battle#ControlBeastsView",open_type=8,task_level=26,},
["BeastsCulture"]={name="BeastsCulture",show_name="驭兽-培养",funopen_seq=435,open_param="beasts_culture#ControlBeastsView",},
["BeastsRefining"]={name="BeastsRefining",show_name="驭兽-宝物",funopen_seq=436,open_param="beasts_refining#ControlBeastsView",},
["BeastsStable"]={name="BeastsStable",show_name="驭兽-孵化",funopen_seq=437,open_param="beasts_stable#ControlBeastsView",},
["BeastsCompose"]={name="BeastsCompose",show_name="驭兽-升星",funopen_seq=438,open_param="beasts_compose#ControlBeastsView",},
["BeastsKing"]={name="BeastsKing",show_name="驭兽-兽王",funopen_seq=439,open_param="beasts_king#ControlBeastsView",},
["ControlBeastsContractWGView"]={name="ControlBeastsContractWGView",show_name="驭兽结缘",funopen_seq=440,open_param="ControlBeastsContractWGView",},
["beasts_alchemy"]={name="beasts_alchemy",show_name="幻兽-内丹",funopen_seq=550,open_param="beasts_alchemy#ControlBeastsView",},
["beasts_alchemy_strengthen"]={name="beasts_alchemy_strengthen",show_name="幻兽-内丹-强化",funopen_seq=551,open_param="beasts_alchemy_strengthen#ControlBeastsCultivateWGView",},
["beasts_alchemy_succinc"]={name="beasts_alchemy_succinc",show_name="幻兽-内丹-洗练",funopen_seq=552,open_param="beasts_alchemy_succinct#ControlBeastsCultivateWGView",},
["beasts_alchemy_inherit"]={name="beasts_alchemy_inherit",show_name="幻兽-内丹-传承",funopen_seq=553,open_param="beasts_alchemy_inherit#ControlBeastsCultivateWGView",},
["beasts_alchemy_break"]={name="beasts_alchemy_break",show_name="幻兽-内丹-分解",funopen_seq=554,open_param="beasts_alchemy_break#ControlBeastsCultivateWGView",},
["ControlBeastsPrizeDrawWGView"]={name="ControlBeastsPrizeDrawWGView",show_name="幻兽奇遇",funopen_seq=441,trigger_type=1,trigger_param=950,open_param="ControlBeastsPrizeDrawWGView",task_level=87,},
["jingmai"]={name="jingmai",show_name="经脉",funopen_seq=442,trigger_type=16,trigger_param=3,trigger_param2=100,open_param="jingmai#role_view",task_level=100,},
["CultivationView"]={name="CultivationView",show_name="境界修为",funopen_seq=443,trigger_type=2,trigger_param=1240,open_param="CultivationView",task_level=110,},
["esoterica"]={name="esoterica",show_name="仙法",funopen_seq=444,trigger_param=1193,open_param="esoterica#CultivationView",fly_pos="0|7|0",},
["XiuWeiView"]={name="XiuWeiView",show_name="气衍万劫",funopen_seq=445,trigger_type=2,trigger_param=920,open_param="XiuWeiView",open_type=4,task_level=95,},
["tiandao_stone"]={name="tiandao_stone",show_name="天道石",funopen_seq=446,open_param="tiandao_stone#CultivationView",},
["CustomizedRumorsView"]={name="CustomizedRumorsView",show_name="定制传闻",funopen_seq=447,zjm_zp_btn_name="btn_customized_rumors",trigger_param2=9999,open_param="CustomizedRumorsView",},
["MechaView"]={name="MechaView",show_name="机甲",funopen_seq=448,zjm_fp_btn_name="BtnFPMechaView",zjm_fp_btn_hide=1,trigger_type=16,trigger_param2=100,open_param="MechaView",},
["mecha_fighter_plane"]={name="mecha_fighter_plane",show_name="机甲- 战机",funopen_seq=449,open_param="mecha_fighter_plane#MechaView",},
["mecha_wing"]={name="mecha_wing",show_name="机甲- 羽翼",funopen_seq=450,open_param="mecha_wing#MechaView",},
["mecha_weapon"]={name="mecha_weapon",show_name="机甲- 武器",funopen_seq=451,open_param="mecha_weapon#MechaView",},
["mecha_to_fight"]={name="mecha_to_fight",show_name="机甲- 上阵",funopen_seq=452,open_param="mecha_to_fight#MechaView",},
["mecha_equip"]={name="mecha_equip",show_name="机甲- 装备",funopen_seq=453,open_param="mecha_equip#MechaView",},
["EverythingUnderTheSun"]={name="EverythingUnderTheSun",show_name="森罗万象",funopen_seq=454,trigger_type=16,trigger_param2=100,open_param="EverythingUnderTheSun",open_type=5,},
["notice_two_chibang"]={name="notice_two_chibang",show_name="羽翼领取框",funopen_seq=455,trigger_type=2,trigger_param=180,open_param="1#chibang#notice_two",open_type=5,task_level=9,},
["notice_two_fabao"]={name="notice_two_fabao",show_name="神器领取框",funopen_seq=456,trigger_type=2,trigger_param=1240,open_param="1#fabao#notice_two",open_type=5,task_level=30,},
["notice_two_mount"]={name="notice_two_mount",show_name="坐骑领取框",funopen_seq=457,trigger_type=2,trigger_param=100,open_param="1#mount#notice_two",open_type=5,task_level=23,},
["PositionalWarfareView"]={name="PositionalWarfareView",show_name="阵地战",funopen_seq=458,trigger_type=16,trigger_param=2,trigger_param2=200,open_param="PositionalWarfareView",task_level=200,},
["LandWarFbPersonView"]={name="LandWarFbPersonView",show_name="阵地战单人本",funopen_seq=459,trigger_param2=180,close_trigger_type=16,close_trigger_param=2,open_param="LandWarFbPersonView",task_level=180,},
["LongYunZhanLingTaskView"]={name="LongYunZhanLingTaskView",show_name="龙云战令",funopen_seq=460,trigger_type=16,trigger_param2=9999,open_param="LongYunZhanLingTaskView",open_type=1,},
["WuHunPrerogativeView"]={name="WuHunPrerogativeView",show_name="武魂特权",funopen_seq=461,trigger_type=16,open_param="WuHunPrerogativeView",open_type=1,},
["beasts_depose"]={name="beasts_depose",show_name="幻兽献祭",funopen_seq=462,trigger_type=16,open_param="beasts_depose#ControlBeastsView",},
["MengLingView"]={name="MengLingView",show_name="灵阵",funopen_seq=463,zjm_fp_btn_name="BtnFPMengLing",icon="a3_open_lm",trigger_param2=150,open_param="MengLingView",fly_pos="0|-35|0",},
["LordEveryDayShopView"]={name="LordEveryDayShopView",show_name="领主商店",funopen_seq=464,open_param="LordEveryDayShopView",open_type=1,task_level=120,},
["FashionExchangeShopView"]={name="FashionExchangeShopView",show_name="璇玑云楼",funopen_seq=465,trigger_type=2,trigger_param=1193,open_param="FashionExchangeShopView",task_level=110,},
["tianshen_temple"]={name="tianshen_temple",show_name="神灵殿",funopen_seq=466,open_param="tianshen_temple#TianShenView",},
["WorldTreasureView"]={name="WorldTreasureView",show_name="天财地宝",funopen_seq=467,trigger_param2=120,open_param="WorldTreasureView",task_level=200,},
["BossNewPrivilegeView"]={name="BossNewPrivilegeView",show_name="灭魔神雷-入口",funopen_seq=475,open_param="BossNewPrivilegeView",task_level=120,},
["boss_mabi_skill"]={name="boss_mabi_skill",show_name="猎魔宝典",funopen_seq=476,trigger_param2=130,open_param="boss_mabi_skill#BossNewPrivilegeView",},
["boss_godwar"]={name="boss_godwar",show_name="战神特权",funopen_seq=477,open_param="boss_godwar#BossNewPrivilegeView",task_level=240,},
["boss_zaibaoyici"]={name="boss_zaibaoyici",show_name="双倍掉落",funopen_seq=478,open_param="boss_zaibaoyici#BossNewPrivilegeView",open_type=4,task_level=160,},
["boss_kill_every"]={name="boss_kill_every",show_name="灭魔神雷",funopen_seq=479,trigger_param=1,open_param="boss_kill_every#BossNewPrivilegeView",task_level=120,},
["BossZhanLingView"]={name="BossZhanLingView",show_name="猎魔战令",funopen_seq=480,trigger_param2=100,open_param="BossZhanLingView",},
["SecretRecordView"]={name="SecretRecordView",show_name="紫云秘录",funopen_seq=481,trigger_param=115,open_param="SecretRecordView",open_type=1,task_level=100,},
["arena_tianti"]={name="arena_tianti",show_name="天梯争霸",funopen_seq=482,open_param="arena_tianti#act_jjc",},
["CrossAirWarView"]={name="CrossAirWarView",show_name="跨服空战",funopen_seq=483,},
["boss_entrance"]={name="boss_entrance",show_name="boss入口",funopen_seq=484,trigger_param=44,},
["BillionSubsidy"]={name="BillionSubsidy",show_name="百亿补贴",funopen_seq=485,open_param="BillionSubsidy",},
["billion_subsidy_bybt"]={name="billion_subsidy_bybt",show_name="百亿补贴-百亿补贴",funopen_seq=486,open_param="billion_subsidy_bybt#BillionSubsidy",},
["billion_subsidy_xdzk"]={name="billion_subsidy_xdzk",show_name="百亿补贴-限定折扣",funopen_seq=487,open_param="billion_subsidy_xdzk#BillionSubsidy",},
["billion_subsidy_fyms"]={name="billion_subsidy_fyms",show_name="百亿补贴-付1买3",funopen_seq=488,open_param="billion_subsidy_fyms#BillionSubsidy",},
["billion_subsidy_jkjzc"]={name="billion_subsidy_jkjzc",show_name="百亿补贴-九块九专场",funopen_seq=489,open_param="billion_subsidy_jkjzc#BillionSubsidy",},
["billion_subsidy_lysd"]={name="billion_subsidy_lysd",show_name="百亿补贴-灵玉商店",funopen_seq=490,open_param="billion_subsidy_lysd#BillionSubsidy",},
["billion_subsidy_dezg"]={name="billion_subsidy_dezg",show_name="百亿补贴-大额直购",funopen_seq=491,open_param="billion_subsidy_dezg#BillionSubsidy",},
["billion_subsidy_vip"]={name="billion_subsidy_vip",show_name="百亿补贴-会员专场",funopen_seq=492,open_param="billion_subsidy_vip#BillionSubsidy",},
["billion_subsidy_drpt"]={name="billion_subsidy_drpt",show_name="百亿补贴-多人拼团",funopen_seq=493,open_param="billion_subsidy_drpt#BillionSubsidy",},
["billion_subsidy_mrsy"]={name="billion_subsidy_mrsy",show_name="百亿补贴-每日试用",funopen_seq=494,open_param="billion_subsidy_mrsy#BillionSubsidy",},
["DujieView"]={name="DujieView",show_name="渡劫",funopen_seq=495,trigger_type=2,trigger_param=1470,open_param="DujieView",task_level=140,},
["jingjie"]={name="jingjie",show_name="仙阶",funopen_seq=496,trigger_type=16,trigger_param=999,trigger_param2=9999,open_param="jingjie#role_view",},
["LifeIndulgenceView"]={name="LifeIndulgenceView",show_name="终身特惠",funopen_seq=497,trigger_param2=118,open_param="LifeIndulgenceView",task_level=100,},
["HundredEquipView"]={name="HundredEquipView",show_name="百倍爆装",funopen_seq=498,trigger_type=1,trigger_param=990,open_param="HundredEquipView",open_type=4,task_level=150,},
["YanYuGePrivilegeView"]={name="YanYuGePrivilegeView",show_name="烟雨特权",funopen_seq=499,open_param="YanYuGePrivilegeView",},
["YanYuGeExchangeShopView"]={name="YanYuGeExchangeShopView",show_name="烟雨兑换商店",funopen_seq=500,open_param="YanYuGeExchangeShopView",},
["YanYuGeEntranceView"]={name="YanYuGeEntranceView",show_name="烟雨阁",funopen_seq=501,zjm_fp_btn_hide=1,open_param="YanYuGeEntranceView",},
["YanYuGeNobleView"]={name="YanYuGeNobleView",show_name="烟雨贵族",funopen_seq=502,open_param="YanYuGeNobleView",},
["YanYuGePrivilegeYYTQ"]={name="YanYuGePrivilegeYYTQ",show_name="烟雨阁 - 烟雨特权",funopen_seq=503,open_param="yanyuge_privilege_yytq#YanYuGePrivilegeView",},
["YanYuGePrivilegeWLTZ"]={name="YanYuGePrivilegeWLTZ",show_name="烟雨阁 - 未来投资",funopen_seq=504,trigger_param=3,open_param="yanyuge_privilege_wltz#YanYuGePrivilegeView",},
["YanYuGePrivilegeZZTQ"]={name="YanYuGePrivilegeZZTQ",show_name="烟雨阁 - 赞助特权",funopen_seq=505,open_param="yanyuge_privilege_zztq#YanYuGePrivilegeView",},
["YanYuGePrivilegeNZTQ"]={name="YanYuGePrivilegeNZTQ",show_name="烟雨阁 - 哪吒特权",funopen_seq=506,open_param="yanyuge_privilege_nztq#YanYuGePrivilegeView",},
["YanYuGeShopTZSD"]={name="YanYuGeShopTZSD",show_name="烟雨阁 - 投资商店",funopen_seq=507,trigger_param2=101,open_param="yanyuge_shop_tzsd#YanYuGePrivilegeView",task_level=101,},
["YanYuGeShopNWSD"]={name="YanYuGeShopNWSD",show_name="烟雨阁 - 女娲商店",funopen_seq=508,open_param="yanyuge_shop_nwsd#YanYuGePrivilegeView",},
["FanRenXiuZhenRewardView"]={name="FanRenXiuZhenRewardView",show_name="登神豪礼",funopen_seq=509,trigger_type=16,trigger_param=999,trigger_param2=9999,open_param="FanRenXiuZhenRewardView",open_type=1,},
["HolyBeastsView"]={name="HolyBeastsView",show_name="圣兽",funopen_seq=510,open_param="HolyBeastsView",open_type=1,},
["holy_beasts_spirit"]={name="holy_beasts_spirit",show_name="圣兽-圣魂",funopen_seq=511,trigger_type=16,trigger_param=5,trigger_param2=150,open_param="holy_beasts_spirit#HolyBeastsView",task_level=150,},
["conquest_war_kfkz"]={name="conquest_war_kfkz",show_name="大闹天宫",funopen_seq=512,open_param="conquest_war_kfkz#ConquestWarView",},
["PrivilegeCollectionView"]={name="PrivilegeCollectionView",show_name="特权",funopen_seq=513,trigger_param=120,open_param="PrivilegeCollectionView",task_level=120,},
["pri_col_zztq"]={name="pri_col_zztq",show_name="特权-至尊特权",funopen_seq=514,open_param="pri_col_zztq#PrivilegeCollectionView",},
["pri_col_nstq"]={name="pri_col_nstq",show_name="特权-女神特权",funopen_seq=515,open_param="pri_col_nstq#PrivilegeCollectionView",},
["pri_col_zjtq"]={name="pri_col_zjtq",show_name="特权-终极特权",funopen_seq=516,trigger_type=27,trigger_param=300,trigger_param2=1,trigger_param3=120,open_param="pri_col_zjtq#PrivilegeCollectionView",},
["pri_col_sqcy"]={name="pri_col_sqcy",show_name="特权-异火特权",funopen_seq=517,trigger_type=16,trigger_param=2,trigger_param2=120,open_param="pri_col_sqcy#PrivilegeCollectionView",task_level=120,},
["DressingRoleDiyView"]={name="DressingRoleDiyView",show_name="易容",funopen_seq=518,open_param="DressingRoleDiyView",},
["DragonTrialView"]={name="DragonTrialView",show_name="五气朝元",funopen_seq=519,open_param="DragonTrialView",open_type=1,task_level=150,},
["GameAssistant"]={name="GameAssistant",show_name="小助手",funopen_seq=520,open_param="GameAssistant",},
["WangqiView"]={name="WangqiView",show_name="望气",funopen_seq=521,trigger_type=2,trigger_param=810,task_level=1,},
["HalfPurchaseView"]={name="HalfPurchaseView",show_name="五折充值",funopen_seq=522,open_param="HalfPurchaseView",},
["HundredEquipRatio"]={name="HundredEquipRatio",show_name="洪荒龙巢",funopen_seq=523,open_param="HundredEquipRatio",},
["RechargeVolumeRewardView"]={name="RechargeVolumeRewardView",show_name="财神爷-提取奖励",funopen_seq=524,trigger_type=16,trigger_param2=9999,open_param="RechargeVolumeRewardView",},
["NewHuanHuaFetterView"]={name="NewHuanHuaFetterView",show_name="换色羁绊",funopen_seq=525,open_param="NewHuanHuaFetterView",},
["Flower"]={name="Flower",show_name="赠花",funopen_seq=526,trigger_param=160,open_param="Flower",open_type=4,task_level=130,},
["flower_upgrade"]={name="flower_upgrade",show_name="赠花突破",funopen_seq=527,open_param="flower_upgrade#Flower",},
["flower_send"]={name="flower_send",funopen_seq=528,open_param="flower_send#Flower",},
["recharge_leichong"]={name="recharge_leichong",show_name="VIP-每日累充",funopen_seq=531,open_param="recharge_leichong#recharge",},
["recharge_xiaofei"]={name="recharge_xiaofei",show_name="VIP-每日累消",funopen_seq=532,trigger_type=16,trigger_param=2,trigger_param2=150,open_param="recharge_xiaofei#recharge",task_level=150,},
["billion_subsidy_rapidly"]={name="billion_subsidy_rapidly",show_name="百亿补贴-秒杀礼包",funopen_seq=533,trigger_type=16,trigger_param=1,trigger_param2=150,open_param="billion_subsidy_rapidly#BillionSubsidy",task_level=150,},
["billion_subsidy_dailygift"]={name="billion_subsidy_dailygift",show_name="百亿补贴-每日礼包",funopen_seq=534,open_param="billion_subsidy_dailygift#BillionSubsidy",},
["CrossTreasureView"]={name="CrossTreasureView",show_name="域外夺宝",funopen_seq=535,open_param="CrossTreasureView",},
["billion_subsidy_yiyuan"]={name="billion_subsidy_yiyuan",show_name="百亿补贴-一元福利",funopen_seq=536,open_param="BillionSubsidy#billion_subsidy_yiyuan",open_type=1,},
["ArtifactAffection"]={name="ArtifactAffection",show_name="双修好感",funopen_seq=537,zjm_fp_btn_hide=0,open_param="ArtifactAffectionView",task_level=100,},
["pri_col_shtq"]={name="pri_col_shtq",show_name="特权-守护特权",funopen_seq=538,open_param="pri_col_shtq#PrivilegeCollectionView",task_level=120,},
["treasurehunt_thunder"]={name="treasurehunt_thunder",show_name="神龙密藏",funopen_seq=539,trigger_type=16,trigger_param=3,trigger_param2=120,open_param="treasurehunt_thunder#TreasureHunt",task_level=120,}
},

funopen_list_meta_table_map={
["rank_jinjie"]="rank_jianzhen",	-- depth:1
["worserv_boss_hmsy"]="worserv_boss_sgyj",	-- depth:1
["rank_baby"]="rank_jinjie",	-- depth:2
["NewFightMountView"]="XiuXianShiLian",	-- depth:1
["equipmeng_yuling_set"]="equipment_zhushen",	-- depth:1
["equipment_zhushentai"]="equipment_zhushen",	-- depth:1
["national_war"]="XiuXianShiLian",	-- depth:1
["country_map_act_view"]="XiuXianShiLian",	-- depth:1
["main_ui_Target"]="XiuXianShiLian",	-- depth:1
["EquipmentIntegration"]="worserv_boss_sgyj",	-- depth:1
["arena_field1v1"]="XiuXianShiLian",	-- depth:1
["arena_1v1jfreward"]="arena_1v1paragraph",	-- depth:1
["FairyLandEquipmentNoticeView"]="XiuXianShiLian",	-- depth:1
["arena_1v1rank"]="arena_1v1paragraph",	-- depth:1
["shen_equip"]="XiuXianShiLian",	-- depth:1
["CustomActionView"]="worserv_boss_sgyj",	-- depth:1
["ExpPoolView"]="XiuXianShiLian",	-- depth:1
["everyday_recharge_zhigou"]="XiuXianShiLian",	-- depth:1
["shop_daoju"]="shop_hot",	-- depth:1
["TianxianPavilionView"]="XiuXianShiLian",	-- depth:1
["YinianMagicView"]="worserv_boss_sgyj",	-- depth:1
["shop_moyu"]="shop_hot",	-- depth:1
["worlds_no1_view"]="XiuXianShiLian",	-- depth:1
["hide_task"]="XiuXianShiLian",	-- depth:1
["role_sit"]="XiuXianShiLian",	-- depth:1
["equipmeng_yuling_strength"]="equipment_zhushen",	-- depth:1
["role_beidong_skill"]="XiuXianShiLian",	-- depth:1
["custom_action"]="worserv_boss_sgyj",	-- depth:1
["other_compose_daoju"]="other_compose_eq_hecheng_three",	-- depth:1
["recharge"]="recharge_vip",	-- depth:1
["skill_beidong"]="wenxin_remind",	-- depth:1
["skill_zhudong"]="wenxin_remind",	-- depth:1
["country_map_act_cross_spy"]="cross_spy",	-- depth:1
["country_map_xingtu"]="cross_spy",	-- depth:1
["country_map_secret_area"]="cross_spy",	-- depth:1
["other_compose_shengyin"]="other_compose_eq_hecheng_three",	-- depth:1
["wuhun_front"]="wuhun_tower",	-- depth:1
["guild_history"]="guild_info",	-- depth:1
["recharge_tqtz"]="wenxin_remind",	-- depth:1
["rolebag_Target"]="main_ui_Target",	-- depth:2
["welfare_libao"]="wenxin_remind",	-- depth:1
["country_map_actshow"]="cross_spy",	-- depth:1
[" country_map_xianwei_view"]="cross_spy",	-- depth:1
["arena_kf1v1"]="arena_1v1paragraph",	-- depth:1
["rank_fabao"]="rank_jinjie",	-- depth:2
["rank_lingchong"]="rank_yuyi",	-- depth:1
["HmGodView"]="hide_task",	-- depth:2
["everyday_recharge_leichong"]="RechargeVolumeRewardView",	-- depth:1
["rank_fanrenxiuzhen"]="rank_fabao",	-- depth:3
["rank_guaji"]="rank_fabao",	-- depth:3
["CrossTreasureView"]="RechargeVolumeRewardView",	-- depth:1
["rank_meili"]="rank_lingchong",	-- depth:2
["rank_xianmeng"]="rank_equip",	-- depth:1
["shj_tianshen"]="shj_chongwu",	-- depth:1
["shj_tianjipu"]="shj_chongwu",	-- depth:1
["other_compose_eq_hecheng_one"]="other_compose_eq_hecheng_two",	-- depth:1
["first_recharge_zhigou"]="equipxunbao_exchange",	-- depth:1
["everyday_recharge_xiaofei"]="RechargeVolumeRewardView",	-- depth:1
["fl_activity"]="open_server_compete",	-- depth:1
["guild_boss"]="guild_guildlist",	-- depth:1
["dragon_temp_rank"]="dragon_temp_draw",	-- depth:1
["guild_activity"]="guild_guildlist",	-- depth:1
["guild_shouhu"]="guild_guildlist",	-- depth:1
["daohang_keling"]="dragon_temp_draw",	-- depth:1
["daohang_qianghua"]="dragon_temp_draw",	-- depth:1
["daohang_jinjie"]="dragon_temp_draw",	-- depth:1
["daohang_keyin"]="dragon_temp_draw",	-- depth:1
["daohang_kaiguang"]="dragon_temp_draw",	-- depth:1
["new_appearance_mount_hualing"]="new_appearance_lingchong_hualing",	-- depth:1
["ScreenShotView"]="HmGodView",	-- depth:3
["CustomizedSuitView"]="RechargeVolumeRewardView",	-- depth:1
["shileding"]="HmGodView",	-- depth:3
["near_player"]="HmGodView",	-- depth:3
["teamicon"]="HmGodView",	-- depth:3
["activity_109"]="HmGodView",	-- depth:3
["boss_entrance"]="ExpPoolView",	-- depth:2
["tiandao_stone"]="other_compose_taozhuang",	-- depth:1
["mecha_fighter_plane"]="dragon_temp_draw",	-- depth:1
["mecha_wing"]="dragon_temp_draw",	-- depth:1
["mecha_weapon"]="dragon_temp_draw",	-- depth:1
["mecha_to_fight"]="dragon_temp_draw",	-- depth:1
["mecha_equip"]="dragon_temp_draw",	-- depth:1
["WardrobeView"]="HmGodView",	-- depth:3
["GuiXuDreamView"]="ExpPoolView",	-- depth:2
["welfare_vipgift"]="welfare_qiandao",	-- depth:1
["dragon_temp_equip"]="dragon_temp_draw",	-- depth:1
["huakun_equip_strength"]="huakun_equip_star",	-- depth:1
["huakun_equip_suit"]="huakun_equip_star",	-- depth:1
["guild_wage"]="guild_info",	-- depth:1
["rolebag_bag_all"]="wenxin_remind",	-- depth:1
["rolebag_stuff"]="wenxin_remind",	-- depth:1
["rolebag_storge"]="wenxin_remind",	-- depth:1
["welfare_wekqiandao"]="guild_guildlist",	-- depth:1
["guild_redpacket"]="guild_info",	-- depth:1
["guild_juanxian"]="guild_info",	-- depth:1
["AlchemyView"]="cross_spy",	-- depth:1
["pri_col_nstq"]="RechargeVolumeRewardView",	-- depth:1
["huakun_equip_bag"]="huakun_equip_star",	-- depth:1
["huakun_equip_shengpin"]="huakun_equip_star",	-- depth:1
["pri_col_zztq"]="PrivilegeCollectionView",	-- depth:1
["NewTeamView"]="crossTeamView",	-- depth:1
["five_elements_cangming"]="dragon_temp_draw",	-- depth:1
["five_elements_overview"]="dragon_temp_draw",	-- depth:1
["five_elements_knapsack"]="dragon_temp_draw",	-- depth:1
["five_elements_talent"]="dragon_temp_draw",	-- depth:1
["guild_skill"]="guild_info",	-- depth:1
["welfare_upgrade"]="welfare_qiandao",	-- depth:1
["marry_baoxia"]="marry_jiehun",	-- depth:1
["marry_baby"]="marry_jiehun",	-- depth:1
["marry_hunjie"]="marry_jiehun",	-- depth:1
["marry_profess_wall"]="marry_jiehun",	-- depth:1
["rank_zhanli"]="rank_meili",	-- depth:3
["arena_tianti"]="GuiXuDreamView",	-- depth:3
["CrossAirWarView"]="arena_tianti",	-- depth:4
["YanYuGePrivilegeNZTQ"]="jingjie",	-- depth:1
["conquest_war_kfkz"]="GuiXuDreamView",	-- depth:3
["DressingRoleDiyView"]="WardrobeView",	-- depth:4
["flower_upgrade"]="Flower",	-- depth:1
["flower_send"]="Flower",	-- depth:1
["season_privilege_shop"]="sup_privilege_shop",	-- depth:1
["HongHuangGoodCoremonyView"]="ChaoticVipSpecialView",	-- depth:1
["rank_level"]="rank_zhanli",	-- depth:4
["ShenBingLinZhiSkillView"]="LongYunZhanLingTaskView",	-- depth:1
["skill_view"]="HmGodView",	-- depth:3
["society_team"]="HmGodView",	-- depth:3
["society"]="HmGodView",	-- depth:3
["society_mail"]="HmGodView",	-- depth:3
["society_friend"]="HmGodView",	-- depth:3
["society_enemy"]="HmGodView",	-- depth:3
["treasurehunt_zhizun"]="treasurehunt_dianfeng",	-- depth:1
["ChaoticPurchaseView"]="HongHuangClassicView",	-- depth:1
["privilegeshop"]="sup_privilege_shop",	-- depth:1
["JumpOpen"]="WangqiView",	-- depth:1
["CashPointView"]="LongYunZhanLingTaskView",	-- depth:1
["XianyuTrunTableView"]="GuiXuDreamView",	-- depth:3
["open_server_recharge"]="open_server_compete",	-- depth:1
["open_server_activity"]="fl_activity",	-- depth:2
["counrty_map_task_view"]="FiveElementsTreasuryView",	-- depth:1
["counrty_map_officer_view"]="FiveElementsTreasuryView",	-- depth:1
["role"]="HmGodView",	-- depth:3
["WingLinZhiSkillView"]="LongYunZhanLingTaskView",	-- depth:1
["role_view"]="role",	-- depth:4
["role_intro"]="HmGodView",	-- depth:3
["role_atr"]="role_intro",	-- depth:4
["sevenday"]="CapabilityContrastView ",	-- depth:1
["change_head_view"]="GuiXuDreamView",	-- depth:3
["counrty_map_news_view"]="FiveElementsTreasuryView",	-- depth:1
["everyday_recharge_lianchong"]="jingjie",	-- depth:1
["new_appearance_zhuangban_shouhuan"]="new_appearance_zhuangban_belt",	-- depth:1
["new_appearance_zhuangban_mask"]="new_appearance_zhuangban_belt",	-- depth:1
["guild"]="AccumulativeLoginView",	-- depth:1
["JianZhenLinZhiSkillView"]="LongYunZhanLingTaskView",	-- depth:1
["new_appearance_zhuangban_weiba"]="new_appearance_zhuangban_belt",	-- depth:1
["market_sell"]="market_buy",	-- depth:1
["market_record"]="market_buy",	-- depth:1
["market_want_list"]="market_buy",	-- depth:1
["market_my_want"]="market_buy",	-- depth:1
["market_country_auction"]="market_auction_record",	-- depth:1
["market_guild_auction"]="market_auction_record",	-- depth:1
["market_cross_server"]="market_auction_record",	-- depth:1
["market_my_auction"]="market_auction_record",	-- depth:1
["new_appearance_zhuangban_foot"]="new_appearance_zhuangban_belt",	-- depth:1
["welfare_dailyfind"]="bizuo_time_table",	-- depth:1
["everyday_recharge_yingji"]="LongYunZhanLingTaskView",	-- depth:1
["everyday_recharge_dailygift"]="GuiXuDreamView",	-- depth:3
["FaBaoLinZhiSkillView"]="LongYunZhanLingTaskView",	-- depth:1
["everyday_recharge_rapidly"]="GuiXuDreamView",	-- depth:3
["mount_equip_shengpin"]="mount_equip_bag",	-- depth:1
["mount_equip_star"]="mount_equip_bag",	-- depth:1
["mount_equip_suit"]="mount_equip_bag",	-- depth:1
["pet_equip_shengpin"]="pet_equip_bag",	-- depth:1
["pet_equip_star"]="pet_equip_bag",	-- depth:1
["pet_equip_strength"]="pet_equip_bag",	-- depth:1
["pet_equip_suit"]="pet_equip_bag",	-- depth:1
["BeastsCulture"]="BeastsBattle",	-- depth:1
["BeastsRefining"]="BeastsBattle",	-- depth:1
["mount_equip_strength"]="mount_equip_bag",	-- depth:1
["new_appearance_kun_upstar"]="new_appearance_kun_upgrade",	-- depth:1
["sxbs_xianyi"]="sxbs_shenwu",	-- depth:1
["sxbs_fabao"]="sxbs_shenwu",	-- depth:1
["new_appearance_mount_upgrade"]="new_appearance_mount_upstar",	-- depth:1
["new_appearance_lingchong_upgrade"]="new_appearance_lingchong_upstar",	-- depth:1
["sxbs_mount"]="sxbs_pet",	-- depth:1
["ControlBeastsContractWGView"]="BeastsBattle",	-- depth:1
["BeastsCompose"]="ControlBeastsView",	-- depth:1
["BeastsKing"]="BeastsBattle",	-- depth:1
["BeastsStable"]="BeastsBattle",	-- depth:1
["new_appearance_zhuangban_photoframe"]="new_appearance_waiguan_body",	-- depth:1
["recharge_leichong"]="recharge_xiaofei",	-- depth:1
["rank_bianshen"]="tianshen_huanhua_jinjie",	-- depth:1
["TianShenLingHeView"]="tianshen_bagua",	-- depth:1
["tianshen_linghe_uplevel"]="tianshen_bagua",	-- depth:1
["billion_subsidy_dezg"]="FanRenXiuZhenRewardView",	-- depth:1
["billion_subsidy_drpt"]="FanRenXiuZhenRewardView",	-- depth:1
["billion_subsidy_mrsy"]="FanRenXiuZhenRewardView",	-- depth:1
["new_appearance_zhuangban_bubble"]="new_appearance_waiguan_body",	-- depth:1
["fuwen_fenjie"]="fuwen_duihuan",	-- depth:1
["fuwen_xiangqian"]="fuwen_duihuan",	-- depth:1
["fuwen"]="fuwen_duihuan",	-- depth:1
["billion_subsidy_dailygift"]="billion_subsidy_rapidly",	-- depth:1
["tianshen_linghe_compose"]="tianshen_bagua",	-- depth:1
["tianshen_linghe_reslove"]="tianshen_bagua",	-- depth:1
["GoldStoneView"]="jingmai",	-- depth:1
["billion_subsidy_yiyuan"]="billion_subsidy_rapidly",	-- depth:1
["ArtifactAffection"]="FiveElementsTreasuryView",	-- depth:1
["YanYuGeShopTZSD"]="sxbs_pet",	-- depth:1
["pri_col_zjtq"]="pri_col_sqcy",	-- depth:1
["billion_subsidy_bybt"]="billion_subsidy_yiyuan",	-- depth:2
["billion_subsidy_xdzk"]="billion_subsidy_yiyuan",	-- depth:2
["billion_subsidy_fyms"]="billion_subsidy_yiyuan",	-- depth:2
["billion_subsidy_jkjzc"]="billion_subsidy_yiyuan",	-- depth:2
["billion_subsidy_lysd"]="billion_subsidy_yiyuan",	-- depth:2
["billion_subsidy_vip"]="billion_subsidy_yiyuan",	-- depth:2
["LifeIndulgenceView"]="sxbs_pet",	-- depth:1
["YanYuGePrivilegeView"]="YanYuGeShopTZSD",	-- depth:2
["BossZhanLingView"]="LifeIndulgenceView",	-- depth:2
["boss_kill_every"]="new_appearance_upgrade_jianzhen",	-- depth:1
["boss_godwar"]="boss_kill_every",	-- depth:2
["boss_mabi_skill"]="boss_kill_every",	-- depth:2
["YanYuGeExchangeShopView"]="YanYuGeShopTZSD",	-- depth:2
["BossNewPrivilegeView"]="BossZhanLingView",	-- depth:3
["WorldTreasureView"]="sxbs_pet",	-- depth:1
["YanYuGeEntranceView"]="FanRenXiuZhenRewardView",	-- depth:1
["YanYuGeNobleView"]="YanYuGeShopTZSD",	-- depth:2
["YanYuGePrivilegeYYTQ"]="YanYuGeShopTZSD",	-- depth:2
["YanYuGePrivilegeWLTZ"]="YanYuGeShopTZSD",	-- depth:2
["YanYuGePrivilegeZZTQ"]="YanYuGeShopTZSD",	-- depth:2
["GodOfWealthView"]="god_of_wealth",	-- depth:1
["YanYuGeShopNWSD"]="YanYuGeShopTZSD",	-- depth:2
["HolyBeastsView"]="holy_beasts_spirit",	-- depth:1
["DragonTrialView"]="pri_col_sqcy",	-- depth:1
["BillionSubsidy"]="billion_subsidy_yiyuan",	-- depth:2
["HolyWeapon"]="fight_soul_cuizhuo",	-- depth:1
["tianshen_heji"]="tianshen_huanhua_jinjie",	-- depth:1
["wuhun_details"]="JingHuaShuiYue",	-- depth:1
["TianShenShuangShengView"]="tianshen_heji",	-- depth:2
["NewAppearanceWGView"]="new_appearance_waiguan_body",	-- depth:1
["TianShenHuamoView"]="tianshen_heji",	-- depth:2
["TianShenLingHeDrawView"]="tianshen_bagua",	-- depth:1
["tianshen_shenQi"]="HolyBeastsView",	-- depth:2
["tianshen_shentong"]="tianshen_shenQi",	-- depth:3
["tianshen_shenshi"]="tianshen_shenQi",	-- depth:3
["TianShenActivationView"]="tianshen_shenQi",	-- depth:3
["tianshen_activation"]="tianshen_shenQi",	-- depth:3
["TianShenView"]="HundredEquipView",	-- depth:1
["TreasureHunt"]="sxbs_beishi",	-- depth:1
["counrty_map_map_view"]="BossZhanLingView",	-- depth:3
["ShenJiTianCiView"]="ShenJi",	-- depth:1
["fubenpanel_exp"]="boss_vip",	-- depth:1
["fubenpanel_equip_high"]="ProfessWallView",	-- depth:1
["fubenpanel_welkin"]="HundredEquipView",	-- depth:1
["fubenpanel_bootybay"]="FanRenXiuZhenRewardView",	-- depth:1
["fubenpanel_beauty"]="fubenpanel_wuhun",	-- depth:1
["equipment_lingyu"]="tianshen_bagua",	-- depth:1
["fight_soul_bone"]="HolyWeapon",	-- depth:2
["MainUILongZhuSkill"]="GoldStoneView",	-- depth:2
["marry"]="ProfessWallView",	-- depth:1
["HomesView"]="marry",	-- depth:2
["NewAppearanceHaloWGView"]="new_appearance_upgrade_jianzhen",	-- depth:1
["treasurehunt_equip"]="TreasureHunt",	-- depth:2
["pri_col_shtq"]="WorldTreasureView",	-- depth:2
["new_appearance_upgrade_shenbing"]="treasurehunt_fuwen",	-- depth:1
["SupremeFieldsWGView"]="tianshen_bagua",	-- depth:1
["new_appearance_upgrade_mount"]="new_appearance_mount_upstar",	-- depth:1
["MountEquipView"]="tianshen_bagua",	-- depth:1
["LingChongEquipView"]="SupremeFieldsWGView",	-- depth:2
["DarkWeapon"]="HolyWeapon",	-- depth:2
["BoundlessJoyView"]="LongYunZhanLingTaskView",	-- depth:1
["LongXiView"]="tianshen_bagua",	-- depth:1
["dragon_king_token"]="LongXiView",	-- depth:2
["new_appearance_upgrade_fabao"]="new_appearance_waiguan_fabao",	-- depth:1
["super_dragon_seal"]="LongXiView",	-- depth:2
["rolebag_longzhu"]="jingmai",	-- depth:1
["HolyHeavenlyDomainView"]="ShenJi",	-- depth:1
["act_jjc"]="HundredEquipView",	-- depth:1
["new_appearance_upgrade_wing"]="new_appearance_waiguan_wing",	-- depth:1
["sky_curtain"]="tianshen_bagua",	-- depth:1
["esoterica"]="bizuo",	-- depth:1
["CustomizedRumorsView"]="JingHuaShuiYue",	-- depth:1
["zhanling"]="tianshen_heji",	-- depth:2
["FiveElementsView"]="JingHuaShuiYue",	-- depth:1
["MultiFunctionView"]="MechaView",	-- depth:1
["shenshou"]="SupremeFieldsWGView",	-- depth:2
["fubenpanel_zhuogui"]="tianshen_bagua",	-- depth:1
["fubenpanel_pet"]="fubenpanel_zhuogui",	-- depth:2
["equipment_yingji"]="new_appearance_multi_mount",	-- depth:1
["FightSoulView"]="fight_soul_bone",	-- depth:3
["ArtifactView"]="ArtifactAffection",	-- depth:2
["equipment_suit"]="equipment_shengpin",	-- depth:1
["sixiang_call_sx"]="ShenJiTianCiView",	-- depth:2
["country_map_act_boss"]="country_map_act_ship",	-- depth:1
["country_map_act_farm"]="country_map_act_boss",	-- depth:2
["ShiTianSuitView"]="FightSoulView",	-- depth:4
["HolyDarkWeaponEnter"]="FightSoulView",	-- depth:4
["boss_dabao"]="fubenpanel_copper",	-- depth:1
["boss_personal"]="boss_dabao",	-- depth:2
["LandWarFbPersonView"]="boss_kill_every",	-- depth:2
["equipment_baoshi_jl"]="LongXiView",	-- depth:2
["new_appearance_waiguan_weapon"]="MountEquipView",	-- depth:2
["skill_awake"]="equipment_baoshi_jl",	-- depth:3
["MengLingView"]="sky_curtain",	-- depth:2
["skill_talent"]="equipment_lingyu",	-- depth:2
["ThunderManaSelectView"]="NewAppearanceHaloWGView",	-- depth:2
["xianjie_boss"]="equipment_baoshi_jl",	-- depth:3
["world_new_shenyuan_boss"]="xianjie_boss",	-- depth:4
["worerv_boss_mh"]="xianjie_boss",	-- depth:4
["worldserver"]="world_new_shenyuan_boss",	-- depth:5
["equipment_xilian"]="equipment_suit",	-- depth:2
["worldboss"]="equipment_baoshi_jl",	-- depth:3
},
advance_notice={

},

advance_notice_meta_table_map={
},
funopen_list_default_table={name="shen_equip",show_name="无双挑战",funopen_seq=114,zjm_zp_btn_name="",zjm_fp_btn_name="",icon="",name_icon="",zjm_fp_btn_hide="",cross_gs_level=0,trigger_type=3,trigger_param=9999,trigger_param2="",trigger_param3="",with_param="",with_param2="",parent_fun="",child_fun="",vip_lev="",close_trigger_type="",close_trigger_param="",open_param="",open_type=3,task_level=9999,zjm_open_btn_name="",desc_txt="",open_audio="",fly_pos="",},

advance_notice_default_table={}

}


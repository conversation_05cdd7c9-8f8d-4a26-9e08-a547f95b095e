-- K-开服活动配置.xls
local item_table={
[1]={item_id=26130,num=200,is_bind=1},
[2]={item_id=38980,num=1,is_bind=1},
[3]={item_id=26151,num=1,is_bind=1},
[4]={item_id=18661,num=1,is_bind=1},
[5]={item_id=57834,num=1,is_bind=1},
[6]={item_id=26368,num=30,is_bind=1},
[7]={item_id=26130,num=100,is_bind=1},
[8]={item_id=38981,num=1,is_bind=1},
[9]={item_id=26130,num=50,is_bind=1},
[10]={item_id=18660,num=1,is_bind=1},
[11]={item_id=26344,num=300,is_bind=1},
[12]={item_id=26130,num=30,is_bind=1},
[13]={item_id=18659,num=1,is_bind=1},
[14]={item_id=26368,num=20,is_bind=1},
[15]={item_id=26344,num=200,is_bind=1},
[16]={item_id=26130,num=10,is_bind=1},
[17]={item_id=18679,num=1,is_bind=1},
[18]={item_id=26368,num=10,is_bind=1},
[19]={item_id=26344,num=100,is_bind=1},
[20]={item_id=26130,num=300,is_bind=1},
[21]={item_id=38969,num=1,is_bind=1},
[22]={item_id=44507,num=1,is_bind=1},
[23]={item_id=26559,num=1,is_bind=1},
[24]={item_id=22533,num=5,is_bind=1},
[25]={item_id=38970,num=1,is_bind=1},
[26]={item_id=26553,num=1,is_bind=1},
[27]={item_id=22533,num=3,is_bind=1},
[28]={item_id=38971,num=1,is_bind=1},
[29]={item_id=43661,num=1,is_bind=1},
[30]={item_id=22533,num=2,is_bind=1},
[31]={item_id=26551,num=1,is_bind=1},
[32]={item_id=22533,num=1,is_bind=1},
[33]={item_id=26546,num=1,is_bind=1},
[34]={item_id=38973,num=1,is_bind=1},
[35]={item_id=18666,num=1,is_bind=1},
[36]={item_id=26194,num=3,is_bind=1},
[37]={item_id=26506,num=3,is_bind=1},
[38]={item_id=38974,num=1,is_bind=1},
[39]={item_id=18665,num=1,is_bind=1},
[40]={item_id=26194,num=2,is_bind=1},
[41]={item_id=26506,num=1,is_bind=1},
[42]={item_id=38975,num=1,is_bind=1},
[43]={item_id=26194,num=1,is_bind=1},
[44]={item_id=26193,num=1,is_bind=1},
[45]={item_id=18664,num=1,is_bind=1},
[46]={item_id=26505,num=1,is_bind=1},
[47]={item_id=18663,num=1,is_bind=1},
[48]={item_id=26504,num=1,is_bind=1},
[49]={item_id=26191,num=1,is_bind=1},
[50]={item_id=18681,num=1,is_bind=1},
[51]={item_id=26503,num=1,is_bind=1},
[52]={item_id=38924,num=1,is_bind=1},
[53]={item_id=18678,num=1,is_bind=1},
[54]={item_id=30805,num=50,is_bind=1},
[55]={item_id=18776,num=10,is_bind=1},
[56]={item_id=38925,num=1,is_bind=1},
[57]={item_id=18677,num=1,is_bind=1},
[58]={item_id=30805,num=30,is_bind=1},
[59]={item_id=18776,num=5,is_bind=1},
[60]={item_id=38926,num=1,is_bind=1},
[61]={item_id=30805,num=20,is_bind=1},
[62]={item_id=18776,num=3,is_bind=1},
[63]={item_id=18676,num=1,is_bind=1},
[64]={item_id=48559,num=1,is_bind=1},
[65]={item_id=18776,num=2,is_bind=1},
[66]={item_id=18675,num=1,is_bind=1},
[67]={item_id=30551,num=1,is_bind=1},
[68]={item_id=18776,num=1,is_bind=1},
[69]={item_id=18684,num=1,is_bind=1},
[70]={item_id=48567,num=1,is_bind=1},
[71]={item_id=40200,num=1,is_bind=1},
[72]={item_id=18658,num=1,is_bind=1},
[73]={item_id=38123,num=1,is_bind=1},
[74]={item_id=62004,num=10,is_bind=1},
[75]={item_id=40201,num=1,is_bind=1},
[76]={item_id=18657,num=1,is_bind=1},
[77]={item_id=62004,num=8,is_bind=1},
[78]={item_id=62000,num=100,is_bind=1},
[79]={item_id=40202,num=1,is_bind=1},
[80]={item_id=62004,num=5,is_bind=1},
[81]={item_id=18656,num=1,is_bind=1},
[82]={item_id=18655,num=1,is_bind=1},
[83]={item_id=18680,num=1,is_bind=1},
[84]={item_id=38901,num=1,is_bind=1},
[85]={item_id=18689,num=1,is_bind=1},
[86]={item_id=30355,num=1,is_bind=1},
[87]={item_id=30425,num=30,is_bind=1},
[88]={item_id=38902,num=1,is_bind=1},
[89]={item_id=18688,num=1,is_bind=1},
[90]={item_id=30354,num=1,is_bind=1},
[91]={item_id=30425,num=20,is_bind=1},
[92]={item_id=38903,num=1,is_bind=1},
[93]={item_id=30225,num=1,is_bind=1},
[94]={item_id=18687,num=1,is_bind=1},
[95]={item_id=29625,num=18,is_bind=1},
[96]={item_id=30224,num=1,is_bind=1},
[97]={item_id=18686,num=1,is_bind=1},
[98]={item_id=39105,num=1000,is_bind=1},
[99]={item_id=18685,num=1,is_bind=1},
[100]={item_id=38954,num=1,is_bind=1},
[101]={item_id=18694,num=1,is_bind=1},
[102]={item_id=48120,num=5,is_bind=1},
[103]={item_id=28851,num=10,is_bind=1},
[104]={item_id=38955,num=1,is_bind=1},
[105]={item_id=18693,num=1,is_bind=1},
[106]={item_id=48120,num=3,is_bind=1},
[107]={item_id=38956,num=1,is_bind=1},
[108]={item_id=26944,num=1,is_bind=1},
[109]={item_id=18692,num=1,is_bind=1},
[110]={item_id=18691,num=1,is_bind=1},
[111]={item_id=18690,num=1,is_bind=1},
[112]={item_id=38942,num=1,is_bind=1},
[113]={item_id=18670,num=1,is_bind=1},
[114]={item_id=18781,num=1,is_bind=1},
[115]={item_id=22074,num=20,is_bind=1},
[116]={item_id=38943,num=1,is_bind=1},
[117]={item_id=18669,num=1,is_bind=1},
[118]={item_id=18780,num=1,is_bind=1},
[119]={item_id=22074,num=15,is_bind=1},
[120]={item_id=38944,num=1,is_bind=1},
[121]={item_id=18668,num=1,is_bind=1},
[122]={item_id=22074,num=10,is_bind=1},
[123]={item_id=26545,num=1,is_bind=1},
[124]={item_id=18667,num=1,is_bind=1},
[125]={item_id=26452,num=1,is_bind=1},
[126]={item_id=18682,num=1,is_bind=1},
[127]={item_id=38945,num=1,is_bind=1},
[128]={item_id=18674,num=1,is_bind=1},
[129]={item_id=26071,num=3,is_bind=1},
[130]={item_id=18777,num=1,is_bind=1},
[131]={item_id=38946,num=1,is_bind=1},
[132]={item_id=18673,num=1,is_bind=1},
[133]={item_id=26071,num=1,is_bind=1},
[134]={item_id=38947,num=1,is_bind=1},
[135]={item_id=18672,num=1,is_bind=1},
[136]={item_id=26079,num=10,is_bind=1},
[137]={item_id=18671,num=1,is_bind=1},
[138]={item_id=18683,num=1,is_bind=1},
[139]={item_id=28566,num=1,is_bind=1},
[140]={item_id=28567,num=1,is_bind=1},
[141]={item_id=28568,num=1,is_bind=1},
[142]={item_id=28569,num=1,is_bind=1},
[143]={item_id=28570,num=1,is_bind=1},
[144]={item_id=28571,num=1,is_bind=1},
[145]={item_id=28572,num=1,is_bind=1},
[146]={item_id=28659,num=1,is_bind=1},
[147]={item_id=28660,num=1,is_bind=1},
[148]={item_id=26502,num=2,is_bind=1},
[149]={item_id=26517,num=2,is_bind=1},
[150]={item_id=26415,num=15,is_bind=1},
[151]={item_id=26502,num=1,is_bind=1},
[152]={item_id=26517,num=1,is_bind=1},
[153]={item_id=26415,num=10,is_bind=1},
[154]={item_id=30447,num=10,is_bind=1},
[155]={item_id=43075,num=1,is_bind=1},
[156]={item_id=39784,num=1,is_bind=1},
[157]={item_id=22958,num=1,is_bind=1},
[158]={item_id=22009,num=1,is_bind=1},
[159]={item_id=22011,num=1,is_bind=1},
[160]={item_id=22621,num=1,is_bind=1},
[161]={item_id=22587,num=1,is_bind=1},
[162]={item_id=22012,num=1,is_bind=1},
[163]={item_id=22639,num=1,is_bind=1},
[164]={item_id=22622,num=1,is_bind=1},
[165]={item_id=22640,num=1,is_bind=1},
[166]={item_id=22611,num=1,is_bind=1},
[167]={item_id=22575,num=1,is_bind=1},
[168]={item_id=26368,num=1,is_bind=1},
[169]={item_id=22013,num=1,is_bind=1},
[170]={item_id=22643,num=1,is_bind=1},
[171]={item_id=26344,num=2,is_bind=1},
[172]={item_id=26344,num=3,is_bind=1},
[173]={item_id=26344,num=4,is_bind=1},
[174]={item_id=26368,num=2,is_bind=1},
[175]={item_id=26365,num=1,is_bind=1},
[176]={item_id=26349,num=1,is_bind=1},
[177]={item_id=26349,num=3,is_bind=1},
[178]={item_id=26358,num=1,is_bind=1},
[179]={item_id=26350,num=1,is_bind=1},
[180]={item_id=26359,num=1,is_bind=1},
[181]={item_id=26359,num=2,is_bind=1},
[182]={item_id=28068,num=1,is_bind=1},
[183]={item_id=28069,num=1,is_bind=1},
[184]={item_id=28070,num=1,is_bind=1},
[185]={item_id=26346,num=1,is_bind=1},
[186]={item_id=26346,num=3,is_bind=1},
[187]={item_id=26347,num=1,is_bind=1},
[188]={item_id=26355,num=1,is_bind=1},
[189]={item_id=26356,num=2,is_bind=1},
[190]={item_id=38930,num=1,is_bind=1},
[191]={item_id=39107,num=3,is_bind=1},
[192]={item_id=39470,num=1,is_bind=1},
[193]={item_id=22733,num=3,is_bind=1},
[194]={item_id=22734,num=3,is_bind=1},
[195]={item_id=39107,num=5,is_bind=1},
[196]={item_id=43077,num=1,is_bind=1},
[197]={item_id=26346,num=20,is_bind=1},
[198]={item_id=26347,num=5,is_bind=1},
[199]={item_id=26348,num=3,is_bind=1},
[200]={item_id=26356,num=1,is_bind=1},
[201]={item_id=26357,num=1,is_bind=1},
[202]={item_id=26344,num=150,is_bind=1},
[203]={item_id=26345,num=1,is_bind=1},
[204]={item_id=26367,num=1,is_bind=1},
[205]={item_id=26369,num=1,is_bind=1},
[206]={item_id=26993,num=30,is_bind=1},
[207]={item_id=26993,num=150,is_bind=1},
[208]={item_id=26994,num=1,is_bind=1},
[209]={item_id=26995,num=2,is_bind=1},
[210]={item_id=26996,num=2,is_bind=1},
[211]={item_id=26997,num=1,is_bind=1},
[212]={item_id=26998,num=30,is_bind=1},
[213]={item_id=26998,num=150,is_bind=1},
[214]={item_id=26999,num=2,is_bind=1},
[215]={item_id=26990,num=2,is_bind=1},
[216]={item_id=26991,num=2,is_bind=1},
[217]={item_id=26992,num=1,is_bind=1},
[218]={item_id=26349,num=20,is_bind=1},
[219]={item_id=26350,num=5,is_bind=1},
[220]={item_id=26351,num=3,is_bind=1},
[221]={item_id=26360,num=1,is_bind=1},
[222]={item_id=37809,num=1,is_bind=1},
[223]={item_id=44182,num=1,is_bind=1},
[224]={item_id=30447,num=2,is_bind=1},
[225]={item_id=48127,num=1,is_bind=1},
[226]={item_id=26344,num=10,is_bind=1},
[227]={item_id=29428,num=1,is_bind=1},
[228]={item_id=44183,num=1,is_bind=1},
[229]={item_id=30447,num=3,is_bind=1},
[230]={item_id=26344,num=20,is_bind=1},
[231]={item_id=29429,num=1,is_bind=1},
[232]={item_id=30447,num=5,is_bind=1},
[233]={item_id=26344,num=30,is_bind=1},
[234]={item_id=29430,num=1,is_bind=1},
[235]={item_id=29431,num=1,is_bind=1},
[236]={item_id=44184,num=1,is_bind=1},
[237]={item_id=48126,num=1,is_bind=1},
[238]={item_id=29432,num=1,is_bind=1},
[239]={item_id=26344,num=50,is_bind=1},
[240]={item_id=29433,num=1,is_bind=1},
[241]={item_id=29434,num=1,is_bind=1},
[242]={item_id=44185,num=1,is_bind=1},
[243]={item_id=30447,num=20,is_bind=1},
[244]={item_id=48125,num=1,is_bind=1},
[245]={item_id=44180,num=3,is_bind=1},
[246]={item_id=29435,num=1,is_bind=1},
[247]={item_id=29436,num=1,is_bind=1},
[248]={item_id=44495,num=1,is_bind=1},
[249]={item_id=48187,num=1,is_bind=1},
[250]={item_id=29437,num=1,is_bind=1},
[251]={item_id=30805,num=10,is_bind=1},
[252]={item_id=44180,num=5,is_bind=1},
[253]={item_id=29438,num=1,is_bind=1},
[254]={item_id=29439,num=1,is_bind=1},
[255]={item_id=44496,num=1,is_bind=1},
[256]={item_id=44180,num=10,is_bind=1},
[257]={item_id=29440,num=1,is_bind=1},
[258]={item_id=26363,num=1,is_bind=1},
[259]={item_id=26361,num=1,is_bind=1},
[260]={item_id=26362,num=1,is_bind=1},
[261]={item_id=22576,num=1,is_bind=1},
[262]={item_id=26344,num=1,is_bind=1},
[263]={item_id=26978,num=1,is_bind=1},
[264]={item_id=22060,num=1,is_bind=0},
[265]={item_id=22045,num=1,is_bind=1},
[266]={item_id=22058,num=1,is_bind=1},
[267]={item_id=26345,num=2,is_bind=1},
[268]={item_id=26000,num=2,is_bind=1},
[269]={item_id=26000,num=1,is_bind=1},
[270]={item_id=26347,num=2,is_bind=1},
[271]={item_id=26350,num=2,is_bind=1},
[272]={item_id=26348,num=1,is_bind=1},
[273]={item_id=26351,num=1,is_bind=1},
[274]={item_id=22734,num=1,is_bind=1},
[275]={item_id=26422,num=5,is_bind=1},
[276]={item_id=26344,num=5,is_bind=1},
[277]={item_id=26424,num=5,is_bind=1},
[278]={item_id=28038,num=1,is_bind=1},
[279]={item_id=26421,num=5,is_bind=1},
[280]={item_id=26979,num=1,is_bind=1},
[281]={item_id=26978,num=5,is_bind=1},
[282]={item_id=26978,num=2,is_bind=1},
[283]={item_id=26420,num=5,is_bind=1},
[284]={item_id=26353,num=1,is_bind=1},
[285]={item_id=26331,num=1,is_bind=1},
[286]={item_id=26352,num=1,is_bind=1},
[287]={item_id=26330,num=1,is_bind=1},
[288]={item_id=22599,num=1,is_bind=1},
[289]={item_id=36417,num=10000,is_bind=1},
[290]={item_id=26000,num=50,is_bind=1},
[291]={item_id=26000,num=100,is_bind=1},
[292]={item_id=26000,num=200,is_bind=1},
[293]={item_id=27611,num=1,is_bind=1},
[294]={item_id=29101,num=1,is_bind=1},
[295]={item_id=27881,num=1,is_bind=1},
[296]={item_id=26000,num=4,is_bind=1},
[297]={item_id=43076,num=1,is_bind=1},
[298]={item_id=39159,num=1,is_bind=1},
[299]={item_id=29214,num=1,is_bind=1},
[300]={item_id=48086,num=1,is_bind=1},
[301]={item_id=43078,num=1,is_bind=1},
[302]={item_id=48087,num=1,is_bind=1},
[303]={item_id=26369,num=1,is_bind=0},
[304]={item_id=26368,num=5,is_bind=0},
[305]={item_id=26367,num=5,is_bind=0},
[306]={item_id=26345,num=10,is_bind=0},
[307]={item_id=26344,num=10,is_bind=0},
[308]={item_id=48071,num=5,is_bind=0},
[309]={item_id=26502,num=2,is_bind=0},
[310]={item_id=26517,num=2,is_bind=0},
[311]={item_id=26200,num=20,is_bind=0},
[312]={item_id=26203,num=20,is_bind=0},
[313]={item_id=62000,num=10,is_bind=0},
[314]={item_id=26376,num=150,is_bind=1},
[315]={item_id=26376,num=200,is_bind=1},
[316]={item_id=26376,num=300,is_bind=1},
[317]={item_id=26344,num=80,is_bind=1},
[318]={item_id=26344,num=120,is_bind=1},
[319]={item_id=26344,num=180,is_bind=1},
[320]={item_id=22531,num=2,is_bind=1},
[321]={item_id=22532,num=1,is_bind=1},
[322]={item_id=22014,num=2484,is_bind=1},
[323]={item_id=30443,num=2,is_bind=1},
[324]={item_id=27611,num=5,is_bind=1},
[325]={item_id=27612,num=1,is_bind=1},
[326]={item_id=27800,num=1,is_bind=1},
[327]={item_id=28033,num=2,is_bind=1},
[328]={item_id=27613,num=1,is_bind=1},
[329]={item_id=37003,num=1,is_bind=1},
[330]={item_id=26346,num=5,is_bind=1},
[331]={item_id=37206,num=1,is_bind=1},
[332]={item_id=27656,num=1,is_bind=1},
[333]={item_id=48073,num=1,is_bind=1},
[334]={item_id=27657,num=1,is_bind=1},
[335]={item_id=22014,num=100,is_bind=1},
[336]={item_id=30805,num=3,is_bind=1},
[337]={item_id=30805,num=8,is_bind=1},
[338]={item_id=26501,num=2,is_bind=1},
[339]={item_id=26354,num=1,is_bind=1},
[340]={item_id=37618,num=1,is_bind=1},
[341]={item_id=50077,num=2,is_bind=1},
[342]={item_id=22530,num=3,is_bind=1},
[343]={item_id=50077,num=4,is_bind=1},
[344]={item_id=22531,num=3,is_bind=1},
[345]={item_id=50077,num=8,is_bind=1},
[346]={item_id=22531,num=5,is_bind=1},
[347]={item_id=50077,num=15,is_bind=1},
[348]={item_id=22531,num=10,is_bind=1},
[349]={item_id=50077,num=1,is_bind=1},
[350]={item_id=22530,num=2,is_bind=1},
[351]={item_id=27611,num=2,is_bind=1},
[352]={item_id=27611,num=3,is_bind=1},
[353]={item_id=27612,num=3,is_bind=1},
[354]={item_id=27612,num=5,is_bind=1},
[355]={item_id=27612,num=10,is_bind=1},
[356]={item_id=48504,num=1,is_bind=1},
[357]={item_id=48504,num=10,is_bind=1},
[358]={item_id=48504,num=20,is_bind=1},
[359]={item_id=48504,num=40,is_bind=1},
[360]={item_id=48504,num=80,is_bind=1},
[361]={item_id=26172,num=1,is_bind=1},
[362]={item_id=26376,num=2,is_bind=1},
[363]={item_id=26172,num=10,is_bind=1},
[364]={item_id=26376,num=3,is_bind=1},
[365]={item_id=26172,num=20,is_bind=1},
[366]={item_id=26376,num=10,is_bind=1},
[367]={item_id=26172,num=40,is_bind=1},
[368]={item_id=26376,num=20,is_bind=1},
[369]={item_id=26172,num=80,is_bind=1},
[370]={item_id=26376,num=30,is_bind=1},
[371]={item_id=26197,num=1,is_bind=1},
[372]={item_id=26199,num=3,is_bind=1},
[373]={item_id=26197,num=10,is_bind=1},
[374]={item_id=26199,num=6,is_bind=1},
[375]={item_id=26197,num=20,is_bind=1},
[376]={item_id=26199,num=15,is_bind=1},
[377]={item_id=26197,num=40,is_bind=1},
[378]={item_id=26199,num=30,is_bind=1},
[379]={item_id=26197,num=80,is_bind=1},
[380]={item_id=26199,num=60,is_bind=1},
[381]={item_id=26174,num=1,is_bind=1},
[382]={item_id=26352,num=2,is_bind=1},
[383]={item_id=26174,num=10,is_bind=1},
[384]={item_id=26352,num=3,is_bind=1},
[385]={item_id=26174,num=20,is_bind=1},
[386]={item_id=26353,num=3,is_bind=1},
[387]={item_id=26174,num=40,is_bind=1},
[388]={item_id=26353,num=5,is_bind=1},
[389]={item_id=26174,num=80,is_bind=1},
[390]={item_id=26353,num=10,is_bind=1},
[391]={item_id=47585,num=1,is_bind=1},
[392]={item_id=26349,num=2,is_bind=1},
[393]={item_id=47585,num=10,is_bind=1},
[394]={item_id=47585,num=20,is_bind=1},
[395]={item_id=26350,num=3,is_bind=1},
[396]={item_id=47585,num=40,is_bind=1},
[397]={item_id=47585,num=80,is_bind=1},
[398]={item_id=26350,num=10,is_bind=1},
[399]={item_id=37823,num=1,is_bind=1},
[400]={item_id=43804,num=1,is_bind=1},
[401]={item_id=22530,num=1,is_bind=1},
[402]={item_id=22531,num=1,is_bind=1},
[403]={item_id=37811,num=1,is_bind=1},
[404]={item_id=26200,num=1,is_bind=1},
[405]={item_id=26203,num=1,is_bind=1},
[406]={item_id=26379,num=1,is_bind=1},
[407]={item_id=27830,num=1,is_bind=1},
[408]={item_id=37812,num=1,is_bind=1},
[409]={item_id=37813,num=1,is_bind=1},
[410]={item_id=26377,num=1,is_bind=1},
[411]={item_id=37818,num=1,is_bind=1},
[412]={item_id=37822,num=1,is_bind=1},
[413]={item_id=28826,num=1,is_bind=1},
[414]={item_id=26374,num=1,is_bind=1},
[415]={item_id=37820,num=1,is_bind=1},
[416]={item_id=22733,num=1,is_bind=1},
[417]={item_id=22615,num=1,is_bind=1},
[418]={item_id=39153,num=1,is_bind=1},
[419]={item_id=39153,num=2,is_bind=1},
[420]={item_id=39101,num=1,is_bind=1},
[421]={item_id=39110,num=1,is_bind=1},
[422]={item_id=22532,num=2,is_bind=1},
[423]={item_id=39143,num=1,is_bind=1},
[424]={item_id=22532,num=3,is_bind=1},
[425]={item_id=39159,num=2,is_bind=1},
[426]={item_id=30447,num=30,is_bind=1},
[427]={item_id=39102,num=1,is_bind=1},
[428]={item_id=22532,num=5,is_bind=1},
[429]={item_id=39159,num=3,is_bind=1},
[430]={item_id=30447,num=1,is_bind=1},
[431]={item_id=43804,num=3,is_bind=1},
[432]={item_id=28826,num=2,is_bind=1},
[433]={item_id=45003,num=1,is_bind=1},
[434]={item_id=45004,num=1,is_bind=1},
[435]={item_id=45005,num=1,is_bind=1},
[436]={item_id=45006,num=1,is_bind=1},
[437]={item_id=27832,num=1,is_bind=1},
[438]={item_id=27832,num=2,is_bind=1},
[439]={item_id=26367,num=2,is_bind=1},
[440]={item_id=27832,num=6,is_bind=1},
[441]={item_id=26367,num=6,is_bind=1},
[442]={item_id=27611,num=10,is_bind=1},
[443]={item_id=27833,num=6,is_bind=1},
[444]={item_id=26367,num=12,is_bind=1},
[445]={item_id=27611,num=15,is_bind=1},
[446]={item_id=26344,num=15,is_bind=1},
[447]={item_id=27833,num=10,is_bind=1},
[448]={item_id=26367,num=15,is_bind=1},
[449]={item_id=27611,num=20,is_bind=1},
[450]={item_id=27836,num=3,is_bind=1},
[451]={item_id=26367,num=30,is_bind=1},
[452]={item_id=27838,num=1,is_bind=1},
[453]={item_id=26369,num=5,is_bind=1},
[454]={item_id=26345,num=5,is_bind=1},
[455]={item_id=27838,num=2,is_bind=1},
[456]={item_id=26369,num=10,is_bind=1},
[457]={item_id=27613,num=5,is_bind=1},
[458]={item_id=26345,num=10,is_bind=1},
[459]={item_id=26379,num=2,is_bind=1},
[460]={item_id=26355,num=2,is_bind=1},
[461]={item_id=26376,num=5,is_bind=1},
[462]={item_id=26379,num=5,is_bind=1},
[463]={item_id=26355,num=5,is_bind=1},
[464]={item_id=26346,num=10,is_bind=1},
[465]={item_id=26379,num=10,is_bind=1},
[466]={item_id=26355,num=10,is_bind=1},
[467]={item_id=26346,num=15,is_bind=1},
[468]={item_id=26376,num=15,is_bind=1},
[469]={item_id=26379,num=15,is_bind=1},
[470]={item_id=26355,num=15,is_bind=1},
[471]={item_id=26347,num=10,is_bind=1},
[472]={item_id=26379,num=20,is_bind=1},
[473]={item_id=26355,num=20,is_bind=1},
[474]={item_id=26347,num=20,is_bind=1},
[475]={item_id=26376,num=50,is_bind=1},
[476]={item_id=26380,num=3,is_bind=1},
[477]={item_id=26357,num=3,is_bind=1},
[478]={item_id=26347,num=30,is_bind=1},
[479]={item_id=26377,num=5,is_bind=1},
[480]={item_id=26380,num=5,is_bind=1},
[481]={item_id=26357,num=5,is_bind=1},
[482]={item_id=26348,num=10,is_bind=1},
[483]={item_id=26377,num=10,is_bind=1},
[484]={item_id=28764,num=1,is_bind=1},
[485]={item_id=26370,num=3,is_bind=1},
[486]={item_id=26358,num=2,is_bind=1},
[487]={item_id=26370,num=5,is_bind=1},
[488]={item_id=26349,num=5,is_bind=1},
[489]={item_id=28764,num=3,is_bind=1},
[490]={item_id=26358,num=5,is_bind=1},
[491]={item_id=26370,num=10,is_bind=1},
[492]={item_id=26349,num=10,is_bind=1},
[493]={item_id=48073,num=2,is_bind=1},
[494]={item_id=26358,num=10,is_bind=1},
[495]={item_id=26370,num=15,is_bind=1},
[496]={item_id=26349,num=15,is_bind=1},
[497]={item_id=48073,num=3,is_bind=1},
[498]={item_id=26358,num=15,is_bind=1},
[499]={item_id=26371,num=10,is_bind=1},
[500]={item_id=48073,num=5,is_bind=1},
[501]={item_id=26358,num=20,is_bind=1},
[502]={item_id=26371,num=20,is_bind=1},
[503]={item_id=26350,num=20,is_bind=1},
[504]={item_id=48117,num=5,is_bind=1},
[505]={item_id=26360,num=3,is_bind=1},
[506]={item_id=26371,num=30,is_bind=1},
[507]={item_id=26350,num=30,is_bind=1},
[508]={item_id=26360,num=5,is_bind=1},
[509]={item_id=26372,num=10,is_bind=1},
[510]={item_id=26351,num=10,is_bind=1},
[511]={item_id=26373,num=1,is_bind=1},
[512]={item_id=26362,num=2,is_bind=1},
[513]={item_id=26373,num=2,is_bind=1},
[514]={item_id=26352,num=5,is_bind=1},
[515]={item_id=26362,num=5,is_bind=1},
[516]={item_id=26373,num=5,is_bind=1},
[517]={item_id=26352,num=10,is_bind=1},
[518]={item_id=26362,num=10,is_bind=1},
[519]={item_id=26373,num=10,is_bind=1},
[520]={item_id=26352,num=15,is_bind=1},
[521]={item_id=26362,num=15,is_bind=1},
[522]={item_id=26373,num=15,is_bind=1},
[523]={item_id=26362,num=20,is_bind=1},
[524]={item_id=26373,num=20,is_bind=1},
[525]={item_id=26353,num=20,is_bind=1},
[526]={item_id=26363,num=3,is_bind=1},
[527]={item_id=26375,num=3,is_bind=1},
[528]={item_id=26353,num=30,is_bind=1},
[529]={item_id=26363,num=5,is_bind=1},
[530]={item_id=26375,num=5,is_bind=1},
[531]={item_id=26354,num=10,is_bind=1},
[532]={item_id=26567,num=1,is_bind=1},
[533]={item_id=26568,num=1,is_bind=1},
[534]={item_id=26567,num=3,is_bind=1},
[535]={item_id=26568,num=3,is_bind=1},
[536]={item_id=26567,num=5,is_bind=1},
[537]={item_id=26568,num=5,is_bind=1},
[538]={item_id=26567,num=10,is_bind=1},
[539]={item_id=26568,num=10,is_bind=1},
[540]={item_id=26567,num=15,is_bind=1},
[541]={item_id=26568,num=15,is_bind=1},
[542]={item_id=26567,num=20,is_bind=1},
[543]={item_id=26568,num=20,is_bind=1},
[544]={item_id=26567,num=30,is_bind=1},
[545]={item_id=26568,num=30,is_bind=1},
[546]={item_id=37437,num=1,is_bind=1},
[547]={item_id=37208,num=1,is_bind=1},
[548]={item_id=37402,num=1,is_bind=1},
[549]={item_id=37306,num=1,is_bind=1},
[550]={item_id=38705,num=1,is_bind=1},
[551]={item_id=37007,num=1,is_bind=1},
[552]={item_id=37203,num=1,is_bind=1},
[553]={item_id=37021,num=1,is_bind=1},
[554]={item_id=36242,num=1,is_bind=0},
[555]={item_id=48044,num=1,is_bind=0},
[556]={item_id=28029,num=1,is_bind=0},
[557]={item_id=27720,num=1,is_bind=0},
[558]={item_id=27712,num=1,is_bind=0},
[559]={item_id=28874,num=1,is_bind=1},
[560]={item_id=62003,num=1,is_bind=1},
[561]={item_id=22007,num=1,is_bind=1},
[562]={item_id=38979,num=1,is_bind=1},
[563]={item_id=18662,num=1,is_bind=1},
[564]={item_id=37014,num=1,is_bind=1},
[565]={item_id=57834,num=2,is_bind=1},
[566]={item_id=28565,num=1,is_bind=1},
[567]={item_id=36028,num=1,is_bind=1},
[568]={item_id=38651,num=1,is_bind=1},
[569]={item_id=22581,num=1,is_bind=1},
[570]={item_id=39108,num=1,is_bind=1},
[571]={item_id=28845,num=1,is_bind=1},
[572]={item_id=26415,num=20,is_bind=1},
[573]={item_id=22850,num=1,is_bind=1},
[574]={item_id=43075,num=3,is_bind=1},
[575]={item_id=37910,num=1,is_bind=1},
[576]={item_id=48049,num=2,is_bind=1},
[577]={item_id=22053,num=1,is_bind=0},
[578]={item_id=22081,num=1,is_bind=1},
[579]={item_id=22753,num=1,is_bind=1},
[580]={item_id=22601,num=1,is_bind=1},
[581]={item_id=22010,num=1,is_bind=1},
[582]={item_id=26419,num=5,is_bind=1},
[583]={item_id=36417,num=5000,is_bind=1},
[584]={item_id=29210,num=1,is_bind=1},
[585]={item_id=26376,num=100,is_bind=1},
[586]={item_id=39480,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
act_time_limit={
{level_limit=40,},
{act_id=114,act_index=2,close_day=3,},
{act_id=2123,act_index=3,open_day=9999,close_day=9999,level_limit=9999,},
{act_id=70,act_index=4,},
{act_id=54,act_index=5,close_day=5,level_limit=180,},
{act_id=55,act_index=6,close_day=4,},
{act_id=159,act_index=7,level_limit=75,},
{act_id=160,act_index=8,close_day=5,level_limit=75,},
{act_id=2219,act_index=9,},
{act_id=171,act_index=11,},
{act_id=2357,act_index=13,}
},

act_time_limit_meta_table_map={
[4]=2,	-- depth:1
[9]=5,	-- depth:1
[10]=3,	-- depth:1
},
rush_rank_type={
{close_day_index=1,gold_type="1|1|1|1|1|1",price=7,init_price=10,no_limit_item="26344:1:1",show_type=1,model_scale=0.9,},
{rush_type=1,show_close_day=8,reach_goal="180|200|215|230",reach_goal_reward_item="22733:40:1|22733:80:1|22733:120:1|22733:160:1",open_game_day=1,daily_buy_item="39110:1:1|22753:1:1|22013:1:1|39102:1:1|39103:1:1",daily_buy_times="1|1|2|2|1",old_gold="200|100|600|600|1000",need_gold="150|50|288|196|256",get_way=13,rank_type=31,show_title="等级",ranking_name="玩家等级榜",ranking_title="玩家等级",bubble_title="等级",turn_link="fubenpanel#fubenpanel_exp",rank_condition="玩家等级达到%d级",show_item_id=38969,},
{rush_type=9,reach_goal="386800|482400|564800|628800",reach_goal_reward_item="26200:50:1|26200:100:1|26200:150:1|26200:200:1",open_game_day=1,daily_buy_item="26200:100:1|26203:100:1|22615:1:1|29615:8:1|28739:1:1",daily_buy_times="2|2|5|1|2",old_gold="100|200|100|1200|900",need_gold="70|88|30|588|488",get_way=19,rank_type=42,show_title="装备",ranking_name="装备评分榜",ranking_title="装备评分",bubble_title="装备",turn_link="bag_view#rolebag_bag_all",rank_condition="装备评分达到%d",show_item_id=38973,},
{rush_type=11,open_day_index=2,close_day_index=3,reach_value=100000,reach_goal="500000|800000|1200000|1500000",reach_goal_reward_item="30447:5:1|30447:10:1|30447:15:1|30447:20:1",open_game_day=1,gold_type="1|1|1|1|1|1",daily_buy_item="26347:1:1|26348:1:1|26347:1:1|26348:1:1|26355:1:1|26356:1:1",daily_buy_times="10|10|50|70|5|10",old_gold="60|300|60|300|300|500",need_gold="10|60|20|120|88|200",get_way=17,rank_type=84,show_title="幻兽",ranking_name="幻兽战力榜",ranking_title="幻兽战力",bubble_title="幻兽",turn_link="ControlBeastsView#",rank_condition="幻兽战力达到%d",show_type=1,show_item_id=30653,model_scale=0.8,},
{rush_type=8,open_day_index=3,close_day_index=3,show_close_day=8,reach_goal="220000|300000|360000|400000",reach_goal_reward_item="62000:50:1|62000:100:1|62000:150:1|62000:200:1",rank_type=98,show_title="雷法",ranking_name="雷法战力榜",ranking_title="雷法战力",bubble_title="雷法",turn_link="ThunderManaSelectView",rank_condition="雷法战力达到%d",show_item_id=38123,model_scale=1,},
{rush_type=17,open_day_index=4,close_day_index=4,reach_value=1000,reach_goal="80000|120000|160000|200000",reach_goal_reward_item="30424:5:1|30424:10:1|30424:15:1|30424:30:1",open_game_day=7,daily_buy_item="28762:1:1|28763:1:1|28761:1:1|43087:1:1|29262:1:1",daily_buy_times="2|2|2|10|1",old_gold="920|920|2080|600|720",need_gold="588|388|888|240|360",get_way=17,rank_type=91,show_title="万魂幡",ranking_name="万魂幡评分榜",ranking_title="万魂幡评分",bubble_title="万魂幡",turn_link="MingWenView",rank_condition="万魂幡评分达到%d",show_item_id=38901,},
{rush_type=16,open_day_index=5,close_day_index=5,reach_goal="300000|420000|520000|600000",reach_goal_reward_item="27657:30:1|27657:60:1|27657:80:1|27657:100:1",rank_type=89,show_title="魂环",ranking_name="魂环战力榜",ranking_title="魂环战力",bubble_title="魂环",turn_link="shenshou",rank_condition="魂环战力达到%d",show_type=3,show_item_id=91629,soul_ring_id="9|18|27|36|45|54|63|72",},
{rush_type=18,open_day_index=6,close_day_index=6,show_close_day=8,reach_goal="120000|200000|260000|300000",reach_goal_reward_item="22072:5:1|22072:10:1|22072:20:1|22072:30:1",get_way=18,rank_type=92,show_title="万图谱",ranking_name="万图谱战力榜",ranking_title="万图谱战力",bubble_title="万图谱",turn_link="ShanHaiJingView",rank_condition="万图谱战力达到%d",show_item_id=38942,},
{rush_type=19,open_day_index=7,close_day_index=7,reach_goal="50000|120000|160000|200000",reach_goal_reward_item="26391:50:1|26391:100:1|26391:150:1|26391:200:1",rank_type=93,show_title="双修",ranking_name="双修战力榜",ranking_title="双修战力",bubble_title="双修",turn_link="ArtifactView",rank_condition="双修战力达到%d",show_item_id=38945,}
},

rush_rank_type_meta_table_map={
[5]=1,	-- depth:1
[7]=1,	-- depth:1
[8]=6,	-- depth:1
[9]=8,	-- depth:2
},
rush_rank_reward={
{reach_value=81,},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},reach_value=75,get_way_param=2,},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},reach_value=71,get_way_param=3,},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[6],[3]=item_table[11]},reach_value=65,get_way_param=4,},
{min_rank=11,max_rank=20,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15]},reach_value=61,get_way_param=5,},
{min_rank=21,max_rank=40,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19]},reach_value=51,get_way_param=6,},
{rush_type=1,reward_item={[0]=item_table[20],[1]=item_table[21],[2]=item_table[3],[3]=item_table[22],[4]=item_table[23],[5]=item_table[24]},reach_value=250,},
{rush_type=1,reward_item={[0]=item_table[1],[1]=item_table[25],[2]=item_table[3],[3]=item_table[22],[4]=item_table[26],[5]=item_table[27]},reach_value=240,},
{rush_type=1,reward_item={[0]=item_table[7],[1]=item_table[28],[2]=item_table[3],[3]=item_table[22],[4]=item_table[26],[5]=item_table[27]},reach_value=235,},
{rush_type=1,reward_item={[0]=item_table[9],[1]=item_table[29],[2]=item_table[30],[3]=item_table[31]},reach_value=220,},
{rush_type=1,reward_item={[0]=item_table[12],[1]=item_table[29],[2]=item_table[32],[3]=item_table[33]},reach_value=210,},
{rush_type=1,reward_item={[0]=item_table[16],[1]=item_table[29],[2]=item_table[32],[3]=item_table[33]},reach_value=200,},
{rush_type=9,reward_item={[0]=item_table[20],[1]=item_table[34],[2]=item_table[3],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},reach_value=1480000,},
{rush_type=9,reward_item={[0]=item_table[1],[1]=item_table[38],[2]=item_table[3],[3]=item_table[39],[4]=item_table[40],[5]=item_table[41]},reach_value=1280000,},
{rush_type=9,reward_item={[0]=item_table[7],[1]=item_table[42],[2]=item_table[3],[3]=item_table[39],[4]=item_table[43],[5]=item_table[41]},reach_value=1080000,},
{rush_type=9,reward_item={[0]=item_table[9],[1]=item_table[44],[2]=item_table[45],[3]=item_table[46]},reach_value=888000,},
{rush_type=9,reward_item={[0]=item_table[12],[1]=item_table[44],[2]=item_table[47],[3]=item_table[48]},reach_value=688000,},
{rush_type=9,reward_item={[0]=item_table[16],[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},reach_value=580000,},
{rush_type=11,reward_item={[0]=item_table[20],[1]=item_table[52],[2]=item_table[3],[3]=item_table[53],[4]=item_table[54],[5]=item_table[55]},reach_value=3280000,},
{rush_type=11,reward_item={[0]=item_table[1],[1]=item_table[56],[2]=item_table[3],[3]=item_table[57],[4]=item_table[58],[5]=item_table[59]},reach_value=2880000,},
{rush_type=11,reward_item={[0]=item_table[7],[1]=item_table[60],[2]=item_table[3],[3]=item_table[57],[4]=item_table[61],[5]=item_table[62]},reach_value=2480000,},
{rush_type=11,reward_item={[0]=item_table[9],[1]=item_table[63],[2]=item_table[64],[3]=item_table[65]},reach_value=2000000,},
{rush_type=11,reward_item={[0]=item_table[12],[1]=item_table[66],[2]=item_table[67],[3]=item_table[68]},reach_value=1600000,},
{rush_type=11,reward_item={[0]=item_table[16],[1]=item_table[69],[2]=item_table[70],[3]=item_table[68]},},
{rush_type=8,reward_item={[0]=item_table[20],[1]=item_table[71],[2]=item_table[3],[3]=item_table[72],[4]=item_table[73],[5]=item_table[74]},reach_value=712800,},
{rush_type=8,reward_item={[0]=item_table[1],[1]=item_table[75],[2]=item_table[3],[3]=item_table[76],[4]=item_table[77],[5]=item_table[78]},reach_value=683000,},
{rush_type=8,reward_item={[0]=item_table[7],[1]=item_table[79],[2]=item_table[3],[3]=item_table[76],[4]=item_table[77],[5]=item_table[78]},reach_value=652000,},
{rush_type=8,reward_item={[0]=item_table[9],[1]=item_table[80],[2]=item_table[81],[3]=item_table[78]},reach_value=481600,},
{rush_type=8,reward_item={[0]=item_table[12],[1]=item_table[80],[2]=item_table[82],[3]=item_table[78]},reach_value=285200,},
{rush_type=8,reward_item={[0]=item_table[16],[1]=item_table[80],[2]=item_table[83],[3]=item_table[78]},},
{rush_type=17,reward_item={[0]=item_table[20],[1]=item_table[84],[2]=item_table[3],[3]=item_table[85],[4]=item_table[86],[5]=item_table[87]},reach_value=320000,},
{rush_type=17,reward_item={[0]=item_table[1],[1]=item_table[88],[2]=item_table[3],[3]=item_table[89],[4]=item_table[90],[5]=item_table[91]},reach_value=288000,},
{rush_type=17,reward_item={[0]=item_table[7],[1]=item_table[92],[2]=item_table[3],[3]=item_table[89],[4]=item_table[90],[5]=item_table[91]},reach_value=252000,},
{rush_type=17,reward_item={[0]=item_table[9],[1]=item_table[93],[2]=item_table[94],[3]=item_table[95]},reach_value=188000,},
{rush_type=17,reward_item={[0]=item_table[12],[1]=item_table[96],[2]=item_table[97],[3]=item_table[98]},reach_value=144000,},
{rush_type=17,reward_item={[0]=item_table[16],[1]=item_table[96],[2]=item_table[99],[3]=item_table[98]},reach_value=77760,},
{rush_type=16,reward_item={[0]=item_table[20],[1]=item_table[100],[2]=item_table[3],[3]=item_table[101],[4]=item_table[102],[5]=item_table[103]},reach_value=5280000,},
{rush_type=16,reward_item={[0]=item_table[1],[1]=item_table[104],[2]=item_table[3],[3]=item_table[105],[4]=item_table[106],[5]=item_table[103]},reach_value=4880000,},
{rush_type=16,reward_item={[0]=item_table[7],[1]=item_table[107],[2]=item_table[3],[3]=item_table[105],[4]=item_table[106],[5]=item_table[103]},reach_value=4260000,},
{rush_type=16,reward_item={[0]=item_table[9],[1]=item_table[108],[2]=item_table[109],[3]=item_table[103]},reach_value=3476000,},
{rush_type=16,reward_item={[0]=item_table[12],[1]=item_table[108],[2]=item_table[110],[3]=item_table[103]},reach_value=2418000,},
{rush_type=16,min_rank=21,max_rank=40,reward_item={[0]=item_table[16],[1]=item_table[108],[2]=item_table[111],[3]=item_table[103]},get_way_param=6,},
{rush_type=18,reward_item={[0]=item_table[20],[1]=item_table[112],[2]=item_table[3],[3]=item_table[113],[4]=item_table[114],[5]=item_table[115]},reach_value=560000,},
{rush_type=18,reward_item={[0]=item_table[1],[1]=item_table[116],[2]=item_table[3],[3]=item_table[117],[4]=item_table[118],[5]=item_table[119]},reach_value=520000,},
{rush_type=18,reward_item={[0]=item_table[7],[1]=item_table[120],[2]=item_table[3],[3]=item_table[117],[4]=item_table[118],[5]=item_table[119]},reach_value=460000,},
{rush_type=18,reward_item={[0]=item_table[9],[1]=item_table[121],[2]=item_table[122],[3]=item_table[123]},reach_value=388000,},
{rush_type=18,reward_item={[0]=item_table[12],[1]=item_table[124],[2]=item_table[122],[3]=item_table[125]},reach_value=276800,},
{rush_type=18,reward_item={[0]=item_table[16],[1]=item_table[126],[2]=item_table[122],[3]=item_table[125]},reach_value=120000,},
{rush_type=19,reward_item={[0]=item_table[20],[1]=item_table[127],[2]=item_table[3],[3]=item_table[128],[4]=item_table[129],[5]=item_table[130]},reach_value=488000,},
{rush_type=19,reward_item={[0]=item_table[1],[1]=item_table[131],[2]=item_table[3],[3]=item_table[132],[4]=item_table[133],[5]=item_table[130]},reach_value=450000,},
{rush_type=19,reward_item={[0]=item_table[7],[1]=item_table[134],[2]=item_table[3],[3]=item_table[132],[4]=item_table[133],[5]=item_table[130]},reach_value=416000,},
{rush_type=19,reward_item={[0]=item_table[9],[1]=item_table[135],[2]=item_table[136],[3]=item_table[130]},reach_value=340000,},
{rush_type=19,reward_item={[0]=item_table[12],[1]=item_table[137],[2]=item_table[136],[3]=item_table[130]},reach_value=260000,},
{rush_type=19,reward_item={[0]=item_table[16],[1]=item_table[138],[2]=item_table[136],[3]=item_table[130]},reach_value=150000,}
},

rush_rank_reward_meta_table_map={
[24]=42,	-- depth:1
[35]=5,	-- depth:1
[36]=6,	-- depth:1
[38]=2,	-- depth:1
[34]=4,	-- depth:1
[39]=3,	-- depth:1
[45]=3,	-- depth:1
[41]=5,	-- depth:1
[44]=2,	-- depth:1
[46]=4,	-- depth:1
[47]=5,	-- depth:1
[48]=6,	-- depth:1
[50]=2,	-- depth:1
[51]=3,	-- depth:1
[52]=4,	-- depth:1
[40]=4,	-- depth:1
[33]=3,	-- depth:1
[27]=3,	-- depth:1
[30]=48,	-- depth:2
[8]=2,	-- depth:1
[9]=3,	-- depth:1
[10]=4,	-- depth:1
[11]=5,	-- depth:1
[12]=6,	-- depth:1
[14]=2,	-- depth:1
[15]=3,	-- depth:1
[16]=4,	-- depth:1
[32]=2,	-- depth:1
[17]=5,	-- depth:1
[20]=2,	-- depth:1
[21]=3,	-- depth:1
[22]=4,	-- depth:1
[23]=5,	-- depth:1
[26]=2,	-- depth:1
[53]=5,	-- depth:1
[28]=4,	-- depth:1
[29]=5,	-- depth:1
[18]=6,	-- depth:1
[54]=6,	-- depth:1
},
total_consume={
{},
{seq=1,consume_gold=2560,reward_item=item_table[139],},
{seq=2,consume_gold=4560,reward_item=item_table[140],},
{seq=3,consume_gold=9760,reward_item=item_table[141],},
{seq=4,consume_gold=19800,reward_item=item_table[142],},
{seq=5,consume_gold=30000,reward_item=item_table[143],},
{seq=6,consume_gold=50000,reward_item=item_table[144],},
{seq=7,consume_gold=100000,reward_item=item_table[145],},
{seq=8,consume_gold=150000,reward_item=item_table[146],},
{seq=9,consume_gold=200000,reward_item=item_table[147],}
},

total_consume_meta_table_map={
},
perfect_lover={
{}
},

perfect_lover_meta_table_map={
},
guild_battle={
{},
{type=1,reward_item={[0]=item_table[148],[1]=item_table[149],[2]=item_table[150]},},
{type=2,reward_item={[0]=item_table[151],[1]=item_table[152],[2]=item_table[153]},}
},

guild_battle_meta_table_map={
},
word_exchange={
{item_id_5=26399,},
{seq=1,reward_item=item_table[154],},
{seq=2,item_id_3=0,item_id_4=0,reward_item=item_table[155],limit=10,},
{seq=3,item_id_1=26397,item_id_2=26398,reward_item=item_table[156],}
},

word_exchange_meta_table_map={
[4]=3,	-- depth:1
},
icon_jump={
{rush_type=1,param_1="fubenpanel#fubenpanel_exp",jump_icon="act_14",icon_name="日月修行",icon_describe="日月修行",},
{rush_type=1,type=1,param_1="shop#Tab_Shop50#uip=10100",jump_icon="zj_aoxiaobin",icon_name="购买敖小丙",icon_describe="购买敖小丙",},
{rush_type=1,type=2,},
{rush_type=1,type=3,},
{rush_type=1,type=4,param_1="qifu",jump_icon="zj_huodon_qifu",icon_name="祈福",icon_describe="祈福",},
{rush_type=2,},
{rush_type=2,},
{rush_type=2,},
{rush_type=2,},
{rush_type=2,type=4,param_1="EveryDayRechargeView",jump_icon="btn_daily_recharge",icon_name="每日充值",icon_describe="每日充值",},
{rush_type=3,},
{rush_type=3,type=1,param_1="vip#recharge_monthcard",jump_icon="zj_huodon_yueka",icon_name="月卡回报",icon_describe="月卡回报",},
{rush_type=3,type=2,param_1="shop#Tab_Shop50",jump_icon="zj_huodon_bangxian",icon_name="绑仙商城",icon_describe="绑仙商城",},
{rush_type=3,type=3,param_1="shop#Tab_Shop20",jump_icon="zj_shop",icon_name="限购商城",icon_describe="限购商城",},
{rush_type=3,type=4,},
{rush_type=4,type=2,},
{rush_type=5,},
{rush_type=5,type=1,execute=1,},
{rush_type=6,param_1="TreasureHunt#treasurehunt_equip",jump_icon="zj_zbxunbao",icon_name="装备寻宝",icon_describe="装备寻宝",},
{rush_type=6,type=1,param_1="TreasureHunt#treasurehunt_dianfeng",jump_icon="zj_dfxunbao",icon_name="巅峰寻宝",icon_describe="巅峰寻宝",},
{rush_type=10,param_1="vip#recharge_cz",jump_icon="zj_chongzhi",icon_name="充值",icon_describe="充值",},
{rush_type=10,type=1,param_1="MustBuy",jump_icon="btn_mustbuy",icon_name="超值必买",activity_id=2208,icon_describe="超值必买",},
{rush_type=10,type=2,param_1="market",jump_icon="zj_shichang",icon_name="市场",icon_describe="市场",},
{param_1="other_compose",jump_icon="kf-fzhc",icon_name="粉装合成",icon_describe="粉装合成",di_res_id="kf_mingzdi_2",},
{type=1,param_1="boss#boss_world",jump_icon="act_30",icon_name="蛮荒魔谷",icon_describe="蛮荒魔谷",},
{type=2,param_1="boss#boss_personal",jump_icon="act_39",icon_name="心魔牢狱",icon_describe="心魔牢狱",},
{type=3,param_1="boss#boss_vip",jump_icon="act_38",icon_name="混沌魔域",icon_describe="混沌魔域",},
{type=4,param_1="boss#boss_dabao",jump_icon="act_8",icon_name="天道苍茫",icon_describe="天道苍茫",icon_scale="",},
{type=5,param_1="WorldServer#world_new_shenyuan_boss",jump_icon="act_5",icon_name="天魔深渊",icon_describe="天魔深渊",icon_scale="",}
},

icon_jump_meta_table_map={
[17]=19,	-- depth:1
[16]=21,	-- depth:1
[9]=14,	-- depth:1
[8]=13,	-- depth:1
[7]=12,	-- depth:1
[4]=12,	-- depth:1
[18]=14,	-- depth:1
[15]=22,	-- depth:1
},
exp_pool={
{}
},

exp_pool_meta_table_map={
},
firstchongzhi_groupbuy={
{seq=0,groupbuy_active_need_person=1,reward_item={[0]=item_table[157]},fetch_need_min_chongzhi_value=0,},
{seq=1,groupbuy_active_need_person=1,reward_item={[0]=item_table[158],[1]=item_table[159]},fetch_need_min_chongzhi_value=1,},
{seq=2,groupbuy_active_need_person=1,reward_item={[0]=item_table[160]},fetch_need_min_chongzhi_value=288,},
{seq=3,groupbuy_active_need_person=1,},
{seq=4,groupbuy_active_need_person=2,reward_item={[0]=item_table[158]},fetch_need_min_chongzhi_value=0,},
{seq=5,groupbuy_active_need_person=2,reward_item={[0]=item_table[161],[1]=item_table[162]},fetch_need_min_chongzhi_value=1,},
{seq=6,groupbuy_active_need_person=2,reward_item={[0]=item_table[163]},fetch_need_min_chongzhi_value=288,},
{seq=7,groupbuy_active_need_person=2,},
{seq=8,reward_item={[0]=item_table[161]},fetch_need_min_chongzhi_value=0,},
{seq=9,reward_item={[0]=item_table[164],[1]=item_table[162]},fetch_need_min_chongzhi_value=1,},
{seq=10,reward_item={[0]=item_table[165]},fetch_need_min_chongzhi_value=288,},
{},
{seq=12,groupbuy_active_need_person=4,reward_item={[0]=item_table[164]},fetch_need_min_chongzhi_value=0,},
{seq=13,groupbuy_active_need_person=4,reward_item={[0]=item_table[166],[1]=item_table[162]},fetch_need_min_chongzhi_value=1,},
{seq=14,groupbuy_active_need_person=4,reward_item={[0]=item_table[167]},fetch_need_min_chongzhi_value=288,},
{seq=15,groupbuy_active_need_person=4,reward_item={[0]=item_table[168]},},
{seq=16,groupbuy_active_need_person=5,reward_item={[0]=item_table[166]},fetch_need_min_chongzhi_value=0,},
{seq=17,groupbuy_active_need_person=5,reward_item={[0]=item_table[166],[1]=item_table[169]},fetch_need_min_chongzhi_value=1,},
{seq=18,groupbuy_active_need_person=5,reward_item={[0]=item_table[170]},fetch_need_min_chongzhi_value=288,},
{seq=19,groupbuy_active_need_person=5,},
{opengame_day=2,reward_item={[0]=item_table[171]},},
{opengame_day=2,reward_item={[0]=item_table[172]},},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,reward_item={[0]=item_table[171]},},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,reward_item={[0]=item_table[171]},},
{opengame_day=2,reward_item={[0]=item_table[172]},},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,reward_item={[0]=item_table[173]},},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,reward_item={[0]=item_table[172]},},
{opengame_day=3,reward_item={[0]=item_table[173]},},
{seq=7,groupbuy_active_need_person=2,},
{opengame_day=3,reward_item={[0]=item_table[171]},},
{opengame_day=3,reward_item={[0]=item_table[172]},},
{opengame_day=3,reward_item={[0]=item_table[173]},},
{opengame_day=3,reward_item={[0]=item_table[168]},},
{opengame_day=3,reward_item={[0]=item_table[171]},},
{opengame_day=3,reward_item={[0]=item_table[172]},},
{seq=14,groupbuy_active_need_person=4,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,seq=18,groupbuy_active_need_person=5,fetch_need_min_chongzhi_value=288,},
{opengame_day=3,reward_item={[0]=item_table[174]},},
{opengame_day=4,reward_item={[0]=item_table[175]},},
{opengame_day=4,reward_item={[0]=item_table[175]},},
{opengame_day=5,reward_item={[0]=item_table[176]},},
{opengame_day=5,reward_item={[0]=item_table[177]},},
{opengame_day=5,reward_item={[0]=item_table[177]},},
{opengame_day=5,reward_item={[0]=item_table[178]},},
{opengame_day=5,reward_item={[0]=item_table[176]},},
{opengame_day=5,reward_item={[0]=item_table[177]},},
{opengame_day=5,reward_item={[0]=item_table[177]},},
{opengame_day=5,reward_item={[0]=item_table[178]},},
{opengame_day=5,reward_item={[0]=item_table[176]},},
{opengame_day=5,reward_item={[0]=item_table[177]},},
{opengame_day=5,reward_item={[0]=item_table[179]},},
{opengame_day=5,reward_item={[0]=item_table[180]},},
{opengame_day=5,reward_item={[0]=item_table[176]},},
{opengame_day=5,reward_item={[0]=item_table[177]},},
{opengame_day=5,reward_item={[0]=item_table[178]},},
{opengame_day=5,reward_item={[0]=item_table[181]},},
{opengame_day=6,reward_item={[0]=item_table[182]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[184]},},
{opengame_day=6,reward_item={[0]=item_table[182]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[184]},},
{opengame_day=6,reward_item={[0]=item_table[182]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[184]},},
{opengame_day=6,reward_item={[0]=item_table[182]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[183]},},
{opengame_day=6,reward_item={[0]=item_table[184]},},
{opengame_day=7,reward_item={[0]=item_table[185]},},
{opengame_day=7,reward_item={[0]=item_table[186]},},
{opengame_day=7,reward_item={[0]=item_table[187]},},
{opengame_day=7,reward_item={[0]=item_table[188]},},
{opengame_day=7,reward_item={[0]=item_table[185]},},
{opengame_day=7,reward_item={[0]=item_table[186]},},
{opengame_day=7,reward_item={[0]=item_table[187]},},
{opengame_day=7,reward_item={[0]=item_table[188]},},
{opengame_day=7,reward_item={[0]=item_table[185]},},
{opengame_day=7,reward_item={[0]=item_table[186]},},
{opengame_day=7,reward_item={[0]=item_table[187]},},
{opengame_day=7,reward_item={[0]=item_table[189]},},
{opengame_day=7,reward_item={[0]=item_table[185]},},
{opengame_day=7,reward_item={[0]=item_table[187]},},
{opengame_day=7,reward_item={[0]=item_table[188]},},
{opengame_day=7,reward_item={[0]=item_table[189]},}
},

firstchongzhi_groupbuy_meta_table_map={
[32]=52,	-- depth:1
[44]=4,	-- depth:1
[24]=44,	-- depth:2
[48]=44,	-- depth:2
[28]=48,	-- depth:3
[8]=16,	-- depth:1
[56]=16,	-- depth:1
[60]=20,	-- depth:1
[62]=16,	-- depth:1
[51]=11,	-- depth:1
[66]=44,	-- depth:2
[50]=10,	-- depth:1
[49]=9,	-- depth:1
[70]=8,	-- depth:2
[71]=9,	-- depth:1
[82]=44,	-- depth:2
[73]=11,	-- depth:1
[78]=16,	-- depth:1
[86]=8,	-- depth:2
[87]=9,	-- depth:1
[88]=10,	-- depth:1
[89]=11,	-- depth:1
[94]=16,	-- depth:1
[98]=44,	-- depth:2
[102]=8,	-- depth:2
[103]=9,	-- depth:1
[104]=10,	-- depth:1
[105]=11,	-- depth:1
[72]=10,	-- depth:1
[55]=59,	-- depth:1
[110]=16,	-- depth:1
[30]=50,	-- depth:2
[29]=49,	-- depth:2
[35]=55,	-- depth:2
[36]=56,	-- depth:2
[31]=51,	-- depth:2
[39]=59,	-- depth:1
[40]=60,	-- depth:2
[84]=6,	-- depth:1
[83]=5,	-- depth:1
[85]=7,	-- depth:1
[21]=1,	-- depth:1
[96]=2,	-- depth:1
[92]=14,	-- depth:1
[93]=15,	-- depth:1
[95]=1,	-- depth:1
[22]=2,	-- depth:1
[97]=3,	-- depth:1
[99]=5,	-- depth:1
[100]=6,	-- depth:1
[101]=7,	-- depth:1
[107]=13,	-- depth:1
[108]=14,	-- depth:1
[91]=13,	-- depth:1
[81]=3,	-- depth:1
[77]=15,	-- depth:1
[79]=1,	-- depth:1
[46]=6,	-- depth:1
[47]=7,	-- depth:1
[42]=22,	-- depth:2
[41]=21,	-- depth:2
[38]=18,	-- depth:1
[53]=13,	-- depth:1
[54]=14,	-- depth:1
[109]=15,	-- depth:1
[37]=17,	-- depth:1
[57]=37,	-- depth:2
[58]=38,	-- depth:2
[34]=54,	-- depth:2
[80]=2,	-- depth:1
[33]=53,	-- depth:2
[64]=2,	-- depth:1
[65]=3,	-- depth:1
[67]=5,	-- depth:1
[68]=6,	-- depth:1
[69]=7,	-- depth:1
[27]=47,	-- depth:2
[26]=46,	-- depth:2
[25]=5,	-- depth:1
[75]=13,	-- depth:1
[76]=14,	-- depth:1
[43]=3,	-- depth:1
[23]=43,	-- depth:2
[63]=1,	-- depth:1
[45]=25,	-- depth:2
},
createguild={
{reward_type=1,all_server_count=15,reward_item={[0]=item_table[190],[1]=item_table[191],[2]=item_table[192]},},
{reward_id=2,all_server_count=10,reward_condition=20,title_desc="广纳人才",target_desc="拥有20名仙盟成员",index=3,seq=2,},
{reward_type=3,reward_id=3,reward_level=2,reward_item={[0]=item_table[192],[1]=item_table[191],[2]=item_table[193]},title_desc="扩建仙盟",target_desc="仙盟达到2级",index=4,seq=3,},
{reward_id=4,all_server_count=8,reward_condition=30,title_desc="八方聚气",target_desc="拥有30名仙盟成员",index=5,seq=4,},
{reward_id=5,all_server_count=3,reward_level=3,reward_item={[0]=item_table[192],[1]=item_table[191],[2]=item_table[194]},title_desc="扩张丹房",target_desc="仙盟达到3级",index=6,seq=5,},
{reward_type=4,reward_id=6,reward_condition=2,reward_item={[0]=item_table[192],[1]=item_table[191],[2]=item_table[193]},title_desc="招聘师妹",target_desc="任命两位副盟主",index=2,seq=6,},
{reward_type=5,reward_id=7,reward_condition=8,reward_level=6,reward_item={[0]=item_table[192],[1]=item_table[195],[2]=item_table[196]},title_desc="一方霸主",target_desc="拥有8名V6成员",index=7,seq=7,},
{reward_id=8,reward_condition=15,title_desc="雄踞一方",target_desc="拥有15名V6成员",index=8,seq=8,}
},

createguild_meta_table_map={
[5]=3,	-- depth:1
[6]=2,	-- depth:1
[8]=7,	-- depth:1
},
daily_limit_buy={
{seq=0,need_gold=288,reward_item={[0]=item_table[197]},zheshu=7,},
{seq=1,need_gold=328,reward_item={[0]=item_table[198]},zheshu=5,},
{seq=2,need_gold=428,reward_item={[0]=item_table[199]},zheshu=4,},
{seq=3,reward_item={[0]=item_table[188]},},
{reward_item={[0]=item_table[200]},},
{seq=5,need_gold=588,reward_item={[0]=item_table[201]},},
{opengame_day=2,need_gold=238,},
{opengame_day=2,need_gold=888,reward_item={[0]=item_table[202]},},
{opengame_day=2,need_gold=128,reward_item={[0]=item_table[203]},zheshu=6,},
{opengame_day=2,},
{opengame_day=2,reward_item={[0]=item_table[168]},},
{opengame_day=2,},
{opengame_day=3,seq=0,need_gold=218,zheshu=7,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,reward_item={[0]=item_table[204]},},
{opengame_day=3,},
{opengame_day=3,reward_item={[0]=item_table[205]},},
{opengame_day=4,reward_item={[0]=item_table[175]},},
{seq=0,need_gold=198,reward_item={[0]=item_table[206]},},
{opengame_day=5,reward_item={[0]=item_table[207]},},
{opengame_day=5,reward_item={[0]=item_table[208]},},
{opengame_day=5,reward_item={[0]=item_table[209]},},
{opengame_day=5,reward_item={[0]=item_table[210]},},
{opengame_day=5,reward_item={[0]=item_table[211]},},
{seq=0,reward_item={[0]=item_table[212]},zheshu=6,},
{opengame_day=6,reward_item={[0]=item_table[213]},},
{opengame_day=6,need_gold=188,reward_item={[0]=item_table[214]},},
{seq=3,reward_item={[0]=item_table[215]},},
{opengame_day=6,need_gold=288,reward_item={[0]=item_table[216]},},
{opengame_day=6,reward_item={[0]=item_table[217]},},
{opengame_day=7,reward_item={[0]=item_table[218]},},
{opengame_day=7,need_gold=168,reward_item={[0]=item_table[219]},},
{opengame_day=7,reward_item={[0]=item_table[220]},},
{opengame_day=7,reward_item={[0]=item_table[178]},},
{opengame_day=7,reward_item={[0]=item_table[180]},},
{opengame_day=7,reward_item={[0]=item_table[221]},}
},

daily_limit_buy_meta_table_map={
[17]=11,	-- depth:1
[16]=4,	-- depth:1
[10]=16,	-- depth:2
[24]=30,	-- depth:1
[35]=4,	-- depth:1
[29]=30,	-- depth:1
[31]=6,	-- depth:1
[23]=29,	-- depth:2
[18]=6,	-- depth:1
[25]=6,	-- depth:1
[37]=6,	-- depth:1
[12]=18,	-- depth:2
[7]=13,	-- depth:1
[34]=3,	-- depth:1
[33]=2,	-- depth:1
[32]=1,	-- depth:1
[8]=2,	-- depth:1
[28]=3,	-- depth:1
[27]=8,	-- depth:2
[26]=28,	-- depth:2
[9]=3,	-- depth:1
[22]=9,	-- depth:2
[21]=8,	-- depth:2
[20]=22,	-- depth:3
[15]=9,	-- depth:2
[14]=8,	-- depth:2
},
boss_hunter={
{}
},

boss_hunter_meta_table_map={
},
boss_hunter_reward={
{},
{rank=2,rank_bind_gold=1500,},
{rank=3,rank_bind_gold=900,},
{rank=4,rank_bind_gold=500,}
},

boss_hunter_reward_meta_table_map={
},
total_chongzhi={
{},
{seq=1,chongzhi_gold=300,reward_item={[0]=item_table[222],[1]=item_table[223],[2]=item_table[224],[3]=item_table[225],[4]=item_table[226]},broadcast_item={[0]=item_table[222],[1]=item_table[223],[2]=item_table[224],[3]=item_table[225],[4]=item_table[226]},},
{seq=2,chongzhi_gold=980,reward_item={[0]=item_table[227],[1]=item_table[228],[2]=item_table[229],[3]=item_table[225],[4]=item_table[230]},broadcast_item={[0]=item_table[227],[1]=item_table[228],[2]=item_table[229],[3]=item_table[225],[4]=item_table[230]},},
{seq=3,chongzhi_gold=3280,reward_item={[0]=item_table[231],[1]=item_table[228],[2]=item_table[232],[3]=item_table[225],[4]=item_table[233]},broadcast_item={[0]=item_table[231],[1]=item_table[228],[2]=item_table[232],[3]=item_table[225],[4]=item_table[233]},},
{seq=4,chongzhi_gold=6480,reward_item={[0]=item_table[234],[1]=item_table[228],[2]=item_table[232],[3]=item_table[225],[4]=item_table[233]},broadcast_item={[0]=item_table[234],[1]=item_table[228],[2]=item_table[232],[3]=item_table[225],[4]=item_table[233]},},
{seq=5,chongzhi_gold=15000,reward_item={[0]=item_table[235],[1]=item_table[236],[2]=item_table[232],[3]=item_table[237],[4]=item_table[233]},broadcast_item={[0]=item_table[235],[1]=item_table[236],[2]=item_table[232],[3]=item_table[237],[4]=item_table[233]},},
{seq=6,chongzhi_gold=30000,reward_item={[0]=item_table[238],[1]=item_table[236],[2]=item_table[154],[3]=item_table[237],[4]=item_table[239]},broadcast_item={[0]=item_table[238],[1]=item_table[236],[2]=item_table[154],[3]=item_table[237],[4]=item_table[239]},},
{seq=7,chongzhi_gold=60000,reward_item={[0]=item_table[240],[1]=item_table[236],[2]=item_table[154],[3]=item_table[237],[4]=item_table[239]},broadcast_item={[0]=item_table[240],[1]=item_table[236],[2]=item_table[154],[3]=item_table[237],[4]=item_table[239]},},
{seq=8,chongzhi_gold=120000,reward_item={[0]=item_table[241],[1]=item_table[242],[2]=item_table[243],[3]=item_table[244],[4]=item_table[245]},broadcast_item={[0]=item_table[241],[1]=item_table[242],[2]=item_table[243],[3]=item_table[244],[4]=item_table[245]},},
{seq=9,chongzhi_gold=180000,reward_item={[0]=item_table[246],[1]=item_table[242],[2]=item_table[64],[3]=item_table[244],[4]=item_table[245]},broadcast_item={[0]=item_table[246],[1]=item_table[242],[2]=item_table[64],[3]=item_table[244],[4]=item_table[245]},},
{seq=10,chongzhi_gold=280000,reward_item={[0]=item_table[247],[1]=item_table[248],[2]=item_table[64],[3]=item_table[249],[4]=item_table[245]},broadcast_item={[0]=item_table[247],[1]=item_table[248],[2]=item_table[64],[3]=item_table[249],[4]=item_table[245]},},
{seq=11,chongzhi_gold=380000,reward_item={[0]=item_table[250],[1]=item_table[248],[2]=item_table[251],[3]=item_table[249],[4]=item_table[252]},broadcast_item={[0]=item_table[250],[1]=item_table[248],[2]=item_table[251],[3]=item_table[249],[4]=item_table[252]},},
{seq=12,chongzhi_gold=480000,reward_item={[0]=item_table[253],[1]=item_table[248],[2]=item_table[251],[3]=item_table[46],[4]=item_table[252]},broadcast_item={[0]=item_table[253],[1]=item_table[248],[2]=item_table[251],[3]=item_table[46],[4]=item_table[252]},},
{seq=13,chongzhi_gold=580000,reward_item={[0]=item_table[254],[1]=item_table[255],[2]=item_table[251],[3]=item_table[46],[4]=item_table[256]},broadcast_item={[0]=item_table[254],[1]=item_table[255],[2]=item_table[251],[3]=item_table[46],[4]=item_table[256]},},
{seq=14,chongzhi_gold=680000,reward_item={[0]=item_table[257],[1]=item_table[255],[2]=item_table[61],[3]=item_table[41],[4]=item_table[256]},broadcast_item={[0]=item_table[257],[1]=item_table[255],[2]=item_table[61],[3]=item_table[41],[4]=item_table[256]},}
},

total_chongzhi_meta_table_map={
},
cloud_buy_times={
{buy_times=15,},
{vip_level=1,buy_times=20,},
{vip_level=2,},
{vip_level=3,},
{vip_level=4,buy_times=35,},
{vip_level=5,buy_times=40,},
{vip_level=6,},
{vip_level=7,},
{vip_level=8,},
{vip_level=9,},
{vip_level=10,},
{vip_level=11,},
{vip_level=12,}
},

cloud_buy_times_meta_table_map={
[3]=2,	-- depth:1
[4]=3,	-- depth:2
},
cloud_buy_item={
{reward_item=item_table[258],},
{seq=1,},
{seq=2,},
{seq=3,reward_item=item_table[259],},
{seq=4,weight=150,reward_item=item_table[260],},
{seq=5,weight=100,},
{seq=6,weight=900,},
{seq=7,weight=300,},
{seq=8,reward_item=item_table[261],},
{seq=9,weight=167,},
{seq=10,},
{seq=11,weight=75,},
{seq=12,weight=400,reward_item=item_table[162],},
{seq=13,weight=2200,reward_item=item_table[262],},
{seq=15,reward_item=item_table[263],},
{seq=16,weight=600,reward_item=item_table[166],},
{batch=1,},
{batch=1,},
{batch=1,},
{batch=1,},
{batch=1,},
{batch=1,},
{seq=6,},
{batch=1,},
{batch=1,},
{batch=1,},
{batch=1,},
{seq=11,},
{batch=1,},
{batch=1,},
{batch=1,},
{batch=1,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{seq=11,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=2,},
{batch=3,},
{batch=3,},
{batch=3,},
{batch=3,},
{batch=3,},
{batch=3,},
{seq=6,},
{batch=3,},
{batch=3,},
{batch=3,},
{seq=10,},
{batch=3,},
{batch=3,},
{batch=3,},
{batch=3,},
{batch=3,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{seq=10,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=4,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,},
{seq=11,},
{batch=5,},
{batch=5,},
{batch=5,},
{batch=5,}
},

cloud_buy_item_meta_table_map={
[49]=1,	-- depth:1
[81]=49,	-- depth:2
[82]=2,	-- depth:1
[18]=82,	-- depth:2
[17]=81,	-- depth:3
[33]=17,	-- depth:4
[65]=33,	-- depth:5
[50]=18,	-- depth:3
[66]=50,	-- depth:4
[34]=66,	-- depth:5
[3]=7,	-- depth:1
[11]=12,	-- depth:1
[43]=11,	-- depth:2
[44]=43,	-- depth:3
[83]=3,	-- depth:2
[42]=10,	-- depth:1
[51]=83,	-- depth:3
[60]=44,	-- depth:4
[55]=51,	-- depth:4
[56]=8,	-- depth:1
[58]=42,	-- depth:2
[59]=60,	-- depth:5
[40]=56,	-- depth:2
[76]=60,	-- depth:5
[75]=76,	-- depth:6
[74]=58,	-- depth:3
[67]=51,	-- depth:4
[72]=40,	-- depth:3
[54]=6,	-- depth:1
[39]=55,	-- depth:5
[87]=39,	-- depth:6
[86]=54,	-- depth:2
[4]=8,	-- depth:1
[9]=5,	-- depth:1
[15]=14,	-- depth:1
[19]=67,	-- depth:5
[22]=86,	-- depth:3
[38]=22,	-- depth:4
[24]=72,	-- depth:4
[23]=19,	-- depth:6
[71]=23,	-- depth:7
[70]=38,	-- depth:5
[26]=74,	-- depth:4
[27]=75,	-- depth:7
[28]=27,	-- depth:8
[35]=19,	-- depth:6
[91]=27,	-- depth:8
[92]=91,	-- depth:9
[90]=26,	-- depth:5
[88]=24,	-- depth:5
[73]=9,	-- depth:2
[85]=5,	-- depth:1
[77]=13,	-- depth:1
[84]=4,	-- depth:2
[94]=14,	-- depth:1
[93]=77,	-- depth:2
[78]=94,	-- depth:2
[79]=15,	-- depth:2
[89]=73,	-- depth:3
[80]=16,	-- depth:1
[48]=80,	-- depth:2
[68]=84,	-- depth:3
[20]=68,	-- depth:4
[21]=85,	-- depth:2
[25]=89,	-- depth:4
[29]=93,	-- depth:3
[30]=78,	-- depth:3
[31]=79,	-- depth:3
[32]=48,	-- depth:3
[36]=20,	-- depth:5
[37]=21,	-- depth:3
[41]=25,	-- depth:5
[45]=29,	-- depth:4
[46]=30,	-- depth:4
[47]=31,	-- depth:4
[95]=47,	-- depth:5
[52]=36,	-- depth:6
[53]=37,	-- depth:4
[57]=41,	-- depth:6
[61]=45,	-- depth:5
[62]=46,	-- depth:5
[63]=95,	-- depth:6
[64]=32,	-- depth:4
[69]=53,	-- depth:5
[96]=64,	-- depth:5
},
cloud_buy_flush={
{},
{batch=1,flush_day=2,reward_item=item_table[264],},
{batch=2,flush_day=3,},
{batch=3,flush_day=4,},
{batch=4,flush_day=5,},
{batch=5,flush_day=6,}
},

cloud_buy_flush_meta_table_map={
[4]=2,	-- depth:1
[6]=2,	-- depth:1
},
coolsui_crazybuy={
{},
{seq=1,reward_item=item_table[265],part_type=1,view_resouce=8,},
{seq=2,need_gold=998,reward_item=item_table[266],part_type=-1,view_resouce=11101,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=2,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=3,},
{opengame_day=4,},
{opengame_day=4,},
{opengame_day=4,},
{opengame_day=5,},
{opengame_day=5,},
{opengame_day=5,},
{opengame_day=6,},
{opengame_day=6,},
{opengame_day=6,},
{opengame_day=7,},
{opengame_day=7,},
{opengame_day=7,}
},

coolsui_crazybuy_meta_table_map={
[17]=2,	-- depth:1
[14]=17,	-- depth:2
[11]=14,	-- depth:3
[8]=11,	-- depth:4
[5]=8,	-- depth:5
[20]=5,	-- depth:6
[12]=3,	-- depth:1
[9]=12,	-- depth:2
[15]=9,	-- depth:3
[6]=15,	-- depth:4
[18]=6,	-- depth:5
[21]=18,	-- depth:6
},
discount_gift={
{opengame_day=1,},
{opengame_day=1,},
{opengame_day=1,need_gold=30,show_gold=50,goods_item={[0]=item_table[162]},},
{opengame_day=2,},
{opengame_day=2,need_gold=5,show_gold=15,goods_item={[0]=item_table[159]},},
{opengame_day=2,},
{goods_item={[0]=item_table[230]},},
{seq=1,need_gold=30,show_gold=100,goods_item={[0]=item_table[226]},discount=3,},
{seq=2,need_gold=180,show_gold=300,goods_item={[0]=item_table[233]},discount=6,},
{seq=3,need_gold=350,show_gold=500,goods_item={[0]=item_table[267],[1]=item_table[230]},discount=7,},
{opengame_day=5,need_gold=120,show_gold=240,goods_item={[0]=item_table[268],[1]=item_table[268]},},
{opengame_day=5,need_gold=54,show_gold=180,goods_item={[0]=item_table[269]},},
{opengame_day=5,need_gold=430,show_gold=720,goods_item={[0]=item_table[268],[1]=item_table[268]},},
{opengame_day=5,need_gold=756,show_gold=1080,goods_item={[0]=item_table[269],[1]=item_table[269]},},
{opengame_day=6,need_gold=160,show_gold=320,goods_item={[0]=item_table[270],[1]=item_table[271]},},
{opengame_day=6,need_gold=48,show_gold=160,goods_item={[0]=item_table[270]},},
{opengame_day=6,need_gold=240,show_gold=400,goods_item={[0]=item_table[272]},},
{opengame_day=6,need_gold=560,show_gold=800,goods_item={[0]=item_table[273],[1]=item_table[272]},},
{opengame_day=7,need_gold=250,show_gold=500,goods_item={[0]=item_table[204],[1]=item_table[174]},}
},

discount_gift_meta_table_map={
[5]=8,	-- depth:1
[18]=10,	-- depth:1
[3]=9,	-- depth:1
[12]=8,	-- depth:1
[13]=9,	-- depth:1
[14]=10,	-- depth:1
[2]=5,	-- depth:2
[16]=8,	-- depth:1
[17]=9,	-- depth:1
[6]=3,	-- depth:2
},
discount_describe={
{},
{activity_open=2,activity_close=2,},
{activity_open=3,activity_close=3,activity_explain="开服狂欢，限时折扣礼包，活动期间低价出售灵骑进阶丹、祝福卡，灵骑升不停，冲榜必备！",},
{activity_open=4,activity_close=4,activity_explain="开服狂欢，限时折扣礼包，活动期间低价出售宠物进阶丹、祝福卡，宠物升不停，冲榜必备！",},
{activity_open=5,activity_close=5,activity_explain="开服狂欢，限时折扣礼包，活动期间低价出售优惠礼包，买不了吃亏买不了上当，冲榜必备！",},
{activity_open=6,activity_close=6,activity_explain="开服狂欢，限时折扣礼包，活动期间低价出售玉魄礼包，玉魄升不停，冲榜必备！",},
{activity_open=7,activity_close=7,activity_explain="开服狂欢，限时折扣礼包，活动期间低价出售战力礼包，战力升不停，冲榜必备！",}
},

discount_describe_meta_table_map={
},
zero_gift={
{},
{opengame_day=2,com_reward={[0]=item_table[162],[1]=item_table[274],[2]=item_table[274],[3]=item_table[274]},vip_reward={[0]=item_table[159],[1]=item_table[275],[2]=item_table[275],[3]=item_table[275]},},
{opengame_day=4,com_reward={[0]=item_table[203],[1]=item_table[276],[2]=item_table[276],[3]=item_table[276]},vip_reward={[0]=item_table[171],[1]=item_table[277],[2]=item_table[277],[3]=item_table[277]},},
{opengame_day=5,com_reward={[0]=item_table[269],[1]=item_table[268],[2]=item_table[268],[3]=item_table[268]},vip_reward={[0]=item_table[278],[1]=item_table[279],[2]=item_table[279],[3]=item_table[279]},},
{opengame_day=6,com_reward={[0]=item_table[280],[1]=item_table[281],[2]=item_table[281],[3]=item_table[281]},vip_reward={[0]=item_table[282],[1]=item_table[283],[2]=item_table[283],[3]=item_table[283]},},
{opengame_day=7,com_reward={[0]=item_table[187],[1]=item_table[179],[2]=item_table[284],[3]=item_table[285]},vip_reward={[0]=item_table[185],[1]=item_table[176],[2]=item_table[286],[3]=item_table[287]},},
{opengame_day=8,com_reward={[0]=item_table[288]},vip_reward={[0]=item_table[288]},have_vip_reward=0,}
},

zero_gift_meta_table_map={
},
kaizonglipai_new={
{},
{max_zhanli=2,min_zhanli=4,title_id=4002,drop_rate=1000000,zhanli=12480,},
{max_zhanli=5,min_zhanli=10,title_id=4003,drop_rate=500000,zhanli=6240,}
},

kaizonglipai_new_meta_table_map={
},
kaizonglipai_drop={
{},
{},
{},
{}
},

kaizonglipai_drop_meta_table_map={
},
qfjx_role_jx_reward={
{},
{reward_id=2,reward_item={[0]=item_table[289]},},
{reward_id=3,reward_item={[0]=item_table[290]},},
{reward_id=4,reward_item={[0]=item_table[291]},},
{reward_id=5,reward_item={[0]=item_table[292]},},
{reward_id=6,reward_item={[0]=item_table[176]},},
{reward_id=7,reward_item={[0]=item_table[293]},}
},

qfjx_role_jx_reward_meta_table_map={
},
qfjx_role_lj_reward={
{reward_item={[0]=item_table[178]},},
{index=2,juanxian_num=15,reward_item={[0]=item_table[269]},},
{index=3,juanxian_num=30,},
{index=4,juanxian_num=45,reward_item={[0]=item_table[180]},},
{index=5,juanxian_num=60,},
{index=6,juanxian_num=75,reward_item={[0]=item_table[221]},},
{index=7,juanxian_num=95,reward_item={[0]=item_table[284]},},
{index=8,juanxian_num=120,reward_item={[0]=item_table[259]},},
{index=9,juanxian_num=145,reward_item={[0]=item_table[294]},},
{index=10,juanxian_num=170,reward_item={[0]=item_table[260]},},
{index=11,juanxian_num=195,},
{index=12,juanxian_num=225,reward_item={[0]=item_table[295]},}
},

qfjx_role_lj_reward_meta_table_map={
[11]=7,	-- depth:1
},
qfjx_server_day_reward={
{param1=36416,param2=50000,desc="铜钱*50000",},
{index=2,server_day_juanxian_num=500,reward_type=4,param2=0,param3=0,name="魔王重生",icon="jx_boss",},
{index=3,server_day_juanxian_num=750,param1=28791,desc="5阶1星红色灵甲*1",},
{index=4,server_day_juanxian_num=1000,},
{index=5,server_day_juanxian_num=1250,param1=28792,desc="5阶1星红色玄带*1",},
{index=6,server_day_juanxian_num=1500,},
{index=7,server_day_juanxian_num=1750,},
{index=8,server_day_juanxian_num=2000,},
{index=9,server_day_juanxian_num=2250,param1=28793,desc="5阶1星红色灵匕*1",},
{index=10,server_day_juanxian_num=2500,},
{index=11,server_day_juanxian_num=2750,param1=28794,desc="5阶1星红色灵盔*1",},
{index=12,server_day_juanxian_num=3000,},
{index=13,server_day_juanxian_num=3250,param1=28795,desc="5阶1星红色玄腕*1",},
{index=14,server_day_juanxian_num=3500,},
{index=15,server_day_juanxian_num=3850,param1=27821,desc="元神之力*1",},
{index=16,server_day_juanxian_num=4575,},
{index=17,server_day_juanxian_num=5300,param1=28796,desc="5阶1星红色靴子*1",},
{index=18,server_day_juanxian_num=6025,},
{index=19,server_day_juanxian_num=6750,param1=28797,desc="5阶1星红色灵剑*1",},
{index=20,server_day_juanxian_num=8250,},
{index=21,server_day_juanxian_num=9750,param1=36528,desc="固定经验丹*1",},
{index=22,server_day_juanxian_num=10700,},
{index=23,server_day_juanxian_num=11650,param1=27611,param2=3,desc="初级神灵神纹*3",},
{index=24,server_day_juanxian_num=12500,reward_type=4,param1=2,param3=0,name="魔王重生",icon="jx_boss",desc="混沌魔域重生",},
{index=25,server_day_juanxian_num=13350,param1=26000,param2=3000,desc="真气*3000",},
{index=26,server_day_juanxian_num=13725,},
{index=27,server_day_juanxian_num=14100,param1=29100,desc="真龙密匙*1",},
{index=28,server_day_juanxian_num=14450,},
{index=29,server_day_juanxian_num=14800,param2=100000,desc="铜钱*100000",},
{index=30,server_day_juanxian_num=15200,},
{index=31,server_day_juanxian_num=15600,param1=27820,desc="残暴之力*1",},
{index=32,server_day_juanxian_num=16000,},
{index=33,server_day_juanxian_num=16400,param1=27822,desc="自然之力*1",}
},

qfjx_server_day_reward_meta_table_map={
[7]=27,	-- depth:1
[29]=1,	-- depth:1
[30]=2,	-- depth:1
[26]=2,	-- depth:1
[22]=2,	-- depth:1
[10]=2,	-- depth:1
[18]=2,	-- depth:1
[6]=2,	-- depth:1
[14]=2,	-- depth:1
[32]=24,	-- depth:1
[16]=24,	-- depth:1
[28]=24,	-- depth:1
[12]=24,	-- depth:1
[8]=24,	-- depth:1
[20]=24,	-- depth:1
[4]=24,	-- depth:1
},
openserver_act_order={
{},
{actid=113,order=2,},
{actid=2219,order=3,},
{actid=114,order=4,},
{actid=2123,order=5,},
{actid=2209,order=6,},
{actid=2232,order=7,},
{actid=2233,order=8,}
},

openserver_act_order_meta_table_map={
},
xunyulu={
{reward_item={[0]=item_table[296]},},
{id=2,activity_id=37,name="云梦秘境",des="激战神兽，共赢大礼",icon="btn_30",task_type=46,},
{id=3,activity_id=27,name="守卫守护",des="守卫仙盟， 人人有责",icon="btn_31",task_type=19,},
{id=4,activity_id=3073,name="九层妖塔",des="更上一层楼",icon="btn_22",task_type=17,},
{id=5,activity_id=33,name="夜战云巅",des="夜战云巅，征战四方",icon="btn_34",task_type=25,},
{id=6,activity_id=1,name="诛邪战场",des="诛邪战场，抵抗魔君",icon="btn_35",task_type=23,},
{id=7,activity_id=3074,name="沧海夺锋",des="天下第一，舍我其谁",icon="btn_19",task_type=20,},
{id=8,activity_id=3075,name="逐鹿仙缘",des="这是一场团队游戏",icon="btn_20",task_type=21,}
},

xunyulu_meta_table_map={
},
herd={
{}
},

herd_meta_table_map={
},
herd_grade={
{},
{id=2,boss_level_limit=250,reward={[0]=item_table[297],[1]=item_table[298]},join_reward={[0]=item_table[299]},},
{id=3,boss_level_limit=270,times=100,reward={[0]=item_table[196],[1]=item_table[196]},join_reward={[0]=item_table[300]},},
{id=4,boss_level_limit=300,times=10,reward={[0]=item_table[301],[1]=item_table[301]},join_reward={[0]=item_table[302]},}
},

herd_grade_meta_table_map={
},
herd_time={
{}
},

herd_time_meta_table_map={
},
tehuilibao={
{},
{index=2,model_scale=0.75,whole_display_pos="-80|-40",},
{index=3,gift_icon="kf_gift_icon_17",gift_bannar="ditu_9",show_type=1,res_path="model/weapon/9011004_prefab",res_id=901100401,model_scale=0.65,whole_display_pos="-110|-60",},
{index=4,price=1888,reward={[0]=item_table[303],[1]=item_table[304],[2]=item_table[305],[3]=item_table[306],[4]=item_table[307]},gift_name="坐骑礼包",show_type=6,discount="6折",},
{index=5,price=1888,reward={[0]=item_table[308],[1]=item_table[309],[2]=item_table[310],[3]=item_table[311],[4]=item_table[312]},gift_name="装备礼包",show_item_id=30530,res_id=4036,discount="6折",},
{index=6,price=1888,reward={[0]=item_table[313]},gift_name="雷法礼包",gift_icon="kf_gift_icon_03",gift_bannar="ditu_1",show_type=10,show_item_id=37302,res_id="",model_scale=0.6,whole_display_pos="-90|-20",discount="6折",}
},

tehuilibao_meta_table_map={
},
rank_reach_reward={
{},
{reach_goal=41,index=2,reach_goal_reward_item={[0]=item_table[314]},},
{reach_goal=51,index=3,reach_goal_reward_item={[0]=item_table[315]},},
{reach_goal=61,index=4,reach_goal_reward_item={[0]=item_table[316]},},
{rush_type=3,reach_goal=21,reach_goal_reward_item={[0]=item_table[239]},},
{rush_type=3,index=2,reach_goal_reward_item={[0]=item_table[317]},},
{rush_type=3,reach_goal=41,index=3,reach_goal_reward_item={[0]=item_table[318]},},
{rush_type=3,reach_goal=51,index=4,reach_goal_reward_item={[0]=item_table[319]},}
},

rank_reach_reward_meta_table_map={
},
invest_type={
{art_title=1,txt="加速幻兽培养，自选极品幻兽",model_bundle_name="model/yushou/4050_prefab",model_asset_name=4050,display_pos="-140|-50",display_scale=0.8,basic_reward_item={[0]=item_table[320],[1]=item_table[320],[2]=item_table[320],[3]=item_table[320],[4]=item_table[321],[5]=item_table[320],[6]=item_table[320],[7]=item_table[320],[8]=item_table[320],[9]=item_table[321],[10]=item_table[320],[11]=item_table[320],[12]=item_table[320],[13]=item_table[320],[14]=item_table[321],[15]=item_table[320],[16]=item_table[320],[17]=item_table[320],[18]=item_table[320],[19]=item_table[321],[20]=item_table[320],[21]=item_table[320],[22]=item_table[320],[23]=item_table[320],[24]=item_table[321],[25]=item_table[320],[26]=item_table[320],[27]=item_table[320],[28]=item_table[320],[29]=item_table[321]},invest_reward_item={[0]=item_table[322],[1]=item_table[64],[2]=item_table[232],[3]=item_table[323],[4]=item_table[232],[5]=item_table[323],[6]=item_table[232],[7]=item_table[323],[8]=item_table[232],[9]=item_table[323],[10]=item_table[232],[11]=item_table[323],[12]=item_table[232],[13]=item_table[323],[14]=item_table[232],[15]=item_table[323],[16]=item_table[232],[17]=item_table[323],[18]=item_table[232],[19]=item_table[323],[20]=item_table[232],[21]=item_table[323],[22]=item_table[232],[23]=item_table[323],[24]=item_table[232],[25]=item_table[323],[26]=item_table[232],[27]=item_table[323],[28]=item_table[232],[29]=item_table[323],[30]=item_table[232]},},
{type=1,rmb_seq=1,art_title=2,txt="加速神灵培养，获取真武大帝",model_bundle_name="model/tianshen/10113_prefab",model_asset_name=10113,display_pos="-130|-20",open_panel="TianShenView#tianshen_activation",label_name="神灵投资",basic_reward_item={[0]=item_table[324],[1]=item_table[324],[2]=item_table[324],[3]=item_table[324],[4]=item_table[325],[5]=item_table[324],[6]=item_table[324],[7]=item_table[324],[8]=item_table[324],[9]=item_table[325],[10]=item_table[324],[11]=item_table[324],[12]=item_table[324],[13]=item_table[324],[14]=item_table[325],[15]=item_table[324],[16]=item_table[324],[17]=item_table[324],[18]=item_table[324],[19]=item_table[325],[20]=item_table[324],[21]=item_table[324],[22]=item_table[324],[23]=item_table[324],[24]=item_table[325],[25]=item_table[324],[26]=item_table[324],[27]=item_table[324],[28]=item_table[324],[29]=item_table[325]},invest_reward_item={[0]=item_table[322],[1]=item_table[326],[2]=item_table[327],[3]=item_table[328],[4]=item_table[327],[5]=item_table[328],[6]=item_table[327],[7]=item_table[328],[8]=item_table[327],[9]=item_table[328],[10]=item_table[327],[11]=item_table[328],[12]=item_table[327],[13]=item_table[328],[14]=item_table[327],[15]=item_table[328],[16]=item_table[327],[17]=item_table[328],[18]=item_table[327],[19]=item_table[328],[20]=item_table[327],[21]=item_table[328],[22]=item_table[327],[23]=item_table[328],[24]=item_table[327],[25]=item_table[328],[26]=item_table[327],[27]=item_table[328],[28]=item_table[327],[29]=item_table[328],[30]=item_table[327]},},
{type=2,rmb_seq=2,art_title=3,txt="加速珍骑培养，获取天仙魔牛",model_bundle_name="model/zuoqi/2004_prefab",model_asset_name=2004,display_pos="-150|-20",open_panel="NewAppearanceWGView#new_appearance_upgrade_mount",label_name="珍骑投资",basic_reward_item={[0]=item_table[276],[1]=item_table[276],[2]=item_table[276],[3]=item_table[276],[4]=item_table[203],[5]=item_table[276],[6]=item_table[276],[7]=item_table[276],[8]=item_table[276],[9]=item_table[203],[10]=item_table[276],[11]=item_table[276],[12]=item_table[276],[13]=item_table[276],[14]=item_table[203],[15]=item_table[276],[16]=item_table[276],[17]=item_table[276],[18]=item_table[203],[19]=item_table[276],[20]=item_table[276],[21]=item_table[276],[22]=item_table[276],[23]=item_table[203],[24]=item_table[276],[25]=item_table[276],[26]=item_table[276],[27]=item_table[276],[28]=item_table[203]},invest_reward_item={[0]=item_table[322],[1]=item_table[329],[2]=item_table[204],[3]=item_table[267],[4]=item_table[204],[5]=item_table[267],[6]=item_table[204],[7]=item_table[267],[8]=item_table[204],[9]=item_table[267],[10]=item_table[204],[11]=item_table[267],[12]=item_table[204],[13]=item_table[267],[14]=item_table[204],[15]=item_table[267],[16]=item_table[204],[17]=item_table[267],[18]=item_table[204],[19]=item_table[267],[20]=item_table[204],[21]=item_table[267],[22]=item_table[204],[23]=item_table[267],[24]=item_table[204],[25]=item_table[267],[26]=item_table[204],[27]=item_table[267],[28]=item_table[204],[29]=item_table[267],[30]=item_table[204]},},
{type=3,rmb_seq=3,art_title=4,txt="加速仙翼培养，获取寒天楚辞",model_bundle_name="model/wings/2009_prefab",model_asset_name=2009,display_pos="-130|-20",display_scale=0.9,open_panel="NewAppearanceWGView#new_appearance_upgrade_wing",label_name="仙翼投资",basic_reward_item={[0]=item_table[330],[1]=item_table[330],[2]=item_table[330],[3]=item_table[330],[4]=item_table[187],[5]=item_table[330],[6]=item_table[330],[7]=item_table[330],[8]=item_table[330],[9]=item_table[187],[10]=item_table[330],[11]=item_table[330],[12]=item_table[330],[13]=item_table[330],[14]=item_table[187],[15]=item_table[330],[16]=item_table[330],[17]=item_table[330],[18]=item_table[330],[19]=item_table[187],[20]=item_table[330],[21]=item_table[330],[22]=item_table[330],[23]=item_table[330],[24]=item_table[187],[25]=item_table[330],[26]=item_table[330],[27]=item_table[330],[28]=item_table[330],[29]=item_table[187]},invest_reward_item={[0]=item_table[322],[1]=item_table[331],[2]=item_table[188],[3]=item_table[272],[4]=item_table[188],[5]=item_table[272],[6]=item_table[188],[7]=item_table[272],[8]=item_table[188],[9]=item_table[272],[10]=item_table[188],[11]=item_table[272],[12]=item_table[188],[13]=item_table[272],[14]=item_table[188],[15]=item_table[272],[16]=item_table[188],[17]=item_table[272],[18]=item_table[188],[19]=item_table[272],[20]=item_table[188],[21]=item_table[272],[22]=item_table[188],[23]=item_table[272],[24]=item_table[188],[25]=item_table[272],[26]=item_table[188],[27]=item_table[272],[28]=item_table[188],[29]=item_table[272],[30]=item_table[188]},},
{type=4,rmb_seq=4,art_title=5,txt="加速魂环培养，自选万年魂骨",show_type=2,soul_ring_id="9|18|27|36|45|54|63|72",display_pos="-140|-60",rotation="-13|0|0",display_scale=0.9,open_panel="shenshou",label_name="魂环投资",basic_reward_item={[0]=item_table[330],[1]=item_table[330],[2]=item_table[330],[3]=item_table[187],[4]=item_table[330],[5]=item_table[330],[6]=item_table[330],[7]=item_table[330],[8]=item_table[187],[9]=item_table[330],[10]=item_table[330],[11]=item_table[330],[12]=item_table[330],[13]=item_table[187],[14]=item_table[330],[15]=item_table[330],[16]=item_table[330],[17]=item_table[330],[18]=item_table[187],[19]=item_table[330],[20]=item_table[330],[21]=item_table[330],[22]=item_table[330],[23]=item_table[187],[24]=item_table[330],[25]=item_table[330],[26]=item_table[330],[27]=item_table[330],[28]=item_table[187],[29]=item_table[332]},invest_reward_item={[0]=item_table[322],[1]=item_table[333],[2]=item_table[332],[3]=item_table[334],[4]=item_table[332],[5]=item_table[334],[6]=item_table[332],[7]=item_table[334],[8]=item_table[332],[9]=item_table[334],[10]=item_table[332],[11]=item_table[334],[12]=item_table[332],[13]=item_table[334],[14]=item_table[332],[15]=item_table[334],[16]=item_table[332],[17]=item_table[334],[18]=item_table[332],[19]=item_table[334],[20]=item_table[332],[21]=item_table[334],[22]=item_table[332],[23]=item_table[334],[24]=item_table[332],[25]=item_table[334],[26]=item_table[332],[27]=item_table[334],[28]=item_table[332],[29]=item_table[334],[30]=item_table[332]},},
{type=5,rmb_seq=5,model_show_itemid=37618,open_panel="NewAppearanceWGView#new_appearance_upgrade_shenbing",label_name="神武投资",},
{type=6,rmb_seq=6,open_panel="equipment#equipment_strength",label_name="装备投资",},
{type=7,rmb_seq=7,open_panel="MingWenView",label_name="万魂投资",},
{type=8,rmb_seq=8,open_panel="ShanHaiJingView",label_name="图鉴投资",},
{type=9,rmb_seq=9,open_panel="ArtifactView",label_name="双修投资",}
},

invest_type_meta_table_map={
[7]=6,	-- depth:1
[8]=6,	-- depth:1
[9]=6,	-- depth:1
[10]=6,	-- depth:1
},
invest_task={
{item_list={[0]=item_table[320]},rmb_item_list={[0]=item_table[335],[1]=item_table[336]},describe_type=4,},
{seq=2,param1=2,},
{seq=3,param1=4,},
{seq=4,param1=6,},
{seq=5,param1=8,},
{seq=6,param1=10,},
{seq=7,param1=12,},
{seq=8,param1=14,},
{seq=9,param1=16,},
{seq=10,param1=18,},
{seq=11,param1=20,},
{seq=12,param1=22,},
{seq=13,param1=24,},
{seq=14,param1=26,},
{seq=15,param1=28,},
{seq=16,param1=30,},
{seq=17,param1=32,},
{seq=18,param1=34,},
{seq=19,param1=36,},
{seq=20,param1=38,rmb_item_list={[0]=item_table[323],[1]=item_table[261]},},
{seq=21,param1=40,},
{seq=22,param1=42,rmb_item_list={[0]=item_table[323],[1]=item_table[261]},},
{seq=23,param1=44,},
{seq=24,param1=46,},
{seq=25,param1=48,item_list={[0]=item_table[321]},},
{seq=26,param1=50,},
{seq=27,param1=52,},
{seq=28,param1=54,},
{seq=29,param1=56,rmb_item_list={[0]=item_table[335],[1]=item_table[337]},},
{seq=30,param1=58,rmb_item_list={[0]=item_table[64],[1]=item_table[323]},is_fix_show=1,},
{type=1,item_list={[0]=item_table[324]},rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=2,param1=5,},
{seq=3,param1=10,},
{seq=4,param1=15,rmb_item_list={[0]=item_table[49],[1]=item_table[261]},},
{seq=5,param1=20,item_list={[0]=item_table[325]},},
{seq=6,param1=25,},
{seq=7,param1=30,},
{seq=8,param1=35,},
{seq=9,param1=40,},
{seq=10,param1=45,rmb_item_list={[0]=item_table[49],[1]=item_table[261]},},
{seq=11,param1=50,},
{seq=12,param1=55,},
{seq=13,param1=60,},
{seq=14,param1=65,},
{seq=15,param1=70,},
{seq=16,param1=75,},
{seq=17,param1=80,},
{seq=18,param1=85,},
{seq=19,param1=90,},
{seq=20,param1=95,},
{seq=21,param1=100,},
{seq=22,param1=105,},
{seq=23,param1=110,},
{seq=24,param1=115,},
{seq=25,param1=120,},
{seq=26,param1=125,},
{seq=27,param1=130,},
{seq=28,param1=135,},
{seq=29,param1=140,},
{type=1,param1=150,item_list={[0]=item_table[325]},rmb_item_list={[0]=item_table[326],[1]=item_table[261]},},
{type=2,item_list={[0]=item_table[276]},rmb_item_list={[0]=item_table[335],[1]=item_table[204]},describe_type=2,},
{seq=2,param1=2,},
{seq=3,param1=4,},
{seq=4,param1=6,},
{seq=5,param1=8,},
{seq=6,param1=10,},
{seq=7,param1=12,},
{seq=8,param1=14,},
{seq=9,param1=16,},
{seq=10,param1=18,},
{seq=11,param1=20,},
{seq=12,param1=22,},
{seq=13,param1=24,},
{seq=14,param1=26,},
{seq=15,param1=28,},
{seq=16,param1=30,},
{seq=17,param1=32,},
{seq=18,param1=34,},
{seq=19,param1=36,},
{seq=20,param1=40,rmb_item_list={[0]=item_table[267],[1]=item_table[261]},},
{seq=21,param1=44,},
{seq=22,param1=48,},
{seq=23,param1=52,},
{seq=24,param1=56,},
{seq=25,param1=60,item_list={[0]=item_table[203]},},
{seq=26,param1=64,},
{seq=27,param1=68,},
{seq=28,param1=72,rmb_item_list={[0]=item_table[267],[1]=item_table[261]},},
{seq=29,param1=76,},
{seq=30,param1=80,is_fix_show=1,},
{type=3,item_list={[0]=item_table[330]},rmb_item_list={[0]=item_table[335],[1]=item_table[188]},},
{seq=2,param1=5,},
{seq=3,param1=10,},
{seq=4,param1=15,},
{seq=5,param1=20,},
{seq=6,param1=25,},
{seq=7,param1=30,},
{seq=8,param1=35,},
{seq=9,param1=40,},
{seq=10,param1=50,},
{seq=11,param1=60,},
{seq=12,param1=70,},
{seq=13,param1=80,},
{seq=14,param1=90,},
{seq=15,param1=100,item_list={[0]=item_table[187]},},
{seq=16,param1=110,},
{seq=17,param1=120,},
{seq=18,param1=130,},
{seq=19,param1=140,},
{seq=20,param1=150,item_list={[0]=item_table[187]},},
{seq=21,param1=165,},
{seq=22,param1=180,},
{seq=23,param1=195,},
{seq=24,param1=210,},
{seq=25,param1=225,},
{seq=26,param1=240,},
{seq=27,param1=255,},
{seq=28,param1=270,rmb_item_list={[0]=item_table[272]},},
{seq=29,param1=285,},
{type=3,param1=300,item_list={[0]=item_table[187]},rmb_item_list={[0]=item_table[331]},},
{type=4,item_list={[0]=item_table[332]},rmb_item_list={[0]=item_table[335],[1]=item_table[332]},describe_type=3,},
{seq=2,param1=50000,rmb_item_list={[0]=item_table[334]},},
{seq=3,param1=100000,},
{seq=4,param1=150000,},
{seq=5,param1=200000,},
{seq=6,param1=250000,},
{seq=7,param1=300000,},
{seq=8,param1=350000,},
{seq=9,param1=400000,},
{seq=10,param1=450000,},
{seq=11,param1=500000,},
{seq=12,param1=550000,},
{seq=13,param1=600000,},
{seq=14,param1=650000,},
{seq=15,param1=700000,},
{seq=16,param1=750000,},
{seq=17,param1=800000,},
{seq=18,param1=850000,},
{seq=19,param1=900000,},
{seq=20,param1=950000,},
{seq=21,param1=1000000,},
{seq=22,param1=1050000,},
{seq=23,param1=1100000,},
{seq=24,param1=1150000,},
{seq=25,param1=1200000,},
{seq=26,param1=1250000,},
{seq=27,param1=1300000,},
{seq=28,param1=1350000,},
{seq=29,param1=1400000,},
{type=4,item_list={[0]=item_table[332]},rmb_item_list={[0]=item_table[333]},},
{type=5,},
{seq=2,param1=2,},
{seq=3,param1=4,},
{seq=4,param1=6,},
{seq=5,param1=8,},
{seq=6,param1=10,},
{type=5,},
{seq=8,param1=14,},
{type=5,},
{type=5,},
{type=5,},
{seq=12,param1=22,},
{type=5,},
{seq=14,param1=26,},
{seq=15,param1=28,},
{type=5,},
{type=5,},
{type=5,},
{type=5,},
{seq=20,param1=38,item_list={[0]=item_table[284]},},
{type=5,},
{type=5,},
{type=5,},
{type=5,},
{type=5,},
{type=5,seq=26,param1=50,rmb_item_list={[0]=item_table[339]},},
{type=5,},
{seq=28,param1=54,},
{type=5,},
{type=5,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=2,param1=50000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=4,param1=150000,},
{seq=5,param1=200000,},
{seq=6,param1=250000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=8,param1=350000,rmb_item_list={[0]=item_table[49],[1]=item_table[261]},},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=10,param1=450000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=12,param1=550000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=14,param1=650000,},
{seq=15,param1=700000,},
{seq=16,param1=750000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=18,param1=850000,},
{seq=19,param1=900000,},
{type=6,rmb_item_list={[0]=item_table[49],[1]=item_table[261]},},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{type=6,rmb_item_list={[0]=item_table[49],[1]=item_table[261]},},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=24,param1=1150000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{type=6,rmb_item_list={[0]=item_table[49],[1]=item_table[261]},},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{seq=28,param1=1350000,},
{type=6,rmb_item_list={[0]=item_table[335],[1]=item_table[338]},},
{type=6,param1=1450000,rmb_item_list={[0]=item_table[326],[1]=item_table[261]},describe_type=3,},
{type=7,describe_type=3,},
{seq=2,param1=50000,},
{seq=3,param1=100000,},
{seq=4,param1=150000,},
{seq=5,param1=200000,},
{seq=6,param1=250000,},
{seq=7,param1=300000,},
{seq=8,param1=350000,},
{seq=9,param1=400000,},
{seq=10,param1=450000,},
{seq=11,param1=500000,},
{seq=12,param1=550000,},
{seq=13,param1=600000,},
{seq=14,param1=650000,},
{seq=15,param1=700000,},
{seq=16,param1=750000,},
{seq=17,param1=800000,},
{seq=18,param1=850000,},
{seq=19,param1=900000,},
{seq=20,param1=950000,item_list={[0]=item_table[284]},},
{seq=21,param1=1000000,},
{seq=22,param1=1050000,},
{seq=23,param1=1100000,},
{seq=24,param1=1150000,},
{type=7,param1=1200000,describe_type=3,},
{type=7,param1=1250000,describe_type=3,},
{seq=27,param1=1300000,},
{seq=28,param1=1350000,},
{seq=29,param1=1400000,},
{type=7,rmb_item_list={[0]=item_table[340]},},
{type=8,},
{type=8,},
{type=8,},
{type=8,},
{type=8,},
{type=8,},
{seq=7,param1=12,},
{type=8,},
{seq=9,param1=16,},
{type=8,},
{seq=11,param1=20,},
{type=8,},
{seq=13,param1=24,},
{type=8,},
{type=8,},
{seq=16,param1=30,},
{seq=17,param1=32,},
{seq=18,param1=34,},
{seq=19,param1=36,},
{type=8,},
{type=8,},
{type=8,seq=22,param1=42,rmb_item_list={[0]=item_table[339]},},
{seq=23,param1=44,},
{seq=24,param1=46,},
{type=8,seq=25,param1=48,item_list={[0]=item_table[284]},},
{type=8,},
{type=8,},
{type=8,},
{type=8,},
{seq=30,param1=60,rmb_item_list={[0]=item_table[340]},is_fix_show=1,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,seq=10,param1=18,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{seq=21,param1=40,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{type=9,},
{seq=27,param1=52,},
{type=9,},
{seq=29,param1=56,},
{type=9,}
},

invest_task_meta_table_map={
[153]=151,	-- depth:1
[299]=271,	-- depth:1
[243]=153,	-- depth:2
[247]=243,	-- depth:3
[249]=243,	-- depth:3
[251]=243,	-- depth:3
[253]=243,	-- depth:3
[257]=243,	-- depth:3
[297]=299,	-- depth:2
[259]=243,	-- depth:3
[263]=243,	-- depth:3
[157]=247,	-- depth:4
[293]=263,	-- depth:4
[291]=299,	-- depth:2
[289]=259,	-- depth:4
[287]=257,	-- depth:4
[267]=297,	-- depth:3
[283]=253,	-- depth:4
[281]=251,	-- depth:4
[269]=299,	-- depth:2
[279]=249,	-- depth:4
[277]=157,	-- depth:5
[261]=291,	-- depth:3
[159]=279,	-- depth:5
[273]=243,	-- depth:3
[161]=281,	-- depth:5
[177]=267,	-- depth:4
[171]=261,	-- depth:4
[169]=289,	-- depth:5
[179]=269,	-- depth:3
[173]=293,	-- depth:5
[181]=211,	-- depth:1
[167]=287,	-- depth:5
[163]=283,	-- depth:5
[231]=211,	-- depth:1
[227]=231,	-- depth:2
[223]=231,	-- depth:2
[178]=176,	-- depth:1
[221]=231,	-- depth:2
[219]=231,	-- depth:2
[264]=262,	-- depth:1
[217]=231,	-- depth:2
[237]=231,	-- depth:2
[266]=176,	-- depth:1
[268]=178,	-- depth:2
[233]=231,	-- depth:2
[213]=231,	-- depth:2
[175]=265,	-- depth:1
[256]=262,	-- depth:1
[258]=262,	-- depth:1
[158]=176,	-- depth:1
[162]=176,	-- depth:1
[156]=176,	-- depth:1
[155]=175,	-- depth:2
[154]=176,	-- depth:1
[152]=176,	-- depth:1
[164]=176,	-- depth:1
[165]=175,	-- depth:2
[272]=152,	-- depth:2
[174]=264,	-- depth:2
[166]=256,	-- depth:2
[244]=154,	-- depth:2
[245]=155,	-- depth:3
[246]=156,	-- depth:2
[168]=258,	-- depth:2
[248]=158,	-- depth:2
[252]=162,	-- depth:2
[172]=262,	-- depth:1
[254]=164,	-- depth:2
[255]=165,	-- depth:3
[242]=272,	-- depth:3
[239]=231,	-- depth:2
[229]=231,	-- depth:2
[275]=245,	-- depth:4
[296]=266,	-- depth:2
[295]=175,	-- depth:2
[294]=174,	-- depth:3
[292]=172,	-- depth:2
[298]=268,	-- depth:3
[288]=168,	-- depth:3
[286]=166,	-- depth:3
[285]=255,	-- depth:4
[274]=244,	-- depth:3
[276]=246,	-- depth:3
[278]=248,	-- depth:3
[282]=252,	-- depth:3
[284]=254,	-- depth:3
[188]=181,	-- depth:2
[189]=219,	-- depth:3
[235]=265,	-- depth:1
[191]=221,	-- depth:3
[192]=188,	-- depth:3
[22]=1,	-- depth:1
[187]=217,	-- depth:3
[24]=22,	-- depth:2
[25]=1,	-- depth:1
[26]=22,	-- depth:2
[193]=223,	-- depth:3
[23]=1,	-- depth:1
[186]=188,	-- depth:3
[184]=188,	-- depth:3
[170]=176,	-- depth:1
[21]=23,	-- depth:2
[20]=25,	-- depth:2
[19]=23,	-- depth:2
[18]=22,	-- depth:2
[17]=23,	-- depth:2
[236]=176,	-- depth:1
[16]=22,	-- depth:2
[14]=22,	-- depth:2
[13]=23,	-- depth:2
[12]=22,	-- depth:2
[194]=188,	-- depth:3
[182]=188,	-- depth:3
[183]=213,	-- depth:3
[15]=25,	-- depth:2
[11]=23,	-- depth:2
[196]=188,	-- depth:3
[197]=227,	-- depth:3
[2]=22,	-- depth:2
[226]=236,	-- depth:2
[225]=235,	-- depth:2
[224]=236,	-- depth:2
[3]=23,	-- depth:2
[222]=236,	-- depth:2
[4]=22,	-- depth:2
[5]=25,	-- depth:2
[218]=236,	-- depth:2
[6]=22,	-- depth:2
[216]=236,	-- depth:2
[215]=235,	-- depth:2
[214]=236,	-- depth:2
[7]=23,	-- depth:2
[212]=236,	-- depth:2
[8]=22,	-- depth:2
[9]=23,	-- depth:2
[209]=239,	-- depth:3
[208]=188,	-- depth:3
[207]=237,	-- depth:3
[206]=236,	-- depth:2
[232]=236,	-- depth:2
[204]=206,	-- depth:3
[203]=233,	-- depth:3
[202]=232,	-- depth:3
[201]=231,	-- depth:2
[10]=20,	-- depth:3
[199]=201,	-- depth:3
[198]=206,	-- depth:3
[234]=236,	-- depth:2
[27]=23,	-- depth:2
[34]=31,	-- depth:1
[29]=1,	-- depth:1
[118]=91,	-- depth:1
[117]=91,	-- depth:1
[116]=118,	-- depth:2
[228]=236,	-- depth:2
[114]=118,	-- depth:2
[113]=117,	-- depth:2
[112]=118,	-- depth:2
[111]=117,	-- depth:2
[110]=118,	-- depth:2
[109]=117,	-- depth:2
[108]=118,	-- depth:2
[107]=117,	-- depth:2
[106]=118,	-- depth:2
[119]=117,	-- depth:2
[105]=91,	-- depth:1
[103]=117,	-- depth:2
[102]=118,	-- depth:2
[101]=117,	-- depth:2
[100]=110,	-- depth:3
[99]=117,	-- depth:2
[98]=118,	-- depth:2
[97]=117,	-- depth:2
[96]=118,	-- depth:2
[95]=105,	-- depth:2
[94]=118,	-- depth:2
[93]=117,	-- depth:2
[92]=118,	-- depth:2
[280]=170,	-- depth:2
[104]=118,	-- depth:2
[28]=22,	-- depth:2
[290]=170,	-- depth:2
[59]=31,	-- depth:1
[160]=280,	-- depth:3
[32]=34,	-- depth:2
[33]=59,	-- depth:2
[35]=31,	-- depth:1
[36]=34,	-- depth:2
[37]=59,	-- depth:2
[38]=34,	-- depth:2
[39]=59,	-- depth:2
[40]=35,	-- depth:2
[41]=59,	-- depth:2
[238]=236,	-- depth:2
[42]=34,	-- depth:2
[43]=59,	-- depth:2
[260]=290,	-- depth:3
[44]=34,	-- depth:2
[46]=34,	-- depth:2
[58]=34,	-- depth:2
[57]=59,	-- depth:2
[56]=34,	-- depth:2
[55]=35,	-- depth:2
[54]=34,	-- depth:2
[53]=59,	-- depth:2
[45]=35,	-- depth:2
[115]=105,	-- depth:2
[250]=160,	-- depth:4
[51]=59,	-- depth:2
[50]=40,	-- depth:3
[49]=59,	-- depth:2
[48]=34,	-- depth:2
[47]=59,	-- depth:2
[52]=34,	-- depth:2
[270]=265,	-- depth:1
[230]=236,	-- depth:2
[300]=270,	-- depth:2
[123]=121,	-- depth:1
[122]=121,	-- depth:1
[120]=270,	-- depth:2
[60]=270,	-- depth:2
[89]=61,	-- depth:1
[88]=61,	-- depth:1
[87]=89,	-- depth:2
[86]=88,	-- depth:2
[85]=61,	-- depth:1
[84]=88,	-- depth:2
[83]=89,	-- depth:2
[82]=88,	-- depth:2
[81]=89,	-- depth:2
[80]=85,	-- depth:2
[79]=89,	-- depth:2
[78]=88,	-- depth:2
[77]=89,	-- depth:2
[76]=88,	-- depth:2
[75]=85,	-- depth:2
[74]=88,	-- depth:2
[73]=89,	-- depth:2
[72]=88,	-- depth:2
[71]=89,	-- depth:2
[70]=80,	-- depth:3
[69]=89,	-- depth:2
[68]=88,	-- depth:2
[67]=89,	-- depth:2
[66]=88,	-- depth:2
[65]=85,	-- depth:2
[64]=88,	-- depth:2
[220]=230,	-- depth:3
[124]=122,	-- depth:2
[125]=123,	-- depth:2
[126]=122,	-- depth:2
[205]=235,	-- depth:2
[200]=230,	-- depth:3
[195]=205,	-- depth:3
[190]=200,	-- depth:4
[185]=205,	-- depth:3
[180]=300,	-- depth:3
[149]=123,	-- depth:2
[148]=122,	-- depth:2
[30]=25,	-- depth:2
[147]=123,	-- depth:2
[146]=122,	-- depth:2
[145]=123,	-- depth:2
[144]=122,	-- depth:2
[143]=123,	-- depth:2
[63]=89,	-- depth:2
[142]=122,	-- depth:2
[140]=122,	-- depth:2
[139]=123,	-- depth:2
[138]=122,	-- depth:2
[137]=123,	-- depth:2
[136]=122,	-- depth:2
[135]=123,	-- depth:2
[134]=122,	-- depth:2
[133]=123,	-- depth:2
[132]=122,	-- depth:2
[131]=123,	-- depth:2
[130]=122,	-- depth:2
[129]=123,	-- depth:2
[128]=122,	-- depth:2
[127]=123,	-- depth:2
[141]=123,	-- depth:2
[62]=88,	-- depth:2
[210]=270,	-- depth:2
[240]=210,	-- depth:3
[90]=80,	-- depth:3
[150]=210,	-- depth:3
},
oga_rmb_buy={
{},
{seq=1,rmb_seq=1,price=6,reward={[0]=item_table[341],[1]=item_table[342]},gift_name="6元礼包",origin_price=28,},
{seq=2,rmb_seq=2,price=30,buy_limit=2,reward={[0]=item_table[343],[1]=item_table[344]},gift_name="30元礼包",origin_price=88,},
{seq=3,rmb_seq=3,price=98,buy_limit=3,reward={[0]=item_table[345],[1]=item_table[346]},gift_name="98元礼包",origin_price=288,},
{seq=4,rmb_seq=4,price=168,buy_limit=5,reward={[0]=item_table[347],[1]=item_table[348]},gift_name="168元礼包",origin_price=688,},
{seq=5,rmb_type=213,price=1188,reward={[0]=item_table[349],[1]=item_table[350],[2]=item_table[341],[3]=item_table[342],[4]=item_table[343],[5]=item_table[344],[6]=item_table[345],[7]=item_table[346],[8]=item_table[347],[9]=item_table[348]},gift_name="1188元礼包",origin_price=2888,},
{start_open_day=2,close_open_day=2,rmb_seq=5,reward={[0]=item_table[349],[1]=item_table[351]},},
{start_open_day=2,close_open_day=2,rmb_seq=6,reward={[0]=item_table[341],[1]=item_table[352]},},
{start_open_day=2,close_open_day=2,rmb_seq=7,reward={[0]=item_table[343],[1]=item_table[353]},},
{start_open_day=2,close_open_day=2,rmb_seq=8,reward={[0]=item_table[345],[1]=item_table[354]},},
{start_open_day=2,close_open_day=2,rmb_seq=9,reward={[0]=item_table[347],[1]=item_table[355]},},
{start_open_day=2,close_open_day=2,rmb_seq=1,reward={[0]=item_table[349],[1]=item_table[351],[2]=item_table[341],[3]=item_table[352],[4]=item_table[343],[5]=item_table[353],[6]=item_table[345],[7]=item_table[354],[8]=item_table[347],[9]=item_table[355]},},
{start_open_day=3,close_open_day=3,rmb_seq=10,reward={[0]=item_table[356],[1]=item_table[171]},},
{start_open_day=3,close_open_day=3,rmb_seq=11,reward={[0]=item_table[357],[1]=item_table[172]},},
{start_open_day=3,close_open_day=3,rmb_seq=12,reward={[0]=item_table[358],[1]=item_table[226]},},
{start_open_day=3,close_open_day=3,rmb_seq=13,reward={[0]=item_table[359],[1]=item_table[230]},},
{start_open_day=3,close_open_day=3,rmb_seq=14,reward={[0]=item_table[360],[1]=item_table[233]},},
{start_open_day=3,close_open_day=3,rmb_seq=2,reward={[0]=item_table[356],[1]=item_table[171],[2]=item_table[357],[3]=item_table[172],[4]=item_table[358],[5]=item_table[226],[6]=item_table[359],[7]=item_table[230],[8]=item_table[360],[9]=item_table[233]},},
{start_open_day=4,close_open_day=4,rmb_seq=15,reward={[0]=item_table[361],[1]=item_table[362]},},
{start_open_day=4,close_open_day=4,rmb_seq=16,reward={[0]=item_table[363],[1]=item_table[364]},},
{start_open_day=4,close_open_day=4,rmb_seq=17,reward={[0]=item_table[365],[1]=item_table[366]},},
{start_open_day=4,close_open_day=4,rmb_seq=18,reward={[0]=item_table[367],[1]=item_table[368]},},
{start_open_day=4,close_open_day=4,rmb_seq=19,reward={[0]=item_table[369],[1]=item_table[370]},},
{start_open_day=4,close_open_day=4,rmb_seq=3,reward={[0]=item_table[361],[1]=item_table[362],[2]=item_table[363],[3]=item_table[364],[4]=item_table[365],[5]=item_table[366],[6]=item_table[367],[7]=item_table[368],[8]=item_table[369],[9]=item_table[370]},},
{start_open_day=5,close_open_day=5,rmb_seq=20,reward={[0]=item_table[371],[1]=item_table[372]},},
{start_open_day=5,close_open_day=5,rmb_seq=21,reward={[0]=item_table[373],[1]=item_table[374]},},
{start_open_day=5,close_open_day=5,rmb_seq=22,reward={[0]=item_table[375],[1]=item_table[376]},},
{start_open_day=5,close_open_day=5,rmb_seq=23,reward={[0]=item_table[377],[1]=item_table[378]},},
{start_open_day=5,close_open_day=5,rmb_seq=24,reward={[0]=item_table[379],[1]=item_table[380]},},
{start_open_day=5,close_open_day=5,rmb_seq=4,reward={[0]=item_table[371],[1]=item_table[372],[2]=item_table[373],[3]=item_table[374],[4]=item_table[375],[5]=item_table[376],[6]=item_table[377],[7]=item_table[378],[8]=item_table[379],[9]=item_table[380]},},
{start_open_day=6,close_open_day=6,rmb_seq=25,reward={[0]=item_table[381],[1]=item_table[382]},},
{start_open_day=6,close_open_day=6,rmb_seq=26,reward={[0]=item_table[383],[1]=item_table[384]},},
{start_open_day=6,close_open_day=6,rmb_seq=27,reward={[0]=item_table[385],[1]=item_table[386]},},
{start_open_day=6,close_open_day=6,rmb_seq=28,reward={[0]=item_table[387],[1]=item_table[388]},},
{start_open_day=6,close_open_day=6,rmb_seq=29,reward={[0]=item_table[389],[1]=item_table[390]},},
{start_open_day=6,close_open_day=6,rmb_seq=5,reward={[0]=item_table[381],[1]=item_table[382],[2]=item_table[383],[3]=item_table[384],[4]=item_table[385],[5]=item_table[386],[6]=item_table[387],[7]=item_table[388],[8]=item_table[389],[9]=item_table[390]},},
{start_open_day=7,close_open_day=7,rmb_seq=30,reward={[0]=item_table[391],[1]=item_table[392]},},
{start_open_day=7,close_open_day=7,rmb_seq=31,reward={[0]=item_table[393],[1]=item_table[177]},},
{start_open_day=7,close_open_day=7,rmb_seq=32,reward={[0]=item_table[394],[1]=item_table[395]},},
{start_open_day=7,close_open_day=7,rmb_seq=33,reward={[0]=item_table[396],[1]=item_table[219]},},
{start_open_day=7,close_open_day=7,rmb_seq=34,reward={[0]=item_table[397],[1]=item_table[398]},},
{start_open_day=7,close_open_day=7,rmb_seq=6,reward={[0]=item_table[391],[1]=item_table[392],[2]=item_table[393],[3]=item_table[177],[4]=item_table[394],[5]=item_table[395],[6]=item_table[396],[7]=item_table[219],[8]=item_table[397],[9]=item_table[398]},}
},

oga_rmb_buy_meta_table_map={
[32]=2,	-- depth:1
[20]=2,	-- depth:1
[14]=2,	-- depth:1
[26]=2,	-- depth:1
[38]=2,	-- depth:1
[8]=2,	-- depth:1
[35]=5,	-- depth:1
[34]=4,	-- depth:1
[33]=3,	-- depth:1
[39]=3,	-- depth:1
[40]=4,	-- depth:1
[30]=6,	-- depth:1
[29]=5,	-- depth:1
[36]=6,	-- depth:1
[28]=4,	-- depth:1
[21]=3,	-- depth:1
[24]=6,	-- depth:1
[23]=5,	-- depth:1
[22]=4,	-- depth:1
[41]=5,	-- depth:1
[18]=6,	-- depth:1
[17]=5,	-- depth:1
[16]=4,	-- depth:1
[15]=3,	-- depth:1
[12]=6,	-- depth:1
[11]=5,	-- depth:1
[10]=4,	-- depth:1
[9]=3,	-- depth:1
[27]=3,	-- depth:1
[42]=6,	-- depth:1
},
oga_draw={
{reward={[0]=item_table[399]},},
{reward_id=1,reward={[0]=item_table[400]},},
{reward_id=2,reward={[0]=item_table[401]},},
{reward_id=3,reward={[0]=item_table[402]},},
{reward_id=4,reward={[0]=item_table[187]},},
{reward_id=5,reward={[0]=item_table[203]},},
{reward_id=6,reward={[0]=item_table[200]},},
{reward_id=7,},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[403]},},
{reward_id=1,reward={[0]=item_table[400]},},
{reward_id=2,reward={[0]=item_table[401]},},
{reward_id=3,reward={[0]=item_table[402]},},
{reward_id=4,reward={[0]=item_table[404]},},
{reward_id=5,reward={[0]=item_table[405]},},
{reward_id=6,reward={[0]=item_table[406]},},
{reward_id=7,reward={[0]=item_table[407]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[408]},},
{reward_id=1,reward={[0]=item_table[400]},},
{reward_id=2,reward={[0]=item_table[401]},},
{reward_id=3,reward={[0]=item_table[402]},},
{reward_id=4,reward={[0]=item_table[203]},},
{reward_id=5,reward={[0]=item_table[325]},},
{start_open_day=3,close_open_day=3,},
{reward_id=7,reward={[0]=item_table[407]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[409]},},
{reward_id=1,reward={[0]=item_table[400]},},
{reward_id=2,reward={[0]=item_table[401]},},
{reward_id=3,reward={[0]=item_table[402]},},
{reward_id=4,reward={[0]=item_table[187]},},
{reward_id=5,reward={[0]=item_table[410]},},
{reward_id=6,reward={[0]=item_table[200]},},
{reward_id=7,reward={[0]=item_table[406]},},
{start_open_day=5,close_open_day=5,reward={[0]=item_table[411]},},
{reward_id=1,reward={[0]=item_table[400]},},
{reward_id=2,reward={[0]=item_table[401]},},
{reward_id=3,reward={[0]=item_table[402]},},
{reward_id=4,reward={[0]=item_table[187]},},
{reward_id=5,reward={[0]=item_table[410]},},
{reward_id=6,reward={[0]=item_table[200]},},
{reward_id=7,reward={[0]=item_table[406]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[412]},},
{reward_id=1,reward={[0]=item_table[413]},},
{reward_id=2,reward={[0]=item_table[200]},},
{reward_id=3,reward={[0]=item_table[406]},},
{start_open_day=6,close_open_day=6,reward_id=4,},
{reward_id=5,reward={[0]=item_table[407]},},
{reward_id=6,},
{reward_id=7,reward={[0]=item_table[414]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[415]},},
{reward_id=1,reward={[0]=item_table[413]},},
{reward_id=2,reward={[0]=item_table[200]},},
{reward_id=3,reward={[0]=item_table[406]},},
{reward_id=4,},
{reward_id=5,reward={[0]=item_table[407]},},
{start_open_day=7,close_open_day=7,reward_id=6,},
{reward_id=7,reward={[0]=item_table[414]},}
},

oga_draw_meta_table_map={
[23]=55,	-- depth:1
[47]=45,	-- depth:1
[53]=55,	-- depth:1
[51]=49,	-- depth:1
[44]=45,	-- depth:1
[43]=45,	-- depth:1
[52]=49,	-- depth:1
[42]=45,	-- depth:1
[48]=45,	-- depth:1
[54]=49,	-- depth:1
[40]=33,	-- depth:1
[50]=49,	-- depth:1
[39]=33,	-- depth:1
[38]=33,	-- depth:1
[37]=33,	-- depth:1
[36]=33,	-- depth:1
[46]=45,	-- depth:1
[35]=33,	-- depth:1
[28]=25,	-- depth:1
[32]=25,	-- depth:1
[10]=9,	-- depth:1
[11]=9,	-- depth:1
[12]=9,	-- depth:1
[13]=9,	-- depth:1
[14]=9,	-- depth:1
[15]=9,	-- depth:1
[16]=9,	-- depth:1
[18]=17,	-- depth:1
[34]=33,	-- depth:1
[19]=17,	-- depth:1
[21]=17,	-- depth:1
[22]=17,	-- depth:1
[24]=17,	-- depth:1
[26]=25,	-- depth:1
[27]=25,	-- depth:1
[29]=25,	-- depth:1
[30]=25,	-- depth:1
[31]=25,	-- depth:1
[20]=17,	-- depth:1
[56]=49,	-- depth:1
},
oga_task={
{},
{seq=1,param1=2,},
{seq=2,param1=3,},
{seq=3,param1=4,},
{seq=4,param1=5,},
{seq=5,param1=6,},
{seq=6,param1=30,},
{seq=7,param1=50,},
{seq=8,param1=90,},
{seq=9,param1=120,},
{seq=10,type=1,param1=160,task_desc="日常活跃度",open_panel="bizuo#bizuo_bizuo",},
{seq=11,param1=200,},
{start_open_day=2,close_open_day=2,},
{start_open_day=2,close_open_day=2,},
{start_open_day=2,close_open_day=2,},
{start_open_day=2,close_open_day=2,},
{start_open_day=2,close_open_day=2,},
{seq=5,param1=6,},
{start_open_day=2,close_open_day=2,},
{seq=7,param1=50,},
{seq=8,param1=90,},
{seq=9,param1=120,},
{seq=10,param1=160,},
{seq=11,param1=200,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{seq=7,param1=50,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=3,close_open_day=3,},
{start_open_day=4,close_open_day=4,},
{start_open_day=4,close_open_day=4,},
{start_open_day=4,close_open_day=4,},
{seq=3,param1=4,},
{seq=4,param1=5,},
{seq=5,param1=6,},
{start_open_day=4,close_open_day=4,},
{seq=7,param1=50,},
{seq=8,param1=90,},
{seq=9,param1=120,},
{seq=10,param1=160,},
{seq=11,param1=200,},
{start_open_day=5,close_open_day=5,},
{start_open_day=5,close_open_day=5,},
{seq=2,param1=3,},
{seq=3,param1=4,},
{seq=4,param1=5,},
{seq=5,param1=6,},
{start_open_day=5,close_open_day=5,},
{seq=7,param1=50,},
{seq=8,param1=90,},
{start_open_day=5,close_open_day=5,},
{start_open_day=5,close_open_day=5,},
{start_open_day=5,close_open_day=5,},
{start_open_day=6,close_open_day=6,},
{start_open_day=6,close_open_day=6,},
{start_open_day=6,close_open_day=6,},
{seq=3,param1=4,},
{seq=4,param1=5,},
{seq=5,param1=6,},
{start_open_day=6,close_open_day=6,},
{seq=7,param1=50,},
{seq=8,param1=90,},
{seq=9,param1=120,},
{seq=10,param1=160,},
{seq=11,param1=200,},
{start_open_day=7,close_open_day=7,},
{start_open_day=7,close_open_day=7,},
{start_open_day=7,close_open_day=7,},
{seq=3,param1=4,},
{start_open_day=7,close_open_day=7,},
{seq=5,param1=6,},
{start_open_day=7,close_open_day=7,},
{seq=7,param1=50,},
{seq=8,param1=90,},
{seq=9,param1=120,},
{seq=10,param1=160,},
{seq=11,param1=200,}
},

oga_task_meta_table_map={
[39]=3,	-- depth:1
[40]=39,	-- depth:2
[41]=39,	-- depth:2
[50]=2,	-- depth:1
[51]=50,	-- depth:2
[52]=50,	-- depth:2
[53]=50,	-- depth:2
[77]=53,	-- depth:3
[78]=77,	-- depth:4
[62]=50,	-- depth:2
[63]=39,	-- depth:2
[38]=50,	-- depth:2
[65]=63,	-- depth:3
[66]=63,	-- depth:3
[74]=50,	-- depth:2
[75]=39,	-- depth:2
[76]=75,	-- depth:3
[54]=50,	-- depth:2
[64]=63,	-- depth:3
[42]=39,	-- depth:2
[17]=53,	-- depth:3
[16]=52,	-- depth:3
[15]=39,	-- depth:2
[14]=50,	-- depth:2
[30]=42,	-- depth:3
[29]=53,	-- depth:3
[28]=52,	-- depth:3
[27]=39,	-- depth:2
[26]=50,	-- depth:2
[18]=14,	-- depth:3
[12]=11,	-- depth:1
[7]=11,	-- depth:1
[9]=11,	-- depth:1
[8]=11,	-- depth:1
[10]=11,	-- depth:1
[67]=7,	-- depth:2
[68]=67,	-- depth:3
[69]=67,	-- depth:3
[33]=69,	-- depth:4
[71]=67,	-- depth:3
[72]=67,	-- depth:3
[79]=67,	-- depth:3
[80]=79,	-- depth:4
[81]=79,	-- depth:4
[82]=79,	-- depth:4
[70]=67,	-- depth:3
[19]=67,	-- depth:3
[58]=70,	-- depth:4
[59]=71,	-- depth:4
[34]=70,	-- depth:4
[35]=71,	-- depth:4
[36]=72,	-- depth:4
[31]=67,	-- depth:3
[83]=79,	-- depth:4
[43]=67,	-- depth:3
[44]=43,	-- depth:4
[45]=43,	-- depth:4
[46]=43,	-- depth:4
[60]=72,	-- depth:4
[47]=43,	-- depth:4
[24]=19,	-- depth:4
[23]=19,	-- depth:4
[22]=19,	-- depth:4
[21]=19,	-- depth:4
[20]=19,	-- depth:4
[55]=67,	-- depth:3
[56]=55,	-- depth:4
[57]=55,	-- depth:4
[32]=31,	-- depth:4
[48]=43,	-- depth:4
[84]=79,	-- depth:4
},
oga_cumulate_recharge={
{},
{seq=1,num=60,reward={[0]=item_table[416],[1]=item_table[417],[2]=item_table[162],[3]=item_table[418]},},
{seq=2,num=300,reward={[0]=item_table[274],[1]=item_table[417],[2]=item_table[162],[3]=item_table[419]},},
{seq=3,num=680,reward={[0]=item_table[232],[1]=item_table[420],[2]=item_table[169],[3]=item_table[419]},},
{seq=4,num=980,reward={[0]=item_table[154],[1]=item_table[420],[2]=item_table[321],[3]=item_table[298]},},
{seq=5,num=1980,reward={[0]=item_table[154],[1]=item_table[421],[2]=item_table[422],[3]=item_table[298]},},
{seq=6,num=3280,reward={[0]=item_table[243],[1]=item_table[423],[2]=item_table[424],[3]=item_table[425]},},
{seq=7,num=6480,reward={[0]=item_table[426],[1]=item_table[427],[2]=item_table[428],[3]=item_table[429]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[430],[1]=item_table[400],[2]=item_table[402],[3]=item_table[418]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[224],[1]=item_table[431],[2]=item_table[402],[3]=item_table[418]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[229],[1]=item_table[413],[2]=item_table[402],[3]=item_table[419]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[232],[1]=item_table[432],[2]=item_table[320],[3]=item_table[419]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[154],[1]=item_table[433],[2]=item_table[321],[3]=item_table[298]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[154],[1]=item_table[434],[2]=item_table[422],[3]=item_table[298]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[243],[1]=item_table[435],[2]=item_table[424],[3]=item_table[425]},},
{start_open_day=2,close_open_day=2,reward={[0]=item_table[426],[1]=item_table[436],[2]=item_table[428],[3]=item_table[429]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[437],[1]=item_table[204],[2]=item_table[351],[3]=item_table[172]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[438],[1]=item_table[439],[2]=item_table[352],[3]=item_table[276]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[440],[1]=item_table[441],[2]=item_table[442],[3]=item_table[226]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[443],[1]=item_table[444],[2]=item_table[445],[3]=item_table[446]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[447],[1]=item_table[448],[2]=item_table[449],[3]=item_table[230]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[450],[1]=item_table[451],[2]=item_table[353],[3]=item_table[239]},},
{start_open_day=3,close_open_day=3,reward={[0]=item_table[452],[1]=item_table[453],[2]=item_table[354],[3]=item_table[454]},},
{seq=7,num=6480,reward={[0]=item_table[455],[1]=item_table[456],[2]=item_table[457],[3]=item_table[458]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[406],[1]=item_table[188],[2]=item_table[186],[3]=item_table[364]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[459],[1]=item_table[460],[2]=item_table[330],[3]=item_table[461]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[462],[1]=item_table[463],[2]=item_table[464],[3]=item_table[366]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[465],[1]=item_table[466],[2]=item_table[467],[3]=item_table[468]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[469],[1]=item_table[470],[2]=item_table[471],[3]=item_table[368]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[472],[1]=item_table[473],[2]=item_table[474],[3]=item_table[475]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[476],[1]=item_table[477],[2]=item_table[478],[3]=item_table[479]},},
{start_open_day=4,close_open_day=4,reward={[0]=item_table[480],[1]=item_table[481],[2]=item_table[482],[3]=item_table[483]},},
{start_open_day=5,close_open_day=5,reward={[0]=item_table[484],[1]=item_table[178],[2]=item_table[485],[3]=item_table[177]},},
{seq=1,num=60,reward={[0]=item_table[484],[1]=item_table[486],[2]=item_table[487],[3]=item_table[488]},},
{seq=2,num=300,reward={[0]=item_table[489],[1]=item_table[490],[2]=item_table[491],[3]=item_table[492]},},
{seq=3,num=680,reward={[0]=item_table[493],[1]=item_table[494],[2]=item_table[495],[3]=item_table[496]},},
{start_open_day=5,close_open_day=5,reward={[0]=item_table[497],[1]=item_table[498],[2]=item_table[499],[3]=item_table[398]},},
{seq=5,num=1980,reward={[0]=item_table[500],[1]=item_table[501],[2]=item_table[502],[3]=item_table[503]},},
{seq=6,num=3280,reward={[0]=item_table[504],[1]=item_table[505],[2]=item_table[506],[3]=item_table[507]},},
{seq=7,num=6480,reward={[0]=item_table[102],[1]=item_table[508],[2]=item_table[509],[3]=item_table[510]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[260],[1]=item_table[511],[2]=item_table[485],[3]=item_table[384]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[512],[1]=item_table[513],[2]=item_table[487],[3]=item_table[514]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[515],[1]=item_table[516],[2]=item_table[491],[3]=item_table[517]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[518],[1]=item_table[519],[2]=item_table[495],[3]=item_table[520]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[521],[1]=item_table[522],[2]=item_table[499],[3]=item_table[390]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[523],[1]=item_table[524],[2]=item_table[502],[3]=item_table[525]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[526],[1]=item_table[527],[2]=item_table[506],[3]=item_table[528]},},
{start_open_day=6,close_open_day=6,reward={[0]=item_table[529],[1]=item_table[530],[2]=item_table[509],[3]=item_table[531]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[437],[1]=item_table[532],[2]=item_table[533],[3]=item_table[351]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[438],[1]=item_table[532],[2]=item_table[533],[3]=item_table[352]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[440],[1]=item_table[534],[2]=item_table[535],[3]=item_table[442]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[443],[1]=item_table[536],[2]=item_table[537],[3]=item_table[445]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[447],[1]=item_table[538],[2]=item_table[539],[3]=item_table[449]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[450],[1]=item_table[540],[2]=item_table[541],[3]=item_table[353]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[452],[1]=item_table[542],[2]=item_table[543],[3]=item_table[354]},},
{start_open_day=7,close_open_day=7,reward={[0]=item_table[455],[1]=item_table[544],[2]=item_table[545],[3]=item_table[457]},}
},

oga_cumulate_recharge_meta_table_map={
[50]=2,	-- depth:1
[35]=33,	-- depth:1
[36]=33,	-- depth:1
[37]=5,	-- depth:1
[38]=33,	-- depth:1
[39]=33,	-- depth:1
[40]=33,	-- depth:1
[54]=6,	-- depth:1
[42]=2,	-- depth:1
[43]=3,	-- depth:1
[44]=4,	-- depth:1
[53]=5,	-- depth:1
[46]=6,	-- depth:1
[47]=7,	-- depth:1
[34]=33,	-- depth:1
[48]=8,	-- depth:1
[52]=4,	-- depth:1
[51]=3,	-- depth:1
[45]=5,	-- depth:1
[28]=4,	-- depth:1
[31]=7,	-- depth:1
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
[13]=5,	-- depth:1
[14]=6,	-- depth:1
[15]=7,	-- depth:1
[16]=8,	-- depth:1
[18]=2,	-- depth:1
[19]=3,	-- depth:1
[20]=4,	-- depth:1
[21]=5,	-- depth:1
[22]=6,	-- depth:1
[23]=7,	-- depth:1
[24]=17,	-- depth:1
[26]=2,	-- depth:1
[27]=3,	-- depth:1
[55]=7,	-- depth:1
[29]=5,	-- depth:1
[30]=6,	-- depth:1
[32]=8,	-- depth:1
[56]=8,	-- depth:1
},
oga_other={
{model_scale=0.9,rotation="0|183|0",},
{start_open_day=2,close_open_day=2,model_show_itemid=37503,},
{start_open_day=3,close_open_day=3,model_show_itemid=37022,model_scale=1.2,rotation="0|-289|0",},
{start_open_day=4,close_open_day=4,model_show_itemid=38735,rotation="0|-375|0",},
{start_open_day=5,close_open_day=5,model_show_itemid=37407,model_pos="44|59|0",model_scale=1.5,rotation="0|-246|0",},
{start_open_day=6,close_open_day=6,model_show_itemid=37611,model_scale=1.3,rotation="0|-376|0",},
{start_open_day=7,close_open_day=7,model_show_itemid=37123,model_pos="44|-52|0",model_scale=1.3,rotation="0|368|0",},
{map_type=1,model_show_itemid=37437,model_pos="0|0.5|0",model_scale=1.1,reward_show={[0]=item_table[546],[1]=item_table[547],[2]=item_table[548],[3]=item_table[549],[4]=item_table[550],[5]=item_table[551],[6]=item_table[552],[7]=item_table[553]},open_panel="JiangShanRuHuaView",},
{map_type=1,start_open_day=2,close_open_day=2,model_show_itemid=37208,model_pos="0|-0.3|0",model_scale=0.8,reward_show={[0]=item_table[546],[1]=item_table[547],[2]=item_table[548],[3]=item_table[549],[4]=item_table[550],[5]=item_table[551],[6]=item_table[552],[7]=item_table[553]},open_panel="HolyBeastCallView",},
{start_open_day=3,close_open_day=3,model_show_itemid=37402,model_scale=1.4,rotation="0|180|0",},
{map_type=1,start_open_day=4,close_open_day=4,model_show_itemid=37021,model_pos="1|-0.5|0",rotation="0|-219|0",reward_show={[0]=item_table[546],[1]=item_table[547],[2]=item_table[548],[3]=item_table[549],[4]=item_table[550],[5]=item_table[551],[6]=item_table[552],[7]=item_table[553]},open_panel="MoRanXuanYuanView",},
{map_type=1,start_open_day=5,close_open_day=5,model_show_itemid=38705,model_pos="0|0|0",reward_show={[0]=item_table[546],[1]=item_table[547],[2]=item_table[548],[3]=item_table[549],[4]=item_table[550],[5]=item_table[551],[6]=item_table[552],[7]=item_table[553]},open_panel="SunRainbowView",},
{start_open_day=6,close_open_day=6,model_show_itemid=37007,model_pos="0|0.1|0",rotation="0|-2|0",open_panel="SunRainbowView",},
{start_open_day=7,close_open_day=7,model_show_itemid=37203,rotation="0|5|0",open_panel="LuckyGiftBagView",}
},

oga_other_meta_table_map={
[2]=1,	-- depth:1
[10]=9,	-- depth:1
[13]=9,	-- depth:1
[14]=13,	-- depth:2
},
other_default_table={percentage_time=20,cloud_buy_total_times=300,cloud_buy_limit_time=1400,cloud_buy_need_gold=30,cloud_buy_gift_id=29420,limit_level=60,zero_gift_open_level=1,zero_gift_price=380,zero_gift_vip_level=4,kaizonglipai_open_level=60,kaizonglipai_close_day=4,kaizonglipai_close_time=0,kaizonglipai_notice_day=3,kaizonglipai_notice_time=2300,total_chongzhi_level_limit=40,kaifujizi_open_level=90,kaifujizi_close_day=8,juanxian_stuff_item_id=26000,juanxian_stuff_item_num=1,zhenlong_rate1=1,zhenlong_rate2=2,jx_desc="开服<color=#72eba9>第1~7日</color>，开启<color=#72eba9>全民嗨翻天</color>活动\n活动期间，消耗<color=#c26af5>如烟碧玉</color>完成捐献，即可获得随机奖励，捐献达到一定次数，更可获得<color=#cfa94d>神兵技能书、斩龙剑魂、七星剑魂</color>等极品道具\n活动期间，<color=#72eba9>全服捐献次数达到一定目标</color>，可解锁<color=#72eba9>全服大礼</color>、<color=#72eba9>BOSS重生</color>等全服福利\n<color=#c26af5>如烟碧玉</color>可在<color=#72eba9>活跃转盘、真龙送礼</color>中获得\n活动结束后，未主动领取的奖励将通过<color=#72eba9>邮件</color>发放",zhenglong_desc="开服<color=#72eba9>第1~7日</color>，开启<color=#72eba9>真龙送礼</color>活动\n活动期间，<color=#72eba9>每进行1次</color>真龙寻宝，即可获得<color=#c26af5>如烟碧玉</color>\n每消耗<color=#72eba9>1</color>把真龙密匙，可获得<color=#72eba9>1</color>个如烟碧玉；每消耗<color=#72eba9>1</color>把高级真龙密匙，可获得<color=#72eba9>2个</color>如烟碧玉",reward_show={[0]=item_table[554],[1]=item_table[555],[2]=item_table[556],[3]=item_table[557],[4]=item_table[558]},rare_sign="1,1,1,0,0",open_panel="xunBao#equipxunbao_xunbao",xianmeng_title_tip_level=100,seq=4,word_list="26395,26396,26397,26398,26399",recharge_reward_item={[0]=item_table[54],[1]=item_table[41],[2]=item_table[248],[3]=item_table[41],[4]=item_table[64],[5]=item_table[256]},daily_reward={[0]=item_table[559]},draw_item=item_table[560],show_oga_extend_part=1,},

act_time_limit_default_table={act_id=113,act_index=1,open_day=1,close_day=7,show_day=0,level_limit=100,},

rush_rank_type_default_table={rush_type=3,open_day_index=1,close_day_index=2,show_close_day=11,reach_value=10000,reach_goal="1|2|3|4",reach_goal_reward_item="26344:500:1|26344:400:1|26344:250:1|26344:150:1",join_value=10,reward_item={[0]=item_table[404],[1]=item_table[404],[2]=item_table[404],[3]=item_table[404]},open_game_day=3,gold_type="2|1|1|1|1",price="",init_price="",daily_reward_item={[0]=item_table[561]},no_limit_item="",daily_buy_item="26345:1:1|26345:1:1|26345:1:1|26345:1:1|43086:1:1|26369:1:1",daily_buy_times="20|30|50|70|5|10",old_gold="150|150|150|150|300|500",need_gold="15|30|50|60|88|200",get_way=15,tips_discount="",rank_type=33,show_title="灵骑",ranking_name="灵骑战力榜",ranking_title="灵骑战力",bubble_title="灵骑",turn_link="NewAppearanceWGView#new_appearance_upgrade_mount",rank_condition="灵骑达到%d阶%d星",show_type=2,show_item_id=37065,soul_ring_id="",res_path="",res_id="",model_scale="",},

rush_rank_reward_default_table={rush_type=3,min_rank=1,max_rank=1,reward_item={[0]=item_table[20],[1]=item_table[562],[2]=item_table[3],[3]=item_table[563],[4]=item_table[564],[5]=item_table[565]},reach_value=1200000,get_way_param=1,},

total_consume_default_table={seq=0,consume_gold=980,reward_item=item_table[566],},

perfect_lover_default_table={openday="1,7",reward_item={[0]=item_table[567],[1]=item_table[568]},resource_id=20101,title_id=3002,capability_show=200000,},

guild_battle_default_table={type=0,reward_item={[0]=item_table[569],[1]=item_table[570],[2]=item_table[571],[3]=item_table[572]},},

word_exchange_default_table={seq=0,item_id_1=26395,item_id_2=26396,item_id_3=26397,item_id_4=26398,item_id_5=0,reward_item=item_table[573],limit=3,},

icon_jump_default_table={rush_type=9,type=0,execute=2,param_1="vip#recharge_tqtz",remind_id="",jump_icon="zj_huodon_touzi",icon_name="特权回报",activity_id="",icon_describe="特权回报",icon_scale=1.2,di_res_id="kf_mingzdi",},

exp_pool_default_table={original_exp=100000,reward_level=120,},

firstchongzhi_groupbuy_default_table={opengame_day=1,seq=11,groupbuy_active_need_person=3,reward_item={[0]=item_table[204]},fetch_need_min_chongzhi_value=680,},

createguild_default_table={reward_type=2,reward_id=1,all_server_count=5,reward_condition=1,reward_level=1,reward_item={[0]=item_table[192],[1]=item_table[191],[2]=item_table[574]},title_desc="建立仙盟",target_desc="创建仙盟",index=1,seq=1,},

daily_limit_buy_default_table={opengame_day=1,seq=4,limit_buy=1,need_gold=158,reward_item={[0]=item_table[233]},zheshu=1,},

boss_hunter_default_table={openday="1,3",boss_level=200,world_boss_score=2,dabao_boss_score=1,vip_boss_score=1,person_boss_score=1,secret_boss_score=1,target_score=200,target_bind_gold=500,},

boss_hunter_reward_default_table={rank=1,rank_bind_gold=3000,},

total_chongzhi_default_table={seq=0,chongzhi_gold=60,reward_item={[0]=item_table[575],[1]=item_table[223],[2]=item_table[430],[3]=item_table[576],[4]=item_table[172]},broadcast_item={[0]=item_table[575],[1]=item_table[223],[2]=item_table[430],[3]=item_table[576],[4]=item_table[172]},capability_show=0,},

cloud_buy_times_default_table={vip_level=0,buy_times=45,},

cloud_buy_item_default_table={opengame_day=10,batch=0,seq=0,weight=25,reward_item=item_table[269],},

cloud_buy_flush_default_table={opengame_day=10,batch=0,flush_day=1,flush_time=2400,reward_item=item_table[577],gift_bag=0,goods_price=18888,},

coolsui_crazybuy_default_table={opengame_day=1,seq=0,need_gold=388,reward_item=item_table[578],part_type=6,view_resouce=7,},

discount_gift_default_table={opengame_day=4,seq=0,count_limit=1,need_gold=100,show_gold=200,goods_item={[0]=item_table[579]},discount=5,},

discount_describe_default_table={activity_open=1,activity_close=1,activity_explain="开服狂欢，限时折扣礼包，活动期间低价出售等级礼包，等级升不停，冲榜必备！",},

zero_gift_default_table={opengame_day=1,com_reward={[0]=item_table[580],[1]=item_table[581],[2]=item_table[581],[3]=item_table[581]},vip_reward={[0]=item_table[166],[1]=item_table[582],[2]=item_table[582],[3]=item_table[582]},have_com_reward=1,have_vip_reward=1,gold_com_reward=0,view_resouce="",},

kaizonglipai_new_default_table={max_zhanli=1,min_zhanli=1,title_id=4001,drop_rate=1500000,zhanli=23400,pos="0|30|0",scale=1.1,title_effect=0,},

kaizonglipai_drop_default_table={},

qfjx_role_jx_reward_default_table={reward_id=1,reward_item={[0]=item_table[583]},add_exp=0,},

qfjx_role_lj_reward_default_table={index=1,juanxian_num=5,reward_item={[0]=item_table[179]},},

qfjx_server_day_reward_default_table={index=1,server_day_juanxian_num=300,reward_type=2,param1=1,param2=1,param3=1,param4=0,param5=0,name="道具奖励",icon="jx_bx",desc="蛮荒魔谷重生",},

openserver_act_order_default_table={actid=2207,order=1,},

xunyulu_default_table={id=1,activity_id=7,name="仙盟争霸",join_times=1,des="同心协助，名震三界",icon="btn_33",reward_item={[0]=item_table[268]},act_open_days=9999,open_server_day=9999,task_type=24,},

herd_default_table={openday="1,4",tipdes1="争夺最强仙盟",tipdes2="争夺极品称号",tipdes3="角逐冠军",tipdes4="主宰神殿",},

herd_grade_default_table={id=1,boss_level_limit=200,times=200,reward={[0]=item_table[155],[1]=item_table[298]},join_reward={[0]=item_table[584]},},

herd_time_default_table={open_day_index=1,close_day_index=3,show_close_day=3,},

tehuilibao_default_table={index=1,num=1,price_type=0,price=888,reward={[0]=item_table[154]},dur_times=10080,old_price=3000,is_split=0,is_tuijian=1,gift_name="幻兽礼包",gift_desc="助力冲榜|提升战力",gift_icon="kf_gift_icon_16",gift_cap_value=0,gift_bannar="ditu_11",show_type=0,show_item_id="",res_path="",res_id=2006,model_scale=0.55,model_rot="0|-10|0",whole_display_pos="-80|-20",discount="3折",},

rank_reach_reward_default_table={rush_type=2,reach_goal=31,index=1,reach_goal_reward_item={[0]=item_table[585]},},

invest_type_default_table={type=0,rmb_type=194,rmb_seq=0,price=128,open_day=999,close_day=1000,level_limit=999,reward_item={[0]=item_table[586]},gold_num=1280,art_title=6,txt="加速神武培养，获取夜影离梵",show_type=1,model_show_itemid=0,soul_ring_id=0,model_bundle_name="",model_asset_name="",display_pos="-160|-50",rotation="0|0|0",display_scale=1.1,profit_label=1,open_panel="ControlBeastsView#beasts_culture",label_name="幻兽投资",basic_reward_item={[0]=item_table[330],[1]=item_table[330],[2]=item_table[187],[3]=item_table[330],[4]=item_table[330],[5]=item_table[330],[6]=item_table[330],[7]=item_table[187],[8]=item_table[330],[9]=item_table[330],[10]=item_table[330],[11]=item_table[330],[12]=item_table[187],[13]=item_table[330],[14]=item_table[330],[15]=item_table[330],[16]=item_table[330],[17]=item_table[187],[18]=item_table[330],[19]=item_table[330],[20]=item_table[330],[21]=item_table[330],[22]=item_table[187],[23]=item_table[330],[24]=item_table[330],[25]=item_table[330],[26]=item_table[330],[27]=item_table[187],[28]=item_table[332],[29]=item_table[332]},invest_reward_item={[0]=item_table[322],[1]=item_table[340],[2]=item_table[284],[3]=item_table[339],[4]=item_table[284],[5]=item_table[339],[6]=item_table[284],[7]=item_table[339],[8]=item_table[284],[9]=item_table[339],[10]=item_table[284],[11]=item_table[339],[12]=item_table[284],[13]=item_table[339],[14]=item_table[284],[15]=item_table[339],[16]=item_table[284],[17]=item_table[339],[18]=item_table[284],[19]=item_table[339],[20]=item_table[284],[21]=item_table[339],[22]=item_table[284],[23]=item_table[339],[24]=item_table[284],[25]=item_table[339],[26]=item_table[284],[27]=item_table[339],[28]=item_table[284],[29]=item_table[339],[30]=item_table[284]},profit_num=1000,},

invest_task_default_table={type=0,seq=1,param1=0,param2=0,item_list={[0]=item_table[514]},rmb_item_list={[0]=item_table[335],[1]=item_table[284]},describe_type=1,is_fix_show=0,},

oga_rmb_buy_default_table={start_open_day=1,close_open_day=1,seq=0,rmb_type=212,rmb_seq=0,price=1,buy_limit=1,reward={[0]=item_table[349],[1]=item_table[350]},gift_name="1元礼包",origin_price=18,},

oga_draw_default_table={start_open_day=1,close_open_day=1,reward_id=0,reward={[0]=item_table[168]},},

oga_task_default_table={start_open_day=1,close_open_day=1,seq=0,type=0,param1=1,param2=0,reward={[0]=item_table[560]},task_desc="仙遗洞天击杀Boss",open_panel="boss#boss_vip",},

oga_cumulate_recharge_default_table={start_open_day=1,close_open_day=1,seq=0,num=10,reward={[0]=item_table[416],[1]=item_table[417],[2]=item_table[401],[3]=item_table[418]},task_desc="累计充值",},

oga_other_default_table={map_type=0,start_open_day=1,close_open_day=1,model_show_itemid=37230,model_pos="44|0|0",model_scale=1,rotation="0|0|0",reward_show={[0]=item_table[554],[1]=item_table[555],[2]=item_table[556],[3]=item_table[557],[4]=item_table[558]},open_panel="vip#recharge_cz",}

}


--世界服系统
WorldServerView = WorldServerView or BaseClass(SafeBaseView)
local UIOverrideOrder = typeof(UIOverrideOrder)

function WorldServerView:__init()
	-- self:SetMaskBg()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	local boss_bundel = "uis/view/boss_ui_prefab"
	local no_xianjie_tab = { TabIndex.worserv_boss_mh, TabIndex.worserv_boss_sgyj, TabIndex.worserv_boss_hmsy }
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, boss_bundel, "layout_world_boss_model")
	self:AddViewResource(no_xianjie_tab, boss_bundel, "layout_world_boss")
	self:AddViewResource(no_xianjie_tab, boss_bundel, "layout_common_right")
	self:AddViewResource({ TabIndex.worserv_boss_sgyj }, boss_bundel, "layout_sg_boss_1")
	self:AddViewResource({ TabIndex.worserv_boss_mh }, boss_bundel, "layout_kf_boss_1")
	self:AddViewResource({ TabIndex.worserv_boss_hmsy }, boss_bundel, "layout_shenyu_boss")

	self:AddViewResource({ TabIndex.world_new_shenyuan_boss }, boss_bundel, "layout_shenyuan_right")
	self:AddViewResource({ TabIndex.xianjie_boss }, boss_bundel, "layout_xianjie_right")
	self:AddViewResource({ TabIndex.worserv_everyday_recharge_boss }, boss_bundel, "layout_everyday_recharge_boss")

	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource({ TabIndex.world_new_shenyuan_boss }, boss_bundel, "layout_shenyuan_common")
	
	self:AddViewResource(no_xianjie_tab, boss_bundel, "layout_boss_panel_up")
	self:AddViewResource(0, boss_bundel, "VerticalTabbar")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
	self:AddViewResource(0, boss_bundel, "layout_boss_common_btn") --公共显示按钮

	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BOSS_HUANG})

	self.remind_tab = {
		{ RemindName.WorldServer_SYMW },
		{ RemindName.WorldServer_SGYJ,                  RemindName.WorldServer_MHSS, RemindName.WorldServer_HMSY },
		{ RemindName.WorldServer_Xianjie },
		{ RemindName.WorldServer_EveryDay_Recharge_Boss }
	}
	self.default_index = TabIndex.world_new_shenyuan_boss
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)

	self.boss_list_data = nil
	self.boss_cambered_list = nil
	self.list_index = 1
	self.boss_drag_select_index = 1
	self.cell_list = nil
end

function WorldServerView:__delete()

end

function WorldServerView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.layer_btn_list then
		self.layer_btn_list:DeleteMe()
		self.layer_btn_list = nil
	end

	for i = 1, 2 do
		if self.cell_list and self.cell_list[i] then
			self.cell_list[i]:DeleteMe()
			self.cell_list[i] = nil
		end
	end
	self.cell_list = nil

	if nil ~= self.boss_display_model then
		self.boss_display_model:DeleteMe()
		self.boss_display_model = nil
	end

	if nil ~= self.boss_display_model_1 then
		self.boss_display_model_1:DeleteMe()
		self.boss_display_model_1 = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.boss_cambered_list then
		self.boss_cambered_list:DeleteMe()
		self.boss_cambered_list = nil
	end

	self.worldserver_data = nil
	self.worldserver_ctrl = nil
	self.AddClickEventListener = nil
	self.common_init = nil
	self.select_item_data = nil
	self.cache_resid = nil
	self.is_show_hide = nil
	self.layer_index = nil
	self.old_show_index = nil
	self.model_block = nil

	self.boss_list_data = nil
	self.list_index = nil
	self.boss_drag_select_index = -1

	if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end

	RemindManager.Instance:UnBind(self.world_server_remind_callback)

	self:DeleteKfBossView()
	self:DeleteShenYunBossView()
	self:DeleteNewSYBossView()
	self:DeleteKfDropRecordView()
	self:DeleteSGBossView()
	self:ReleaseShenYuanView()
	self:ReleaseXianjieView()
	self:ReleaseEveryDayRechargeBossView()
end

function WorldServerView:OpenCallBack()

end

function WorldServerView:CloseCallBack()
	BossWGData.Instance:SetCurrentShijieServerShowIndex(nil)
	self.list_index = nil
end

function WorldServerView:SetRendering(value)
	SafeBaseView.SetRendering(self, value)
end

function WorldServerView:CreateOnceLoad()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.TabbarLoadCallBack, self))
		self.tabbar:Init(Language.WorldServer.TabGrop, nil, "uis/view/boss_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.WorldServer, self.tabbar)
	end
	self.worldserver_data = WorldServerWGData.Instance
	self.worldserver_ctrl = WorldServerWGCtrl.Instance
	self.AddClickEventListener = XUI.AddClickEventListener
	self.node_list.title_view_name.text.text = Language.ViewName.KuaFu

	self.AddClickEventListener(self.node_list["btn_open_zhanling"], BindTool.Bind(self.OnClickZhanLing, self))
	self.AddClickEventListener(self.node_list["btn_open_privilege"], BindTool.Bind(self.OnClickTeQuan, self))
end

function WorldServerView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	self:FlushManHuangEffect()
	local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(SceneType.KF_BOSS)
	if cfg and cfg.consume_item and item_id == cfg.consume_item then
		if self.node_list.btn_manhuang_effect then
			self.node_list.btn_manhuang_effect:SetActive(new_num > 0)
		end
	end
	self:FlushXianJieBossViewItem(item_id)
end

function WorldServerView:LoadCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	local bundle, asset = ResPath.GetRawImagesJPG("a3_boss_bgzi2")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self:CreateOnceLoad()

	self.world_server_remind_callback = BindTool.Bind(self.WorldServerRemindCallback, self)
	RemindManager.Instance:Bind(self.world_server_remind_callback, RemindName.Boss_ZhanLing)				-- 战令
end

function WorldServerView:FlushManHuangEffect()
	local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(SceneType.KF_BOSS)
	if cfg and cfg.consume_item then
		local num = ItemWGData.Instance:GetItemNumInBagById(cfg.consume_item)
		if self.node_list.btn_manhuang_effect then
			self.node_list.btn_manhuang_effect:SetActive(num > 0)
		end
	end
end

--列表的箭头显示.
function WorldServerView:WorldBossScrollerEndScrolled()
	local val = self.node_list.ph_btn_world_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.boss_left_arrow:SetActive(val ~= 0 and val > 0.1)
	self.node_list.boss_right_arrow:SetActive(val ~= 0 and val < 0.9)
end

function WorldServerView:LoadIndexCallBack(index)
	if index == TabIndex.worserv_boss_mh then
		self:InitCommonView()
		self:InitKfBossView()
		self:FlushManHuangEffect()
		--列表的箭头显示.
		self.node_list.ph_btn_world_list.scroller.scrollerEndScrolled = BindTool.Bind(self.WorldBossScrollerEndScrolled,self)
	elseif index == TabIndex.world_new_shenyuan_boss then
		self:InitShenYuanView()
	elseif index == TabIndex.xianjie_boss then
		self:InitXianjieView()
	elseif index == TabIndex.worserv_boss_hmsy then
		self:InitCommonView()
		self:InitNewSYBossView()
	elseif index == TabIndex.worserv_boss_sgyj then
		self:InitCommonView()
		self:InitSGBossView()
		--elseif index == TabIndex.worserv_boss_record then
		--	self:InitKfDropRecordView()
	elseif index == TabIndex.worserv_everyday_recharge_boss then
		self:InitEveryDayRechargeBossView()
	end
end

function WorldServerView:InitCommonView()
	if self.common_init then
		return
	end
	self.node_list["layout_check_hook"].button:AddClickListener(BindTool.Bind1(self.FouseOnBoss, self))
	self.node_list["btn_goto_kill"].button:AddClickListener(BindTool.Bind1(self.GoToKillBoss, self))
	self.node_list["btn_play_des"].button:AddClickListener(BindTool.Bind1(self.BrowsePlayInfo, self))
	self.AddClickEventListener(self.node_list["btn_record"], BindTool.Bind(self.OnClickRecord, self))

	self.node_list.top_title_bg_1:SetActive(false)
	self.node_list.top_title_bg_2:SetActive(true)
	self.node_list.middle_title_bg_1:SetActive(false)
	self.node_list.middle_title_bg_2:SetActive(true)

	self.node_list.ph_world_boss_cambered_list_1:SetActive(false)
	self.node_list.ph_world_boss_cambered_list_2:SetActive(true)

	self.node_list.down_container_1:SetActive(false)
	self.node_list.down_container_2:SetActive(true)

	local bundle, asset = ResPath.GetRawImagesPNG("a3_boss_bgzizz2")
	self.node_list.ph_world_boss_cambered_bg.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.ph_world_boss_cambered_bg.raw_image:SetNativeSize()
	end)

	self:CreateLayerBtnList()
	self:CreateRareItem()
	self:InitCreateCamberedList()
	self.common_init = true
end

function WorldServerView:CreateLayerBtnList()
	if nil == self.layer_btn_list then
		self.layer_btn_list = AsyncListView.New(ShiJieFuLayerBtnRender, self.node_list["ph_btn_world_list"])

		self.layer_btn_list.ListEventCallback = function(layer_btn_list, item_cell)
			if self:GetShowIndex() == BossViewIndex.KFBoss then
				local falg, need_level = BossWGData.Instance:GetKfLayerIsEnter(item_cell.index)
				if not falg and item_cell.index ~= 1 then
					need_level = RoleWGData.GetLevelString(need_level)
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Boss.EnterWorldBossLevel, need_level))
					--layer_btn_list:SelectIndex(self.cur_kflayer)
					return
				end
			end
			AsyncListView.ListEventCallback(layer_btn_list, item_cell)
			self.list_index = 1
			self:OnFlushBossList()
		end
		self.layer_btn_list:SetSelectCallBack(BindTool.Bind1(self.BossLayerBtnSelectCallBack, self))
	end
end

function WorldServerView:CreateRareItem()
	if not self.cell_list then
		self.cell_list = {}
		for i = 1, 2 do
			self.cell_list[i] = AsyncBaseGrid.New()
			local t = {}
			t.col = 3
			t.change_cells_num = 1
			t.itemRender = BossRewardCell
			if i == 1 then
				t.list_view = self.node_list["cell_list"]
			elseif i == 2 then
				t.list_view = self.node_list["cell_list_2"]
			end
			self.cell_list[i]:CreateCells(t)
			self.cell_list[i]:SetStartZeroIndex(false)
		end
	end
end

function WorldServerView:InitCreateCamberedList()
	local cambered_list_data = {
		item_render = BossWorldItemRender,
		asset_bundle = "uis/view/boss_ui_prefab",
		asset_name = "ph_world_cambered_render",

		scroll_list = self.node_list.ph_world_boss_cambered_list_2,
		center_x = 800,
		center_y = -110,
		radius_x = 800,
		radius_y = 800,
		angle_delta = Mathf.PI / BossView.ANGLE_DELTA,
		origin_rotation = Mathf.PI * 0.456,
		is_drag_horizontal = false,
		is_clockwise_list = false,
		speed = 1,
		arg_speed = 0.2,
		viewport_count = BossView.DRAG_COUNT,

		click_item_cb = BindTool.Bind(self.OnClickBossBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragBossToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragBossToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragBossLEndCallBack, self),
	}

	self.boss_cambered_list = CamberedList.New(cambered_list_data)
end

function WorldServerView:OnClickBossBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.list_index == select_index) then
		return
	end

	self.list_index = select_index
	self.boss_drag_select_index = select_index

	self:OnBossSelectedBtnChange(function()
		self:BossLsitSelectCallBack(item_cell)
	end, true)
end

function WorldServerView:OnDragBossToNextCallBack()
	local max_index = self.boss_list_data and #self.boss_list_data or 6
	self.boss_drag_select_index = self.boss_drag_select_index + 1
	self.boss_drag_select_index = self.boss_drag_select_index > max_index and max_index or self.boss_drag_select_index
end

function WorldServerView:OnDragBossToLastCallBack()
	self.boss_drag_select_index = self.boss_drag_select_index - 1
	self.boss_drag_select_index = self.boss_drag_select_index < 1 and 1 or self.boss_drag_select_index
end

function WorldServerView:OnDragBossLEndCallBack()
	self:OnBossSelectedBtnChange(nil, false, self.boss_drag_select_index)
end

function WorldServerView:OnBossSelectedBtnChange(callback, is_click, drag_index)
	if self.boss_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.list_index or 1
	self.boss_cambered_list:ScrollToIndex(to_index, callback, is_click)

	if is_click then
		local item_list = self.boss_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

function WorldServerView:CancelBossSelectCell()
	self.list_index = nil
	local item_list = self.boss_cambered_list:GetRenderList()
	for k, item_cell in ipairs(item_list) do
		item_cell:SetSelectedHL(false)
	end
end

function WorldServerView:ClearShowIndexCache(index)
	if self.old_show_index == nil then
		self.old_show_index = index
	end
	if self.old_show_index ~= index then
		self.cache_resid = nil
		self.list_index = nil
		self.old_show_index = index
	end
end

function WorldServerView:ShowIndexCallBack(index)
	-- local bg_name = index == TabIndex.xianjie_boss and "xianjie_boss_bg" or "boss_bejing"
	-- self.node_list.raw_xunbao_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(bg_name))
	BossWGData.Instance:SetCurrentShijieServerShowIndex(index)
	self:OnFlushCommon()
	self:ClearShowIndexCache(index)

	if index ~= TabIndex.xianjie_boss then
		self:ClearXianjieInfo()
	end

	if index == TabIndex.worserv_boss_mh then
		self.node_list["btn_record"]:SetActive(false)
		self:ShowIndexMHSS(false)
		if self.cur_kflayer then
			BossWGCtrl.Instance:SendCrossBossReq(BossView.KfReqType.ALLINFO, self.cur_kflayer)
		end
	elseif index == TabIndex.worserv_boss_sgyj then
		self:ShowIndexMHSS(false)
		self:ShowIndexSGYJ()
		BossWGCtrl.Instance:SendShangGuBossReq(WorldServerView.SG_OPERA.ALLINFO)
	elseif index == TabIndex.world_new_shenyuan_boss then

	elseif index == TabIndex.xianjie_boss then
		self:ShowIndexXianjieView()
	elseif index == TabIndex.worserv_boss_hmsy then
		self:ShowIndexMHSS(false)
	elseif index == TabIndex.worserv_everyday_recharge_boss then
		self:ShowIndexEveryDayRechargeBossView()
	end

	-- self:ShowBossViewAnimation(index)
end

function WorldServerView:ShowIndexMHSS(state)
	if state then
		self.node_list["btn_record"]:SetActive(self.show_index ~= TabIndex.xianjie_boss)
	end
end

function WorldServerView:ShowBossViewAnimation(index)
	local tween_info = UITween_CONSTS.WorldServerSys
	UITween.CleanAllMoveToShowPanel()

	UITween.CleanAlphaShow(GuideModuleName.WorldServer)
	UITween.FakeHideShow(self.node_list.world_boss_right_1)
	UITween.FakeHideShow(self.node_list.world_right_container)

	--右边面板
	if TabIndex.worserv_boss_mh == index then
		UITween.FakeHideShow(self.node_list.btn_goto_kill)
		UITween.FakeHideShow(self.node_list.layout_check_hook)
		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.Left_list_parent, u3dpool.vec2(-400, 0),
			u3dpool.vec2(0, 0), tween_info.LeftMoveTime)
		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.right_common_container,
			u3dpool.vec2(183, -36),
			u3dpool.vec2(-217, -36), tween_info.RightMoveTime)

		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.kf_gather_render, u3dpool.vec2(-380, -101),
			u3dpool.vec2(20, -101), tween_info.LeftMoveTime)
		UITween.FakeHideShow(self.node_list.kf_other_container)
		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.layout_check_hook, tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.btn_goto_kill, tween_info.FromAlpha,
				tween_info.ToAlpha
				, tween_info.AlphaTime, tween_info.AlphaShowType)
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.world_right_container, tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.kf_other_container, tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "worldserver_boss_view_right")
	elseif TabIndex.world_new_shenyuan_boss == index then
		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.Left_shenyuan, u3dpool.vec2(-400, 0),
			u3dpool.vec2(0, 0), tween_info.LeftMoveTime)
		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.shenyuan_right_bg, u3dpool.vec2(183, -36),
			u3dpool.vec2(-217, -36), tween_info.RightMoveTime)
		UITween.FakeHideShow(self.node_list.btn_shenyuan_kill)
		UITween.FakeHideShow(self.node_list.layout_shenyuan_right_contianer)
		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.layout_shenyuan_right_contianer,
				tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.btn_shenyuan_kill, tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "sehnyuan_boss_view_right")
	elseif TabIndex.xianjie_boss == index then
		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.xianjie_right_bg, u3dpool.vec2(183, 0),
			u3dpool.vec2(-240, 0), tween_info.RightMoveTime)
		UITween.MoveToShowPanel(GuideModuleName.WorldServer, self.node_list.Left_xianjie, u3dpool.vec2(-400, 0),
			u3dpool.vec2(0, 0), tween_info.LeftMoveTime)
		UITween.FakeHideShow(self.node_list.mid_xianjie)
		UITween.FakeHideShow(self.node_list.xianjie_focus_btn)

		ReDelayCall(self, function()
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.mid_xianjie, tween_info.FromAlpha,
				tween_info.ToAlpha,
				tween_info.AlphaTime, tween_info.AlphaShowType)
			UITween.AlphaShow(GuideModuleName.WorldServer, self.node_list.xianjie_focus_btn, tween_info.FromAlpha,
				tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
		end, tween_info.AlphaDelay, "xianjie_boss_view_right")
	elseif index == TabIndex.worserv_everyday_recharge_boss then

	end
end

function WorldServerView:OnFlush(param_t, index)
	for i, v in pairs(param_t) do
		if "all" == i then
			if index == TabIndex.worserv_boss_mh then
				self:OnFlushKfBossView()
				self:FlushTodayCrystal()
			elseif index == TabIndex.world_new_shenyuan_boss then
				self:FlushShenyuanView()
			elseif index == TabIndex.xianjie_boss then
				self:OnFlushXianjieView()
			elseif index == TabIndex.worserv_boss_sgyj then
				self:OnFlushSGBossView()
			elseif index == TabIndex.worserv_boss_hmsy then
				self:OnFlushNewSYBossView(param_t)
			elseif index == TabIndex.worserv_everyday_recharge_boss then
				self:OnFlushEveryDayRechargeBossView()
			end
		elseif "kf_scene" == i or i == "sg_concern" or i == "kf_concern" then
			self:OnFlushBossList()
		elseif "sy_total" == i then
			self:FlushTotalInfo()
		elseif "focus_cell" == i then
			self:FlushFocusCell()
		elseif "flushsolt" == i then
			self:OnFlushXianJieReward()
		elseif "clear_xianjie_select" == i then
			--self:ClearXianjieInfo()
			self:OnFlushXianjieView()
		elseif "jum_info" == i then
			--self.list_index = v.boss_list_index
			--if self.layer_btn_list then
			--self.layer_btn_list:SelectIndex(v.boss_list_layer)
			--end
			--self.layer_index = v.boss_list_layer
			self:OnFlushBossList()
		end
	end
end

function WorldServerView:OnFlushCommon()
	self:FlushBtnList()
end

function WorldServerView:FlushBtnList()
	if self.node_list["boss_kill_every_btn"] then
		self.node_list["boss_kill_every_btn"]:SetActive(false)
	end

	if self.node_list["button_award_tip"] then
		self.node_list["button_award_tip"]:SetActive(false)
	end

	if self.node_list["button_pc_shtq"] then
		self.node_list["button_pc_shtq"]:SetActive(false)
	end

	local view_index, number_index, card_index = BossWGData.Instance:GetBossTuJianIndex()
	if view_index ~= nil then
		if view_index == TabIndex.xianjie_boss then
			self:XianjieJump(view_index, number_index, card_index)
			return
		else
			self:TuJianKuaFuJump(view_index, number_index, card_index)
		end
	end

	local btn_list = BossWGData.Instance:GetBossLayerCfg(self.show_index + 1) --+1和本服boss做区分
	if self.show_index == TabIndex.worserv_boss_sgyj then
		btn_list = BossWGData.Instance:GetSGYJLimitLayer(btn_list)
	end

	if self.show_index == TabIndex.world_new_shenyuan_boss or self.show_index == TabIndex.xianjie_boss or self.show_index == TabIndex.worserv_everyday_recharge_boss then
		return
	end

	if btn_list == nil then
		self:OnFlushBossList()
		return
	end

	local layer = self.layer_index or 1
	if self.show_index == TabIndex.worserv_boss_mh then
		if self.layer_index == nil then
			layer = BossWGData.Instance:GetKfLayerLevel()
		end
	elseif self.show_index == TabIndex.worserv_boss_sgyj then
		if self.layer_index == nil then
			layer = BossWGData.Instance:GetSGLayerLevel()
		else
			layer = self.layer_index
		end
	elseif self.show_index == TabIndex.worserv_boss_hmsy then
		if self.layer_index == nil then
			layer = BossWGData.Instance:GetHMSYDefaultLayer()
		end
	end

	self.layer_index = nil
	self.layer_btn_list:SetDataList(btn_list or {})
	self.layer_btn_list:JumpToIndex(layer or 1)

	-- local level = GameVoManager.Instance:GetMainRoleVo().level
	-- local open_level = BossPrivilegeWGData.Instance:PrivilegeOpenLevel()
	-- self.node_list["btn_open_privilege"]:SetActive(level >= open_level)
end

function WorldServerView:OnFlushBossList()
	local list_data
	local default_index = self.list_index or 1
	local min_show_len = 4 --界面最多显示5个boss格子，选中第六个的时候可能看不到选中效果
	--print_error("def", default_index, self.list_index)
	if self.show_index == TabIndex.worserv_boss_mh then
		list_data = BossWGData.Instance:GetCrossLayerBossBylayer(self.cur_kflayer)
		if self.list_index == nil then
			default_index = self:GetKFBossDefalutIndex(default_index, list_data)
		end
		min_show_len = 3 --蛮荒神兽固定了一个格子
	elseif self.show_index == TabIndex.worserv_boss_sgyj then
		if not self.cur_sg_layer then
			self.cur_sg_layer = BossWGData.Instance:GetSGLayerLevel()
		end
		list_data = BossWGData.Instance:GetSGAllBossByLayer(self.cur_sg_layer + 1)
	elseif self.show_index == TabIndex.worserv_boss_hmsy then
		list_data = BossWGData.Instance:GetHMSYCommonBossList()
		if self.list_index == nil then
			default_index = BossWGData.Instance:GetWorldBossDefaultIndex(default_index, list_data)
		end
	elseif self.show_index == TabIndex.world_new_shenyuan_boss then
		list_data = BossWGData.Instance:GetShenyuanBossData()
		if self.list_index == nil then
			default_index = BossWGData.Instance:GetShenYuanBossDefaultIndex()
		end
	end
	list_data = list_data or {}

	if self.show_index == TabIndex.world_new_shenyuan_boss then
		self.shenyuan_btn_select_index = default_index
		self.shenyuan_list_data = list_data

		self.shenyuan_cambered_list:CreateCellList(#list_data)
		local btn_item_list = self.shenyuan_cambered_list:GetRenderList()
		for k, item_cell in ipairs(btn_item_list) do
			local item_data = self.boss_list_data[k]
			item_cell:SetData(item_data)
			if self.shenyuan_btn_select_index == item_cell:GetIndex() then
				self:OnShenYuanSelectedBtnChange(function()
					self:ShenYuanBossSelected(item_cell)
				end, true)
			end
		end
	else
		--设置默认值.
		self.list_index = default_index
		self.boss_list_data = list_data

		self.boss_cambered_list:CreateCellList(#list_data)
		local btn_item_list = self.boss_cambered_list:GetRenderList()
		for k, item_cell in ipairs(btn_item_list) do
			local item_data = self.boss_list_data[k]
			item_cell:SetData(item_data)
			if self.list_index == item_cell:GetIndex() then
				self:OnBossSelectedBtnChange(function()
					self:BossLsitSelectCallBack(item_cell)
				end, true)
			end
		end

		self:CheckRoleConcern(list_data)
	end

	if default_index == 0 and self.show_index == TabIndex.worserv_boss_mh and self.kf_gather_render.data ~= nil then --麒麟水晶为0
		self:SelectKFGather(self.kf_gather_render)
	end
end

--检查角色关注的boss中有没有等级过高无产出的
function WorldServerView:CheckRoleConcern(list_data)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for k, v in pairs(list_data) do
		if role_level - v.boss_level >= v.max_delta_level then
			if v.is_concern and v.is_concern > 0 then
				if self.show_index == TabIndex.worserv_boss_mh then
					local concern_opera = BossView.KfReqType.UNCONCERN
					BossWGCtrl.Instance:SendCrossBossReq(concern_opera, self.cur_kflayer, v.boss_id)
					BossWGData.Instance:SetCrossBossConcern(self.cur_kflayer, v.boss_id, false, self:GetShowIndex())
				elseif self.show_index == TabIndex.worserv_boss_hmsy then
					local oper_type = WorldServerView.SYOPERATE.UNCONCERN
					BossWGCtrl.Instance:SendHMSYOpera(oper_type, v.scene_id, v.boss_id)
				end
			elseif self:GetShowIndex() == TabIndex.worserv_boss_sgyj then
				local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(v.boss_id)
				local is_concern
				if boss_info ~= nil then
					is_concern = boss_info.is_concern
				end
				if is_concern and is_concern > 0 then
					local concern_opera = WorldServerView.SG_OPERA.UNCONCERN
					local scene_cfg = BossWGData.Instance:GetSgLayerCfg(self.cur_sg_layer)
					BossWGCtrl.Instance:SendShangGuBossReq(concern_opera, scene_cfg.scene_id, v.boss_id)
					BossWGData.Instance:SetCrossBossConcern(BossWGData.Instance:GetSGLayerLevel(), v.boss_id, false,
						self:GetShowIndex())
				end
			end
		end
	end
end

--显示击杀boss记录
function WorldServerView:BrowseKillRecord()
	local data = self.select_item_data
	if data ~= nil then
		--BossWGCtrl.Instance:SendBossKillRecordReq(data.boss_id)
		BossWGCtrl.Instance:SendCrossBossReq(BossView.KfReqType.KILLRECORD, self.cur_kflayer, data.boss_id)
	end
end

function WorldServerView:BossLayerBtnSelectCallBack(cell, _, _, is_click)
	local cur_index = cell.index
	--如果点击切换层数，选中默认值
	if is_click then
		self.list_index = nil
	end

	if self.show_index == TabIndex.worserv_boss_mh then
		self:SelectLayerBtnMH(cur_index)
	elseif self.show_index == TabIndex.worserv_boss_sgyj then
		self:SelectLayerBtnSGYJ(cur_index)
	elseif self.show_index == TabIndex.worserv_boss_hmsy then
		self:OnClickSyBtnLayer(cur_index)
	end
	--self:OnFlushBossList()
end

function WorldServerView:BossLsitSelectCallBack(cell)
	self.select_item_data = cell.data

	self:ClearBossTuJianIndex()

	self:ChangeHideBoss(cell)
	local index = self:GetShowIndex()
	if index == TabIndex.worserv_boss_sgyj then
		self:SelectSGBoss()
	elseif index == TabIndex.worserv_boss_mh then
		self:SelectKFBoss()
	elseif index == TabIndex.worserv_boss_hmsy then
		self:OnClickSYBossCell(cell)
	end

	if not self.is_show_hide then
		self:OnFlushBossInfo()
	end

	-- if self.select_item_data then
	-- 	self.node_list["layout_check_hook"]:SetActive(self.select_item_data.type == BossWGData.MonsterType.Boss)
	-- else
	-- 	self.node_list["layout_check_hook"]:SetActive(false)
	-- end
	self.node_list["layout_check_hook"]:SetActive(false)
end

function WorldServerView:OnFlushBossInfo()
	if not self.select_item_data then
		return
	end

	--self:OnFlushConcern()
	-- self:OnFlushLastKill()
	self:OnFlushBossAnim()
	self:OnFlushRareFall()

	local data = self.select_item_data
	local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(data.boss_id)
	if boss_server_info == nil then
		boss_server_info = BossWGData.Instance:GetCrossBossById(data.boss_id)
	end

	local time = 0
	if boss_server_info ~= nil then
		time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
	end

	if boss_server_info == nil then
		boss_server_info = {}
	end

	local boss_state = time > 1
	if not boss_state then
		self.node_list["right_title"].text.text = Language.Boss.RightTitleName[2]
		local str = boss_server_info.belong_name or ""
		if str == "" then
			str = Language.Boss.GuiShuNews
		else
			str = Language.Boss.GuiShuNews2
		end
		self.node_list["right_kill_name"].text.text = str
	else
		self.node_list["right_title"].text.text = Language.Boss.RightTitleName[2]
		self.node_list["right_kill_name"].text.text = Language.Boss.GuiShuNews3
	end
end

function WorldServerView:OnFlushConcern()
	if not self.select_item_data then
		return
	end

	local is_concern = 0
	local boss_data = BossWGData.Instance:GetCrossBossInfoByBossId(self.cur_kfboss_id)
	if self:GetShowIndex() == TabIndex.worserv_boss_sgyj and self.cur_sg_boss_id ~= nil then
		local boss_info = BossWGData.Instance:GetBossRefreshInfoByBossId(self.cur_sg_boss_id)
		if boss_info ~= nil then
			is_concern = boss_info.is_concern
		end
		boss_data = BossWGData.Instance:GetSGAllBossByBossId(self.cur_sg_boss_id)
	elseif self:GetShowIndex() == TabIndex.worserv_boss_mh then
		is_concern = BossWGData.Instance:GetCrossBossIsConcern(self.cur_kflayer, self.select_item_data.boss_index)
	elseif self:GetShowIndex() == TabIndex.worserv_boss_hmsy then
		is_concern = BossWGData.Instance:GetHMSYORIBossFlag(self.select_item_data.boss_index) and 1 or 0
	end
	self.node_list["img_check_hook"]:SetActive(is_concern ~= nil and is_concern > 0)
end

function WorldServerView:OnFlushLastKill()
	local record_list = BossWGData.Instance:GetBossRefreshInfoByBossId(self.select_item_data.boss_id)
	local str
	local vo
	if record_list and record_list.kill_vo then
		vo = record_list.kill_vo
	else
		vo = self.select_item_data.kill_vo
	end

	if vo and vo[#vo] then
		str = Language.Boss.LastKillText .. ToColorStr(vo[#vo].last_killer_name, COLOR3B.WHITE)
	else
		str = Language.Boss.LastKillText .. ToColorStr(Language.Common.ZanWu, COLOR3B.WHITE)
	end
	self.node_list.last_kill_name.text.text = str
end

function WorldServerView:OnFlushBossAnim()
	if nil == self.boss_display_model then
		self.boss_display_model = RoleModel.New()
		self.boss_display_model:SetUISceneModel(self.node_list["world_boss_model"].event_trigger_listener,
								MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.boss_display_model, 0)
	end

	local data = self.select_item_data
	if self.show_index == TabIndex.world_new_shenyuan_boss then
		data = self.shenyuan_select_data
	elseif self.show_index == TabIndex.xianjie_boss then
		data = self.xianjie_select_data
	elseif self.show_index == TabIndex.worserv_everyday_recharge_boss then
		data = self.erb_select_boss_data
	end

	if IsEmptyTable(data) then
		return
	end

	local bundle, asset
	local need_anim = false

	if data.view_resouce then
		self.boss_name = ""
		local monster_cfg
		if data.type ~= BossWGData.MonsterType.Gather then
			monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[data.view_resouce] --获取boss模型
			self.boss_name = monster_cfg.name
		end

		if data.is_baoxiang or monster_cfg == nil then
			monster_cfg = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[data.view_resouce]
			self.boss_name = monster_cfg.name
			if nil ~= monster_cfg then
				if self.show_index == TabIndex.worserv_boss_mh then
				else
					if not self.model_block then
						self.model_block = self.node_list["world_boss_model"]:GetComponent("UIBlock")
					end
					self.model_block.enabled = false
					self:ShowSpecialGatherModel() --boss_sg_boss_view
					self.cache_resid = -1
					return
				end
				bundle, asset = ResPath.GetGatherModel(monster_cfg.resid)
			end
			need_anim = true
		elseif nil ~= monster_cfg and self.boss_display_model then
			self.boss_name = monster_cfg.name
			bundle, asset = ResPath.GetMonsterModel(monster_cfg.resid)
			if self.cache_resid == asset and self.boss_display_model then
				if self.boss_display_model:GetDrawObj() ~= nil then
					return
				end
			end
			need_anim = true
		end
		if self.show_index == TabIndex.worserv_boss_mh then
			self.node_list.world_boss_first_kill:SetActive(false)
			self.node_list.world_boss_name_bg:SetActive(false)
			self.node_list.world_boss_name_bg_2:SetActive(true)
			self.node_list.world_boss_name_2.text.text = self.boss_name
		end
	else
		bundle, asset = ResPath.GetMonsterModel(data.resid)
		if self.cache_resid == asset and self.boss_display_model then
			if self.boss_display_model:GetDrawObj() ~= nil then
				return
			end
		end
		need_anim = true
	end

	self.boss_display_model:SetMainAsset(bundle, asset)
	self.boss_display_model:SetVisible(true)

	self.cache_resid = asset
	if need_anim then
		self.boss_display_model:PlayMonsterAction(true)
	end

	if self.model_block then
		self.model_block.enabled = true
	end
end

function WorldServerView:OnFlushRareFall()
	local boss_info = self.select_item_data
	if boss_info == nil then return end

	for i = 1, 2 do
		local list = {}
		if self.cell_list == nil or self.cell_list[i] == nil then return end
		if i == 1 then
			local data
			for i, v in ipairs(boss_info.drop_item_list) do
				if boss_info.is_item_list then
					data = BossWGData.Instance:GetBossCellInfo(v, boss_info)
				else
					data = {
						item_id = tonumber(v),
						show_duobei = boss_info.show_duobei,
						task_type = boss_info.task_type,
						cell_scale = 0.9,
					}
				end
				table.insert(list, data)
			end
			if boss_info.is_item_list then
				table.insert(list, 1, BossWGData.Instance:GetBossCellInfo(boss_info.drop_item_list[0], boss_info))
			end
		else
			if not boss_info.reward_item then
				return
			end

			local data
			for i, v in pairs(boss_info.reward_item) do
				data = {
					item_id = v.item_id,
					cell_scale = 0.9,
				}
				table.insert(list, data)
			end
		end
		self.cell_list[i]:SetDataList(list)
	end
end

function WorldServerView:GoToKillBoss()
	--护送拦截
	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end

	-- if self:CheckBossSceneGoto() then
	-- 	return
	-- end

	if self.show_index == TabIndex.worserv_boss_sgyj then
		self:GoToKillSGYJ()
	elseif self.show_index == TabIndex.worserv_boss_mh then
		self:GoToKillMH()
	elseif self.show_index == TabIndex.worserv_boss_hmsy then
		self:OnClickSYGoto()
	end
end

function WorldServerView:CheckBossSceneGoto(scene_id)
	if not scene_id and not self.select_item_data then
		return false
	end

	local scene_type = Scene.Instance:GetSceneType()
	local item_data = self.select_item_data
	local scene_id = scene_id and scene_id or item_data.scene_id
	local same_scene = Scene.Instance:GetSceneId() == scene_id

	if same_scene then
		self:Close()
		BossWGCtrl.Instance:MoveToBoss(item_data.boss_id, SELECT_BOSS_REASON.VIEW)
		return true
	else
		if BossWGData.IsBossScene(scene_type) then
			local str = BossWGData.Instance:GetBossSceneTip(scene_id, same_scene)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			return true
		end
	end

	return false
end

function WorldServerView:FouseOnBoss()
	if self.show_index == TabIndex.worserv_boss_mh or self.show_index == TabIndex.worserv_boss_sgyj then
		self:FouseOnKfBoss()
	elseif self.show_index == TabIndex.worserv_boss_hmsy then
		self:OnClickSYCheck()
	end
end

-- 关注
function WorldServerView:FouseOnKfBoss()
	local tabindex = self:GetShowIndex()

	local data = {}
	data.tabindex = tabindex
	data.cur_layer = self.cur_kflayer
	data.is_world_server = true
	BossWGCtrl.Instance:SetFocusViewDataAndOpen(data)
end

function WorldServerView:FlushFocusCell()
	if self.show_index and self:IsLoadedIndex(self.show_index) then
		if self.show_index == TabIndex.xianjie_boss then
			if self.node_list and self.xianjie_boss_list then
				self.xianjie_boss_list:RefreshActiveCellViews()
			end
		end
	end
end

function WorldServerView:OnClickRecord()
	if self.show_index == TabIndex.xianjie_boss then
		BossWGCtrl.Instance:SetRecordView(BOSS_RECORD_TYPE.XIANJIE_BOSS) --3表示仙界
		ViewManager.Instance:Open(GuideModuleName.BossDropRecord)
	else
		--记得切换跨服日志
		BossWGCtrl.Instance:SetRecordView(BOSS_RECORD_TYPE.WORLD_SERVER) --2表示跨服
		ViewManager.Instance:Open(GuideModuleName.BossDropRecord)
	end
end

--再爆一次 - 特权
function WorldServerView:OnClickTeQuan()
	ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_zaibaoyici)
end

function WorldServerView:OnClickZhanLing()
	BossZhanLingWGCtrl.Instance:OpenZhanLingView()
end

function WorldServerView:WorldServerRemindCallback(remind_name, num)
	if remind_name == RemindName.Boss_ZhanLing and self.node_list["zhanling_remind"] then
		self.node_list["zhanling_remind"]:SetActive(num > 0)
	end
end

function WorldServerView:BrowsePlayInfo()
	--记得切换跨服日志
	if self.show_index == TabIndex.worserv_boss_mh then
		self:BrowseKfPlayInfo()
	elseif self.show_index == TabIndex.worserv_boss_sgyj then
		self:BrowseSGPlayInfo()
	elseif self.show_index == TabIndex.worserv_boss_hmsy then
		self:BrowseHMSYPlayInfo()
	end
end

function WorldServerView:ClearBossTuJianIndex()
	local _, num_index, _ = BossWGData.Instance:GetBossTuJianIndex()
	if num_index ~= nil then
		BossWGData.Instance:SetBossTuJianIndex(nil, nil, nil)
	end
end

function WorldServerView:TuJianKuaFuJump(view_index, num_index, card_index)
	self.list_index = card_index
	self.layer_index = num_index
end

function WorldServerView:TabbarLoadCallBack()
	-- FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.WorldServer, self.tabbar)

	local ver_cell_list = self.tabbar:GetVerCellList()
	if nil == ver_cell_list then
		return
	end

	for k, v in pairs(ver_cell_list) do
		if v:GetView() and v:GetView().gameObject.activeInHierarchy then
			if v.node_list["erb_tabbar_flag"] then
				v.node_list["erb_tabbar_flag"]:CustomSetActive(k * 10 == TabIndex.worserv_everyday_recharge_boss)
			end
		end
	end
end

function WorldServerView:OnClickKfBossAddTimes(scene_type)
	-- local cfg = MainuiWGData.Instance:GetBossDecTiredCfgBySceneType(scene_type)
	-- if cfg ~= nil then
	-- 	BossWGCtrl.Instance:OpenQuickUseView(cfg)
	-- end
	BossWGCtrl.Instance:OpenWorldBossBuyView(VIP_LEVEL_AUTH_TYPE.MAMHUANG_ENTER_TIMES)
end

--旧需求，独立的日志按钮需和tabbr平级，已屏蔽2019.10.24
--function WorldServerView:AddRecordButton()
--    self.node_list["btn_world_server_record"].button:AddClickListener(BindTool.Bind(self.OnClickKFRecord, self))
--	self.tabbar:SetCreateVerCallBack(function()
--		self.tabbar:SetVerToggleCanvas(TabIndex.worserv_boss_record, false)
--	end)
--end
--
--function WorldServerView:OnClickKFRecord()
--	self:ChangeWorldServerIndex(TabIndex.worserv_boss_record)
--end
--
--function WorldServerView:ChangeWorldServerIndex(index)
--	self.node_list["img_server_record_hl"]:SetActive(index == TabIndex.worserv_boss_record)
--	self:ChangeToIndex(index)
--end

ShiJieFuLayerBtnRender = ShiJieFuLayerBtnRender or BaseClass(BaseRender)
function ShiJieFuLayerBtnRender:__init()
	-- self.is_slect_ceng = false
	self.cross_cache = nil
end

function ShiJieFuLayerBtnRender:__delete()
	-- self.is_slect_ceng = nil
end

function ShiJieFuLayerBtnRender:OnFlush()
	-- if self.data == nil or self.is_slect_ceng then return end
	if self.data == nil then return end
	self.view:SetActive(true)
	self.node_list["normal_text"].text.text = self.data.name and self.data.name or self.data
	self.node_list["select_text"].text.text = self.data.name and self.data.name or self.data
	--self.node_list.mark:SetActive(false)

	local show_index = BossWGData.Instance:GetCurrentShijieServerShowIndex()
	-- if show_index == TabIndex.worserv_boss_hmsy or show_index == TabIndex.worserv_boss_mh then
	--     local bundle, asset = ResPath.GetBossUI(name_str)
	--     self.node_list.mark.image:LoadSprite(bundle, asset,
	--     function()
	--         self.node_list.mark.image:SetNativeSize()
	--         self.node_list.mark:SetActive(true)
	--     end)
	-- elseif show_index == TabIndex.worserv_boss_sgyj then
	--     local bundle, asset = ResPath.GetBossUI(name_str)
	--     self.node_list.mark.image:LoadSprite(bundle, asset,
	--     function()
	--         self.node_list.mark.image:SetNativeSize()
	--         self.node_list.mark:SetActive(true)
	--     end)
	-- end
	if show_index == BossViewIndex.KFBoss then
		local falg, need_level = BossWGData.Instance:GetKfLayerIsEnter(self.index)
		self.node_list["img_lock"].text.text = string.format(Language.XiuXianShiLian.Lock,
			RoleWGData.GetLevelString(need_level))
	end
	self:SetJieValue()
end

function ShiJieFuLayerBtnRender:SetJieValue()
	local max_index = WorldServerWGCtrl.Instance:GetBossViewShowIndex()
	local list_data = {}
	if max_index == TabIndex.worserv_boss_mh then
		list_data = BossWGData.Instance:GetCrossLayerBossBylayer(self.index)
	end
	local max = 0
	local min = 0
	if not IsEmptyTable(list_data) then
		max = list_data[#list_data].boss_jieshu
		min = list_data[1].boss_jieshu
	end
	local jie_str = ""
	if max == min then
		jie_str = string.format(Language.Boss.JieText2, Language.Boss.hzNum[max + 1])
	else
		jie_str = string.format(Language.Boss.JieText1, Language.Boss.hzNum[min + 1], Language.Boss.hzNum[max + 1])
	end
	self.node_list.jie_text.text.text = jie_str
end

function ShiJieFuLayerBtnRender:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end

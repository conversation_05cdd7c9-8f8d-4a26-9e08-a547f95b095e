-- S-随机活动-金光鉴福.xls
local item_table={
[1]={item_id=50077,num=1,is_bind=1},
[2]={item_id=50077,num=2,is_bind=1},
[3]={item_id=37274,num=1,is_bind=1},
[4]={item_id=38702,num=1,is_bind=1},
[5]={item_id=26369,num=1,is_bind=1},
[6]={item_id=26502,num=1,is_bind=1},
[7]={item_id=26517,num=1,is_bind=1},
[8]={item_id=26127,num=1,is_bind=1},
[9]={item_id=26126,num=1,is_bind=1},
[10]={item_id=22533,num=1,is_bind=1},
[11]={item_id=26345,num=3,is_bind=1},
[12]={item_id=26200,num=18,is_bind=1},
[13]={item_id=26368,num=1,is_bind=1},
[14]={item_id=26367,num=1,is_bind=1},
[15]={item_id=26344,num=10,is_bind=1},
[16]={item_id=26203,num=15,is_bind=1},
[17]={item_id=30447,num=2,is_bind=1},
[18]={item_id=30447,num=3,is_bind=1},
[19]={item_id=30447,num=5,is_bind=1},
[20]={item_id=50077,num=3,is_bind=1},
[21]={item_id=22576,num=1,is_bind=1},
[22]={item_id=18796,num=1,is_bind=1},
[23]={item_id=37049,num=1,is_bind=1},
[24]={item_id=26130,num=5,is_bind=1},
[25]={item_id=30447,num=10,is_bind=1},
[26]={item_id=50077,num=5,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{}
},

open_day_meta_table_map={
},
task={
{task_type=13,reward_item={[0]=item_table[1]},open_view="",},
{task_id=2,param1=600,reward_item={[0]=item_table[2]},task_dec="累计充值<color=#99ffbb>%s/600灵玉</color>",},
{task_id=3,param1=1000,reward_item={[0]=item_table[2]},task_dec="累计充值<color=#99ffbb>%s/1000灵玉</color>",},
{task_id=4,param1=2000,task_dec="累计充值<color=#99ffbb>%s/2000灵玉</color>",},
{task_id=5,task_type=3,task_dec="购买<color=#99ffbb>无限至尊卡</color>",open_view="vip#recharge_month_card",},
{task_id=6,task_type=7,param1=4,task_dec="购买<color=#99ffbb>%s/4次零元购</color>",open_view="LayoutZeroBuyView",},
{task_id=7,task_type=9,param2=6,task_dec="购买<color=#99ffbb>VIP6礼包</color>",open_view="vip#recharge_vip#uip=6",},
{task_id=8,param2=7,task_dec="购买<color=#99ffbb>VIP7礼包</color>",open_view="vip#recharge_vip#uip=7",}
},

task_meta_table_map={
[8]=7,	-- depth:1
},
special_reward={
{open_reward_time=180,},
{reward_pool_seq=1,total_weight=420,lottery_description="1.完成奖券任务获取大量抽奖券，每次抽奖都可以获得1个随机号码。\n\n2.全服累计抽奖420次后，随机选取1个幸运号码，拥有幸运号码的玩家将获得本期大奖。",reward_item={[0]=item_table[3]},sort_index=2,tab_bg="a3_jgjf_txt_jgjf5",tab_name="玄冥凤魄",model_show_itemid=37274,model_scale=1.4,display_pos="-40|-15|0",},
{reward_pool_seq=2,total_weight=350,lottery_description="1.完成奖券任务获取大量抽奖券，每次抽奖都可以获得1个随机号码。\n\n2.全服累计抽奖350次后，随机选取1个幸运号码，拥有幸运号码的玩家将获得本期大奖。",reward_item={[0]=item_table[4]},sort_index=3,tab_bg="a3_jgjf_txt_jgjf3",tab_name="天罗斩月",model_show_itemid=38702,display_pos="-40|0|0",},
{day=2,},
{day=2,tab_bg="a3_jgjf_txt_jgjf2",},
{day=2,},
{day=3,},
{day=3,},
{day=3,}
},

special_reward_meta_table_map={
[4]=1,	-- depth:1
[7]=4,	-- depth:2
[6]=3,	-- depth:1
[9]=6,	-- depth:2
[5]=2,	-- depth:1
[8]=5,	-- depth:2
},
normol_reward={
{},
{reward_item={[0]=item_table[5]},sort_index=2,},
{reward_item={[0]=item_table[6]},sort_index=3,},
{reward_item={[0]=item_table[7]},sort_index=4,},
{reward_item={[0]=item_table[8]},sort_index=5,},
{reward_item={[0]=item_table[9]},sort_index=6,},
{reward_item={[0]=item_table[10]},sort_index=7,},
{reward_item={[0]=item_table[11]},sort_index=8,},
{reward_item={[0]=item_table[12]},sort_index=9,},
{reward_item={[0]=item_table[13]},sort_index=10,},
{reward_item={[0]=item_table[14]},sort_index=11,},
{reward_item={[0]=item_table[15]},sort_index=12,},
{reward_item={[0]=item_table[16]},sort_index=13,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,}
},

normol_reward_meta_table_map={
[28]=2,	-- depth:1
[37]=11,	-- depth:1
[36]=10,	-- depth:1
[35]=9,	-- depth:1
[34]=8,	-- depth:1
[33]=7,	-- depth:1
[32]=6,	-- depth:1
[31]=5,	-- depth:1
[30]=4,	-- depth:1
[29]=3,	-- depth:1
[20]=33,	-- depth:2
[25]=12,	-- depth:1
[24]=37,	-- depth:2
[23]=36,	-- depth:2
[22]=35,	-- depth:2
[21]=34,	-- depth:2
[38]=25,	-- depth:2
[19]=32,	-- depth:2
[18]=31,	-- depth:2
[17]=30,	-- depth:2
[16]=29,	-- depth:2
[15]=28,	-- depth:2
[26]=13,	-- depth:1
[39]=26,	-- depth:2
},
item_random_desc={
{random_count=2.81,},
{number=2,item_id=26369,random_count=1.1,},
{number=3,item_id=26502,},
{number=4,item_id=26517,},
{number=5,item_id=26127,random_count=2.76,},
{number=6,item_id=26126,random_count=9.67,},
{number=7,item_id=22533,},
{number=8,item_id=26345,random_count=6.91,},
{number=9,item_id=26200,},
{number=10,item_id=26368,random_count=16.57,},
{number=11,item_id=26367,},
{number=12,item_id=26344,random_count=17.96,},
{number=13,item_id=26203,}
},

item_random_desc_meta_table_map={
[7]=6,	-- depth:1
[11]=10,	-- depth:1
},
server_draw_times_reward={
{reward_item={[0]=item_table[17],[1]=item_table[1]},},
{seq=1,draw_times=100,reward_item={[0]=item_table[18],[1]=item_table[2]},},
{seq=2,draw_times=150,},
{seq=3,draw_times=200,},
{seq=4,draw_times=300,reward_item={[0]=item_table[19],[1]=item_table[20]},},
{seq=5,draw_times=500,},
{seq=6,draw_times=1000,},
{seq=7,draw_times=2000,},
{seq=8,draw_times=3000,},
{seq=9,draw_times=4000,},
{seq=10,draw_times=5000,},
{seq=11,draw_times=6000,},
{seq=12,draw_times=7000,},
{seq=13,draw_times=8000,},
{seq=14,draw_times=9000,},
{seq=15,draw_times=10000,}
},

server_draw_times_reward_meta_table_map={
[3]=2,	-- depth:1
[4]=2,	-- depth:1
[6]=5,	-- depth:1
},
other_default_table={consume_item=item_table[1],consume_gold=500,consume_num=1,str_level=200,daily_reward={[0]=item_table[21]},model_show_itemid=37017,model_scale=1.3,display_pos="-50|0|0",model_show_type=1,rotation="-12|347|0",max_redpaper_num=20,big_reward_tips_limited=120,},

open_day_default_table={start_day=1,end_day=999,grade=1,baodi_times=1200,big_reward=item_table[22],model_show_itemid=18796,model_scale=1.3,display_pos="0|0|0",model_show_type=1,rotation="-8|-6|0",},

task_default_table={grade=1,task_id=1,task_type=15,param1=1,param2=0,reward_item={[0]=item_table[20]},task_dec="累计登陆<color=#99ffbb>%s天/第1天</color>",open_view="vip#recharge_cz",},

special_reward_default_table={reward_pool_seq=0,grade=1,day=1,seq=0,total_weight=480,lottery_description="1.完成奖券任务获取大量抽奖券，每次抽奖都可以获得1个随机号码。\n\n2.全服累计抽奖480次后，随机选取1个幸运号码，拥有幸运号码的玩家将获得本期大奖。",open_reward_time=120,reward_item={[0]=item_table[23]},count_limit=10,sort_index=1,refresh_need_role_count=2,tab_bg="a3_jgjf_txt_jgjf4",tab_name="纪元冰凰",model_show_itemid=37049,model_scale=1,display_pos="-55|28|0",rotation="0|0|0",model_show_type=1,},

normol_reward_default_table={grade=1,seq=0,reward_item={[0]=item_table[24]},sort_index=1,},

item_random_desc_default_table={grade=1,number=1,item_id=26130,random_count=4.14,is_rare="",},

server_draw_times_reward_default_table={grade=1,seq=0,draw_times=50,reward_item={[0]=item_table[25],[1]=item_table[26]},}

}


function WardrobeView:ReleaseWardrobeSuitCallBack()
	if self.wardrobe_suit_list_grid then
		self.wardrobe_suit_list_grid:Delete<PERSON><PERSON>()
		self.wardrobe_suit_list_grid = nil
	end

	if self.attr_item_list then
		for k, v in pairs(self.attr_item_list) do
			v.cell:Delete<PERSON>e()
		end
		self.attr_item_list = nil
	end

	if self.wardrobe_suit_part_list then
		self.wardrobe_suit_part_list:DeleteMe()
		self.wardrobe_suit_part_list = nil
	end

	self.select_list_index = nil
	self.select_suit_seq = nil
	self.jump_suit_seq = nil
	self.select_part_index = nil
	self.select_part_data = nil
end

function WardrobeView:LoadWardrobeSuitCallBack()
	if not self.wardrobe_suit_list_grid then
        self.wardrobe_suit_list_grid = AsyncBaseGrid.New()
        self.wardrobe_suit_list_grid:CreateCells({
										col = 2, 
										list_view = self.node_list.wardrobe_suit_list_grid, 
										itemRender = WardrobeSuitRender,
										change_cells_num = 1,
										assetBundle = "uis/view/wardrobe_new_ui_prefab",
										assetName = "wardrobe_suit_cell",
		})
		self.wardrobe_suit_list_grid:SetStartZeroIndex(false)
        self.wardrobe_suit_list_grid:SetSelectCallBack(BindTool.Bind(self.OnSelectSuitItemCB, self))
	end

	self.attr_item_list = {}

	if not self.wardrobe_suit_part_list then
		self.wardrobe_suit_part_list = AsyncBaseGrid.New()
		self.wardrobe_suit_part_list:CreateCells({
			col = 3,
			list_view = self.node_list.wardrobe_suit_part_list,
			change_cells_num = 1,
			assetBundle = "uis/view/wardrobe_new_ui_prefab",
			assetName = "part_item_cell",
			itemRender = WardrobePartRender
		})
		-- self.wardrobe_suit_part_list:IsCanDeselect(true)
		self.wardrobe_suit_part_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSuitPartItemCB, self))
	end

	XUI.AddClickEventListener(self.node_list.btn_close_show_part, BindTool.Bind(self.OnClickCloseShowPart, self))                 --关闭套装
	XUI.AddClickEventListener(self.node_list.wardrobe_suit_quick_use_btn, BindTool.Bind(self.OnClickQuickUseSuit, self))          --一键穿戴
end

-- 选中套装目标
function WardrobeView:OnSelectSuitItemCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.select_list_index == cell_index then
		self:SetIsShowSuitMessage(false)
		self:FlushSuitPartStatus()
		self:FlushSuitPartList()
		return
	end

	local data = item.data
	self.select_list_index = cell_index
	self.select_suit_seq = data.suit
	self.select_part_index = nil
	self:FlushWardrobeSuitPartList()
	self:FlushWardrobeSuitModel()
end

function WardrobeView:OnSelectSuitPartItemCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.select_part_index == cell_index then
		return
	end

	self.select_part_index = cell_index
	self.select_part_data = item.data
	self:FlushSuitPartDataMessage()
end

-- 刷新衣橱套装
function WardrobeView:FlushWardrobeSuit(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.open_param then
				self.jump_suit_seq = tonumber(v.open_param) or 1
			end
		end
	end

	self:FlushWardrobeSuitList()
end

-- 刷新套装列表
function WardrobeView:FlushWardrobeSuitList()
	if nil == self.wardrobe_suit_list_grid then
		return
	end

	local data = WardrobeWGData.Instance:GetSuitShowList()
	self.wardrobe_suit_list_grid:SetDataList(data)

	local need_jump_index = self.select_list_index and self.select_list_index or 1
	if self.jump_suit_seq then
		need_jump_index = WardrobeWGData.Instance:GetJumpIndexOnShowList(self.jump_suit_seq)
		self.jump_suit_seq = nil
	else
		local cur_can_act = self.select_list_index and data[self.select_list_index] and
		data[self.select_list_index].can_act
		local remind, jump_suit = WardrobeWGData.Instance:GetTotalRemindAndSuit()
		if not cur_can_act and remind == 1 and self.select_list_index == nil then
			need_jump_index = jump_suit
		end
	end

	self:FlushSuitPartStatus()
	if self.select_list_index == nil or self.select_list_index ~= need_jump_index then
		self.wardrobe_suit_list_grid:JumpToIndexAndSelect(need_jump_index)
	else
		if not self:GetIsShowSuitMessage() then
			self:FlushSuitPartList()
		else
			self:FlushWardrobeSuitPartList()
			self:FlushWardrobeSuitModel()
		end
	end
end

-- 刷新套装列表
function WardrobeView:FlushSuitPartStatus()
	self.node_list.wardrobe_suit_list_root:CustomSetActive(self:GetIsShowSuitMessage())
	self.node_list.wardrobe_suit_part_root:CustomSetActive(not self:GetIsShowSuitMessage())
	self:SetCommonRightPartVisable(not self:GetIsShowSuitMessage())
end

-- 刷新套装列表
function WardrobeView:FlushSuitPartList()
	if (not self.select_suit_seq) or (self:GetIsShowSuitMessage()) then
		return
	end

	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local state_color = suit_data.real_act_num >= suit_data.total_part_num and COLOR3B.GREEN or COLOR3B.WHITE
	local state_str = string.format("%d/%d", suit_data.real_act_num, suit_data.total_part_num)
	state_str = ToColorStr(state_str, state_color)
    local suit_title = string.format("%s(%s)", suit_data.suit_name, state_str)
    self.node_list.wardrobe_suit_name.tmp.text = suit_title
	self.wardrobe_suit_part_list:SetDataList(suit_data.part_list)

	if self.select_part_index ~= nil then
		local show_index = self.select_part_index - 1
		self.select_part_data = suit_data.part_list[show_index]
		self:FlushSuitPartDataMessage()
	end

	local jump_part_index = self.select_part_index and self.select_part_index or 1
	self.wardrobe_suit_part_list:JumpToIndexAndSelect(jump_part_index, 6)
	self:FlushSuitPartAttrList()
	self:FlushWardrobeSuitModel(true)
end

-- 展示当前选中部位时装
function WardrobeView:FlushSuitPartDataMessage()
	local fashion_data = nil

	if not self.select_part_data then
		self:FlushNowPartMessage(fashion_data)
		return
	end

	local data = self.select_part_data
	local index_is_select = true--self.wardrobe_suit_part_list:GetCellIsSelect(self.select_part_index)

	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		if index_is_select then
			self:ChangeUseIndexByPartType(data.param1, data.param2, data.show_item_id)
			fashion_data = self:AssembleFashionData(data.param1, data.param2)
		else
			self:ChangeUseIndexByPartType(data.param1, 0, 0)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then
		local fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			if index_is_select then
				self:ChangeUseIndexByPartType(SHIZHUANG_TYPE.Mount, fashion_cfg.appe_image_id, data.show_item_id)
				fashion_data = self:AssembleMountOrKunData(fashion_cfg, MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
			else
				self:ChangeUseIndexByPartType(SHIZHUANG_TYPE.Mount, 0, 0)
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		local fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			if index_is_select then
				fashion_data = self:AssembleMountOrKunData(fashion_cfg, MOUNT_LINGCHONG_APPE_TYPE.KUN, data.param1)
				self:ChangeUseIndexByPartType(SHIZHUANG_TYPE.Mount, fashion_cfg.active_id, data.show_item_id)
			else
				self:ChangeUseIndexByPartType(SHIZHUANG_TYPE.Mount, 0, 0)
			end
		end
	end

	if fashion_data ~= nil then
		fashion_data.show_item_id = data.show_item_id
		fashion_data.type = data.type
		local item_cfg = ItemWGData.Instance:GetItemConfig(data.show_item_id)
		fashion_data.show_color = item_cfg and item_cfg.color or 1
	end

	self:FlushNowPartMessage(fashion_data, index_is_select)
	self:AssembleNowFashionCacheData()
end

-- --数据属性列表
function WardrobeView:FlushSuitPartAttrList()
	local attr_data = WardrobeWGData.Instance:GetAttrBySuit(self.select_suit_seq)

	for i, v in ipairs(attr_data) do
		if self.attr_item_list[i] then
			if self.attr_item_list[i].loaded_flag then
				self.attr_item_list[i].cell:SetData(v)
			end
		else
			local async_loader = AllocAsyncLoader(self, "wardrobe_attr" .. i)
			self.attr_item_list[i] = {}
			self.attr_item_list[i].loaded_flag = false
			async_loader:SetParent(self.node_list.wardrobe_suit_attr_list.transform)
			async_loader:Load("uis/view/wardrobe_new_ui_prefab", "wardrobe_attr_cell", function(obj)
				local cell = WardrobeAttrRender.New(obj)
				cell:SetData(v)
				self.attr_item_list[i].cell = cell
				self.attr_item_list[i].loaded_flag = true
			end)
		end
	end

	local active_num = #attr_data
	for i, v in ipairs(self.attr_item_list) do
		if v.loaded_flag then
			v.cell:SetActive(i <= active_num)
		end
	end
end

-- 设置右侧信息显影
function WardrobeView:AssembleMountOrKunData(fashion_cfg, qichong_select_type, qichong_select_index)
	local temp_now_part_data = {}
	temp_now_part_data.qichong_select_type = qichong_select_type
	temp_now_part_data.qichong_select_index = qichong_select_index
	temp_now_part_data.is_act = NewAppearanceWGData.Instance:GetSpecialQiChongIsAct(qichong_select_type, qichong_select_index)
    local info = NewAppearanceWGData.Instance:GetSpecialQiChongInfo(qichong_select_type, qichong_select_index)
    if info == nil then
        return temp_now_part_data
    end

	local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(qichong_select_type, qichong_select_index)
    if act_cfg == nil then
        return temp_now_part_data
    end

    local star_level = info.star_level or info.star
    local max_level = NewAppearanceWGData.Instance:GetSpecialQiChongMaxStarLevel(qichong_select_type, qichong_select_index)
    temp_now_part_data.is_max = star_level >= max_level
	temp_now_part_data.level = star_level + 1
    -- 属性
    local attr_list, capability = {}, 0
    if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
        attr_list, capability = NewAppearanceWGData.Instance:GetKunAttrListAndCap(QICHONG_FUNC_TYPE.UPSTAR, qichong_select_index, 1)
    else
        attr_list, capability = NewAppearanceWGData.Instance:GetSpecialQiChongAttrListAndCap(qichong_select_type, QICHONG_FUNC_TYPE.UPSTAR, qichong_select_index, 1)
    end

	temp_now_part_data.attr_list = attr_list
	temp_now_part_data.name = fashion_cfg.image_name or fashion_cfg.name or ""
	temp_now_part_data.get_msg = fashion_cfg.get_msg
	temp_now_part_data.get_param1 = fashion_cfg.get_param1
	temp_now_part_data.get_param2 = fashion_cfg.get_param2
	temp_now_part_data.get_desc = fashion_cfg.get_desc
	temp_now_part_data.is_open_forge =	0
    -- 消耗
    local is_remind = false
    local need_item_id, need_num = 0, 1
	-- 限时道具激活的限时
	local is_limit_time_type = act_cfg.timed_type == 2

    if is_limit_time_type or (not temp_now_part_data.is_max and not temp_now_part_data.is_act) then
        need_item_id = act_cfg.active_item_id or act_cfg.active_need_item_id
    elseif not temp_now_part_data.is_max and temp_now_part_data.is_act then
        local up_star_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCfg(qichong_select_type, qichong_select_index)
        need_item_id = up_star_cfg.need_item or up_star_cfg.item_id
        need_num = NewAppearanceWGData.Instance:GetSpecialQiChongUpStarCostNum(qichong_select_type, qichong_select_index, star_level)
    end

    if need_item_id > 0 then
        local num = ItemWGData.Instance:GetItemNumInBagById(need_item_id)
        is_remind = num >= need_num
    end

	temp_now_part_data.stuff_id = need_item_id
	temp_now_part_data.stuff_num = need_num
	temp_now_part_data.is_remind = is_remind
	temp_now_part_data.is_limit_time_type = is_limit_time_type

	-- 战力
	temp_now_part_data.cap_value = capability
	return temp_now_part_data
end

-- 刷新左侧套装组成部分
function WardrobeView:FlushWardrobeSuitPartList()
	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local part_list = {}
	local ls_all_lock = true

	for i, v in pairs(suit_data.part_list) do
		local show_data = {}
		show_data.show_item_id = v.show_item_id
		show_data.state = v.state

		if show_data.state ~= REWARD_STATE_TYPE.UNDONE then
			ls_all_lock = false
		end
		table.insert(part_list, show_data)
	end

	self.node_list.wardrobe_suit_no_act_txt:CustomSetActive(ls_all_lock)
	self.node_list.wardrobe_suit_quick_use_btn:CustomSetActive(not ls_all_lock)
	self:FlushPrePartItemList(part_list, true)
end

-- 刷新套装模型数据(is_enter_cache:是否进入缓存)
function WardrobeView:FlushWardrobeSuitModel(is_enter_cache)
	-- 组装数据
	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.select_suit_seq)
	if IsEmptyTable(show_list) then
		return
	end

	local user_model_data = {}
	user_model_data.model_rt_type = ModelRTSCaleType.M
	-- user_model_data.is_ui_scene = true
	user_model_data.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	local fashion_cfg

	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then 
				if data.param1 == SHIZHUANG_TYPE.BODY then	 -- 时装
					local prof = GameVoManager.Instance:GetMainRoleVo().prof	
					local new_resource_id = self:ReSetFashionId(data.part, fashion_cfg.resouce)
					user_model_data.body_res_id = ResPath.GetFashionModelId(prof, new_resource_id)
				end    

				if is_enter_cache then
					self:ChangeUseIndexByPartType(data.param1, data.param2, data.show_item_id)
				end
			end
		end

		if data.type == WARDROBE_PART_TYPE.MOUNT then
			fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(
			MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
			if fashion_cfg then
				user_model_data.mount_res_id = fashion_cfg.appe_image_id
				user_model_data.mount_res_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(
				self.suit, data.part, user_model_data.mount_res_id)
				user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end

				if is_enter_cache then
					self:ChangeUseIndexByPartType(SHIZHUANG_TYPE.Mount, fashion_cfg.appe_image_id, data.show_item_id)
				end
			end
		elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
			fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
			if fashion_cfg then
				user_model_data.mount_res_id = fashion_cfg.active_id
				user_model_data.mount_res_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(
				self.suit, data.part, user_model_data.mount_res_id)
				user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
				local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
				if not IsEmptyTable(action_cfg) then
					user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
				end

				if is_enter_cache then
					self:ChangeUseIndexByPartType(SHIZHUANG_TYPE.Mount, fashion_cfg.active_id, data.show_item_id)
				end
			end
		end
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v, user_model_data)
	end
	user_model_data = self:ChangeModelShowScale(user_model_data)

	if is_enter_cache then
		self:AssembleNowFashionCacheData()
	else
		self:SetShowSuitModelData(user_model_data)
	end
	return user_model_data
end

-- 重新设置时装形象id
function WardrobeView:ReSetFashionId(suit_part, old_id)
	local new_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, suit_part, old_id)
	return new_id
end

function WardrobeView:ShowModelByData(data, user_model_data)
	if IsEmptyTable(data) then
		return
	end

	local fashion_cfg = nil
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		WardrobeWGData.Instance:AssembleRoleModelDataByTypeIndex(data.param1, data.param2, user_model_data)
		self:SetXuanCaiRecordId(data.part, data.param1, user_model_data)
	end
end

-- 设置当前保存的特殊炫彩形象
function WardrobeView:SetXuanCaiRecordId(suit_part, part_type, export_data)
	if part_type == SHIZHUANG_TYPE.MASK then      -- 脸饰
		export_data.mask_id = self:ReSetFashionId(suit_part, export_data.mask_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then  -- 腰饰
		export_data.belt_id = self:ReSetFashionId(suit_part, export_data.belt_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
		export_data.tail_id = self:ReSetFashionId(suit_part, export_data.tail_id)
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
		export_data.shou_huan_id = self:ReSetFashionId(suit_part, export_data.shou_huan_id)
	elseif part_type == SHIZHUANG_TYPE.HALO then  -- 光环
		export_data.halo_id = self:ReSetFashionId(suit_part, export_data.halo_id)
	elseif part_type == SHIZHUANG_TYPE.WING then  -- 羽翼
		export_data.wing_id = self:ReSetFashionId(suit_part, export_data.wing_id)
	elseif part_type == SHIZHUANG_TYPE.FABAO then -- 法宝
		export_data.fabao_id = self:ReSetFashionId(suit_part, export_data.fabao_id)
	elseif part_type == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
		export_data.jianzhen_id = self:ReSetFashionId(suit_part, export_data.jianzhen_id)
	elseif part_type == SHIZHUANG_TYPE.SHENBING then -- 武器
		export_data.weapon_id = self:ReSetFashionId(suit_part, export_data.weapon_id)
	elseif part_type == SHIZHUANG_TYPE.FOOT then  -- 足迹
		export_data.foot_effect_id = self:ReSetFashionId(suit_part, export_data.foot_effect_id)
	end
end

function WardrobeView:ChangeModelShowScale(user_model_data)
	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.suit)
	if IsEmptyTable(data) then
		return user_model_data
	end

	---这里设置角色的所有展示（切换到角色展示的类型）
	local pos_str2 = data.tzsx_pos
	local rotate_str2 = data.tzsx_rot
	if pos_str2 and pos_str2 ~= "" then
		local pos = Split(pos_str2, "|")
		user_model_data.model_adjust_root_local_position  = u3dpool.vec3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if rotate_str2 and rotate_str2 ~= "" then
		local rot = Split(rotate_str2, "|")
		user_model_data.role_rotation = u3dpool.vec3(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
	end

	local scale2 = data.main_scale2
	if scale2 and scale2 ~= "" then
		user_model_data.model_adjust_root_local_scale = scale2
	end

	return user_model_data
end

----------------------------------------------------
----------------------------------------------------
-- 关闭右侧显示数据
function WardrobeView:OnClickCloseShowPart()
	if self.node_list and self.node_list.btn_part_common_dye then
		self.node_list.btn_part_common_dye:CustomSetActive(false)
	end

	self:SetIsShowSuitMessage(true)
	self:FlushWardrobeSuitList()
end

-- 快速穿戴
function WardrobeView:OnClickQuickUseSuit()
	local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local part_list = {}
	local ls_all_lock = true
	for i, v in pairs(suit_data.part_list) do
		local data = v
		if data.state ~= REWARD_STATE_TYPE.UNDONE then
			local fashion_data = nil
			if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
				fashion_data = self:AssembleFashionData(data.param1, data.param2)
			elseif data.type == WARDROBE_PART_TYPE.MOUNT then
				local fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
				if fashion_cfg then
					fashion_data = self:AssembleMountOrKunData(fashion_cfg, MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
				end
			elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
				local fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
				if fashion_cfg then
					fashion_data = self:AssembleMountOrKunData(fashion_cfg, MOUNT_LINGCHONG_APPE_TYPE.KUN, data.param1)
				end
			end

			if fashion_data then
				fashion_data.show_item_id = data.show_item_id
				fashion_data.type = data.type
				self:FlushFashionUse(true, fashion_data, true)
			end
		end
	end

	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.QuickUnseNewAppearanceSuccess)
end

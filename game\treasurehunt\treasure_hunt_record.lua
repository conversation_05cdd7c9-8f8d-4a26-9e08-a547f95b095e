TreasureHuntRecordTip = TreasureHuntRecordTip or BaseClass(SafeBaseView)

function TreasureHuntRecordTip:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 488)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_hunt_record")
end

function TreasureHuntRecordTip:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function TreasureHuntRecordTip:LoadCallBack()
    self.toggle_index = 1

    self.node_list.title_view_name.text.text = Language.Xunbao.HuntRecordTitle
    self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 1))
    self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 2))
    self.record_list = AsyncListView.New(TreasureHuntRecordItem, self.node_list["role_list"])
    -- if self.node_list.ph_xunbao_show_list then
    --     self.record_list_view = AsyncListView.New(RecordItem, self.node_list.ph_xunbao_show_list)
    -- end
    -- if self.node_list.ph_xunbao_person_list then
    --     self.record_person_list_view = AsyncListView.New(TreasureHuntRecordItem, self.node_list.ph_xunbao_person_list)
    -- end
    self.node_list.btn_all.toggle.isOn = true
end

function TreasureHuntRecordTip:SetCurTreasureType(treasure_type)
	self.treasure_type = treasure_type or -1
end

function TreasureHuntRecordTip:OnClickSwitch(state, is_on)
    if is_on and state then
        self.toggle_index = state
        self:FlushRecordList()
    end
end

function TreasureHuntRecordTip:OnFlush(param_t)
    self:FlushRecordList()
end

function TreasureHuntRecordTip:FlushRecordList()
    local data_list = {}
    if  self.treasure_type == TreasureHuntView.TreasureType.Thunder then
        data_list = TreasureHuntThunderWGData.Instance:GetRecordInfoByIndex(self.toggle_index)
    else
        data_list = TreasureHuntWGData.Instance:GetRecordViewByMode(self.toggle_index, self.treasure_type - 1)
    end

    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end


TreasureHuntRecordItem = TreasureHuntRecordItem or BaseClass(BaseRender)

function TreasureHuntRecordItem:OnFlush()
	local index = self:GetIndex()
   	-- local mark = (index % 2) == 1

	if not self.data then
		return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
    local color = ITEM_COLOR[item_cfg.color]
    self.node_list["time"].text.text = os.date("%m-%d  %X", self.data.consume_time)
    -- self.node_list["root_bg"].image.enabled = mark

    local role_name = self.data.role_name or RoleWGData.Instance:GetAttr("name")
    local str1 = string.format(Language.SiXiangCall.TxtRecord3, role_name)
    local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, self.data.item_data.num or 1)
    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
end
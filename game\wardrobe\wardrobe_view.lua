WardrobeView = WardrobeView or BaseClass(SafeBaseView)
local NEED_CACHE_PART_USE_DATA = {
	[1] = SHIZHUANG_TYPE.BODY,
	[2] = SHIZHUANG_TYPE.FOOT,
	[3] = SHIZHUANG_TYPE.HALO,
	[4] = SHIZHUANG_TYPE.WING,
	[5] = SHIZHUANG_TYPE.FABAO,
	[6] = SHIZHUANG_TYPE.SHENBING,
	[7] = SHIZHUANG_TYPE.LINGGONG,
	[8] = SHIZHUANG_TYPE.PHOTOFRAME,
	[9] = SHIZHUANG_TYPE.BUBBLE,
	[10] = SHIZHUANG_TYPE.MASK,
	[11] = SHIZHUANG_TYPE.BELT,
	[12] = SHIZHUANG_TYPE.WEIBA,
	[13] = SHIZHUANG_TYPE.SHOUHUAN,
	[14] = SHIZHUANG_TYPE.JIANZHEN,
	[15] = SHIZHUANG_TYPE.Mount,
}
local RESET_COOL_TIME = 8
local TURN_AROUND_ANGLE = -180				-- 转身角度

function WardrobeView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.default_index = TabIndex.wardrobe_suit

	self.remind_tab = {
		{RemindName.WardrobeSuit},
		{RemindName.WardrobeFashion},
		{RemindName.WardrobeJewelry},
		-- {RemindName.WardrobeEffect},
	}

    local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/wardrobe_new_ui_prefab"
	self:AddViewResource(TabIndex.wardrobe_suit, bundle_name, "layout_wardrobe_suit")
	self:AddViewResource({0, TabIndex.wardrobe_fashion, TabIndex.wardrobe_jewelry}, bundle_name, "layout_wardrobe_common")
	self:AddViewResource(0, common_bundle, "VerticalTabbar")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.WARDROBE})
end

function WardrobeView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Wardrobe.ViewName

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Wardrobe.NameTable, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.WardrobeView, self.tabbar)
	end

	if not self.common_part_item_list then
		self.common_part_item_list = AsyncListView.New(WardrobePartShowRender, self.node_list.common_part_item_list)
	end

	if nil == self.role_model then
		self.role_model = CommonUserModelRender.New(self.node_list.common_middle_display)
		self.role_model:AddUiRoleModel(self)
	end

	if self.common_operate_item == nil then
		self.common_operate_item = ItemCell.New(self.node_list.common_operate_item)
	end

	if not self.common_attr_list then
        self.common_attr_list = {}
        local parent_node = self.node_list["common_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.common_attr_list[i] = cell
        end
    end

	if self.common_stars_list == nil then
        self.common_stars_list = {}
        for i = 1, 5 do
            self.common_stars_list[i] = self.node_list["common_star_" .. i]
        end
    end

	if not self.wardrobe_fashion_part_list then
		self.wardrobe_fashion_part_list = AsyncBaseGrid.New()
		self.wardrobe_fashion_part_list:CreateCells({
			col = 3,
			list_view = self.node_list.wardrobe_fashion_part_list,
			change_cells_num = 1,
			assetBundle = "uis/view/wardrobe_new_ui_prefab",
			assetName = "part_item_cell",
			itemRender = WardrobeFashionPartRender
		})
		self.wardrobe_fashion_part_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectFashionPartItemCB, self))
		self.wardrobe_fashion_part_list:SetStartZeroIndex(false)
		-- self.wardrobe_fashion_part_list:IsCanDeselect(true)
	end

	if not self.show_type_list then
		self.show_type_list = {}
		for i = 1, 4 do
            local cell_obj = self.node_list.show_type_list:FindObj(string.format("show_type_render_0%d", i))
            if cell_obj then
                local cell = WardrobeFashionTypeRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectFashionShowTypeCB, self))
                cell:SetIndex(i)
                self.show_type_list[i] = cell
            end
        end
	end

	if not self.common_part_filter_list then
		self.common_part_filter_list = {}
		for i = 1, 4 do
            local cell_obj = self.node_list.common_part_filter_list:FindObj(string.format("common_part_filter_render_0%d", i))
            if cell_obj then
                local cell = WardrobeFashionFilterRender.New(cell_obj)
                cell:SetClickCallBack(BindTool.Bind1(self.OnSelectFashionFilterCB, self))
                cell:SetIndex(i)
                self.common_part_filter_list[i] = cell
            end
        end
	end

	XUI.AddClickEventListener(self.node_list.btn_common_filter, BindTool.Bind(self.OnClickFilter, self))                	-- 筛选
	XUI.AddClickEventListener(self.node_list.btn_common_amplify, BindTool.Bind(self.OnClickAmplify, self))           		-- 放大展示按钮
	XUI.AddClickEventListener(self.node_list.btn_common_reduce, BindTool.Bind(self.OnClickReduce, self))      				-- 缩小展示按钮
	XUI.AddClickEventListener(self.node_list.btn_common_horse_up, BindTool.Bind(self.OnClickHorseUp, self))           		-- 上马
	XUI.AddClickEventListener(self.node_list.btn_common_horse_down, BindTool.Bind(self.OnClickHorseDown, self))    			-- 下马
	XUI.AddClickEventListener(self.node_list.btn_part_common_power, BindTool.Bind(self.OnClickShowPower, self))    			-- 展示属性相关
	XUI.AddClickEventListener(self.node_list.btn_part_common_operate, BindTool.Bind(self.OnClickPartOperate, self))    		-- 操作按钮
	-- XUI.AddClickEventListener(self.node_list.common_part_attr_root, BindTool.Bind(self.OnClickCloseShowPower, self))    		-- 操作按钮
	XUI.AddClickEventListener(self.node_list.common_part_filter_root, BindTool.Bind(self.OnClickFilterRoot, self))    			-- 展示筛选

	XUI.AddClickEventListener(self.node_list.btn_common_dressing_up, BindTool.Bind(self.OnClickDessingUp, self))    		-- 操作穿搭
	-- XUI.AddClickEventListener(self.node_list.btn_common_collect, BindTool.Bind(self.OnClickCollect, self))    				-- 操作天赏
	XUI.AddClickEventListener(self.node_list.btn_common_dressing, BindTool.Bind(self.OnClickdressing, self))    			-- 操作梳妆
	XUI.AddClickEventListener(self.node_list.btn_part_common_dye, BindTool.Bind(self.OnClickOpenPartDye, self))    			-- 操作时装染色
	XUI.AddClickEventListener(self.node_list.btn_part_use_fashion, BindTool.Bind(self.FlushFashionUse, self, true))    		-- 操作幻化
	XUI.AddClickEventListener(self.node_list.btn_part_unuse_fashion, BindTool.Bind(self.FlushFashionUse, self, false))    	-- 操作取消幻化
	XUI.AddClickEventListener(self.node_list.btn_common_skill_show, BindTool.Bind(self.OnClickSkillShow, self, false))    	-- 技能展示

	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.WardrobeView, self.get_guide_ui_event)
end

function WardrobeView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.increase_time_tips then
		self.increase_time_tips:DeleteMe()
		self.increase_time_tips = nil
	end

	if self.common_part_item_list then
		self.common_part_item_list:DeleteMe()
		self.common_part_item_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.common_operate_item then
		self.common_operate_item:DeleteMe()
		self.common_operate_item = nil
	end

	if self.common_attr_list then
        for k,v in pairs(self.common_attr_list) do
            v:DeleteMe()
        end
        self.common_attr_list = nil
    end

	if self.wardrobe_fashion_part_list then
		self.wardrobe_fashion_part_list:DeleteMe()
		self.wardrobe_fashion_part_list = nil
	end

	if self.show_type_list and #self.show_type_list > 0 then
		for i, v in ipairs(self.show_type_list) do
			v:DeleteMe()
		end

		self.show_type_list = nil
	end

	if self.common_part_filter_list and #self.common_part_filter_list > 0 then
		for i, v in ipairs(self.common_part_filter_list) do
			v:DeleteMe()
		end

		self.common_part_filter_list = nil
	end

	self.cache_mount_res_id = nil				-- 当前缓存的坐骑id
	self.cache_now_use_id = nil					-- 当前的时装缓存
	self.common_stars_list = nil				-- 星星列表
	self.is_show_mount_res_id = nil				-- 是否展示上下坐骑
	self.is_show_big = nil						-- 是否拉近镜头
	self.is_show_filter = nil					-- 是否打开筛选
	self.is_show_part_attr = nil				-- 是否展示属性
	self.now_part_data = nil					-- 当前时装数据
	self.user_fashion_model_data = nil			-- 模型全部数据
	self.user_suit_model_data = nil				-- 衣橱套装展示数据
	self.is_not_reset_cool = nil				-- 播放出场冷却
	self.is_show_suit_message = nil				-- 是否为展示套装
	self.select_show_type_index = nil
	self.select_fashion_part_index = nil
	self.select_fashion_data = nil
	self.select_filter_index = nil
	self.jump_item_id = nil						-- 跳转物品
	self.fashion_part_type = nil
	self.fashion_part_index = nil
	self.qichong_select_type = nil
	self.qichong_select_index = nil
	self.is_first_enter = nil
	self.is_need_turn_around = nil				-- 是否需要模型转身
	self.correct_turn_around_value = nil				-- 是否需要修正的参数
	self.correct_turn_around_tween = nil
	self:ReleaseWardrobeSuitCallBack()
	self:RemoveResetCoolDelayTimer()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.WardrobeView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

-- 关闭前调用
function WardrobeView:CloseCallBack()
	self.is_not_reset_cool = nil				-- 播放出场冷却
	self.is_first_enter = nil
end

-- 选中部件
function WardrobeView:OnSelectFashionPartItemCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.select_fashion_part_index == cell_index then
		return
	end

	self.select_fashion_part_index = cell_index
	self.select_fashion_data = item.data
	self:FlushFashionPartSelectData(self.select_fashion_data)
end

-- 选中类型
function WardrobeView:OnSelectFashionShowTypeCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.select_show_type_index == cell_index then
		return
	end
	
	self.select_show_type_index = cell_index
	-- 切换类型要清空缓存
	self.select_filter_index = nil
	self.select_fashion_part_index = nil
	self.select_fashion_data = nil
	self:FlushFilterPanel()
	self:FlushFashionShowType()
	self:FlushFashionPartSelectType(self.select_show_type_index)
end

-- 筛选类型
function WardrobeView:OnSelectFashionFilterCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	if self.select_filter_index == cell_index then
		return
	end
	
	self.select_filter_index = cell_index
	self:FlushFilterSelect()
end

--移除回调
function WardrobeView:RemoveResetCoolDelayTimer()
    if self.reset_cool_timer then
        GlobalTimerQuest:CancelQuest(self.reset_cool_timer)
        self.reset_cool_timer = nil
    end
end

------------------------------------------------------------------------------------------
----------------------------------------------------
-- 加载下标完成
function WardrobeView:LoadIndexCallBack(index)
	if index == TabIndex.wardrobe_suit then
		self:LoadWardrobeSuitCallBack()
	end	
end

-- 切换标签调用
function WardrobeView:ShowIndexCallBack(index)
	-- 这里制空需要实现预览
	self:SetCommonRightPartVisable(index ~= TabIndex.wardrobe_suit)
	self:SetCommonRightFashionVisable(index ~= TabIndex.wardrobe_suit)
	self.node_list.btn_part_use_fashion:CustomSetActive(false)
	self.node_list.btn_part_unuse_fashion:CustomSetActive(false)
	self.select_show_type_index = nil
	self.is_show_part_attr = false

	if index == TabIndex.wardrobe_suit then
		self.correct_turn_around_value = 0
		self.correct_turn_around_tween = true
		self.is_show_big = false
		self:SetIsShowSuitMessage(true)

		if self.role_model then
			self.role_model:ModelFocusMove(false)
		end
	else
		self.is_show_mount_res_id = false
		self:SetIsShowSuitMessage(false)
	end
end

-- 刷新
function WardrobeView:OnFlush(param_t, index)
	self:InitNowFashionCacheList()
	self:FlushOperateStatus()
	self:FlushFilterPanel()

	if index == TabIndex.wardrobe_suit then
		self:FlushWardrobeSuit(param_t)
	elseif index == TabIndex.wardrobe_fashion then
		self:FlushWardrobeFashion(param_t)
	elseif index == TabIndex.wardrobe_jewelry then
		self:FlushWardrobeJewelry(param_t)
	elseif index == TabIndex.wardrobe_effect then
		self:FlushWardrobeEffect(param_t)
	end	
end

-- 刷新当前的界面
function WardrobeView:FlushCurShowView(param_t, key)
	self:Flush(self.show_index, key, param_t)
end
----------------------------------------------------
----------------------------------------------------
-- 初始化缓存列表
function WardrobeView:InitNowFashionCacheList()
	if self.cache_now_use_id ~= nil then
		return
	end

	self.cache_now_use_id = {}
	for i, v in ipairs(NEED_CACHE_PART_USE_DATA) do
		self.cache_now_use_id[i] = {}
		self.cache_now_use_id[i].part_type = v
		local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(v)
		self.cache_now_use_id[i].used_index = used_index or 0

		if used_index ~= 0 and v ~= SHIZHUANG_TYPE.Mount then
			local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(v, used_index)
			self.cache_now_use_id[i].stuff_id = level_cfg.stuff_id
		end
	end

	-- self:AssembleNowFashionCacheData()
end

-- 使用类型获取缓存
function WardrobeView:GetUseIndexByPartType(var_part_type)
	if not self.cache_now_use_id then
		return nil
	end

	for i, v in ipairs(self.cache_now_use_id) do
		if v.part_type == var_part_type then
			return v
		end
	end

	return nil
end

-- 使用类型修改缓存
function WardrobeView:ChangeUseIndexByPartType(var_part_type, var_use_index, var_stuff_id)
	local cache_data = self:GetUseIndexByPartType(var_part_type)
	if cache_data ~= nil then
		cache_data.used_index = var_use_index
		cache_data.stuff_id = var_stuff_id
	end
end

-- 获取当前使用的坐骑
function WardrobeView:GetNowRoleFashionMount()
	-- 封装一个当前使用的坐骑
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local mount_res_id = 2001
	if role_vo.mount_appeid and role_vo.mount_appeid > 0 then
		mount_res_id = role_vo.mount_appeid
	end

	return mount_res_id
end

-- 刷新按钮状态
function WardrobeView:FlushOperateStatus()
	if self.is_show_mount_res_id then
		self.is_show_big = false
	end

	self.node_list.btn_common_filter:CustomSetActive(self.show_index ~= TabIndex.wardrobe_suit)
	self.node_list.btn_common_amplify:CustomSetActive((not self.is_show_big) and (not self.is_show_mount_res_id) and self.show_index ~= TabIndex.wardrobe_suit)
	self.node_list.btn_common_reduce:CustomSetActive(self.is_show_big and (not self.is_show_mount_res_id) and self.show_index ~= TabIndex.wardrobe_suit)
	self.node_list.btn_common_horse_up:CustomSetActive((not self.is_show_mount_res_id) and self.show_index == TabIndex.wardrobe_suit)
	self.node_list.btn_common_horse_down:CustomSetActive(self.is_show_mount_res_id and self.show_index == TabIndex.wardrobe_suit)

	local is_show_dye = false
	if self.now_part_data then
		is_show_dye = self.now_part_data.is_open_dyeing == 1
	end

	self.node_list.btn_part_common_dye:CustomSetActive(is_show_dye)
	self.node_list.common_part_attr_root:CustomSetActive(self.is_show_part_attr)
	self.node_list.common_part_filter_root:CustomSetActive(self.is_show_filter)

	self.node_list.btn_common_dressing_up:CustomSetActive(self.show_index ~= TabIndex.wardrobe_suit)
	-- self.node_list.btn_common_collect:CustomSetActive(false)
	if self.show_index == TabIndex.wardrobe_fashion then
		self:FlushSkillPreShowState(self.now_part_data and self.now_part_data.show_item_id or 0)
	else
		self:FlushSkillPreShowState(0)
	end
end

-- 选择筛选
function WardrobeView:FlushFilterPanel()
	if self.select_filter_index == nil then
		self.select_filter_index = 1
	end

	for i, v in ipairs(self.common_part_filter_list) do
		v:SetData(Language.Wardrobe.FilterName[i])
		v:OnSelectChange(i == self.select_filter_index)
	end
end

-- 选择筛选
function WardrobeView:FlushFilterSelect()
	for i, v in ipairs(self.common_part_filter_list) do
		v:OnSelectChange(i == self.select_filter_index)
	end

	self:FlushFashionPartSelectType(self.select_show_type_index)
end

-- 刷新属性界面
function WardrobeView:FlushNowPartAttrPanel()
	if not self.now_part_data then
		return
	end

	if not self.is_show_part_attr then
		return
	end

	local attr_count = 0
    for k,v in pairs(self.common_attr_list) do
		v:SetVisible(self.now_part_data.attr_list[k] ~= nil)

		if self.now_part_data.attr_list[k] ~= nil then
			v:SetData(self.now_part_data.attr_list[k])
			attr_count = attr_count + 1
		end
    end

	self.node_list.common_part_attr_can_scroll:CustomSetActive(attr_count > 5)
	local star = self.now_part_data.level - 1
	star = star >= 0 and star or 0
    local star_res_list = GetTwenTyStarImgResByStar(star)

	for k,v in pairs(self.common_stars_list) do
		v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
	end
end

function WardrobeView:FlushSkillPreShowState(show_item_id)
	self.node_list.btn_common_skill_show:SetActive(false)
	local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
	if not item_cfg then
		return
	end

	local display_type = item_cfg.is_display_role
	if nil == display_type or display_type == 0 or display_type == "" then
		return
	end

	if display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI
        or display_type == DisplayItemTip.Display_type.WING then
        local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(show_item_id)
		if cfg then
			local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(cfg.part_type, cfg.index, 1)
			if skill_cfg then
				self.node_list.btn_common_skill_show:SetActive(true)
			end
		end
	end
end

-- 刷新模型
function WardrobeView:FlushWardrobeModel(user_model_data)
	local model_data = user_model_data

	if model_data == nil then
		model_data = self.is_show_suit_message and self.user_suit_model_data or self.user_fashion_model_data
	end

	if model_data == nil or IsEmptyTable(model_data) then
		return
	end

	if model_data.mount_res_id == nil then
		self.is_show_mount_res_id = false
	elseif model_data.mount_res_id ~= 0 then
		self.cache_mount_res_id = model_data.mount_res_id
	end

	if not self.is_show_mount_res_id then
		model_data.mount_res_id = 0
	else
		model_data.mount_res_id = self.cache_mount_res_id
	end

	model_data.show_index = {           
		TabIndex.wardrobe_suit,
		TabIndex.wardrobe_fashion,
		TabIndex.wardrobe_jewelry,
		TabIndex.wardrobe_effect,
	}

	self.role_model:SetData(model_data)
	local wardrobe_offset = WardrobeWGData.Instance:GetSpecialWardrobeOffsetBySexProf()

	if not self.is_show_mount_res_id then
		self.role_model:SetWeaponModel()
		wardrobe_offset = wardrobe_offset + (self.correct_turn_around_value or 0)

		-- if not self.is_show_big then
		self.role_model:SetUSAdjustmentNodeLocalScale(1)
		self.role_model:SetUSAdjustmentNodeLocalPosition(Vector3(-1.8, 0, 0))

		if self.correct_turn_around_tween then
			self.correct_turn_around_tween = false
			self.role_model:SetUSAdjustmentNodeLocalRotationTween(Vector3(0, -40 + wardrobe_offset, 0))
		else
			self.role_model:SetUSAdjustmentNodeLocalRotation(Vector3(0, -40 + wardrobe_offset, 0))
		end


		-- end

		if not self.is_show_suit_message then
			self.role_model:ModelFocusStatus(false)
		else
			self.role_model:ModelFocusStatus(true)
		end
	else
		local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.select_suit_seq)
		if data then
			local display_pos_str = data.main_whole_display_pos
			local rotate_str = data.main_rot
	
			if display_pos_str and display_pos_str ~= "" then
				local pos = Split(display_pos_str, "|")
				self.role_model:SetUSAdjustmentNodeLocalPosition(Vector3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0))
			end
	
			if rotate_str and rotate_str ~= "" then
				local rot = Split(rotate_str, "|")
				self.role_model:SetUSAdjustmentNodeLocalRotation(u3dpool.vec3(tonumber(rot[1]) or 0, (tonumber(rot[2]) or 0) + wardrobe_offset, tonumber(rot[3]) or 0)) 
			end
	
			local main_scale = data.main_scale
			if main_scale and main_scale ~= "" then
				self.role_model:SetUSAdjustmentNodeLocalScale(main_scale)
			end
		end
		
		self.role_model:ModelFocusStatus(true)
		self.role_model:SetRemoveWeaponModel()
	end
end

-- 组装当前显示数据
function WardrobeView:AssembleNowFashionCacheData()
	if not self.cache_now_use_id then
		return 
	end

	if self.user_fashion_model_data == nil then
		self.user_fashion_model_data = {}
	end

	local role_res_id = AppearanceWGData.Instance:GetRoleResId(true)
	local is_normal_play_anim = true
	local cur_anim = SceneObjAnimator.UiIdle

	for i, v in ipairs(self.cache_now_use_id) do
		local part_type = v.part_type
		-- 激活 / 进阶
		local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(v.part_type, v.used_index)
		local res_id = fashion_cfg and fashion_cfg.resouce or 0

		if part_type == SHIZHUANG_TYPE.BODY then      -- 时装
			if res_id == 0 then
				res_id = role_res_id
			end

			self.user_fashion_model_data.body_res_id = res_id
		elseif part_type == SHIZHUANG_TYPE.MASK then      -- 脸饰
			self.user_fashion_model_data.mask_id = res_id
		elseif part_type == SHIZHUANG_TYPE.BELT then  -- 腰饰
			self.user_fashion_model_data.belt_id = res_id
		elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
			self.user_fashion_model_data.tail_id = res_id
		elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
			self.user_fashion_model_data.shou_huan_id = res_id
		elseif part_type == SHIZHUANG_TYPE.HALO then  -- 光环
			self.user_fashion_model_data.halo_id = res_id
		elseif part_type == SHIZHUANG_TYPE.WING then  -- 羽翼
			self.user_fashion_model_data.wing_id = res_id
		elseif part_type == SHIZHUANG_TYPE.FABAO then -- 法宝
			self.user_fashion_model_data.fabao_id = res_id
		elseif part_type == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
			self.user_fashion_model_data.jianzhen_id = res_id
		elseif part_type == SHIZHUANG_TYPE.SHENBING then -- 武器
			self.user_fashion_model_data.weapon_id = res_id
		elseif part_type == SHIZHUANG_TYPE.FOOT then  -- 足迹
			if self.show_index == TabIndex.wardrobe_effect and self.select_show_type_index == 1 then	-- 这里写死了一个1
				self.user_fashion_model_data.foot_effect_id = res_id
				is_normal_play_anim = false
			else
				self.user_fashion_model_data.foot_effect_id = 0
			end
		elseif part_type == SHIZHUANG_TYPE.Mount then  -- 坐骑
			res_id = v.used_index
			if res_id == 0 then
				res_id = self:GetNowRoleFashionMount()
			end

			self.user_fashion_model_data.mount_res_id = res_id
			self.user_fashion_model_data.mount_action = MOUNT_RIDING_TYPE[1]
			local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(res_id)
			
			if not IsEmptyTable(action_cfg) then
				self.user_fashion_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
			end
		end
	end

	self.user_fashion_model_data.model_rt_type = ModelRTSCaleType.M
	self.user_fashion_model_data.is_ui_scene = true
	self.user_fashion_model_data.is_not_reset_pos = self.is_show_big

	-- 正常情况走这一套
	if is_normal_play_anim and (not self.is_not_reset_cool) then
		cur_anim = SceneObjAnimator.Rest
		self:EnterPlayResetCool()
	end

	self.user_fashion_model_data.cur_anim = cur_anim
	self:FlushWardrobeModel()
end

-- 进入播放出场动作冷却(去掉冷却，不再播reset)
function WardrobeView:EnterPlayResetCool()
	-- self:RemoveResetCoolDelayTimer()
	self.is_not_reset_cool = true

	-- self.reset_cool_timer = GlobalTimerQuest:AddDelayTimer(function ()
	-- 	self:RemoveResetCoolDelayTimer()
	-- 	self.is_not_reset_cool = false
	-- end, RESET_COOL_TIME)
end

-- 设置右侧信息显影
function WardrobeView:AssembleFashionData(fashion_part_type, fashion_index)
	local temp_now_part_data = nil
	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(fashion_part_type, fashion_index)

    if fashion_cfg == nil then
        return temp_now_part_data
    end

	temp_now_part_data = {}
	temp_now_part_data.fashion_part_type = fashion_part_type
	temp_now_part_data.fashion_index = fashion_index
	temp_now_part_data.is_act = NewAppearanceWGData.Instance:GetFashionIsAct(fashion_part_type, fashion_index)
    temp_now_part_data.level = NewAppearanceWGData.Instance:GetFashionLevel(fashion_part_type, fashion_index)
	local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(fashion_part_type, fashion_index)
    temp_now_part_data.is_max = temp_now_part_data.level >= level_cfg.max_up_level
	local show_lv = temp_now_part_data.level > 0 and temp_now_part_data.level or 1
	local cur_attr_data = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(fashion_part_type, fashion_index, show_lv)
	temp_now_part_data.attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_attr_data)
	temp_now_part_data.name = fashion_cfg.name or ""
	temp_now_part_data.shizhuang_type = fashion_cfg.shizhuang_type
	temp_now_part_data.get_msg = fashion_cfg.get_msg
	temp_now_part_data.get_param1 = fashion_cfg.get_param1
	temp_now_part_data.get_param2 = fashion_cfg.get_param2
	temp_now_part_data.get_desc = fashion_cfg.get_desc
	temp_now_part_data.is_open_forge = fashion_cfg.is_open_forge
	temp_now_part_data.is_open_dyeing = fashion_cfg.is_open_dyeing
    local is_remind = false
	temp_now_part_data.stuff_id = level_cfg.stuff_id
    local stuff_num = NewAppearanceWGData.Instance:GetFashionUpLevelCostStuffNumCfg(fashion_part_type, fashion_index, temp_now_part_data.level) or 1

    if temp_now_part_data.stuff_id and temp_now_part_data.stuff_id > 0 then
        local num = ItemWGData.Instance:GetItemNumInBagById(temp_now_part_data.stuff_id)
        is_remind = num >= stuff_num
    end
	
	temp_now_part_data.stuff_num = stuff_num
	temp_now_part_data.is_remind = is_remind
    -- 技能
	local skill_cap = 0
	local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(fashion_part_type, fashion_index, 1)
	if skill_cfg then
		if temp_now_part_data.is_act then
			skill_cap = NewAppearanceWGData.Instance:GetSingleSkillCap(skill_cfg)
		end
	end
	
	-- 战力
	local attr_cap = AttributeMgr.GetCapability(AttributeMgr.GetAttributteByClass(cur_attr_data))
	local add_per_val = NewAppearanceWGData.Instance:GetCurFashionPartAddPerValue(fashion_part_type)
	local extra_cap =  NewAppearanceWGData.Instance:CalcFashionAttrCapWithAddPer(cur_attr_data, add_per_val)
	attr_cap = attr_cap + extra_cap
	temp_now_part_data.cap_value = attr_cap + skill_cap

	return temp_now_part_data
end

-- 设置当前部件信息
function WardrobeView:FlushNowPartMessage(part_data, is_select)
	self.now_part_data = part_data
	self.node_list.common_no_have_part_data_root:CustomSetActive(self.now_part_data == nil or (not is_select))
	self.node_list.common_have_part_data_root:CustomSetActive(self.now_part_data ~= nil and is_select)
	self.node_list.btn_part_common_dye:CustomSetActive(part_data and part_data.is_open_dyeing == 1 and is_select)
	local is_used = self:GetNowFashionPartIsUsed()
	self.node_list.btn_part_use_fashion:CustomSetActive((part_data and part_data.is_act) and is_select and (not is_used))
	self.node_list.btn_part_unuse_fashion:CustomSetActive((part_data and part_data.is_act) and is_select and is_used)

	-- 阶数
	if self.now_part_data == nil or (not is_select) then
		self.node_list.common_part_di_image.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_sz_di_color_0%d", 2)))
		self.node_list.common_part_name.tmp.text = Language.Wardrobe.FashionDetails
		self.node_list.common_part_charm_txt.tmp.text = ""
		return
	end

	self.common_operate_item:SetData({item_id = part_data.show_item_id})
    local item_num = ItemWGData.Instance:GetItemNumInBagById(part_data.show_item_id)
	local color = item_num >= part_data.stuff_num and COLOR3B.D_GREEN or COLOR3B.D_RED
	self.common_operate_item:SetRightBottomTextVisible(true)
	self.common_operate_item:SetRightBottomColorText(item_num .. '/' .. part_data.stuff_num , color)
	local color = part_data.show_color <= 8 and part_data.show_color or 8
	self.node_list.common_part_di_image.image:LoadSprite(ResPath.GetWardrobeImg(string.format("a3_sz_di_color_0%d", color)))
	self.node_list.common_part_name.tmp.text = part_data.name
	local star = part_data.level - 1
	local jie = math.floor(star / 10)
	local pin = star % 10

	local jie_str = ToColorStr(string.format(Language.Wardrobe.CharmTxt, jie, pin), "#fff8bb")
	if jie < 0 then
		jie_str = ToColorStr(Language.Common.NoActivate, COLOR3B.RED)
	end

	self.node_list.common_part_charm_txt.tmp.text =  jie_str
	self.node_list.btn_part_common_operate_max:CustomSetActive(part_data.is_max)
	self.node_list.btn_part_common_operate:CustomSetActive(((not part_data.is_max) and part_data.is_remind) or (part_data.get_msg == 1 or part_data.get_msg == 2))
	self.node_list.common_part_go_to_txt:CustomSetActive(part_data.get_msg ~= 1 and (not part_data.is_remind) and (not part_data.is_max))
	self.node_list.common_get_part_buy_root:CustomSetActive(part_data.get_msg == 1 and (not part_data.is_remind) and (not part_data.is_max))
	-- self.node_list.btn_common_collect:CustomSetActive(part_data.is_open_forge == 1)

	local is_collect_red = false

	-- if part_data.is_open_forge == 1 then
	-- 	is_collect_red = WardrobeWGData.Instance:GetWardrobeFashionCollectRed(part_data.fashion_part_type, part_data.fashion_index)
	-- end
	-- self.node_list.btn_common_collect_red:CustomSetActive(is_collect_red)

	local str = part_data.get_msg == 1 and Language.Common.CanPurchase or Language.Common.GoTo
	if part_data.is_remind and (not part_data.is_max) then
		str = part_data.is_act and Language.Wardrobe.BtnViewName[2] or Language.Wardrobe.BtnViewName[1]
	end

	local get_str = part_data.get_msg == 2 and part_data.get_desc or string.format("%s%s", part_data.get_param1, Language.Equip.GetCoin)
	self.node_list.btn_part_common_operate_txt.tmp.text = str
	self.node_list.btn_part_common_operate_red:CustomSetActive(part_data.is_remind)
	self.node_list.common_part_go_to_txt.tmp.text = get_str
	self.node_list.common_get_part_buy_txt.tmp.text = part_data.get_param1
	self.node_list.part_common_cap_value.text.text = part_data.cap_value
	self:FlushNowPartAttrPanel()

	-- 技能展示
	self:FlushSkillPreShowState(part_data.show_item_id)
end

-- 获取当前的幻化状态
function WardrobeView:GetNowFashionPartIsUsed()
	if not self.now_part_data then
		return false
	end

	local is_used = false
	local part_data = self.now_part_data

	if part_data.type == WARDROBE_PART_TYPE.FASHION then
		-- 激活 / 进阶
		local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(part_data.fashion_part_type)
		is_used = part_data.fashion_index == used_index
	elseif part_data.type == WARDROBE_PART_TYPE.MOUNT or part_data.type == WARDROBE_PART_TYPE.HUA_KUN then
		local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(part_data.qichong_select_type, part_data.qichong_select_index)
		local cur_is_kun = part_data.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN
		if cur_is_kun then
			local used_id = NewAppearanceWGData.Instance:GetKunUsedId()
			is_used = part_data.qichong_select_index == used_id
		else
			local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(part_data.qichong_select_type)
			if all_info then
				is_used = act_cfg.appe_image_id == all_info.used_imageid
			end
		end
	end

	return is_used
end

-- 刷新使用
function WardrobeView:FlushFashionUse(is_select, var_data, is_not_need_tips)
	local part_data = var_data or self.now_part_data

	if not part_data then
		return
	end

	if part_data.type == WARDROBE_PART_TYPE.FASHION then
		--如果激活了直接穿戴上去
		if part_data.is_act then
			self:FashionOnClickUse(part_data.fashion_part_type, part_data.fashion_index, is_select, is_not_need_tips)
		end
	elseif part_data.type == WARDROBE_PART_TYPE.MOUNT or part_data.type == WARDROBE_PART_TYPE.HUA_KUN then
		if part_data.is_act then
			self:OnClickQiChongUsed(part_data.qichong_select_type, part_data.qichong_select_index, is_select, is_not_need_tips)
		end
	end
end

-- 使用提示
function WardrobeView:FashionUseTipsPop(is_select, is_not_need_tips)
	if is_not_need_tips then
		return
	end

	if is_select then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.UseNewAppearanceSuccess)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.UnseNewAppearanceSuccess)
	end
end

-- 设置是否为展示套装界面
function WardrobeView:SetIsShowSuitMessage(is_show_message)
	self.is_show_suit_message = is_show_message
end

-- 设置是否为展示套装界面
function WardrobeView:GetIsShowSuitMessage()
	return self.is_show_suit_message
end

-- 设置是否为展示套装界面
function WardrobeView:SetShowSuitModelData(suit_show_model)
	self.user_suit_model_data = suit_show_model
	self.user_suit_model_data.model_rt_type = ModelRTSCaleType.M
	self.user_suit_model_data.is_ui_scene = true
	self.user_suit_model_data.is_not_reset_pos = self.is_show_big
	self.user_suit_model_data.cur_anim = (not self.is_first_enter) and SceneObjAnimator.Rest or SceneObjAnimator.UiIdle
	self.is_first_enter = true
	self.node_list.btn_common_horse_up:CustomSetActive((not self.is_show_mount_res_id) and suit_show_model.mount_res_id ~= nil and suit_show_model.mount_res_id ~= 0)
	self.node_list.btn_common_horse_down:CustomSetActive(self.is_show_mount_res_id and suit_show_model.mount_res_id ~= nil and suit_show_model.mount_res_id ~= 0)
	self:FlushWardrobeModel()
end

-- 刷新左侧列表
function WardrobeView:FlushPrePartItemList(item_list, is_suit)
	if item_list == nil or IsEmptyTable(item_list) then
		return
	end

	local final_item_list = {}
	for i, v in ipairs(item_list) do
		-- 激活 / 进阶
		local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(v.part_type)
		local is_used = v.used_index == used_index

		if not is_suit then
			if not is_used then
				v.show_item_id = 0
				v.state = REWARD_STATE_TYPE.UNDONE
			end
		end

		table.insert(final_item_list, v)
	end

	self.common_part_item_list:SetDataList(final_item_list)
end

-- 刷新类型列表
function WardrobeView:FlushFashionShowTypeList(type_list, jump_index)
	for i, v in ipairs(self.show_type_list) do
		v:SetVisible(type_list[i] ~= nil)

		if type_list[i] ~= nil then
			v:SetData(type_list[i])
		end
	end

	if self.select_show_type_index == nil then
		if jump_index ~= nil then
			self.select_show_type_index = jump_index
		end
	end

	self.select_show_type_index = self.select_show_type_index or 1
	self:FlushFashionPartSelectType(self.select_show_type_index)
	self.is_show_part_attr = false
	self:FlushOperateStatus()
	self:FlushFashionShowType()
end

-- 刷新类型按钮显示
function WardrobeView:FlushFashionShowType()
	if self.select_show_type_index == nil then
		return
	end

	for i, v in ipairs(self.show_type_list) do
		v:OnSelectChange(i == self.select_show_type_index)
	end
end

-- 设置跳转物品id
function WardrobeView:SetFashionJumpItemId(var_jump_item_id)
	self.jump_item_id = var_jump_item_id
end

-- 刷新类型的部件列表数据
function WardrobeView:FlushFashionPartSelectType(show_type_index)
	if self.show_index == TabIndex.wardrobe_fashion then
		self:SelectWardrobeFashionTypeCB(show_type_index, self.select_filter_index, self.jump_item_id)
	elseif self.show_index == TabIndex.wardrobe_jewelry then
		self:SelectWardrobeJewelryTypeCB(show_type_index, self.select_filter_index, self.jump_item_id)
	elseif self.show_index == TabIndex.wardrobe_effect then
		self:SelectWardrobeEffectTypeCB(show_type_index, self.select_filter_index, self.jump_item_id)
	end	
end

-- 刷新类型的部件列表
function WardrobeView:FlushFashionPartList(fashion_part_list, jump_index, is_body_fashion, part_type, is_need_turn_around, change_part_show_big)
	self.wardrobe_fashion_part_list:CancleAllSelectCell()
	self.wardrobe_fashion_part_list:SetDataList(fashion_part_list)
	self.node_list.wardrobe_fashion_part_list:CustomSetActive(not IsEmptyTable(fashion_part_list))

	if IsEmptyTable(fashion_part_list) then
		return
	end

	if jump_index ~= nil then
		if self.select_fashion_part_index == nil then
			self.wardrobe_fashion_part_list:JumpToIndexAndSelect(jump_index, 6)
		else
			local index = self.select_fashion_part_index
			self.select_fashion_part_index = nil
			self.wardrobe_fashion_part_list:JumpToIndexAndSelect(index, 6)
		end
	else
		if self.select_fashion_part_index ~= nil then
			local index = self.select_fashion_part_index
			self.select_fashion_part_index = nil
			self.wardrobe_fashion_part_list:JumpToIndexAndSelect(index, 6)
		else
			self.wardrobe_fashion_part_list:JumpToIndexAndSelect(1, 6)
		end
	end

	self.jump_item_id = nil
	self.correct_turn_around_value = 0
	self.correct_turn_around_tween = false
	-- 切换类型增加特殊转身角度

	if self.is_need_turn_around ~= is_need_turn_around then
		if self.is_need_turn_around ~= nil then
			self.correct_turn_around_tween = true

			if is_need_turn_around then
				self.correct_turn_around_value = TURN_AROUND_ANGLE
			else
				self.correct_turn_around_value = 0
			end
		end

		self.is_need_turn_around = is_need_turn_around
	end

	local model_cache = WardrobeWGData.Instance:GetModelCfgCacheByType(part_type)
	if model_cache then
		self.role_model:SetModelFocus(model_cache.max_pos, model_cache.min_pos, model_cache.max_scale, model_cache.min_scale, 0.4)
	end

	-- 切换类型特殊展示放大缩小
	self.is_show_big = change_part_show_big or false
	self:FlushOperateStatus()
	self.role_model:ModelFocusMove(self.is_show_big)
end

-- 刷新类型的部件列表数据
function WardrobeView:FlushFashionPartSelectData(fashion_part_data)
	local is_select = true	-- 去掉了取消选中操作

	if fashion_part_data ~= nil then
		-- is_select = self.wardrobe_fashion_part_list:GetCellIsSelect(self.select_fashion_part_index)
		local cache_data = self:GetUseIndexByPartType(fashion_part_data.part_type)
		local level_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(fashion_part_data.part_type, fashion_part_data.index)

		if is_select then
			cache_data.used_index = fashion_part_data.index
			cache_data.stuff_id = level_cfg.stuff_id
		else
			cache_data.used_index = 0
			cache_data.stuff_id = 0
		end
	end

	self:AssembleNowFashionCacheData()
	if self.show_index == TabIndex.wardrobe_fashion then
		self:SelectWardrobeFashionCB(self.select_fashion_part_index, fashion_part_data, is_select)
	elseif self.show_index == TabIndex.wardrobe_jewelry then
		self:SelectWardrobeJewelryCB(self.select_fashion_part_index, fashion_part_data, is_select)
	elseif self.show_index == TabIndex.wardrobe_effect then
		self:SelectWardrobeEffectCB(self.select_fashion_part_index, fashion_part_data, is_select)
	end
end

-- 重置 / 幻化
function WardrobeView:FashionOnClickUse(part_type, index, is_to_use, is_not_need_tips)
	self.fashion_part_type = part_type
	self.fashion_part_index = index

	-- 激活 / 进阶
	local used_index = NewAppearanceWGData.Instance:GetFashionUseIndex(self.fashion_part_type)
	local is_used = self.fashion_part_index == used_index
	if is_used and is_to_use then	-- 准备穿戴并且已经穿戴直接返回
		return
	end

	local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(part_type, index)
    if fashion_cfg == nil then
        return
    end

    local res_id = fashion_cfg.resouce
    if self.fashion_part_type == SHIZHUANG_TYPE.WING then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING, res_id, is_to_use, index)
	elseif self.fashion_part_type == SHIZHUANG_TYPE.FABAO then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO, res_id, is_to_use, index)
	elseif self.fashion_part_type == SHIZHUANG_TYPE.SHENBING then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING, res_id, is_to_use, index)
	elseif self.fashion_part_type == SHIZHUANG_TYPE.JIANZHEN then
		self:FashionCheckIsRest(ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN, res_id, is_to_use, index)
	else
        NewAppearanceWGCtrl.Instance:OnUseFashion(part_type, index, 1)
	end
	self:FashionUseTipsPop(is_to_use, is_not_need_tips)
end

-- 进阶系 重置 / 幻化
function WardrobeView:FashionCheckIsRest(ad_type, res_id, is_to_use, index)
    -- 重置使用默认模型
    if not is_to_use then
        local cur_level = NewAppearanceWGData.Instance:GetAdvancedLevel(ad_type)
        if cur_level > 0 then
            local cfg = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(ad_type)
            if cfg then
                res_id = cfg.appe_image_id
				index = cfg.index
            end
        end
    end

	NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, ad_type, res_id, index)
end

-- 使用 / 重置
function WardrobeView:OnClickQiChongUsed(qichong_select_type, qichong_select_index, is_to_use, is_not_need_tips)
	self.qichong_select_type = qichong_select_type
	self.qichong_select_index = qichong_select_index

	local act_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(self.qichong_select_type, self.qichong_select_index)
    local cur_is_kun = self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN
    local is_used = false
	local appe_id = 0

    if cur_is_kun then
        local used_id = NewAppearanceWGData.Instance:GetKunUsedId()
        is_used = self.qichong_select_index == used_id
    else
        local all_info = NewAppearanceWGData.Instance:GetQiChongInfo(self.qichong_select_type)
        if all_info then
            is_used = act_cfg.appe_image_id == all_info.used_imageid
        end
    end

	if is_used and is_to_use then
		return
	end

	if act_cfg then
		appe_id = act_cfg.appe_image_id or act_cfg.active_id
	end

	if is_used then
        local qc_type = self.qichong_select_type
        if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
            qc_type = MOUNT_LINGCHONG_APPE_TYPE.MOUNT
        end

        local grade = NewAppearanceWGData.Instance:GetBaseQiChongGrade(qc_type)
        local base_cfg = NewAppearanceWGData.Instance:GetQiChongBaseCfgByGrade(qc_type, grade)
        if base_cfg then
            appe_id = base_cfg.appe_image_id
        end

		if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
            NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, -1)
        elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
            NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_IMAGE, appe_id)
        end
	else
		if self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
			NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.USE_IMAGE, appe_id, self.qichong_select_index)
		elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
			NewAppearanceWGCtrl.Instance:SendLingChongReq(LINGCHONG_OPERA_TYPE.USE_IMAGE, appe_id)
		elseif self.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
			NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, self.qichong_select_index)
		end	
	end

	self:FashionUseTipsPop(is_to_use, is_not_need_tips)
end

-- 设置右侧信息显影
function WardrobeView:SetCommonRightPartVisable(visable)
	self.node_list.common_right_part_root:CustomSetActive(visable)
end

function WardrobeView:SetCommonRightFashionVisable(visable)
	self.node_list.right_wardrobe_fashion_root:CustomSetActive(visable)
end

function WardrobeView:OpenIncreaseTimeTips(item_id, ok_callback)
    if nil == self.increase_time_tips then
        self.increase_time_tips = Alert.New(nil, nil, nil, nil, true)
        self.increase_time_tips:SetCheckBoxDefaultSelect(false)
    end

    local item_name = ItemWGData.Instance:GetItemName(item_id, nil, true)
    self.increase_time_tips:SetLableString(string.format(Language.NewAppearance.IncreaseTimeStr, item_name))
    self.increase_time_tips:SetOkFunc(ok_callback)
    self.increase_time_tips:Open()
end

-- 获取缓存的使用下标
function WardrobeView:GetNowCacheUseList()
	return self.cache_now_use_id
end

function WardrobeView:SetNowCacheUseList(project_cache_use_id)
	self.cache_now_use_id = project_cache_use_id
end

-- 还原UI场景
function WardrobeView:FlushCheckUISceneShow()
    self:CheckUISceneShow(self.show_index)
    -- 切换UI场景 配置
	Scene.Instance:SetUISceneControllerConfigIndexByType(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.WAIGUAN)
end

function WardrobeView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end
------------------------------------------------------------------------------------------
-- 筛选
function WardrobeView:OnClickFilter()
	self.is_show_filter = true
	self:FlushOperateStatus()
end

-- 放大展示按钮     
function WardrobeView:OnClickAmplify()
	self.is_show_big = true
	self:FlushOperateStatus()
	self.role_model:ModelFocusMove(true)
end 

-- 缩小展示按钮 		
function WardrobeView:OnClickReduce()
	self.is_show_big = false
	self:FlushOperateStatus()
	self.role_model:ModelFocusMove(false)
end

-- 上马    				
function WardrobeView:OnClickHorseUp()
	self.is_show_mount_res_id = true
	self:FlushWardrobeModel()
	self:FlushOperateStatus()
end

-- 下马        		
function WardrobeView:OnClickHorseDown()
	self.is_show_mount_res_id = false
	self:FlushWardrobeModel()
	self:FlushOperateStatus()
end

-- 展示属性        		
function WardrobeView:OnClickShowPower()
	if not self.now_part_data then
		return
	end

	-- 展示属性
	self.is_show_part_attr = not self.is_show_part_attr
	self:FlushNowPartAttrPanel()
	self:FlushOperateStatus()
end

-- -- 展示属性        		
-- function WardrobeView:OnClickCloseShowPower()
-- 	-- 展示属性
-- 	self.is_show_part_attr = false
-- 	self:FlushOperateStatus()
-- end

-- 展示属性        		
function WardrobeView:OnClickFilterRoot()
	-- 展示属性
	self.is_show_filter = false
	self:FlushOperateStatus()
end

-- 操作按钮
function WardrobeView:OnClickPartOperate()
	if not self.now_part_data then
		return
	end

	local part_data = self.now_part_data

	if (not part_data.is_max) then
		local ok_func = function ()
			if part_data.type == WARDROBE_PART_TYPE.FASHION then
				NewAppearanceWGCtrl.Instance:OnFashionUpLevel(part_data.fashion_part_type, part_data.fashion_index, part_data.stuff_id)
			elseif part_data.type == WARDROBE_PART_TYPE.MOUNT or part_data.type == WARDROBE_PART_TYPE.HUA_KUN then
				if part_data.is_act then
					if part_data.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
						NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.SPECIAL_IMAGE_UP_STAR, part_data.qichong_select_index)
					elseif part_data.qichong_select_type == MOUNT_LINGCHONG_APPE_TYPE.KUN then
						NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.UP_STAR, part_data.qichong_select_index)
					end
				else
					local item_cfg = ItemWGData.Instance:GetItemConfig(part_data.stuff_id)
					local bag_index = ItemWGData.Instance:GetItemIndex(part_data.stuff_id)
					BagWGCtrl.Instance:SendUseItem(bag_index, 1, item_cfg.sub_type, item_cfg.need_gold)
				end
			end
		end

		if part_data.is_remind then
			if part_data.shizhuang_type == 2 or part_data.is_limit_time then
				self:OpenIncreaseTimeTips(part_data.stuff_id, ok_func)
			else
				ok_func()
			end
		else
			if part_data.get_msg == 1 then	-- 直接买
				if not RoleWGData.Instance:GetIsEnoughUseGold(part_data.get_param1) then
					VipWGCtrl.Instance:OpenTipNoGold()
					return
				end
				
				if part_data.type == WARDROBE_PART_TYPE.FASHION then
					NewAppearanceWGCtrl.Instance:OnFashionBuyPart(part_data.fashion_part_type, part_data.fashion_index, 1)	
				elseif part_data.type == WARDROBE_PART_TYPE.MOUNT then
					NewAppearanceWGCtrl.Instance:SendMountReq(MOUNT_OPERA_TYPE.MOUNT_OPERA_TYPE_BUY, part_data.qichong_select_index, 1)
				elseif part_data.type == WARDROBE_PART_TYPE.HUA_KUN then
					NewAppearanceWGCtrl.Instance:SendKunOperaReq(KUN_OPERA_TYPE.USE_KUN, part_data.qichong_select_index, 1)
				end
			else	--去到对应功能
				local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way
				if get_way_cfg and get_way_cfg[part_data.get_param1] then
					ViewManager.Instance:OpenByCfg(get_way_cfg[part_data.get_param1].open_panel)
					self:Close()
				end
			end
		end
	end
end

-- 操作穿搭
function WardrobeView:OnClickDessingUp()
	WardrobeWGCtrl.Instance:OpenDressingUpView(self.cache_now_use_id)
end

-- 操作天赏打造
function WardrobeView:OnClickCollect()
	WardrobeWGCtrl.Instance:OpenCastingView(self.now_part_data)
end

-- 操作穿搭
function WardrobeView:OnClickdressing()
	ViewManager.Instance:Open(GuideModuleName.DressingRoleDiyView)
end

-- 操作染色
function WardrobeView:OnClickOpenPartDye()
	if not self.now_part_data then
		return
	end

	local part_data = self.now_part_data
	if part_data.fashion_part_type and part_data.fashion_index then
		NewAppearanceDyeWGCtrl.Instance:OpenAppearanceDyeView(part_data.fashion_part_type, part_data.fashion_index)
	end
end


function WardrobeView:OnClickSkillShow()
	local show_item_id = self.now_part_data and self.now_part_data.show_item_id or 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
	if not item_cfg then
		return
	end

	local display_type = item_cfg.is_display_role
	if nil == display_type or display_type == 0 or display_type == "" then
		return
	end

	if display_type == DisplayItemTip.Display_type.SHENBING or display_type == DisplayItemTip.Display_type.WUQI
        or display_type == DisplayItemTip.Display_type.WING then
        local cfg = NewAppearanceWGData.Instance:GetFashionCfgByItemId(show_item_id)
		if cfg then
			local skill_cfg = NewAppearanceWGData.Instance:GetFashionSkillCfg(cfg.part_type, cfg.index, 1)
			if skill_cfg then
				TipWGCtrl.Instance:OpenCommonSkillPreView(skill_cfg.param1)
			end
		end
	end
end
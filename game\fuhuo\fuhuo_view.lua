FuhuoView = FuhuoView or BaseClass(SafeBaseView)

local FuHuoType = {
	Common = 1,
	Here = 2,
}
local stone_id = COMMON_CONSTS.RESURGENCE_STONE --复活石Id

FuhuoView.FONTSIZE = 20								-- 字体大小
FuhuoView.DISPLAYNUM = 2							-- 功能按钮一页数量
FuhuoView.COUNTDOWNTYPE = "fuhuo_daojishi"			-- 倒计时key
FuhuoView.COUNTDOWNTYPE_GOLD = "fuhuo_gold_daojishi"-- 元宝购买倒计时key
FuhuoView.COUNTDOWNTYPE_FREE = "fuhuo_free_daojishi"-- 免费复活倒计时key
-- FuhuoView.STUFF = 26900								-- 用于查找元宝时用 也用于查找道具复活的图片名字

--策划需求，天帝陵场景或者以下场景（SHOW_FIRST_CHARGE_SCENE_TYPE）死亡时，未购买首冲按钮情况下要显示首冲界面跳转按钮
local TianDiLingSceneId = {
	[9506] = true,
	[9507] = true,
	[9508] = true,
}
local SHOW_FIRST_CHARGE_SCENE_TYPE = {
	[SceneType.Fb_Welkin] = true,
	[SceneType.COPPER_FB] = true,
	[SceneType.PET_FB] = true,
	[SceneType.BaGuaMiZhen_FB] = true,
	[SceneType.HIGH_TEAM_EQUIP_FB] = true,
}

function FuhuoView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(false,true)

	self.rich_tips = nil
	self.fuhuo_layout = nil
	self.is_nolonger = false
	self.is_modal = true
	self.gongneng_sort = {}
	self.killer_name = ""
	self:LoadConfig()
	self.free_btn_x = 300

	self.is_complete_gold_timer = true
	self.is_complete_free_timer = true

	self.death_count_in_day = 0
	self.killer_objid = 0
	self.fuhuo_type = FuHuoType.None
	self.record_cut_time = 0
	self.active_close = false

	self.check_need_send = false
	self.is_show_add_enemy = false
	self.enemy_plat_type = nil
	self.enemy_server_id = nil
	self.enemy_uid = nil
end
function FuhuoView:SetOpenParam(death_count_in_day,killer_objid)
	self.death_count_in_day = death_count_in_day
	self.killer_objid = killer_objid
end
function FuhuoView:SetCommOrFagLayou()
	local fuhuo_count_max = 5
	if self.death_count_in_day < fuhuo_count_max then
		self.node_list.common_layout:SetActive(true)
	else
		self.node_list.common_layout:SetActive(false)
	end
end
function FuhuoView:__delete()
	self.fuhuo_type = nil
	self.fuhuo_common_callback = nil
	self.fuhuo_here_callback = nil
	self.record_cut_time = nil
	self.check_need_send = false
end

function FuhuoView:ReleaseCallBack()
	self.fuhuo_type = FuHuoType.None
	self.btn_fuhuo_common = nil
	self.btn_fuhuo_yu = nil
	self.rich_tips = nil
	self.root_view = nil
	self.btn_cancel = nil
	self.killer_name = nil
	self.is_role = nil
	self.killer_level = nil
	self.is_show_add_enemy = false
	self.type = nil
	self.param = nil 
	XUI.SetButtonEnabled(self.node_list.btn_fuhuo_common, true)

	self.check_need_send = false
	self.enemy_plat_type = nil
	self.enemy_server_id = nil
	self.enemy_uid = nil
end

-- 加载配置
function FuhuoView:LoadConfig()
	local bundle_name = "uis/view/fuhuo_ui_prefab"
	local common_bundle_name =  "uis/view/common_panel_prefab"
	self:AddViewResource(0, bundle_name, "layout_bekilled")
end

function FuhuoView:LoadCallBack()
	self.fuhuo_layout = self.node_list.layout_fuhuo
	self.rich_tips = self.node_list.rich_tips

	self.btn_fuhuo_yu = self.node_list.btn_fuhuo_yu
	self.btn_fuhuo_common = self.node_list.btn_fuhuo_common
	self.btn_stone_stone = self.node_list.btn_stone_stone
	self.btn_fuhuo_free_time = self.node_list.btn_fuhuo_free_time
	self.root_view = self.node_list.root_view

	self.node_list["btn_open_chat"].button:AddClickListener(function()
		ChatWGCtrl.Instance:OpenChatWindow()
	end)

	self.gongneng_sort = FuBenWGData.Instance:GetFuhuoBtn()
	self:CreateGongNengHead()
    self:RegisterEvents()
    local item_config = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.RESURGENCE_STONE)
    if item_config then
        self.node_list["stone_icon"].image:LoadSprite(ResPath.GetItem(item_config.icon_id))
        self.node_list["text_stone_name"].text.text = item_config.name
    end
end

function FuhuoView:RegisterEvents()
	XUI.AddClickEventListener(self.btn_fuhuo_common, BindTool.Bind1(self.OnFuhuoDianHandler, self))
	XUI.AddClickEventListener(self.btn_fuhuo_yu, BindTool.Bind1(self.OnGoldFuHuoHandler, self))
	XUI.AddClickEventListener(self.btn_stone_stone, BindTool.Bind1(self.OnGoldFuHuoHandler, self))
	XUI.AddClickEventListener(self.btn_fuhuo_free_time, BindTool.Bind1(self.OnGoldFuHuoHandler, self))
	XUI.AddClickEventListener(self.node_list.btn_add_enemy, BindTool.Bind1(self.OnClickAddEnemy, self))
end
function FuhuoView:OnCancel()
	self.root_view:SetActive(false)
	self.node_list.layout_commmon_second_root:SetActive(false)
	self.mask_bg:SetActive(false)
end
function FuhuoView:ShowIndexCallBack()
	self:FlushFreeFuHuoCount()
    self:SetReviveBtnPosition()
	self.node_list.tex_btn_free.text.text = Language.Cross.FuoHuoFree
	self:SetTip7Dec()
end

-- 设置复活按钮位置
function FuhuoView:SetReviveBtnPosition()
	if self.is_free_fuhuo then
		self.btn_fuhuo_yu:SetActive(false)
		self.btn_stone_stone:SetActive(false)
		self.btn_fuhuo_common:SetActive(true)
		self.btn_fuhuo_free_time:SetActive(true)
	elseif self:IsOneBtn() then
		self.btn_fuhuo_yu:SetActive(false)
		self.btn_stone_stone:SetActive(false)
		self.btn_fuhuo_common:SetActive(true)
	elseif self:IsOneGoldBtn() then
		self:IsStoneBtn()
		--self.btn_fuhuo_yu:SetActive(true)
		self.btn_fuhuo_common:SetActive(false)
	elseif self:IsNoBtn() then
		self.btn_stone_stone:SetActive(false)
		self.btn_fuhuo_yu:SetActive(false)
		self.btn_fuhuo_common:SetActive(false)
	else
		self:IsStoneBtn()
		--self.btn_fuhuo_yu:SetActive(true)
		self.btn_fuhuo_common:SetActive(true)
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.btn_operate_root.rect)
end

-- 复活按钮上面的字内容
-- 复活价格说明
function FuhuoView:SetTip7Dec()

	local coin_cost = FuhuoWGCtrl.Instance:GetFuhuoGold()
	self.node_list.text_money.text.text = coin_cost
	self.node_list.text_first.text.text = Language.Fuhuo.FuHuoXiaoHaoPrompt
end

--  只显示复活点复活
function FuhuoView:IsOneBtn()
	local flag = false
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.GHOST_FB_GLOBAL then
		flag = true
	end

	return flag
end

--  只显示元宝复活
function FuhuoView:IsOneGoldBtn()
	local flag = false
	local scene_type = Scene.Instance:GetSceneType()
	-- if scene_type == SceneType.Kf_PVP then
	-- 	flag = true
	-- end
	if scene_type == SceneType.HUNDRED_EQUIP then
		flag = true
	end
	
	return flag
end

--  不显示复活按钮
function FuhuoView:IsNoBtn()
	local flag = false
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.PHANTOM_DREAMLAND_FB then
		flag = true
	end
	return flag
end

function FuhuoView:IsStoneBtn() -- 根据是否有复活石显示复活石btn还是仙玉btn
	local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
	self.btn_fuhuo_yu:SetActive(num <= 0)
	self.btn_stone_stone:SetActive(num > 0)
end

function FuhuoView:OpenCallBack()
	self.check_need_send = true
	self.fuhuo_type = FuHuoType.None
end

function FuhuoView:CloseCallBack()
	self.fuhuo_type = FuHuoType.None
	self:SetKillerName("")
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE)
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE_GOLD)
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE_FREE)
	self.is_complete_gold_timer = true
	self.is_complete_free_timer = true
	self:CompleteCountDownTime()

	local flag = self.check_need_send
	self.check_need_send = false

	if flag then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
	end
end

function FuhuoView:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid, type, param)
	self.killer_name = killer_name or ''
    local killer_name_list = Split(killer_name, "_")
    if not IsEmptyTable(killer_name_list) then
        if killer_name_list[2] then
            self.killer_name = string.format(Language.BiZuo.ServerName_2, killer_name_list[2], killer_name_list[1])
        else
            self.killer_name = killer_name_list[1]
        end
    end

	self.is_role = is_role or false
	self.killer_level = killer_level or 0
	self.type = type or nil
	self.param = param or nil 
	local scene_id = Scene.Instance:GetSceneId()
	self.is_show_add_enemy = false
	self.enemy_plat_type = nil
	self.enemy_server_id = nil
	self.enemy_uid = nil
	local scene_type = Scene.Instance:GetSceneType()
	if killer_name ~= nil and killer_name ~= "" and not SocietyWGData.Instance:GetIsLimitAddEnemyScene(scene_type) and is_role and plat_type ~= nil and server_id ~= nil and uid ~= nil then
		local my_server_id = RoleWGData.Instance:GetOriginServerId()
		local my_plat_type = RoleWGData.Instance:GetPlatType()
		-- if my_server_id == server_id and my_plat_type == plat_type then
			self.is_show_add_enemy = true
			self.enemy_plat_type = plat_type
			self.enemy_server_id = server_id
			self.enemy_uid = uid
		-- end
	end

	self:Flush()
end

function FuhuoView:OnFlush(param_t)
	self:FlushFreeFuHuoCount()
	local scene_type = Scene.Instance:GetSceneType()

	if (self.killer_name == "" or self.killer_name == nil) 
	and scene_type ~= scene_type == SceneType.YEZHANWANGCHENGFUBEN
	and scene_type ~= SceneType.COPPER_FB then  --self.killer_objid == 0 or
		self.rich_tips:SetActive(false)
	else
		self.rich_tips:SetActive(true)

		local tip_str1, tip_str2 = "", ""

		-- 被别人渡劫雷劈死的
		if self.type and self.type == FUHUO_TYPE.TYPE_ORDEAL then
			tip_str1 = string.format(Language.Fuhuo.FuHuoTips_5, ToColorStr(self.killer_name, COLOR3B.RED))
			tip_str2 = ""
		else
			tip_str1 = string.format(Language.Fuhuo.FuHuoTips_1, ToColorStr(self.killer_name, COLOR3B.RED))
			tip_str2 = Language.Fuhuo.FuHuoTips_2
		end

		local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.killer_level)
		self.node_list.tips_1.text.text = tip_str1
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_1.rect)
		-- self.node_list.tips_name.text.text = self.killer_name
		-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_name.rect)
		self.node_list.tips_2.text.text = tip_str2 -- string.format(Language.Fuhuo.FuHuoTips_2,(self.is_role and role_level) or self.killer_level)
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.tips_2.rect)
		self.node_list.btn_add_enemy:SetActive(self.is_show_add_enemy)
	end

	for k, v in pairs(param_t) do
		if k == "all" then

		elseif k == "daojishi" then
			self:DaoJiShi(v.time)
			if v.gold_time then
				self:GoldDaoJiShi(v.gold_time)
			end
			if v.free_time then
				self:FreeDaoJiShi(v.free_time)
			end
		elseif k == "stuff" then
			 self:SetTip7Dec()
		end
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(stone_id)
	self.node_list.text_stone.text.text = string.format(Language.Fuhuo.Stone, num <= 999 and num or 999)
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	XUI.SetButtonEnabled(self.node_list.btn_fuhuo_common, fb_scene_cfg.can_free_fuhuo == 0)
end

function FuhuoView:FlushFreeFuHuoCount()
	local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	local free_count = RechargeWGData.Instance:GetFreeFuHuoCount()
	local is_free_fuhuo = is_active and free_count > 0
	if is_free_fuhuo and self.node_list.free_fuhuo_count then
		self.node_list.free_fuhuo_count.text.text = string.format(Language.Fuhuo.FreeCountDesc, free_count)
	end
	self.is_free_fuhuo = is_free_fuhuo
end

--元宝购买限制倒计时
function FuhuoView:GoldDaoJiShi(gold_time)

	self.is_complete_gold_timer = false
	local time = gold_time or 1
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE_GOLD)
	self.node_list.text_gold_coutdown.text.text = string.format(Language.Fuhuo.SecondToOperate, gold_time)
	CountDownManager.Instance:AddCountDown(FuhuoView.COUNTDOWNTYPE_GOLD,
		BindTool.Bind1(self.UpdateGoldCountDownTime, self),
		BindTool.Bind1(self.CompleteGoldCountDownTime, self),
		nil, time, 0.5)

end

function FuhuoView:UpdateGoldCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	self.node_list.text_gold_coutdown.text.text = string.format(Language.Fuhuo.SecondToOperate, last_time)
	self.node_list.fag_count_down_txt.text.text = string.format(Language.Fuhuo.SecondToOperate, last_time)
end

function FuhuoView:CompleteGoldCountDownTime()
	self.is_complete_gold_timer = true
	self.node_list.text_gold_coutdown.text.text = ""
end

--免费复活限制倒计时
function FuhuoView:FreeDaoJiShi(free_time)
	self.is_complete_free_timer = false
	local time = free_time or 1
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE_FREE)
	self.btn_fuhuo_common.text.text = (string.format(Language.Fuhuo.SecondToOperate, free_time))
	CountDownManager.Instance:AddCountDown(FuhuoView.COUNTDOWNTYPE_FREE,
		BindTool.Bind1(self.UpdateFreeCountDownTime, self),
		BindTool.Bind1(self.CompleteFreeCountDownTime, self),
		nil, time, 0.5)
end

function FuhuoView:UpdateFreeCountDownTime(elapse_time, total_time)
	local last_time = math.floor(total_time - elapse_time)
	self.node_list.text_gold_coutdown.text.text = string.format(Language.Fuhuo.SecondToOperate, last_time)
	self.node_list.fag_count_down_txt.text.text = last_time


end

function FuhuoView:CompleteFreeCountDownTime()
	self.is_complete_free_timer = true
	self.btn_fuhuo_common.text.text = ""
	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
	self.check_need_send = false
end

-- 倒计时
function FuhuoView:DaoJiShi(time)
	time = time or COMMON_CONSTS.REALIVE_TIME
	self.node_list.text_fuhuo_times:SetActive(true)
	self.node_list["text_fuhuo_times"].text.text = time
	self.node_list["fuhuo_time_slider"].image.fillAmount = 1

	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE)
	CountDownManager.Instance:AddCountDown(FuhuoView.COUNTDOWNTYPE,
		BindTool.Bind1(self.UpdateCountDownTime, self),
		BindTool.Bind(self.CompleteCountDownTime, self, true),
		nil, time, 0.5)
end

-- 倒计时每次循环执行的函数
function FuhuoView:UpdateCountDownTime(elapse_time, total_time)
	if not self.node_list["text_fuhuo_times"] then
		return
	end

	local last_time = math.floor(total_time - elapse_time)
	if self.node_list.text_fuhuo_times then
		self.node_list.text_fuhuo_times.text.text = last_time 
	else
		print_log(string.format(Language.Common.DebugStr, "['shijian'] is nil"))
	end

	if self.node_list["fuhuo_time_slider"] then
		self.node_list["fuhuo_time_slider"].image.fillAmount = last_time / total_time
	end

	if self.record_cut_time then
		self.record_cut_time = last_time
	end
	
end

function FuhuoView:CompleteCountDownTime(is_auto_fuhuo)
	-- 防止倒计时结束没有自动免费复活
	if self.node_list.btn_fuhuo_common and self.node_list.btn_fuhuo_common:GetActive() then
		XUI.SetButtonEnabled(self.node_list.btn_fuhuo_common, true)
	end
	
	if is_auto_fuhuo then
		self.check_need_send = false
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 1, -1)
	end

	if is_auto_fuhuo then
		-- 容错，倒计时结束，不知道什么原因复活了，可是界面没关闭
		local hp = RoleWGData.Instance:GetAttr("hp")
		if hp ~= nil and hp > 0 then
			self:FuhuoCallback()
		end
	end
end

-- 复活成功后关闭面板和消除倒计时
function FuhuoView:FuhuoCallback()
	self:SetKillerName("")
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE)
	CountDownManager.Instance:RemoveCountDown(FuhuoView.COUNTDOWNTYPE_FREE)
	if self.fuhuo_common_callback and self.fuhuo_type == FuHuoType.Common then
		self.fuhuo_common_callback()
	elseif self.fuhuo_here_callback and self.fuhuo_type == FuHuoType.Here then
		self.fuhuo_here_callback()
	end
	
	self:Close()
end

function FuhuoView:SetFuhuoCallback(common_callback,here_callback)
	self.fuhuo_common_callback = common_callback
	self.fuhuo_here_callback = here_callback
end

--点击铜币复活
function FuhuoView:OnCoinFuHuoHandler()
	--发送消息
	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
	self.check_need_send = false
end
--点击元宝或者复活石复活
function FuhuoView:OnGoldFuHuoHandler()
	local scene_type = Scene.Instance:GetSceneType()
	local num = ItemWGData.Instance:GetItemNumInBagById(stone_id) -- 有复活石使用复活石复活，没有仙玉复活
	if false == self.is_complete_gold_timer and (scene_type ~= SceneType.Kf_PVP) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fuhuo.TimeEndOperate)
		return
	end

	self.fuhuo_type = FuHuoType.Here
	if self.is_free_fuhuo then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.TOZI_FREE)
	elseif num > 0 then
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_STUFF, 0, -1)
	else
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
	end
	self.check_need_send = false
	local old_obj = ReviveWGData.Instance:GetBeHitData()
	local is_need_select = old_obj and old_obj:IsRole()
	if scene_type == SceneType.ZhuXie or scene_type ~= SceneType.KFZhuXieZhanChang then
		is_need_select = false
	end
	
	if is_need_select then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, old_obj, SceneTargetSelectType.SELECT)
	end
end

function FuhuoView:OnFuhuoDianHandler()
	if false == self.is_complete_free_timer then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fuhuo.TimeEndOperate)
		return
	end

	self.fuhuo_type = FuHuoType.Common
	self.check_need_send = false

	FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
end

-- 创建功能头像
function FuhuoView:CreateGongNengHead()
	if nil ~= self.gongneng_sort then
		for i = 1, 3 do
			self.node_list["btn_get_"..i]:SetActive(false)
		end
		local timecount = 0
		for i, v in ipairs(self.gongneng_sort) do
			if nil ~= v.img_name then
				timecount = timecount + 1
				self.node_list["btn_get_"..i]:SetActive(true)
				--self.node_list["getway_img"..i].image:LoadSprite(ResPath.GetF2MainUIImage(v.img_name))
				XUI.AddClickEventListener(self.node_list["btn_get_"..i], BindTool.Bind(self.ClickFunIconHandler, self, v))
			end
		end
	end

	--策划需求，天帝陵场景死亡时，未购买首冲按钮情况下要显示首冲界面跳转按钮
	if nil ~= TianDiLingSceneId[Scene.Instance:GetSceneId()] or nil ~= SHOW_FIRST_CHARGE_SCENE_TYPE[Scene.Instance:GetSceneType()] then
		local first_charge_state = ServerActivityWGData.Instance:GetSCRewardFlagByGradeDay(1, 1)
		self.node_list["btn_get_3"]:SetActive(first_charge_state ~= 0)
		self.node_list["btn_get_4"]:SetActive(first_charge_state == 0)
		XUI.AddClickEventListener(self.node_list["btn_get_4"], BindTool.Bind1(self.OpenFirstRechargeView, self))
	else
		self.node_list["btn_get_3"]:SetActive(true)
		self.node_list["btn_get_4"]:SetActive(false)
	end
end

function FuhuoView:OpenFirstRechargeView()
	FunOpen.Instance:OpenViewByName(GuideModuleName.FirstRechargeView)
end

function FuhuoView:ClickFunIconHandler(param_t)
	ViewManager.Instance:Open(param_t.view_name, param_t.tab_index)
end

function FuhuoView:OnClickAddEnemy()
	if not self.is_show_add_enemy then
		return
	end

	if self.enemy_plat_type == nil or self.enemy_server_id == nil or self.enemy_uid == nil then
		return
	end

	local is_enemy_ed = SocietyWGData.Instance:CheckisRoleEnemy(self.enemy_plat_type, self.enemy_uid)
	if is_enemy_ed then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.AddEnemyError)
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local scene_type = Scene.Instance:GetSceneType()
	if not SocietyWGData.Instance:GetIsLimitAddEnemyScene(scene_type) and self.is_role then
		local my_server_id = RoleWGData.Instance:GetOriginServerId()
		local my_plat_type = RoleWGData.Instance:GetPlatType()
		-- if my_server_id == self.enemy_server_id and my_plat_type == self.enemy_plat_type then
			SocietyWGCtrl.Instance:SendAddEnemy(self.enemy_uid, self.enemy_plat_type)
		-- end
	end
end

function FuhuoView:GetUseFuHuoType()
	return self.fuhuo_type
end
-- Y-运营活动-集卡.xls
local item_table={
[1]={item_id=91630,num=3,is_bind=1},
[2]={item_id=91630,num=5,is_bind=1},
[3]={item_id=18821,num=1,is_bind=1},
[4]={item_id=18822,num=1,is_bind=1},
[5]={item_id=22099,num=100,is_bind=1},
[6]={item_id=22099,num=200,is_bind=1},
[7]={item_id=22099,num=300,is_bind=1},
[8]={item_id=91630,num=2,is_bind=1},
[9]={item_id=18820,num=1,is_bind=1},
[10]={item_id=22099,num=50,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{},
{start_day=8,end_day=15,grade=2,},
{start_day=16,end_day=9999,grade=3,}
},

open_day_meta_table_map={
},
card={
[0]={seq=0,},
[1]={seq=1,item_id=40238,name="梅花2·万界灵狐",des="极品幻兽-<color=#99ffbb>万界灵狐</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[2]={seq=2,item_id=40239,name="梅花3·荷月灵姬",des="极品幻兽-<color=#99ffbb>荷月灵姬</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[3]={seq=3,item_id=40240,name="梅花4·梵音谛听",des="极品幻兽-<color=#99ffbb>梵音谛听</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[4]={seq=4,item_id=40241,name="梅花5·万花之主",des="极品幻兽-<color=#99ffbb>万花之主</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[5]={seq=5,type=2,compose_cost=40,item_id=40243,name="黑桃7·千年小白",des="极品幻兽-<color=#99ffbb>千年小白</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[6]={seq=6,item_id=40244,name="黑桃8·烈日天尊",des="极品幻兽-<color=#99ffbb>烈日天尊</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[7]={seq=7,item_id=40245,name="黑桃9·莲花灵丸",des="极品幻兽-<color=#99ffbb>莲花灵丸</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[8]={seq=8,item_id=40246,name="黑桃10·幽冥统领",des="极品幻兽-<color=#99ffbb>幽冥统领</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[9]={seq=9,type=1,compose_cost=80,item_id=40247,name="红桃J·三太子",des="极品幻兽-<color=#99ffbb>三太子</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[10]={seq=10,item_id=40248,name="红桃Q·通天教主",des="极品幻兽-<color=#99ffbb>通天教主</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[11]={seq=11,item_id=40249,name="红桃K·女娲古神",des="极品幻兽-<color=#99ffbb>女娲古神</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},
[12]={seq=12,type=5,compose_cost=0,item_id=59689,name="万能碎片",des="还未收率的空白卡牌，消耗一定数量可以拓印成任意一张卡牌。",}
},

card_meta_table_map={
[6]=5,	-- depth:1
[7]=5,	-- depth:1
[8]=5,	-- depth:1
[10]=9,	-- depth:1
[11]=9,	-- depth:1
},
mode={
[1]={mode=1,},
[2]={mode=2,times=10,cost_times=10,}
},

mode_meta_table_map={
},
reward_pool={
[1]={grade=1,},
[2]={grade=2,},
[3]={grade=3,}
},

reward_pool_meta_table_map={
},
task={
{grade=1,param1=1,task_description="累计登陆1天",},
{grade=1,task_id=1,},
{grade=1,task_id=2,},
{grade=1,task_id=3,},
{grade=1,task_id=4,},
{grade=1,task_id=5,},
{grade=1,task_id=6,},
{grade=1,task_id=7,},
{grade=1,task_id=8,},
{task_type=20,param1=2,task_description="累计登陆2天",open_panel="welfare#welfare_qiandao",},
{task_id=1,task_type=1,param1=3,task_description="击杀3只伏魔战场BOSS",open_panel="boss#boss_world",},
{task_id=2,task_type=2,param1=1,open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=3,task_description="击杀5只仙遗洞天BOSS",},
{task_id=4,task_type=5,task_description="参与5次天梯争霸",open_panel="act_jjc#arena_tianti",},
{task_id=5,task_type=7,task_description="参与3次护送",open_panel="YunbiaoView",},
{task_id=6,draw_times=3,param1=10,task_description="击杀10只仙遗洞天BOSS",reward_list={[0]=item_table[1]},},
{task_id=7,draw_times=5,task_type=22,param1=6,task_description="充值6元",reward_list={[0]=item_table[2]},open_panel="vip",},
{task_id=8,draw_times=5,task_type=23,param1=10000,task_description="消耗10000灵玉",reward_list={[0]=item_table[2]},open_panel="market#Tab_market20",},
{task_id=9,task_type=14,task_description="完成2次日月修行",open_panel="fubenpanel#fubenpanel_exp",},
{grade=3,param1=3,task_description="累计登陆3天",},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,param1=4,task_description="累计登陆4天",},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=5,task_type=20,task_description="累计登陆5天",open_panel="welfare#welfare_qiandao",},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=6,param1=6,task_description="累计登陆6天",},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=7,param1=7,task_description="累计登陆7天",},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,}
},

task_meta_table_map={
[43]=13,	-- depth:1
[3]=13,	-- depth:1
[23]=43,	-- depth:2
[33]=23,	-- depth:3
[63]=33,	-- depth:4
[53]=63,	-- depth:5
[24]=14,	-- depth:1
[54]=24,	-- depth:2
[52]=12,	-- depth:1
[30]=10,	-- depth:1
[32]=52,	-- depth:2
[34]=54,	-- depth:3
[50]=10,	-- depth:1
[44]=34,	-- depth:4
[22]=32,	-- depth:3
[1]=10,	-- depth:1
[42]=22,	-- depth:4
[19]=10,	-- depth:1
[2]=12,	-- depth:1
[4]=14,	-- depth:1
[64]=44,	-- depth:5
[20]=10,	-- depth:1
[62]=42,	-- depth:5
[60]=10,	-- depth:1
[15]=11,	-- depth:1
[51]=11,	-- depth:1
[61]=51,	-- depth:2
[55]=15,	-- depth:2
[65]=55,	-- depth:3
[66]=16,	-- depth:1
[49]=19,	-- depth:2
[46]=66,	-- depth:2
[45]=65,	-- depth:4
[56]=46,	-- depth:3
[59]=49,	-- depth:3
[35]=45,	-- depth:5
[39]=59,	-- depth:4
[5]=15,	-- depth:2
[6]=16,	-- depth:1
[9]=19,	-- depth:2
[21]=61,	-- depth:3
[41]=21,	-- depth:4
[26]=56,	-- depth:4
[29]=39,	-- depth:5
[25]=35,	-- depth:6
[69]=29,	-- depth:6
[31]=41,	-- depth:5
[36]=26,	-- depth:5
[58]=18,	-- depth:1
[48]=58,	-- depth:2
[68]=48,	-- depth:3
[27]=17,	-- depth:1
[47]=27,	-- depth:2
[8]=18,	-- depth:1
[7]=17,	-- depth:1
[37]=47,	-- depth:3
[38]=68,	-- depth:4
[67]=37,	-- depth:4
[57]=67,	-- depth:5
[28]=38,	-- depth:5
},
reward={
{},
{seq=1,card_type=2,card_num=4,reward_item={[0]=item_table[3]},reward_get_str="黑桃全收集",},
{seq=2,card_type=1,card_num=3,reward_item={[0]=item_table[4]},reward_get_str="红桃全收集",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,}
},

reward_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=5,	-- depth:2
[9]=6,	-- depth:2
},
collect_reward={
{},
{seq=1,card_num=5,reward_item={[0]=item_table[5]},},
{seq=2,card_num=8,reward_item={[0]=item_table[6]},},
{seq=3,card_num=12,reward_item={[0]=item_table[7]},},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,}
},

collect_reward_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[10]=6,	-- depth:2
[11]=7,	-- depth:2
[12]=8,	-- depth:2
},
other_default_table={obtain_times=10,times=10,ship_id=59689,},

open_day_default_table={start_day=1,end_day=7,grade=1,},

card_default_table={seq=0,type=3,compose_cost=20,item_id=40237,name="梅花A·风灵王女",des="极品幻兽-<color=#99ffbb>风灵女王</color>的灵像，被收录于异界的卡牌中。收集全的话可获得大量奖励。",},

mode_default_table={mode=1,times=1,cost_times=1,},

reward_pool_default_table={grade=1,},

task_default_table={grade=2,task_id=0,draw_times=2,task_type=3,param1=5,param2=0,task_description="参与击杀1只谪仙之境BOSS",reward_list={[0]=item_table[8]},open_panel="boss#boss_vip",},

reward_default_table={grade=1,seq=0,card_type=3,card_num=5,reward_item={[0]=item_table[9]},reward_get_str="花牌全收集",},

collect_reward_default_table={grade=1,seq=0,card_num=2,reward_item={[0]=item_table[10]},}

}


LifeIndulgenceWGData = LifeIndulgenceWGData or BaseClass()

function LifeIndulgenceWGData:__init()
    if LifeIndulgenceWGData.Instance then
        error("[LifeIndulgenceWGData] Attempt to create singleton twice!")
    end
    LifeIndulgenceWGData.Instance = self

    self:InitConfig()

    RemindManager.Instance:Register(RemindName.LifeIndulgence, BindTool.Bind(self.IsShowRemind, self))
end

function LifeIndulgenceWGData:__delete()
    LifeIndulgenceWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.LifeIndulgence)
end

function LifeIndulgenceWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("recharge_number_shop_cfg_auto")
    self.shop_cfg = ListToMap(cfg.shop, "seq")
    self.color_cfg = cfg.color_cfg
    self.main_icon_cfg = cfg.icon_name
end

function LifeIndulgenceWGData:SetInfo(protocol)
    self.life_indulgence_shop_buy_num = protocol.life_indulgence_shop_buy
end

--获取礼包购买次数
function LifeIndulgenceWGData:GetShopRewardBuyTimes(seq)
    return self.life_indulgence_shop_buy_num[seq]
end

--获取奖励配置
function LifeIndulgenceWGData:GetShopRewardList(seq)
   return self.shop_cfg[seq].reward_item
end

function LifeIndulgenceWGData:GetAllShopCfg()
    return self.shop_cfg
end

function LifeIndulgenceWGData:IsShowTips()
    local is_show = FunOpen.Instance:GetFunIsOpened(GuideModuleName.LifeIndulgenceView)
    local show_list = self:GetGiftShowList()
    local is_show_tips = is_show and #show_list ~= 0
    return is_show_tips
end

function LifeIndulgenceWGData:GetFirstOpen()
    if self:IsShowTips() then
	    local uuid_str = RoleWGData.Instance:GetUUIDStr()
	    local key = "life_indulgence_uuid_key" .. uuid_str
	    if PlayerPrefsUtil.GetInt(key) > 0 then
	    	return false
	    end

        PlayerPrefsUtil.SetInt(key, 1)
	    return true
    end

    return false
end

function LifeIndulgenceWGData:IsShowRemind()
	if self:CheckDayChangeRed() then
		return 1
	end

	return 0
end

--天数改变提示红点.
function LifeIndulgenceWGData:CheckDayChangeRed(can_set)
    if self:IsShowTips() then
        local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	    local uuid_str = RoleWGData.Instance:GetUUIDStr()
	    local key = string.format("life_indulgence_day_%s_uuid_%s_key", day, uuid_str)
	    if PlayerPrefsUtil.GetInt(key) > 0 then
	    	return false
	    end

        if can_set then
            PlayerPrefsUtil.SetInt(key, 1)
        end
	    return true
    end

    return false
end

function LifeIndulgenceWGData:GetGiftShowList()
    local show_list = {}
    if not self.life_indulgence_shop_buy_num then
        return show_list
    end

    local cfg_list = self.shop_cfg
    local recharge_num = RechargeWGData.Instance:GetRealChongZhiRmb()		-- 玩家累积充值数量
    local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_show = false
    for k, v in pairs(cfg_list) do
        is_show = cur_openserver_day >= v.open_day and cur_openserver_day <= v.close_day and recharge_num >= v.recharge_sum and role_level >= v.open_level
        local buy_times = self:GetShopRewardBuyTimes(v.seq)
        local pre_seq_list = Split(v.pre_seq, "|")
        for k1, v1 in ipairs(pre_seq_list) do
            local pre_seq = tonumber(v1)
            if pre_seq < 0 then
                if is_show and buy_times < v.buy_limit then
                    table.insert(show_list, v)
                    break
                end
            else
                local pre_cfg = self.shop_cfg[pre_seq]
                local pre_buy_times = self:GetShopRewardBuyTimes(pre_seq)
                if is_show and buy_times < v.buy_limit and pre_buy_times >= pre_cfg.buy_limit then
                    table.insert(show_list, v)
                    break
                end
            end
        end
    end
    return show_list
end

--获取配色配置.
function LifeIndulgenceWGData:GetColorCfgByColorType(color_type)
    return self.color_cfg[color_type]
end

--获取主界面图标资源名.
function LifeIndulgenceWGData:GetMainIconRes()
    local icon
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

    for k, v in pairs(self.main_icon_cfg) do
        if server_day >= v.min_day and server_day <= v.max_day then
            icon = v.icon
            break
        end
    end

    return icon
end
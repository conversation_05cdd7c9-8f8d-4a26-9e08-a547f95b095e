LifeIndulgenceView = LifeIndulgenceView or BaseClass(SafeBaseView)

local Display_type = {
	BACKGROUND = 32,						--背景
	MINGQI = 35,                     		--命器
	LNGYU = 42,                             --领域
	SOULRING = 47,  						--魂环
}

function LifeIndulgenceView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
    self:SetMaskBg()

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_background_common_panel")
    self:AddViewResource(0, "uis/view/life_indulgence_prefab", "layout_life_indulgence")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function LifeIndulgenceView:ReleaseCallBack()
    if self.indulgence_buy_list then
        self.indulgence_buy_list:DeleteMe()
        self.indulgence_buy_list = nil
    end

    if self.indulgence_reward_list then
        self.indulgence_reward_list:DeleteMe()
        self.indulgence_reward_list = nil
    end

    if self.reward_model_disply then
		self.reward_model_disply:DeleteMe()
		self.reward_model_disply = nil
	end

	if self.reward_model_list then
		self.reward_model_list:DeleteMe()
		self.reward_model_list = nil
	end

	if self.role_model_disply then
		self.role_model_disply:DeleteMe()
		self.role_model_disply = nil
	end

	if self.lingyu_model_display then
		self.lingyu_model_display:DeleteMe()
		self.lingyu_model_display = nil
	end

	self.background_loader = nil

	self.cur_select_index = 1
end

function LifeIndulgenceView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_zsth_bj")
	XUI.SetNodeImage(self.node_list.RawImage_tongyong, bundle, asset)

    if not self.indulgence_buy_list then
        self.indulgence_buy_list = AsyncListView.New(IndulgenceListRender, self.node_list["reward_btn_list"])
        self.indulgence_buy_list:SetSelectCallBack(BindTool.Bind(self.RightListCallBack, self))
    end

	if not self.reward_model_list then
        self.reward_model_list = AsyncFancyAnimView.New(IndulgenceOperationActRender, self.node_list["reward_model_list"])
    end

    if not self.indulgence_reward_list then
		self.indulgence_reward_list = AsyncBaseGrid.New()
		self.indulgence_reward_list:CreateCells({
			col = 3,
			change_cells_num = 1,
			list_view = self.node_list["reward_center_list"],
			itemRender = ItemCell
		})
		self.indulgence_reward_list:SetStartZeroIndex(false)
    end

    if self.reward_model_disply == nil then
		self.reward_model_disply = OperationActRender.New(self.node_list.reward_model_disply)
		self.reward_model_disply:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.role_model_disply then
		self.role_model_disply = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["role_model_disply"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.XL,
			can_drag = true,
		}
		self.role_model_disply:SetRenderTexUI3DModel(display_data)
	end

	if not self.lingyu_model_display then
		self.lingyu_model_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["lingyu_model_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}
		
		self.lingyu_model_display:SetRenderTexUI3DModel(display_data)
	end

    self.node_list["buy_btn"].button:AddClickListener(BindTool.Bind(self.IndulgenceBuyBtnClick, self))

	self.cur_select_index = 1
end

function LifeIndulgenceView:OpenCallBack()
	LifeIndulgenceWGCtrl.Instance:SetDayChangeRed()
end

function LifeIndulgenceView:CloseCallBack()
	if self.reward_select_data then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.LifeIndulgenceView, 0, BURIED_EVENT_PARAM.closeView, self.reward_select_data.seq)
	end
end
function LifeIndulgenceView:OnFlush()
    local data_list = LifeIndulgenceWGData.Instance:GetGiftShowList()
    self.indulgence_buy_list:SetDataList(data_list)
	if self.cur_select_index > #data_list then
		self.cur_select_index = 1
	end
    self.indulgence_buy_list:JumpToIndex(self.cur_select_index)
    self.node_list.reward_btn_list:SetActive(#data_list > 1)

	self.node_list.buy_btn:SetActive(#data_list > 0)
	self.node_list.not_buy_flag:SetActive(#data_list <= 0)
end

function LifeIndulgenceView:IndulgenceBuyBtnClick()
    local data = self.reward_select_data
    if data == nil then
        return
    end

    RechargeWGCtrl.Instance:Recharge(data.buy_cost, data.rmb_type, data.seq)
end

function LifeIndulgenceView:RightListCallBack(cell)
    if not cell or not cell:GetData() then
        return
    end

    self.reward_select_data = cell:GetData()
    self.cur_select_index = cell:GetIndex()
    self:FlushLeftPanel()
	self:SetColor()

	
	if self.reward_select_data then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.LifeIndulgenceView, 0, BURIED_EVENT_PARAM.openView, self.reward_select_data.seq)
	end
end

function LifeIndulgenceView:SetColor()
	local color_cfg = LifeIndulgenceWGData.Instance:GetColorCfgByColorType(self.reward_select_data.color_type)
	if not color_cfg then
		return
	end

	local bundle, asset = ResPath.GetRawImagesPNG(color_cfg.RawImage_tongyong)
	XUI.SetNodeImage(self.node_list.RawImage_tongyong, bundle, asset)

	bundle, asset = ResPath.GetRawImagesPNG(color_cfg.title_img)
	XUI.SetNodeImage(self.node_list.title_img, bundle, asset)

	bundle, asset = ResPath.GetRawImagesPNG(color_cfg.title_desc_img)
	XUI.SetNodeImage(self.node_list.title_desc_img, bundle, asset)

	bundle, asset = ResPath.GetRawImagesPNG(color_cfg.reward_bg)
	XUI.SetNodeImage(self.node_list.reward_bg, bundle, asset)

	bundle, asset = ResPath.GetLifeIndulgenceImg(color_cfg.model_bg_img)
	XUI.SetNodeImage(self.node_list.model_bg_img, bundle, asset)

	bundle, asset = ResPath.GetLifeIndulgenceImg(color_cfg.free_box_bg)
	XUI.SetNodeImage(self.node_list.free_box_bg, bundle, asset)

	bundle, asset = ResPath.GetLifeIndulgenceImg(color_cfg.free_box_text_bg)
	XUI.SetNodeImage(self.node_list.free_box_text_bg, bundle, asset)
end

--刷新左边物品
function LifeIndulgenceView:FlushLeftPanel()
    local data = self.reward_select_data
    if data == nil then
        return
    end

    local shop_reward_list = LifeIndulgenceWGData.Instance:GetShopRewardList(data.seq)
    self.indulgence_reward_list:SetDataList(SortTableKey(shop_reward_list))
    self.node_list.price_txt.tmp.text = string.format(Language.Common.MoneyTypes[0], data.buy_cost)

    -- 战力
	local capability, show_max_cap, _ = 0, false, nil
	local show_item_id = data.model_show_itemid
	local item_id_list = {}
	if show_item_id then

		if show_item_id ~= 0 and show_item_id ~= "" then
			local split_list = string.split(show_item_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					table.insert(item_id_list,tonumber(v))
				end
			else
				table.insert(item_id_list,show_item_id)
			end
		end

		for i, v in ipairs(item_id_list) do
			local _cap, _show_max_cap = self:CounteCap(v)
			capability = capability + _cap
			show_max_cap = show_max_cap and _show_max_cap
		end
		
	end
	self.node_list.model_name.tmp.text = data.name

	self.node_list["cap_value"].text.text = capability

	self.node_list.common_capability:SetActive(capability > 0)

	self:FlushLeftModelInfo(data)

	self.node_list.bg_effect:SetActive(data.bg_effect == 1)

	local bundle, asset = ResPath.GetEffectUi(data.title_effect)
	self.node_list.title_effect:ChangeAsset(bundle, asset)
end

-- 计算战力
function LifeIndulgenceView:CounteCap(show_item_id)
	local capability, show_max_cap, _ = 0, false, nil

	if ItemWGData.GetIsXiaogGui(show_item_id) then
		_, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
	elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
		capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
	else
		local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
		if item_cfg then
			local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id, item_cfg.sys_attr_cap_location)
			if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
			or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
			or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
				show_max_cap = false
				capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap) or 0
			else
				capability = ItemShowWGData.CalculateCapability(show_item_id) or 0
			end
		end
	end
	return capability, show_max_cap
end

function LifeIndulgenceView:FlushLeftModelInfo(data)
    local show_id = data.model_show_itemid
	local display_data = {}
	display_data.should_ani = true
	if show_id ~= 0 and show_id ~= "" then
		local split_list = string.split(show_id, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
			show_id = split_list[1]
			display_data.should_ani = false
		else
			display_data.item_id = show_id
		end
	end

	local bundle_name = data.model_bundle_name
	local asset_name = data.model_asset_name

	self:FlushLeftModePanel(data.res_type)

	local item_cfg = ItemWGData.Instance:GetItemConfig(show_id)

	if data.res_type == Display_type.BACKGROUND then	--背景.
		local res_id, weapon_res_id, __ = AppearanceWGData.Instance:GetRoleResId()
		self:ShowRoleModel(res_id)
		self.role_model_disply:SetWeaponResid(weapon_res_id)
		local show_id = ItemWGData.Instance:GetComposeProductid(item_cfg.id)
		self:SetBackgroundCellLoader(show_id)
		return
	elseif data.res_type and data.res_type == Display_type.MINGQI then	--双修.
		local res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(show_id).model_id
		bundle_name, asset_name = ResPath.GetShuangXiuTipUI(res_id)
		display_data.item_id = 0
	elseif data.res_type == Display_type.LNGYU then	--领域.
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true}
		self.role_model_disply:SetModelResInfo(role_vo, special_status_table)
		self.role_model_disply:SetRTAdjustmentRootLocalPosition(0, 0, 0)
		self.role_model_disply:SetRTAdjustmentRootLocalScale(0.6)

		local lingyu_type = SupremeFieldsWGData.Instance:GetFootLightTypeByItemId(item_cfg.id)
		self.lingyu_model_display:SetMainAsset(ResPath.GetSkillFaZhenModel(lingyu_type))
		return
	elseif data.res_type == Display_type.SOULRING then	--魂环.
		if data.soul_ring_id and data.soul_ring_id ~= "" then
			local role_vo = GameVoManager.Instance:GetMainRoleVo()
			local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
			self.role_model_disply:SetModelResInfo(role_vo, special_status_table)

			if data.display_pos and data.display_pos ~= "" then
				local pos_list = string.split(data.display_pos, "|")
				local pos_x = tonumber(pos_list[1]) or 0
				local pos_y = tonumber(pos_list[2]) or 0
				local pos_z = tonumber(pos_list[3]) or 0
	
				self.role_model_disply:SetRTAdjustmentRootLocalPosition(pos_x, pos_y, pos_z)
			end
	
			if data.display_rotation and "" ~= data.display_rotation then
				local pos_list = string.split(data.display_rotation, "|")
				local x = tonumber(pos_list[1]) or 0
				local y = tonumber(pos_list[2]) or 0
				local z = tonumber(pos_list[3]) or 0
	
				self.role_model_disply:SetRTAdjustmentRootLocalRotation(x, y, z)
			end
	
			if data.display_scale and "" ~= data.display_scale then
				self.role_model_disply:SetRTAdjustmentRootLocalScale(data.display_scale, data.display_scale, data.display_scale)
			end
	
			local target_data = {}
			if data.soul_ring_id and "" ~= data.soul_ring_id then
				local soul_ring_id_list = string.split(data.soul_ring_id, "|")
				for k, v in pairs(soul_ring_id_list) do
					local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
					target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
				end
	
				self.role_model_disply:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
			end
		end
		return
	end

	display_data.bundle_name = bundle_name
	display_data.asset_name = asset_name
	display_data.render_type = data.model_show_type - 1
	display_data.model_rt_type = ModelRTSCaleType.XL
	display_data.image_effect_bundle = data.image_effect_bundle
	display_data.image_effect_asset = data.image_effect_asset


	if data.display_pos and data.display_pos ~= "" then
		local pos_list = string.split(data.display_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if data.display_rotation and data.display_rotation ~= "" then
		local rot_list = string.split(data.display_rotation, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if data.display_scale and data.display_scale ~= "" then
		display_data.model_adjust_root_local_scale = data.display_scale
	end

	if display_data.render_type == OARenderType.Prefabs  then --or display_data.render_type == OARenderType.Images
		local data_list = {}
		for i, v in pairs(display_data.model_item_id_list) do
			local data = DeepCopy(display_data)
			data.item_id = i
			if display_data.render_type == OARenderType.Prefabs then
				data.render_type = OARenderType.Prefab
				data.model_click_func = function ()
					TipWGCtrl.Instance:OpenItem({item_id =i})
				end
			-- elseif display_data.render_type == OARenderType.Images then
			-- 	data.render_type = OARenderType.Images
			end
			
			table.insert(data_list,data)
		end
		self.reward_model_list:SetDataList(data_list)
		self.node_list.reward_model_disply:SetActive(false)
		self.node_list.reward_model_list:SetActive(true)
	else
		display_data.model_click_func = function ()
			TipWGCtrl.Instance:OpenItem({item_id = show_id})
		end
		self.node_list.reward_model_disply:SetActive(true)
		self.node_list.reward_model_list:SetActive(false)

		self.reward_model_disply:SetData(display_data)
	end

end

--显示当前玩家模型
function LifeIndulgenceView:ShowRoleModel(role_res_id, ani_name)
	ani_name = ani_name or SceneObjAnimator.UiIdle

	if role_res_id then
		local extra_role_model_data = {
			animation_name = ani_name,
		}
		self.role_model_disply:SetRoleResid(role_res_id, nil, extra_role_model_data)
	else
		role_res_id = AppearanceWGData.Instance:GetRoleResId()
		local main_role = Scene.Instance:GetMainRole()
		local vo = main_role and main_role:GetVo()
		local d_body_res, d_hair_res, d_face_res
		if vo and vo.appearance then
			if vo.appearance.fashion_body == 0 then
				d_body_res = vo.appearance.default_body_res_id
				d_hair_res = vo.appearance.default_hair_res_id
				d_face_res = vo.appearance.default_face_res_id
			end
		end

		local extra_role_model_data = {
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
			animation_name = ani_name,
		}
		self.role_model_disply:SetRoleResid(role_res_id, nil, extra_role_model_data)
	end
end

-- 展示天幕
function LifeIndulgenceView:SetBackgroundCellLoader(item_id)
	self.node_list.background_root:SetActive(true)
	local asset, bundle = ResPath.BackgroundShow(item_id)

	if not self.background_loader then
		local background_loader = AllocAsyncLoader(self, "item_tip_back_cell_loader")
		background_loader:SetIsUseObjPool(true)
		background_loader:SetParent(self.node_list["background_root"].transform)
		self.background_loader = background_loader
	end
	self.background_loader:Load(asset, bundle)
end

function LifeIndulgenceView:FlushLeftModePanel(res_type)
	self.node_list.lingyu_model_display:SetActive(res_type == Display_type.LNGYU)
	self.node_list.background_root:SetActive(res_type == Display_type.BACKGROUND)
	self.node_list.role_model_disply:SetActive(res_type == Display_type.BACKGROUND or res_type == Display_type.LNGYU or res_type == Display_type.SOULRING)
	self.node_list.reward_model_disply:SetActive(res_type ~= Display_type.BACKGROUND and res_type ~= Display_type.LNGYU and res_type ~= Display_type.SOULRING)
	self.node_list.reward_model_list:SetActive(res_type ~= Display_type.BACKGROUND and res_type ~= Display_type.LNGYU and res_type ~= Display_type.SOULRING)
end

-----------------列表格子IndulgenceListRender-------------------
IndulgenceListRender = IndulgenceListRender or BaseClass(BaseRender)

function IndulgenceListRender:OnSelectChange(is_select)
    self.node_list["normal_image"]:SetActive(not is_select)
    self.node_list["select_image"]:SetActive(is_select)

	self:SetColor()

end

function IndulgenceListRender:SetColor()
	if not self.data then
		return
	end

	local color_cfg = LifeIndulgenceWGData.Instance:GetColorCfgByColorType(self.data.color_type)
	if not color_cfg then
		return
	end

	local bundle, asset = ResPath.GetLifeIndulgenceImg(color_cfg.normal_text_bg)
	XUI.SetNodeImage(self.node_list.normal_text_bg, bundle, asset)

	bundle, asset = ResPath.GetLifeIndulgenceImg(color_cfg.normal_bg)
	XUI.SetNodeImage(self.node_list.normal_bg, bundle, asset)

	bundle, asset = ResPath.GetLifeIndulgenceImg(color_cfg.normal_icon)
	XUI.SetNodeImage(self.node_list.normal_icon, bundle, asset)

    self.node_list.normal_text.text.text = ToColorStr(self.data.name, color_cfg.normal_text)
    self.node_list.select_text.text.text = self.data.name
end

-----------------------------------------------------------------------------------------------------
IndulgenceOperationActRender = IndulgenceOperationActRender or BaseClass(BaseRender)
function IndulgenceOperationActRender:__init()

end

function IndulgenceOperationActRender:LoadCallBack()
	self.reward_model_disply = OperationActRender.New(self.node_list.reward_model_disply)
	self.reward_model_disply:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
end



function IndulgenceOperationActRender:ReleaseCallBack()
	if self.reward_model_disply then
		self.reward_model_disply:DeleteMe()
		self.reward_model_disply = nil
	end
end

function IndulgenceOperationActRender:OnFlush()
    if not self.data then return end

	if self.data.model_adjust_root_local_position then
		RectTransform.SetAnchoredPositionXY(self.node_list.reward_model_disply.rect, self.data.model_adjust_root_local_position.x, self.data.model_adjust_root_local_position.y)

	end


	self.reward_model_disply:SetData(self.data)
end
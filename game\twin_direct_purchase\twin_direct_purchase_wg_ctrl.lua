require("game/twin_direct_purchase/twin_direct_purchase_view")
require("game/twin_direct_purchase/twin_direct_purchase_wg_data")

TwinDirectPurchaseWGCtrl = TwinDirectPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function TwinDirectPurchaseWGCtrl:__init()
	if TwinDirectPurchaseWGCtrl.Instance then
		error("[TwinDirectPurchaseWGCtrl]:Attempt to create singleton twice!")
	end

    TwinDirectPurchaseWGCtrl.Instance = self

    self.data = TwinDirectPurchaseData.New()
    self.view = TwinDirectPurchaseView.New(GuideModuleName.TwinDirectPurchase)

    self:RegisterAllProtocols()
	self:RegisterAllEvents()
end

function TwinDirectPurchaseWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

	self:UnRegisterAllEvents()

    TwinDirectPurchaseWGCtrl.Instance = nil
end

function TwinDirectPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTianshenAvatarRMBBuyInfo, "OnSCTianshenAvatarRMBBuyInfo")
end

function TwinDirectPurchaseWGCtrl:OnSCTianshenAvatarRMBBuyInfo(protocol)
    self.data:SetAllInfo(protocol)
    self:CheckNeedCloseAct()
end

function TwinDirectPurchaseWGCtrl:RegisterAllEvents()
	self.mainui_open_comlete = self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.CheckNeedCloseAct, self)) --主界面加载完成
end

function TwinDirectPurchaseWGCtrl:UnRegisterAllEvents()
    if self.mainui_open_comlete then
		GlobalEventSystem:UnBind(self.mainui_open_comlete)
		self.mainui_open_comlete = nil
    end
end

function TwinDirectPurchaseWGCtrl:CheckNeedCloseAct()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_Twin_Direct_Purchase)
	if activity_info and activity_info.status == ACTIVITY_STATUS.CLOSE then
		return
	end

	local need_close = self.data:GetCurGradeHasCanBuy()
	if need_close then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_Twin_Direct_Purchase, ACTIVITY_STATUS.CLOSE, 0, 0, 0)
		if self.view:IsOpen() then
			self.view:Close()
		end
		GameAssistantWGCtrl.Instance:FlushView()
	else
		ViewManager.Instance:FlushView(GuideModuleName.TwinDirectPurchase)
	end
end
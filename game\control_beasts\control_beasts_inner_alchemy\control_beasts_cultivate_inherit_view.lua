local INHERIT_COUNT = 2
local BREAK_COUNT = 25

function ControlBeastsCultivateWGView:LoadInheritViewCallBack()
    if nil == self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid = BeastBatchGrid.New(self)
        self.beasts_alchemy_bag_grid:SetStartZeroIndex(false)
        self.beasts_alchemy_bag_grid:SetIsShowTips(false)
        self.beasts_alchemy_bag_grid:SetNoSelectState(false)
        self.beasts_alchemy_bag_grid:SetIsMultiSelect(true)   

        local bundle = "uis/view/control_beasts_alchemy_ui_prefab"
        local asset = "beasts_alchemy_bag_item"
        self.beasts_alchemy_bag_grid:CreateCells({
            col = 3,
            cell_count = BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_BAG_NUM + 5, 
            change_cells_num = 0,
            list_view = self.node_list["beasts_alchemy_bag_grid"],
            assetBundle = bundle, 
            assetName = asset, 
            itemRender = BeastAlchemyBatchSelectRender
        })
        self.beasts_alchemy_bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectBeastAlchemyBagCellCallBack, self))
	end

    if not self.alchemy_inherit_left then
        self.alchemy_inherit_left = AlchemyInheritRender.New(self.node_list.alchemy_inherit_left)
        self.alchemy_inherit_left:SetClickCallBack(BindTool.Bind(self.OnClickInheritLeftItem, self))
    end

    if not self.alchemy_inherit_right then
        self.alchemy_inherit_right = AlchemyInheritRender.New(self.node_list.alchemy_inherit_right)
        self.alchemy_inherit_right:SetClickCallBack(BindTool.Bind(self.OnClickInheritRightItem, self))
    end

    if not self.alchemy_inherit_stuff_list then
        self.alchemy_inherit_stuff_list = {}

        for i = 1, 3 do
            local cell_obj = self.node_list.alchemy_inherit_stuff_list:FindObj(string.format("stuff_cell_0%d", i))
            if cell_obj then
                local cell = AlchemySuccinctStuffSpendRender.New(cell_obj)
                cell:SetIndex(i)
                self.alchemy_inherit_stuff_list[i] = cell
            end
        end
    end

    XUI.AddClickEventListener(self.node_list.alchemy_inherit_btn, BindTool.Bind2(self.OnClickAlchemyInheritBtn, self))
end

function ControlBeastsCultivateWGView:OpenInheritViewCallBack()
end

function ControlBeastsCultivateWGView:CloseInheritViewCallBack()
end

function ControlBeastsCultivateWGView:ShowInheritViewCallBack()
end

function ControlBeastsCultivateWGView:ReleaseInheritViewCallBack()
    if self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:DeleteMe()
        self.beasts_alchemy_bag_grid = nil
    end

    if self.alchemy_inherit_left then
        self.alchemy_inherit_left:DeleteMe()
        self.alchemy_inherit_left = nil
    end

    if self.alchemy_inherit_right then
        self.alchemy_inherit_right:DeleteMe()
        self.alchemy_inherit_right = nil
    end

    if self.alchemy_inherit_alert_window then
		self.alchemy_inherit_alert_window:DeleteMe()
		self.alchemy_inherit_alert_window = nil
	end

    if self.alchemy_inherit_stuff_list then
        for i, v in ipairs(self.alchemy_inherit_stuff_list) do
            v:DeleteMe()
        end

        self.alchemy_inherit_stuff_list = nil
    end

    self.now_show_grid_list = nil
    self.inherit_left_data = nil
    self.inherit_right_data = nil
    self:RemoveRefreshGridTimer()
end
-----------------------------------------------------------------------------
function ControlBeastsCultivateWGView:FlushInheritViewCallBack(param_t)
	-- for k,v in pairs(param_t) do
	-- 	if k == "all" then
    --     elseif k == "main" then
    --     elseif k == "assist" then
    --     end
    -- end

    if self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:CancleAllSelectCell()
        self.inherit_left_data = nil
        self.inherit_right_data = nil
    end

    self:FlushBeastsAlchemyBagGrid()
    self:ShowInheritViewChangeHole()
end

-- 刷新列表
function ControlBeastsCultivateWGView:FlushBeastsAlchemyBagGrid(is_from_break, select_color, select_type)
    if self.node_list.beasts_alchemy_bag_filter_root then
        self.node_list.beasts_alchemy_bag_filter_root:CustomSetActive(is_from_break)
    end
    
    if is_from_break then
        self.beasts_alchemy_bag_grid:SetOtherFullSelectTips(Language.ContralBeastsAlchemy.AlchemyEquipBreakError)
    else
        self.beasts_alchemy_bag_grid:SetOtherFullSelectTips(nil)
    end

    -- 先加入自己选择的
    if (not self.fight_slot_index) or (not self.fight_slot_data) then
        return
    end

    local record_index = 1
    local hole_id = self.fight_slot_index
    self.now_show_grid_list = {}
    if not is_from_break then
        local list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipList(hole_id)
        for k, v in pairs(list) do
            if v.equip_info and v.equip_info.item_id ~= COMMON_CONSTS.NUMBER_ZERO then
                local data = {}
                data.equip_info = v.equip_info
                data.is_wear = true
                data.is_wear_equip = true
                data.is_from_break = is_from_break
                data.hole_id = hole_id
                data.slot_id = k
                data.slot_lv = v.level or 0
                data.grid_index = record_index
                table.insert(self.now_show_grid_list, data)
                record_index = record_index + 1
            end
        end
    end

    local filter_select_color = select_color or COMMON_CONSTS.NUMBER_ZERO
    local filter_select_type = select_type or COMMON_CONSTS.NUMBER_ZERO
    local bag_list = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipBag()
    if bag_list then
        for k, v in pairs(bag_list) do
            if v and v.item_id ~= COMMON_CONSTS.NUMBER_ZERO then
                local equip_cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(v.item_id)
                local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
                local equip_color = item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO
                local equip_type = equip_cfg and equip_cfg.hole or COMMON_CONSTS.NUMBER_ZERO

                local data = {}
                data.equip_info = v
                data.is_wear = false
                data.hole_id = hole_id
                data.is_from_break = is_from_break
                data.grid_index = record_index

                if filter_select_color == COMMON_CONSTS.NUMBER_ZERO and filter_select_type == COMMON_CONSTS.NUMBER_ZERO then
                    table.insert(self.now_show_grid_list, data)
                else
                    if (equip_color == filter_select_color or filter_select_color == COMMON_CONSTS.NUMBER_ZERO) 
                    and (equip_type == (filter_select_type - COMMON_CONSTS.NUMBER_ONE) or filter_select_type == COMMON_CONSTS.NUMBER_ZERO) then
                        table.insert(self.now_show_grid_list, data)
                    end
                end
                
                record_index = record_index + 1
            end
        end
    end

    if #self.now_show_grid_list <= 1 then
        self.beasts_alchemy_bag_grid:CancleAllSelectCell()
    end

    self:RemoveRefreshGridTimer()
    self.show_refresh_grid_timer = GlobalTimerQuest:AddDelayTimer(function ()
        self.beasts_alchemy_bag_grid:SetDataList(self.now_show_grid_list)
	end, 0.05)
end

--移除回调
function ControlBeastsCultivateWGView:RemoveRefreshGridTimer()
    if self.show_refresh_grid_timer then
        GlobalTimerQuest:CancelQuest(self.show_refresh_grid_timer)
        self.show_refresh_grid_timer = nil
    end
end

-- 选择完回调
function ControlBeastsCultivateWGView:SelectBeastAlchemyBagCellCallBack(cell)
    if self.show_index == TabIndex.beasts_alchemy_inherit then		-- 合成时整理一下孵化背包
        self:ShowInheritViewChangeHoleSetShowData(cell)

        if self.inherit_left_data ~= nil and self.inherit_right_data ~= nil then
            local left_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(self.inherit_left_data.equip_info, false)
            local right_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(self.inherit_right_data.equip_info, false)

            if right_score < left_score then
                if not self.alchemy_inherit_alert_window then
                    self.alchemy_inherit_alert_window = Alert.New(nil, nil, nil, nil, true)
                    self.alchemy_inherit_alert_window:SetCheckBoxDefaultSelect(false)
                end
    
                self.alchemy_inherit_alert_window:SetLableString(Language.ContralBeastsAlchemy.AlchemyInheritError2)
                self.alchemy_inherit_alert_window:SetOkFunc(function ()
                    self:ShowInheritViewChangeHole(cell)
                end)
                self.alchemy_inherit_alert_window:SetCancelFunc(function ()
                    self:OnClickInheritRightItem(cell)
                end)
                self.alchemy_inherit_alert_window:SetCloseFunc(function ()
                    self:OnClickInheritRightItem(cell)
                end)
                
                self.alchemy_inherit_alert_window:Open()
                return
            else
                self:ShowInheritViewChangeHole(cell)  
            end
        else
            self:ShowInheritViewChangeHole(cell)
        end
	elseif self.show_index == TabIndex.beasts_alchemy_break then
		self:ShowBreakViewChangeHole()
	end
end

-- 获取数量
function ControlBeastsCultivateWGView:GetNeedNum()
    if self.show_index == TabIndex.beasts_alchemy_inherit then
        return INHERIT_COUNT
	elseif self.show_index == TabIndex.beasts_alchemy_break then
        return BREAK_COUNT
	end
 
    return 0
end

-- 判断是否能否放入
function ControlBeastsCultivateWGView:GridCellSelectLimit(cell)
    if (not cell) or (not cell.data) then
        return false
    end

    if self.show_index ~= TabIndex.beasts_alchemy_inherit then
        return false
	end

    local selected_cells = self.beasts_alchemy_bag_grid:GetAllSelectCell()
    local left_data = selected_cells[COMMON_CONSTS.NUMBER_ONE] or nil
    if left_data == nil then
        return false
    end

    if cell.data.is_wear_equip then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.AlchemyInheritError4)
        return true
    end

    return false
end

-- 设置选中值
function ControlBeastsCultivateWGView:ShowInheritViewChangeHoleSetShowData(cell)
    if (not cell) or (not cell.data) then
        return
    end

    if self.inherit_left_data == nil then
        self.inherit_left_data = cell.data
        return
    elseif cell.data == self.inherit_left_data then
        self.beasts_alchemy_bag_grid:CancleAllSelectCell()
        self.inherit_left_data = nil
        self.inherit_right_data = nil
        self.beasts_alchemy_bag_grid:RefreshSelectCellState()
        return
    end

    if self.inherit_right_data == nil then
        self.inherit_right_data = cell.data
    elseif cell.data == self.inherit_right_data then
        self.inherit_right_data = nil
    end
end

-- 选择数据刷新
function ControlBeastsCultivateWGView:ShowInheritViewChangeHole(cell)
    local left_data = self.inherit_left_data or {}
    local right_data = self.inherit_right_data or {}
    local hole_id = self.fight_slot_index
    self.alchemy_inherit_left:SetData(left_data)
    self.alchemy_inherit_right:SetData(right_data)

    self.final_inherit_refresh_item = {}
    local base_cfg = ControlBeastsCultivateWGData.Instance:GetBaseCfg()
    local inheritance_item = base_cfg and base_cfg.inheritance_item
    if inheritance_item then
        table.insert(self.final_inherit_refresh_item, inheritance_item)
    end

    for i, v in ipairs(self.alchemy_inherit_stuff_list) do
        v:SetVisible(self.final_inherit_refresh_item[i] ~= nil)

        if self.final_inherit_refresh_item[i] ~= nil then
            v:SetData(self.final_inherit_refresh_item[i])
        end
    end
end

-- 点击传承对象
function ControlBeastsCultivateWGView:OnClickInheritLeftItem(cell)
    if (not cell) or (not cell.data) then
        return
    end

    self.beasts_alchemy_bag_grid:CancleAllSelectCell()
    self.inherit_left_data = nil
    self.inherit_right_data = nil
    self.beasts_alchemy_bag_grid:RefreshSelectCellState()
    self:ShowInheritViewChangeHole()
end

-- 点击传承材料
function ControlBeastsCultivateWGView:OnClickInheritRightItem(cell)
    if (not cell) or (not cell.data) or (cell.data.grid_index == nil) then
        return
    end

    self.beasts_alchemy_bag_grid.select_tab[1][cell.data.grid_index] = false --选中之前选择的
    self.inherit_right_data = nil
    self.beasts_alchemy_bag_grid:RefreshSelectCellState()
    self:ShowInheritViewChangeHole()
end

--------------------------------------------------------------------------------
-- 丹药点击传承
function ControlBeastsCultivateWGView:OnClickAlchemyInheritBtn()
    local left_data = self.inherit_left_data
    local right_data = self.inherit_right_data
    
    if left_data == nil or right_data == nil then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.AlchemyInheritError)
        return
    end

    local word_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(right_data.equip_info)
    if word_score <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.AlchemyInheritError5)
        return
    end

    if self.final_inherit_refresh_item then
        for i, v in ipairs(self.final_inherit_refresh_item) do
            if v and v.item_id then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
                if item_num < v.num then
                    TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = v.item_id })
                    return
                end
            end
        end
    end

    ControlBeastsCultivateWGCtrl.Instance:OpenCultivateInheritPreView(self.inherit_left_data, self.inherit_right_data)
end

-------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------------------------------
BeastAlchemyBatchSelectRender = BeastAlchemyBatchSelectRender or BaseClass(BaseRender)
function BeastAlchemyBatchSelectRender:LoadCallBack()
    if not self.alchemy_bag_item then
        self.alchemy_bag_item = ItemCell.New(self.node_list.item_pos)
        self.alchemy_bag_item:SetIsShowTips(false)
        self.alchemy_bag_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
        self.alchemy_bag_item:UseNewSelectEffect(true)
    end
end

function BeastAlchemyBatchSelectRender:OnClick()
    self.alchemy_bag_item:UseNewSelectEffect(true)
    if not self.data then
        return
    end

    local equip_item_id = (self.data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    if equip_item_id == 0 then
        return
    end

    if self.data.is_from_break then
        BaseRender.OnClick(self)
        return
    end

    local item_data = {
        item_id = equip_item_id, 
        equip_info = self.data.equip_info,          -- 穿戴内丹装备数据
        is_bag_equip = false,                       -- 穿戴内丹背包点击
        is_wear = self.data.is_wear,                -- 是否是已穿戴
        fight_slot = self.data.hole_id,             -- 出战位置
        equip_slot = self.data.slot_id,             -- 出战位置孔位
        equip_slot_lv = self.data.slot_lv,          -- 出战位置孔位等级
        is_wear_equip = self.data.is_wear_equip,    -- 是否展示装备字样
    }

    local btn_index = ItemTip.HANDLE_TAKEON
    if self:IsSelect() then
        btn_index = ItemTip.HANDLE_TAKEOFF
    end
    
    local item_tips_btn_click_callback = {}
    local operate_data = {}
    operate_data.btn_text = Language.Tip.ButtonLabel[btn_index]
    operate_data.show_red = false
	operate_data.btn_click = function()
        TipWGCtrl.Instance:CloseNormalItemTip()
        BaseRender.OnClick(self)
    end
   
    table.insert(item_tips_btn_click_callback, operate_data)
    TipWGCtrl.Instance:OpenItem(item_data, ItemTip.BEAST_ALCHEMY_EQUIP_BAG, nil, nil, item_tips_btn_click_callback)
end

function BeastAlchemyBatchSelectRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.alchemy_bag_item:Reset()
		return
    end

    self.alchemy_bag_item:SetIgnoreDataToSelect(self.data.is_plus)

    local equip_item_id = (self.data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    local item_data = {
        item_id = equip_item_id, 
        equip_info = self.data.equip_info,          -- 穿戴内丹装备数据
        is_bag_equip = false,                       -- 穿戴内丹背包点击
        is_wear = self.data.is_wear,                -- 是否是已穿戴
        fight_slot = self.data.hole_id,             -- 出战位置
        equip_slot = self.data.slot_id,             -- 出战位置孔位
        equip_slot_lv = self.data.slot_lv,          -- 出战位置孔位等级
        is_wear_equip = self.data.is_wear_equip,    -- 是否展示装备字样
    }
    self.alchemy_bag_item:SetData(item_data)
    
    if self.data.is_plus then
        self.alchemy_bag_item:Reset()
        local bundle, asset = ResPath.GetCommonImages("a2_ty_jia")
        self.alchemy_bag_item:SetItemIcon(bundle, asset)
        self.alchemy_bag_item:SetButtonComp(true)
        self.alchemy_bag_item:SetEffectRootEnable(false)
        self.alchemy_bag_item:SetSelectEffect(false)
    end

    -- local score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(self.data.equip_info, false, self.data.hole_id, self.index, self.data.level)
    -- if self.old_record_score ~= nil and self.old_record_score ~= score then
    --     self.alchemy_bag_item:SetRightBottomImg(ResPath.GetCommon("a3_zjm_xin"))
    -- else
    --     self.alchemy_bag_item:SetRightBottomImg()
    -- end        
    local str = ""
    if (not self.data.is_from_break) and (not self.data.is_wear_equip) then
        -- 设置箭头
        local role_lv = RoleWGData.Instance:GetRoleLevel()
        local aim_hole_id = self.data.aim_hole_id or COMMON_CONSTS.NUMBER_ZERO  
        local equip_cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(equip_item_id)
        local self_level_limit = equip_cfg and equip_cfg.level_limit or COMMON_CONSTS.NUMBER_ZERO  
        local now_equip_data = ControlBeastsCultivateWGData.Instance:GetFightBeastEquipBySlot(aim_hole_id, equip_cfg.hole)
        local word_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(now_equip_data.equip_info)
        local self_word_score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipWordScore(self.data.equip_info)

        if word_score ~= 0 and self_word_score > word_score then
            str = "a3_hsnd_xl_jb_fj" 
        end
    end

    if str ~= "" then
        self.alchemy_bag_item:SetTopUpFlagIconVisible(true)
        self.alchemy_bag_item:SetTopUpFlagIcon(ResPath.GetCommon(str))
    else
        self.alchemy_bag_item:SetTopUpFlagIconVisible(false)
    end

    -- self.old_record_score = score
end

function BeastAlchemyBatchSelectRender:SetSelect(is_select, item_call_back)
	if is_select and IsEmptyTable(self.data) then
		return 
    end

	self.is_select = is_select
    self.alchemy_bag_item:UseNewSelectEffect(true)
	self.alchemy_bag_item:SetSelectEffect(is_select)	
end


function BeastAlchemyBatchSelectRender:SetIsShowTips(is_show_tips)
    self.alchemy_bag_item:SetIsShowTips(is_show_tips)
end


----------------------------------------AlchemyStuffSpendRender---------------------------------
AlchemyInheritRender = AlchemyInheritRender or BaseClass(BaseRender)
function AlchemyInheritRender:LoadCallBack()
    if not self.inherit_item then
        self.inherit_item = ItemCell.New(self.node_list.inherit_item)
        self.inherit_item:SetIsShowTips(false)
        self.inherit_item:SetClickCallBack(BindTool.Bind(self.OnClickInherItem, self))
    end

    -- 附加词条
    if self.alchemy_additional_list == nil then
        self.alchemy_additional_list = {}
        for i = 1, 4 do
            local attr_obj = self.node_list.alchemy_additional_list:FindObj(string.format("additional_0%d", i))
            if attr_obj then
                local cell = AlchemyAdditionalRender.New(attr_obj)
                cell:SetIndex(i)
                self.alchemy_additional_list[i] = cell
            end
        end
    end
end

function AlchemyInheritRender:ReleaseCallBack()
    if self.succinct_item then
        self.succinct_item:DeleteMe()
        self.succinct_item = nil
    end

    if self.alchemy_additional_list and #self.alchemy_additional_list > 0 then
		for _, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
			alchemy_additional_cell:DeleteMe()
			alchemy_additional_cell = nil
		end

		self.alchemy_additional_list = nil
	end
end

function AlchemyInheritRender:OnFlush()
    if not self.data then
        return 
    end

    local is_empty_data = IsEmptyTable(self.data)
    self.node_list.has_data_root:CustomSetActive(not is_empty_data)
    self.node_list.not_has_data_root:CustomSetActive(is_empty_data)

    if is_empty_data then
        return
    end

    -- 不是材料设置数据
    local is_has_data = false
    local is_has_additional = false
    local equip_info = self.data and self.data.equip_info or {}
    local words_list = equip_info and equip_info.words_list or {}
    local words_start_index = COMMON_CONSTS.NUMBER_ZERO

    for i, alchemy_additional_cell in ipairs(self.alchemy_additional_list) do
        local word_data = words_list[words_start_index]
        local final_words_seq = self.cur_succinct_is_stuff and word_data.can_replace_words or word_data.words_seq
        alchemy_additional_cell:SetVisible(word_data ~= nil and final_words_seq ~= -1)

        if word_data ~= nil and final_words_seq ~= -1 then
            is_has_additional = true
            alchemy_additional_cell:ChangeNowlockStatus(false)
            alchemy_additional_cell:SetData(word_data)
        end

        words_start_index = words_start_index + 1
    end

    self.node_list.alchemy_additional_list:CustomSetActive(is_has_additional)
    self.node_list.additional_empty:CustomSetActive(not is_has_additional)
    self:FlushEquipDataMessage()
end

-- 刷新物品数据
function AlchemyInheritRender:FlushEquipDataMessage()
    if not self.data then
        return 
    end

    local data = self.data
    local equip_item_id = (data.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO
    -- self.inherit_item:SetData({item_id = equip_item_id})  
    if equip_item_id ~= 0 then
        local item_data = {
            item_id = equip_item_id, 
            equip_info = data.equip_info,               -- 穿戴内丹装备数据
            is_bag_equip = false,                       -- 穿戴内丹背包点击
            is_wear = data.is_wear,                     -- 是否是已穿戴
            fight_slot = self.data.hole_id,             -- 出战位置
            equip_slot = self.data.slot_id,             -- 出战位置孔位
            equip_slot_lv = self.data.slot_lv,          -- 出战位置孔位等级
            is_wear_equip = self.data.is_wear_equip,    -- 是否展示装备字样
        }

        -- local item_tips_btn_click_callback = {}
        -- local operate_data = {}
        -- operate_data.btn_text = Language.Tip.ButtonLabel[ItemTip.HANDLE_TAKEOFF]
        -- operate_data.show_red = false
        -- operate_data.btn_click = function()
        --     TipWGCtrl.Instance:CloseNormalItemTip()
        --     BaseRender.OnClick(self)
        -- end
       
        -- table.insert(item_tips_btn_click_callback, operate_data)
        -- self.inherit_item:SetItemTipsBtnClickCallback(item_tips_btn_click_callback)
        self.inherit_item:SetData(item_data)  
        local item_cfg = ItemWGData.Instance:GetItemConfig(equip_item_id)
        self.node_list.inherit_name.text.text = ToColorStr(item_cfg and item_cfg.name or "", ITEM_COLOR[item_cfg and item_cfg.color or COMMON_CONSTS.NUMBER_ZERO])
    end
end

-- 物品点击
function AlchemyInheritRender:OnClickInherItem()
    BaseRender.OnClick(self)
end
require("game/treasurehunt/treasure_thunder/treasure_hunt_thunder_wg_data")
require("game/treasurehunt/treasure_thunder/treasure_hunt_thunder_cangku")
require("game/treasurehunt/treasure_thunder/treasure_hunt_thunder_store")

TreasureHuntThunderWGCtrl = TreasureHuntThunderWGCtrl or BaseClass(BaseWGCtrl)

function TreasureHuntThunderWGCtrl:__init()
	if TreasureHuntThunderWGCtrl.Instance then
		ErrorLog("[TreasureHuntThunderWGCtrl] attempt to create singleton twice!")
		return
	end

	TreasureHuntThunderWGCtrl.Instance = self
    self.data = TreasureHuntThunderWGData.New()
	self.storage_view = TreasureHuntThunderStorageView.New()
	self.store_view = TreasureHuntThunderStoreView.New()
	self:RegisterAllProtocols()

	self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
end

function TreasureHuntThunderWGCtrl:__delete()
	TreasureHuntThunderWGCtrl.Instance = nil

    self.data:DeleteMe()
	self.data = nil

	self.storage_view:DeleteMe()
	self.storage_view = nil

	self.store_view:DeleteMe()
	self.store_view = nil

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end


function TreasureHuntThunderWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local other_cfg = self.data:GetOtherInfo()
    if change_item_id == other_cfg.draw_item_id then
		ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_thunder)
		RemindManager.Instance:Fire(RemindName.TreasureHunt_Thunder)
    end
end

function TreasureHuntThunderWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSThunderDrawOperate)

	self:RegisterProtocol(SCThunderDrawInfo, "OnSCThunderDrawInfo")
	self:RegisterProtocol(SCThunderDrawUpdate, "OnSCThunderDrawUpdate")
	self:RegisterProtocol(SCThunderDrawShopUpdate, "OnSCThunderDrawShopUpdate")
	self:RegisterProtocol(SCThunderDrawRewardResult, "OnSCThunderDrawRewardResult")
	self:RegisterProtocol(SCThunderDrawPersonalRecord, "OnSCThunderDrawPersonalRecord")
	self:RegisterProtocol(SCThunderDrawWorldRecord, "OnSCThunderDrawWorldRecord")
	self:RegisterProtocol(SCThunderDrawStorgeInfo, "OnSCThunderDrawStorgeInfo")
end

function TreasureHuntThunderWGCtrl:SendCSThunderDrawRequest(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSThunderDrawOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawInfo(protocol)
	--print_error("=======全部信息======", protocol)
	self.data:SetAllThunderDrawInfo(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_thunder)

	RemindManager.Instance:Fire(RemindName.TreasureHunt_Thunder)
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawUpdate(protocol)
	--print_error("=======抽奖更新信息======", protocol)
	self.data:SetUpdateThunderDrawInfo(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_thunder)
	RemindManager.Instance:Fire(RemindName.TreasureHunt_Thunder)
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawShopUpdate(protocol)
	--print_error("=======兑换商店更新信息======", protocol)
	self.data:SetUpdateThunderDrawShop(protocol)

	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_thunder)
	if self.store_view:IsOpen() then
		self.store_view:Flush()
	end
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawRewardResult(protocol)
	--print_error("=======抽奖结果======", protocol)
	local draw_num_cfg = self.data:GetAllDrawNumCfg()
    local other_cfg = self.data:GetOtherInfo()
	local mode_cfg = draw_num_cfg[protocol.mode_type]

    if not mode_cfg then
        return
    end

	local str = string.format(Language.TreasureHunt.ThunderBtnAgainStr, mode_cfg.draw_num)
    local ok_func = function ()
		self:ClickUse(protocol.mode_type)
    end

	local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.draw_item_id)
	if IsEmptyTable(item_cfg) then
		return
	end

    local draw_reward_list, big_reward_list = self.data:CalDrawRewardList(protocol)
    local other_info = {}
    other_info.again_text = str
    other_info.stuff_id = other_cfg.draw_item_id
    other_info.times = mode_cfg.consume_num
    other_info.spend = other_cfg.draw_item_price
	other_info.cost_type = other_cfg.money_type == 1 and COST_TYPE.LINGYU or COST_TYPE.YANYUGE_SCORE
	other_info.is_not_auti_move = true
	-- other_info.show_spend = false
    local best_data = {}
    if IsEmptyTable(big_reward_list) then
        best_data = nil
    else
        best_data.item_ids = big_reward_list
    end
    other_info.best_data = best_data

	TipWGCtrl.Instance:ShowGetValueReward(draw_reward_list, ok_func, other_info, true)
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawPersonalRecord(protocol)
	--print_error("=======个人记录======", protocol)
	self.data:SetThunderDrawPersonalRecord(protocol)

	TreasureHuntWGCtrl.Instance:FlushTreasureRecordView()
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawWorldRecord(protocol)
	--print_error("=======全服记录======", protocol)
	self.data:SetThunderDrawWorldRecord(protocol)

	TreasureHuntWGCtrl.Instance:FlushTreasureRecordView()
end

function TreasureHuntThunderWGCtrl:OnSCThunderDrawStorgeInfo(protocol)
	--print_error("=======仓库信息======", protocol)
	self.data:SetThunderDrawStorgeInfo(protocol)

	if self.storage_view:IsOpen() then
		self.storage_view:Flush()
    end

	ViewManager.Instance:FlushView(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_thunder)
	RemindManager.Instance:Fire(RemindName.TreasureHunt_Thunder)
end


--使用道具并弹窗
function TreasureHuntThunderWGCtrl:ClickUse(mode_type)
	local draw_num_cfg = self.data:GetAllDrawNumCfg()
    local other_cfg = self.data:GetOtherInfo()
	local mode_cfg = draw_num_cfg[mode_type]

	--数量检测
	local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.draw_item_id)
	if IsEmptyTable(item_cfg) or not mode_cfg then
		return
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.draw_item_id)
	local cost_item_num = mode_cfg.consume_num

	-- 抽奖
	local tips_data = {}
	tips_data.item_id = other_cfg.draw_item_id
	tips_data.is_can_gold_buy = true
	tips_data.price = other_cfg.draw_item_price
	tips_data.draw_count = mode_cfg.consume_num
	tips_data.has_checkbox = true
	tips_data.is_need_open_recharge_view = true
	tips_data.checkbox_str = "treasure_hunt_thunder"
	tips_data.cost_type = other_cfg.money_type == 1 and COST_TYPE.LINGYU or COST_TYPE.YANYUGE_SCORE
	TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, function()
		self:SendCSThunderDrawRequest(THUNDER_DRAW_OPERATE_TYPE.DRAW, mode_type)
		self.data:CacheOrGetDrawIndex(mode_type)
	end)
end

function TreasureHuntThunderWGCtrl:OpenStroageView()
	if self.storage_view:IsOpen() then
		self.storage_view:Flush()
	else
		self.storage_view:Open()
    end
end

function TreasureHuntThunderWGCtrl:OpenStoreView()
	if self.store_view:IsOpen() then
		self.store_view:Flush()
	else
		self.store_view:Open()
    end
end
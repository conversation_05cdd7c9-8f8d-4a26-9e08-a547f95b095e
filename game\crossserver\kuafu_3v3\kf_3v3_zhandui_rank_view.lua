KF3V3ZhanDuiRankView = KF3V3ZhanDuiRankView or BaseClass(SafeBaseView)

function KF3V3ZhanDuiRankView:__init()
	self:SetMaskBg()
	self.is_safe_area_adapter = true
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ vector2 = Vector2(-6, -15), sizeDelta = Vector2(1078, 608) })
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_zhandui_rank") --战队排名
end

function KF3V3ZhanDuiRankView:LoadCallBack()
	self.is_modal = false
	self.cur_rank_list = AsyncListView.New(KF3V3ZhanDuiRankItemRender, self.node_list.cur_rank_list)
	self.last_rank_list = AsyncListView.New(KF3V3ZhanDuiRankItemRender, self.node_list.last_rank_list)
	self.tab_index = 1
	self.suit_toggle_list = {}

	for i = 1, 2 do
		local node = self.node_list["suit_toogle_" .. i]
		node:FindObj("normal_img/text").text.text = Language.KuafuPVP.RankTabGroup[i]
		node:FindObj("select_img/text").text.text = Language.KuafuPVP.RankTabGroup[i]
		XUI.AddClickEventListener(node, BindTool.Bind(self.SetTabActive, self, i))
		self.suit_toggle_list[i] = node
	end
	self.node_list["suit_toogle_1"].toggle.isOn = true
end

function KF3V3ZhanDuiRankView:ReleaseCallBack()
	if self.cur_rank_list then
		self.cur_rank_list:DeleteMe()
		self.cur_rank_list = nil
	end
	if self.last_rank_list then
		self.last_rank_list:DeleteMe()
		self.last_rank_list = nil
	end
end

function KF3V3ZhanDuiRankView:SetTabActive(index, is_on)
	if not is_on then
		return
	end

	self.node_list.cur_rank_list:SetActive(index == 1)
	self.node_list.last_rank_list:SetActive(index == 2)
	self.tab_index = index

	self:Flush()
end

function KF3V3ZhanDuiRankView:OnFlush()
	local data_list = {}
	local my_rank_info = nil

	if self.tab_index == 1 then
		data_list = KF3V3WGData.Instance:GetRankList(EnumCross3V3RankType.CurSeason)
		self.cur_rank_list:SetDataList(data_list)
		my_rank_info = KF3V3WGData.Instance:GetMyRank(EnumCross3V3RankType.CurSeason)
	elseif self.tab_index == 2 then
		data_list = KF3V3WGData.Instance:GetRankList(EnumCross3V3RankType.LastSeason)
		self.last_rank_list:SetDataList(data_list)
		my_rank_info = KF3V3WGData.Instance:GetMyRank(EnumCross3V3RankType.LastSeason)
	end

	if not my_rank_info then
		return
	end

	self.node_list.layout_blank_tip2:SetActive(#data_list <= 0)

	local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
	self.node_list.has_zhandui:SetActive(is_in_zhandui)
	self.node_list.not_zhandui:SetActive(not is_in_zhandui)

	self.node_list.no_rank:SetActive(my_rank_info.rank == 0)
	if my_rank_info.rank ~= 0 and my_rank_info.rank <= 3 then
		self.node_list.rank:SetActive(false)
		self.node_list.top_three_bg:SetActive(true)
		self.node_list.rank_img:SetActive(true)
		self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. my_rank_info.rank))
		self.node_list.top_three_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_list_" .. my_rank_info.rank))
	else
		self.node_list.top_three_bg:SetActive(false)
		self.node_list.rank_img:SetActive(false)
		self.node_list.rank:SetActive(my_rank_info.rank ~= 0)
	end
	self.node_list.rank.text.text = my_rank_info.rank == 0 and Language.KuafuPVP.NotRank or my_rank_info.rank
	local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()

	-- self.node_list.my_zhandui_name.text.text = my_rank_info.group_name ~= "" and my_rank_info.group_name or
	-- 	Language.KuafuPVP.NotZhanDui

	local my_zhandui_name = zhandui_info and zhandui_info.name
	self.node_list.my_zhandui_name.text.text = my_zhandui_name ~= "" and my_zhandui_name or Language.KuafuPVP.NotZhanDui

	self.node_list.my_duizhang_name.text.text = my_rank_info.captain_name ~= "" and my_rank_info.captain_name or
		Language.KuafuPVP
		.XiaHuaXian           --队长名字
	self.node_list.zhandui_zhanling_img:SetActive(my_zhandui_name ~= "")
	--self.node_list.server_name.text.text = ""
	--内网无法请求PHP，先设置默认名字
	local server_id = my_rank_info.server_id ~= 0 and my_rank_info.server_id or RoleWGData.Instance:GetOriginServerId()
	local temp_name = string.format(Language.Common.ServerIdFormat, server_id)
	self.node_list.server_name.text.text = temp_name
	self.node_list.my_server_name_2.text.text = temp_name
	self.node_list.sorce.text.text = my_rank_info.score
	local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(my_rank_info.score)
	self.node_list.duanwei.text.text = grade_cfg.tier_name
	--self.node_list.my_duanwei_bg.image:LoadSprite(ResPath.GetF2Field1v1("a1_kf3v3_dw_" .. grade_cfg.grade))
	self.node_list.capability.text.text = my_rank_info.capability ~= "" and my_rank_info.capability or
		Language.KuafuPVP.XiaHuaXian

	-- self.node_list.zhandui_zhanling_text.text.text = my_rank_info.zhandui_lingpai_name ~= ""
	-- 	and my_rank_info.zhandui_lingpai_name
	-- 	or zhandui_info.zhandui_lingpai_name

	ZhanDuiWGCtrl.SetZhanDuiDuanWeiImage(self.node_list.zhandui_zhanling_img, grade_cfg)

	-- if my_rank_info.zhandui_lingpai_id then
	-- 	local cfg = KF3V3WGData.Instance:GetZhanLingCfg(my_rank_info.zhandui_lingpai_id)
	-- 	self.node_list.zhandui_zhanling_img.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. cfg.icon))
	-- end
end

KF3V3ZhanDuiRankItemRender = KF3V3ZhanDuiRankItemRender or BaseClass(BaseRender)
function KF3V3ZhanDuiRankItemRender:__init()

end

function KF3V3ZhanDuiRankItemRender:OnFlush()
	self.node_list.bg:SetActive(self.index % 2 == 1)
	self.node_list.hei_bg:SetActive(self.index % 2 ~= 1)

	self.node_list.top_three_bg:SetActive(self.index <= 3)
	self.node_list.rank_img:SetActive(self.index <= 3)
	self.node_list.rank:SetActive(self.index > 3)
	if self.index <= 3 then
		self.node_list.rank_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
		self.node_list.top_three_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_list_" .. self.index))
	end
	self.node_list.rank.text.text = self.index
	self.node_list.zhandui_name.text.text = self.data.group_name
	self.node_list.duizhang_name.text.text = self.data.captain_name ~= "" and self.data.captain_name or
		Language.KuafuPVP.XiaHuaXian
	--内网无法请求PHP，先设置默认名字
	local temp_name = string.format(Language.Common.ServerIdFormat, self.data.server_id)
	self.node_list.server_name.text.text = temp_name

	self.node_list.sorce.text.text = self.data.score
	local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.score)
	self.node_list.duanwei.text.text = grade_cfg.tier_name
	--self.node_list.duanwei_bg.image:LoadSprite(ResPath.GetF2Field1v1("a1_kf3v3_dw_" .. grade_cfg.grade))
	self.node_list.capability.text.text = self.data.capability ~= "" and self.data.capability or
		Language.KuafuPVP.XiaHuaXian

	-- self.node_list.zhandui_zhanling_text.text.text = self.data.zhandui_lingpai_name
	local cfg = KF3V3WGData.Instance:GetZhanLingCfg(self.data.zhandui_lingpai_id)
	--ZhanDuiWGCtrl.ChangeZhanLingTextColor(self.node_list.zhandui_zhanling_text.text, cfg.color_type)
	self.node_list.zhandui_zhanling_img.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. cfg.icon))
end

HundredEquipView = HundredEquipView or BaseClass(SafeBaseView)

local RATIO_RARE = 100
local FUBEN_SHOW_TYPE = {
    [1] = SceneType.VIP_BOSS,
    [2] = SceneType.PERSON_BOSS,
    [3] = SceneType.WorldBoss,
    [4] = SceneType.HIGH_TEAM_EQUIP_FB,
    [5] = GuideModuleName.HundredEquipRatio,
}

local FUBEN_FUNC_TYPE = {
    [1] = {FunName.Boss, "boss#boss_vip", "boss_vip"},
    [2] = {FunName.PersonalBoss, "boss#boss_personal", "boss_personal"},
    [3] = {FunName.BossVip, "boss#boss_world", "boss_world"},
    [4] = {GuideModuleName.FuBenPanel, "fubenpanel#fubenpanel_equip_high", "fubenpanel_equip_high"},
    [5] = {GuideModuleName.HundredEquipRatio, "HundredEquipRatio"},
}

local FUBEN_ROOT_POS =
{
    [0] = Vector3(0, 0, 0),            --默认.
    [1] = Vector3(140, -70, 0),
    [2] = Vector3(-200, -200, 0),
    [3] = Vector3(-700, -100, 0),
    [4] = Vector3(0, 320, 0),
    [5] = Vector3(0, 0, 0),
}

local FUBEN_ROOT_SCALE =
{
    [0] = 1,            --默认.
    [1] = 1.2,
    [2] = 1,
    [3] = 1,
    [4] = 1,
    [5] = 1,
}

function HundredEquipView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "layout_hundredequip")
    self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "hundred_equip_award")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function HundredEquipView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.HundredEquip.ViewName
	local bundle, asset = ResPath.GetRawImagesJPG("a3_bbdz_dbj_sp1")
	XUI.SetNodeImage(self.node_list.RawImage_tongyong, bundle, asset)

    if not self.hundred_equip_fuben_list then
        self.hundred_equip_fuben_list = {}
        for i = 1, #FUBEN_SHOW_TYPE do
            self.hundred_equip_fuben_list[i] = HundredEquipFuBenRender.New(self.node_list["node_fuben_" .. i])
            self.hundred_equip_fuben_list[i]:SetIndex(i)
        end
    end

    self.title_model = OperationActRender.New(self.node_list.hd_title_model)

    self.task_items = {}
    self.is_show_hundred_equip_award = nil
    --播放升级动画状态标记.
    self.playing_effect = false

    XUI.AddClickEventListener(self.node_list.button_title_tip, BindTool.Bind(self.OnClickTitle, self))
    XUI.AddClickEventListener(self.node_list.button_award_tip, BindTool.Bind(self.OnClickAwardTip, self))
    XUI.AddClickEventListener(self.node_list.button_pc_shtq, BindTool.Bind(self.OnClickPrivilegeCollectionSHTQBtn, self))
    XUI.AddClickEventListener(self.node_list.button_equip_award, BindTool.Bind(self.ShowEquipAwardPanel, self, true))
    XUI.AddClickEventListener(self.node_list.button_equip_drop_times_reward, BindTool.Bind(self.ShowDropTimesReward, self))
    XUI.AddClickEventListener(self.node_list.ea_close_btn, BindTool.Bind(self.ShowEquipAwardPanel, self, false))

    self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.HundredEquipView, self.get_guide_ui_event)
    self:LoadAwardCallBack()
    self:ShowEquipAwardPanel(false)
end

function HundredEquipView:OpenCallBack()
    self:ShowEquipAwardPanel(false)
end

function HundredEquipView:ReleaseCallBack()
    self.task_items = nil
    self.task_list_data = nil

    if self.hundred_equip_fuben_list then
		for key, value in pairs(self.hundred_equip_fuben_list) do
			value:DeleteMe()
			value = nil
		end
		self.hundred_equip_fuben_list = nil
	end

	if self.title_model then
		self.title_model:DeleteMe()
		self.title_model = nil
	end

    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.HundredEquipView, self.get_guide_ui_event)
    self.get_guide_ui_event = nil
    self:ReleaseAwardCallBack()
    self:KillFuBenRootTween()
end

function HundredEquipView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
		if "all" == k then
			self:SetMapInfoShow()
            self:OnFlushAward()
            self:FlushTitleModel()
            for k, v in pairs(self.hundred_equip_fuben_list) do
                v:SetData(FUBEN_SHOW_TYPE[k])
            end

            if v.open_param == "showAward" then
                self:ShowEquipAwardPanel(true)
            end
        elseif k == "move_tween" then
            if self.is_show_hundred_equip_award then
                if not self.playing_effect then
                    self:PlayEffect()
                    --self.playing_effect = true
                end
            end
		end
	end
end

function HundredEquipView:OnClickTaskItem(index)
    local data = self.task_list_data[index]
    if not data or data.cfg.state == 1 then
        return
    end

    FunOpen.Instance:OpenViewNameByCfg(data.cfg.open_panel)
end

function HundredEquipView:SetMapInfoShow()
    local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level)
    local add_txt = Language.HundredEquip.MainTxt8
    if buy_level > 0 then
        --守护特权.
        local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()
        add_txt = string.format(Language.HundredEquip.MainTxt5, level_cfg.drop_time_add_show + shtq_add_value)
    end
    self.node_list.text_ratio_value.text.text = add_txt
    self.node_list.button_award_tip:SetActive(buy_level > 0)    --已激活的玩家才显示入口

    local level_up = HundredEquipWGData.Instance:GetTaskLevelUpRed()
    self.node_list.equip_award_red:SetActive(level_up)

    local level = HundredEquipWGData.Instance:GetLevelValue()
    local now_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level + 1)
    self.node_list.button_equip_award:SetActive(nil ~= now_cfg)

    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.pri_col_shtq)
    self.node_list.button_pc_shtq:SetActive(is_open)
end

function HundredEquipView:FlushTitleModel()
    local cfg = HundredEquipWGData.Instance:GetDropTimesTargetCfg(0)
    if not cfg then
        return
    end

	local display_data = {}
	display_data.item_id = cfg.reward[0].item_id

	display_data.render_type = OARenderType.Prefab
	display_data.model_rt_type = ModelRTSCaleType.S

	self.title_model:SetData(display_data)

    self.node_list.text_title_desc.text.text = Language.HundredEquip.MainTxt6
    local red_show = HundredEquipWGData.Instance:TitleTargetRewardRed()
    self.node_list.image_title_red:SetActive(red_show)
    local has_receive = HundredEquipWGData.Instance:GetTargetRewardReceive(0)
    self.node_list.button_title_tip:SetActive(not has_receive)
end

function HundredEquipView:ShowEquipAwardPanel(is_show)
    if self.node_list.ea_tween_root then
        self.is_show_hundred_equip_award = is_show
        self.node_list.ea_tween_root:SetActive(is_show)
        self.node_list.ea_close_btn:SetActive(is_show)
        self.node_list.hd_mask_bg:SetActive(is_show)

        --self.playing_effect = true
        --self:HundredMoveTween(is_show)
    end
end

function HundredEquipView:ShowDropTimesReward()
    HundredEquipWGCtrl.Instance:OpenDropTimesRewardView()
end

--加成变化动画.
function HundredEquipView:PlayNumChangeEvent()
    local index = 0
    local index_list = {}
    local level = HundredEquipWGData.Instance:GetLevelValue()
    local next_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level)
    if next_cfg then
        --index = next_cfg.map_type_index
        index_list = string.split(next_cfg.map_type_index, ",")
    end

    if self.hundred_equip_fuben_list then
        local i = 1
        for k, v in pairs(self.hundred_equip_fuben_list) do
            if index_list[i] and v.index == tonumber(index_list[i]) then
                v:PlayNumChangeEvent(self)
                i = i + 1
            end
        end
    end
end

--通知render开始播放动画.
function HundredEquipView:SendHundredEquipRenderStartPlayEffect()
    local index = 0
    local index_list = {}
    local level = HundredEquipWGData.Instance:GetLevelValue()
    local next_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level)
    if next_cfg then
        index = next_cfg.map_type_index
        index_list = string.split(next_cfg.map_type_index, ",")
    end

    if self.hundred_equip_fuben_list then
        local i = 1
        for k, v in pairs(self.hundred_equip_fuben_list) do
            if index_list[i] and v.index == tonumber(index_list[i]) then
                v:StartPlayEffect()
                i = i + 1
            end
        end
    end
end

function HundredEquipView:OnClickAwardTip()
    HundredEquipWGCtrl.Instance:OpenAwardTip()
end

function HundredEquipView:OnClickPrivilegeCollectionSHTQBtn()
    ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_shtq)
end

function HundredEquipView:OnClickTitle()
    local red_show = HundredEquipWGData.Instance:TitleTargetRewardRed()
    if red_show then
        HundredEquipWGCtrl.Instance:SendHundredEquipRequest(HUNDREDFOLD_DROP_OPERATE_TYPE.HUNDREDFOL_FETCH_TARGET_REWARD)
    else
        local cfg = HundredEquipWGData.Instance:GetDropTimesTargetCfg(0)
        TipWGCtrl.Instance:OpenItem(cfg.reward[0])
    end
end

function HundredEquipView:HundredMoveTween(is_show)
    local index = 0
    if is_show then
        local level = HundredEquipWGData.Instance:GetLevelValue()
        local next_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level + 1)
        if next_cfg then
            index = next_cfg.map_type_index
        end
    end

    self:PlayFuBenRootTween(index, function()
        --标记升级动画播放结束
        self.playing_effect = false
	end)

    self:CurSelectFuben(index)
end

-- 播放背景动画
function HundredEquipView:PlayFuBenRootTween(index, callback)
    self:KillFuBenRootTween()
    self.fuben_root_tweenr = DG.Tweening.DOTween.Sequence()
    local pos = FUBEN_ROOT_POS[index]
    local scale = FUBEN_ROOT_SCALE[index]
    local tweer_scale = self.node_list.fuben_root.transform:DOScale(scale, 0.5)
    local tweer_pos = self.node_list.fuben_root.transform:DOLocalMove(pos, 0.5)
    self.fuben_root_tweenr:Join(tweer_scale)
    self.fuben_root_tweenr:Join(tweer_pos)
    self.fuben_root_tweenr:SetEase(DG.Tweening.Ease.Linear)
    self.fuben_root_tweenr:OnComplete(function ()
        if callback then
            callback()
        end
    end)
end

-- 移除tween
function HundredEquipView:KillFuBenRootTween()
    if self.fuben_root_tweenr then
        self.fuben_root_tweenr:Kill()
        self.fuben_root_tweenr = nil
    end
end

function HundredEquipView:CurSelectFuben(index)
    if self.hundred_equip_fuben_list then
        for k, v in pairs(self.hundred_equip_fuben_list) do
            v:OnSelectChange(v.index == index)
        end
    end
end

function HundredEquipView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
    if ui_name == "node_fuben_1" then
        local fun = function()
            local show_data = FUBEN_FUNC_TYPE[1]
            FunOpen.Instance:OpenViewNameByCfg(show_data[2])
        end

        return self.node_list[ui_name], fun
    else
        return self.node_list[ui_name]
    end
end

----------------------------------------------------------------------------
-- HundredEquipFuBenRender
----------------------------------------------------------------------------
HundredEquipFuBenRender = HundredEquipFuBenRender or BaseClass(BaseRender)

--结束回调动画播放的开始时间.  PlayNumBgScaleTween 的0.2秒 + UITween.DONumberTo 的0.4秒
local RenderTweenStartTime = 0.6

function HundredEquipFuBenRender:ReleaseCallBack()
    self:KillNumBgScaleTween()
    self:CleanTimer()
    self.self_parent = nil
end

function HundredEquipFuBenRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["button_fuben"], BindTool.Bind2(self.OnClickFuBen, self))

    self.now_value = 0

    --播放动画状态标记.
    self.playing_effect = false
end

function HundredEquipFuBenRender:OnFlush()
    if self.data == GuideModuleName.HundredEquipRatio then
        self:SetSPFuBenInfo()
    else
        if not self.playing_effect then
            self:SetFuBenInfo()
        end
    end
end

function HundredEquipFuBenRender:SetFuBenInfo()
    local scene_type = FUBEN_SHOW_TYPE[self.index]
    local level = HundredEquipWGData.Instance:GetLevelValue()
    local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
    local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level) or {}
    local droptimes_data = HundredEquipWGData.Instance:GetDropTimesListCfg(level)

    local now_value = (droptimes_data[scene_type] or 0)
    self.now_value = now_value

    local show_txt = ""
    local show_data = FUBEN_FUNC_TYPE[self.index]
    local is_tab_fun_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName(show_data[3], true)
    if is_tab_fun_open then
        local show_name = Language.HundredEquip.MainBtnTxt[self.index] or ""
        local add_value = ((level_cfg.drop_time_add_show or 0) / 100) * now_value	-- 额外倍数
        --守护特权.
        local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()
        show_txt = string.format(Language.HundredEquip.FuBenAddText, show_name, now_value, add_value + shtq_add_value)
    else
        show_txt = tip
    end

    self.node_list["text_ratio"].text.text = show_txt
    self.node_list["text_fuben_name"].text.text = Language.HundredEquip.MainBtnTxt[self.index]

    self.node_list["effect_bg"]:SetActive(is_tab_fun_open)
end

function HundredEquipFuBenRender:SetSPFuBenInfo()
    local cfg = HundredEquipWGData.Instance:GetRechargeTaskCfgBySeq(0)
    if not cfg then
        return
    end

    local is_open = FunOpen.Instance:GetFunIsOpened(FunName.HundredEquipRatio)
    self.node_list["num_bg"]:SetActive(is_open)
    self.node_list["text_bg"]:SetActive(is_open)
    self.node_list["button_fuben"]:SetActive(is_open)

    local need_recharge_num = cfg.recharge_num
    local real_recharge = HundredEquipWGData.Instance:GetRealRechargeNum()
    local show_txt = string.format(Language.HundredEquip.TodayRechargeText, real_recharge, need_recharge_num)
    self.node_list["text_ratio"].text.text = show_txt
    self.node_list["image_ratio_arrow"]:SetActive(false)
    self.node_list["text_fuben_name"].text.text = Language.HundredEquip.MainBtnTxt[self.index]

    local red_recharge = HundredEquipWGData.Instance:RealRechargeRed()
    self.node_list.btn_red:SetActive(red_recharge)
end

function HundredEquipFuBenRender:OnClickFuBen()
    local show_data = FUBEN_FUNC_TYPE[self.index]
    FunOpen.Instance:OpenViewNameByCfg(show_data[2])
end

function HundredEquipFuBenRender:OnSelectChange(is_select)
    self.node_list["effect"]:SetActive(is_select)
end

function HundredEquipFuBenRender:StartPlayEffect()
    self.playing_effect = true
end

function HundredEquipFuBenRender:CleanTimer()
    if self.timer then
        GlobalTimerQuest:CancelQuest(self.timer)
        self.timer = nil
    end
end

--好想鲨了策划zbp啊.
--加成变化动画.
function HundredEquipFuBenRender:PlayNumChangeEvent(parent)
    local show_data = FUBEN_FUNC_TYPE[self.index]
    local is_tab_fun_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName(show_data[3], true)
    --功能未开启，不播放加成变化动画.
    if not is_tab_fun_open then
        --动画结束后，开始播放移动动画.
        --parent:HundredMoveTween(true)
        self.playing_effect = false
        return
    end

    self.self_parent = parent

    self:CleanTimer()
    local complete_fun = function()
        --文本加成动画播放结束后，开始播放缩放动画.
        self:PlayNumBgScaleTween(false, 1, 0.3, BindTool.Bind(self.SetTweenCompleteFun, self, false))
    end
    --由于 UITween.DONumberTo 的结束回调有概率不触发，遂改为计时器执行结束动画.
    self.timer = GlobalTimerQuest:AddDelayTimer(complete_fun, RenderTweenStartTime)

    --父节点动画播放结束后，开始播放缩放动画.
    self:PlayNumBgScaleTween(true, 2, 0.2, BindTool.Bind(self.SetTweenCompleteFun, self, true))
end

-- 移除tween
function HundredEquipFuBenRender:KillNumBgScaleTween()
    if self.bg_num_tweenr then
        self.bg_num_tweenr:Kill()
        self.bg_num_tweenr = nil
    end
end

-- 播放背景动画
function HundredEquipFuBenRender:PlayNumBgScaleTween(is_forward, max_value, time, callback)
    self:KillNumBgScaleTween()
    local aim_value = is_forward and max_value or 1
    self.bg_num_tweenr = self.node_list.num_bg.transform:DOScale(aim_value, time)
    self.bg_num_tweenr:SetEase(DG.Tweening.Ease.Linear)
    self.bg_num_tweenr:OnComplete(function ()
        if callback then
            callback()
        end
    end)
end

-- 设置动画播放完成回调
function HundredEquipFuBenRender:SetTweenCompleteFun(is_forward)
    if is_forward then
        local level = HundredEquipWGData.Instance:GetLevelValue()
        local droptimes_data = HundredEquipWGData.Instance:GetDropTimesListCfg(level)
        local scene_type = FUBEN_SHOW_TYPE[self.index]
        local now_value = (droptimes_data[scene_type] or 0)
        if now_value ~= self.now_value then
            local show_name = Language.HundredEquip.MainBtnTxt[self.index] or ""
            local buy_level = HundredEquipWGData.Instance:GetRmbBuyLevel()
            local level_cfg = HundredEquipWGData.Instance:GetRmbBuyCfg(buy_level) or {}
    
            local update_fun = function(num)
                local show_txt = ""
                local add_value = ((level_cfg.drop_time_add_show or 0) / 100) * num	-- 额外倍数
                --守护特权.
                local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()
    
                local value, postfix_name = CommonDataManager.ConverMoneyBarNew(num)
                if postfix_name == "" then
                    show_txt = (string.format("%.0f", value)) .. postfix_name
                else
                    show_txt = (value) .. postfix_name
                end

                self.node_list["text_ratio"].text.text = string.format(Language.HundredEquip.FuBenAddText, show_name, show_txt, add_value + shtq_add_value)
            end

            local complete_fun = function()
                self.now_value = now_value
                -- --播放结束后，开始播放缩放动画.
                -- self:PlayNumBgScaleTween(false, 1, 0.3, BindTool.Bind(self.SetTweenCompleteFun, self, false))
            end

            --播放结束后，开始播放文本加成动画.
            UITween.DONumberTo(self.node_list["text_ratio"].text, self.now_value, now_value, 0.4, update_fun, complete_fun)
        end
    else
        --动画结束后，开始播放移动动画.
        -- if self.self_parent then
        --     self.self_parent:HundredMoveTween(true)
        -- end
       
        self.playing_effect = false
    end
end
PrivilegeCollectionSHTQExpTips = PrivilegeCollectionSHTQExpTips or BaseClass(SafeBaseView)

local TweenAllTime = 4.5
local TweenAllUpLvTime = 5.5
local TweenPlayingTime = 1.2
local TweenExpEffectTime = 2
local TweenExpTime = 3
local TweenExpSliderTime = 1

function PrivilegeCollectionSHTQExpTips:__init()
    self.view_layer = UiLayer.PopTop
	self.view_style = ViewStyle.Window
    self.view_name = "PrivilegeCollectionSHTQExpTips"
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)

    self:AddViewResource(0, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_shtq_exp_tips")
end

function PrivilegeCollectionSHTQExpTips:LoadCallBack()
	self.skeleton_graphic = self.node_list.SkeletonGraphic.gameObject:GetComponent("SkeletonGraphic")
end

function PrivilegeCollectionSHTQExpTips:ReleaseCallBack()
    self:CleanTimer()
	self.skeleton_graphic = nil
end

function PrivilegeCollectionSHTQExpTips:OpenIndexCallBack(index)
	self:CleanTimer()
	local complete_fun = function()
		self:Close()
		ViewManager.Instance:Open(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_shtq)
    end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	self.timer = GlobalTimerQuest:AddDelayTimer(complete_fun, self.data.last_role_level < vo.level and TweenAllUpLvTime or TweenAllTime)
	self.node_list.root_ani.animation_player:Play("Play")

	self.skeleton_graphic.AnimationState:SetAnimation(0, "idle", true)
	self.skeleton_graphic.AnimationState:AddAnimation(0, "open", true, TweenPlayingTime)
	self.skeleton_graphic.AnimationState:AddAnimation(0, "idle2", true, 2)

	local max_exp = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level).exp
	local save_exp = self.data.save_exp
	self:SetFinalExpChange(self.data.last_role_level, vo.exp - save_exp, max_exp)
	self.node_list.exp_effect:SetActive(false)
	ReDelayCall(self, function ()
		self.node_list.exp_effect:SetActive(true)
	end, TweenExpEffectTime, "exp_effect")

	ReDelayCall(self, function ()
		self:OnExpInitialized()
	end, TweenExpTime, "exp")
end

function PrivilegeCollectionSHTQExpTips:OnFlush()
	
end

function PrivilegeCollectionSHTQExpTips:SetData(data)
	self.data = data

	if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function PrivilegeCollectionSHTQExpTips:CleanTimer()
    if self.timer then
        GlobalTimerQuest:CancelQuest(self.timer)
        self.timer = nil
    end
end

--假经验条动画.
function PrivilegeCollectionSHTQExpTips:OnExpInitialized()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local max_exp = RoleWGData.Instance.GetRoleExpCfgByLv(vo.level).exp
	local now_exp = vo.exp
	local now_lv = vo.level
	local slider = self.node_list.ExpInfo.slider

	if self.old_level < now_lv then
		slider:DOValue(1, TweenExpSliderTime):OnComplete(function()
			self:SetFinalExpChange(now_lv, 0, max_exp)
			self:OnExpInitialized()	-- 重新检测
		end)
	else
		slider:DOValue(now_exp / max_exp, TweenExpSliderTime):OnComplete(function()
			self:SetFinalExpChange(now_lv, now_exp, max_exp)
		end)
	end
end

-- 设置最终值
function PrivilegeCollectionSHTQExpTips:SetFinalExpChange(now_lv, now_exp, max_exp)
	self:SetExpProgressVal(now_exp / max_exp)
	self.old_level = now_lv
end

function PrivilegeCollectionSHTQExpTips:SetExpProgressVal(val)
	self.node_list.ExpInfo.slider.value = val
end
-- 额外消耗类型
COST_TYPE = {
	LINGYU = 1,
	YANYUGE_SCORE = 2,
}

TipsDrawRewardView = TipsDrawRewardView or BaseClass(SafeBaseView)

function TipsDrawRewardView:__init(view_name)
	self.view_name = "TipsDrawRewardView"
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_draw_tips_panel")
end

function TipsDrawRewardView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.ListenCancelCallBack, self))
	XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind1(self.ListenOkCallBack, self))
	XUI.AddClickEventListener(self.node_list.layout_nolonger_tips, BindTool.Bind1(self.NoLongerClick, self))
end

function TipsDrawRewardView:ReleaseCallBack()
	if self.ok_cb then
		self.ok_cb = nil
	end

	if self.cancel_cb then
		self.cancel_cb = nil
	end

	self.info = nil
	self.check_status = nil
end

-- 设置属性值
function TipsDrawRewardView:SetData(info, Ok_CB, Cancel_CB)
	self.info = info
	self.ok_cb = Ok_CB
	self.cancel_cb = Cancel_CB

	self:CheckOpenTips()
end

-- 检测提示框
function TipsDrawRewardView:CheckOpenTips()
	if self.info == nil then
		return
	end

	-- 物品个数
	local need_num = self.info.draw_count or 0
	-- 已拥有物品个数
	local has_num = self.info.has_num and self.info.has_num or ItemWGData.Instance:GetItemNumInBagById(self.info.item_id) --拥有的材料数量
	local is_enough = has_num >= self.info.draw_count
	local item_price = self.info.price or 0
	local total_need_gold = item_price * (need_num - has_num)

	if is_enough then
		if self.ok_cb then
			self.ok_cb()
		end
		return
	end

	if self.info.has_checkbox then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		local str_day = string.format("%s/%s", self.info.checkbox_str, main_role_id)
		local str_check_box = string.format("%s/%s_checkbox", self.info.checkbox_str, main_role_id)
		local save_day = PlayerPrefsUtil.GetInt(str_day, 0)
		local check_status = PlayerPrefsUtil.GetInt(str_check_box, 0)
		self.check_status = check_status
		-- 当前天数等于保存天数，且勾选今日不再提示
		if open_day == save_day and check_status == 1 then
			self:ExecuteOkFunc()
			return
		else
			PlayerPrefsUtil.SetInt(str_check_box, 0)
			self.check_status = 0
		end
	end

	self:Open()
end

function TipsDrawRewardView:ExecuteOkFunc()
	-- 物品个数
	local need_num = self.info.draw_count or 0
	-- 已拥有物品个数
	local has_num = self.info.has_num and self.info.has_num or ItemWGData.Instance:GetItemNumInBagById(self.info.item_id) --拥有的材料数量
	local is_enough = has_num >= self.info.draw_count
	local item_price = self.info.price or 0
	local total_need_gold = item_price * (need_num - has_num)

	if is_enough then
		if self.ok_cb then
			self.ok_cb()
		end

		return true
	else
		local cost_type = self.info.cost_type or COST_TYPE.LINGYU

		-- 灵玉
		if cost_type == COST_TYPE.LINGYU then
			if self.info.is_can_gold_buy then
				-- 仙玉不足
				local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
				if role_gold >= total_need_gold then
					if self.ok_cb then
						self.ok_cb()
					end
	
					return true
				else
					if self.info.is_need_open_recharge_view then
						VipWGCtrl.Instance:OpenTipNoGold()
						return true
					else
						SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
						return true
					end
				end
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.Prop_No_Enough)
				return true
			end
		elseif cost_type == COST_TYPE.YANYUGE_SCORE then
			-- 藏金积分
			local score = YanYuGeWGData.Instance:GetCurScore()

			if score >= total_need_gold then
				if self.ok_cb then
					self.ok_cb()
				end

				return true
			else
				if self.info.is_need_open_recharge_view then
					VipWGCtrl.Instance:OpenTipNoScore()
				else
					SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.NoEnoughScore)
				end
				return true
			end
		end
	end

	return false
end

function TipsDrawRewardView:ExecuteCancelFunc()
	if self.cancel_cb then
		self.cancel_cb()
	end
end

--[[
local tips_data = {
	item_id,				-- 抽奖物品
	price,					-- 抽奖物品单价
	draw_count,				-- 抽奖次数
	has_checkbox,			-- 是否有提示今日弹出勾选框
	checkbox_str,			-- 今日弹出勾选复选框唯一识别字段
	cost_type,				-- 货币不足时，消耗哪种货币抽奖（默认消耗灵玉 COST_TYPE.LINGYU）
	is_can_gold_buy,		--是否可以用仙玉抽奖.默认true
	is_need_open_recharge_view,		--是否需要在货币不足的时候打开充值提示.默认true

	gold_str,               -- 消耗名称
}
--]]
function TipsDrawRewardView:OnFlush()
	if self.info == nil then
		return
	end

	-- 物品个数
	local need_num = self.info.draw_count or 0
	local need_num_str = ToColorStr(need_num, COLOR3B.GREEN)
	-- 已拥有物品个数
	local has_num = self.info.has_num and self.info.has_num or ItemWGData.Instance:GetItemNumInBagById(self.info.item_id) --拥有的材料数量
	local is_enough = has_num >= self.info.draw_count
	local has_num_color = is_enough and COLOR3B.GREEN or COLOR3B.RED
	local has_num_str = ToColorStr(has_num, has_num_color)
	-- 物品配置
	local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.info.item_id)
	local item_name = item_cfg and item_cfg.name or ""
	local item_color = item_cfg and item_cfg.color or ""

	local name_str = ToColorStr(item_name, ITEM_COLOR[item_color])
	local str_1 = string.format(Language.Common.DrawTips_1, need_num_str, name_str)
	local str_2 = string.format(Language.Common.DrawTips_2, name_str, has_num_str)

	self.node_list.stuff_obj_tips.text.text = string.format("%s\n%s", str_1, str_2)
	self.node_list.spend_root:CustomSetActive((self.info.is_can_gold_buy) and not is_enough)
	
	if not is_enough then
		local cost_type = self.info.cost_type or COST_TYPE.LINGYU
		local gold_str
		local item_price = self.info.price or 0
		local total_need_gold = item_price * (need_num - has_num)
		local spend_str = ToColorStr(total_need_gold, COLOR3B.GREEN)

		if cost_type == COST_TYPE.LINGYU then
			gold_str = ToColorStr(Language.Common.Gold, COLOR3B.GREEN)
		elseif cost_type == COST_TYPE.YANYUGE_SCORE then
			gold_str = ToColorStr(Language.Common.CangJingScore, COLOR3B.GREEN)
		end

		if self.info.gold_str and "" ~= self.info.gold_str then
			gold_str = self.info.gold_str
		end

		self.node_list.spend_tips.text.text = string.format(Language.Common.DrawTips_3, name_str, gold_str, spend_str, gold_str)
	end

	self.node_list.layout_nolonger_tips:CustomSetActive(self.info.has_checkbox)

	if self.info.has_checkbox then
		self:FlushNoLonger()
	end
end

-- 刷新复选框
function TipsDrawRewardView:FlushNoLonger()
	self.check_status = self.check_status or 0
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local str_check_box = string.format("%s/%s_checkbox", self.info.checkbox_str, main_role_id)
	PlayerPrefsUtil.SetInt(str_check_box, self.check_status)
	self.node_list.img_nohint_hook:CustomSetActive(self.check_status == 1)

	if self.check_status == 1 then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local str_day = string.format("%s/%s", self.info.checkbox_str, main_role_id)
		PlayerPrefsUtil.SetInt(str_day, open_day)
	end
end

------------------------------------------------------------------------------------------
-- 取消点击
function TipsDrawRewardView:ListenCancelCallBack()
	self:ExecuteCancelFunc()
	self:Close()
end

-- 确认点击
function TipsDrawRewardView:ListenOkCallBack()
	if self:ExecuteOkFunc() then
		self:Close()
	end
end

function TipsDrawRewardView:NoLongerClick()
	self.check_status = self.check_status == 0 and 1 or 0
	self:FlushNoLonger()
end
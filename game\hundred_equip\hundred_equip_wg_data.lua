HundredEquipWGData = HundredEquipWGData or BaseClass()

function HundredEquipWGData:__init()
	if HundredEquipWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[HundredEquipWGData] attempt to create singleton twice!")
		return
	end

	HundredEquipWGData.Instance = self

	local hundredfold_drop_cfg = ConfigManager.Instance:GetAutoConfig("hundredfold_drop_auto")
	self.other_cfg = hundredfold_drop_cfg.other[1]
	self.level_cfg = ListToMap(hundredfold_drop_cfg.level, "level")
	self.drop_times_target_cfg = ListToMap(hundredfold_drop_cfg.drop_times_target, "seq")
	self.rmb_buy_cfg = ListToMap(hundredfold_drop_cfg.rmb_buy, "level")
	self.enter_cfg = ListToMap(hundredfold_drop_cfg.enter, "type", "min_player_level")
	self.refresh_cfg = ListToMap(hundredfold_drop_cfg.refresh, "scene_id", "monster_seq")
	self.recharge_task_cfg = ListToMap(hundredfold_drop_cfg.recharge_task, "seq")
	self.wave_cfg = ListToMap(hundredfold_drop_cfg.wave, "wave")
	self.task_cfg = ListToMap(hundredfold_drop_cfg.level_up_task, "type")

	self.level = 0
	self.rmb_buy_level = 0
	self.real_recharge_reward_flag = {}
	self.real_recharge_num = 0
	self.target_reward_flag = {}
	self.is_open_flag = 0
	self.exp_per_times = 0
	self.task_info = {}
	self.task_list = {}
	self.boss_state = {}
	self.fuben_cur_wave_num = 0

	--缓存的爆率配置
	self.drop_times_list_data = {}
	for k_1, v_1 in pairs(self.level_cfg) do
		self.drop_times_list_data[k_1] = {}
		local str_tab = string.split(v_1.drop_times_list_show, "|")
		for k_2, v_2 in ipairs(str_tab) do
			local type_tab = string.split(v_2, ",")
			self.drop_times_list_data[k_1][tonumber(type_tab[1])] = tonumber(type_tab[2])
		end
	end

	--缓存的额外爆率道具显示配置
	self.added_drop_times_list_show_data = {}
	for k_1, v_1 in pairs(self.level_cfg) do
		if v_1.added_drop_times_list_show ~= "" then
			local data = {}
			local reward_data = {}
			data.level = v_1.level

			local str_tab = string.split(v_1.added_drop_times_list_show, ",")
			for k_2, v_2 in ipairs(str_tab) do
				local type_tab = string.split(v_2, ":")
				local data2 = {item_id = tonumber(type_tab[1]), num = tonumber(type_tab[2]), drop_times = tonumber(type_tab[3])}
				table.insert(reward_data, data2)
			end

			data.reward_data = reward_data
			table.insert(self.added_drop_times_list_show_data, data)
		end
	end

	--结算缓存的额外爆率道具显示配置
	self.added_drop_times_list_show_data2 = {}
	for k_1, v_1 in pairs(self.level_cfg) do
		self.added_drop_times_list_show_data2[k_1] = {}
		local str_tab = string.split(v_1.drop_times_list_show_1, ",")
		for k_2, v_2 in ipairs(str_tab) do
			local type_tab = string.split(v_2, ":")
			local data = {item_id = tonumber(type_tab[1]), num = tonumber(type_tab[2]), drop_times = tonumber(type_tab[3])}
			table.insert(self.added_drop_times_list_show_data2[k_1], data)
		end
	end

	-- 排行榜
	self.rank_info_list = {}
	self.my_rank_info = {}

	RemindManager.Instance:Register(RemindName.HundredEquipView, BindTool.Bind(self.GetHundredEquipViewRemind, self))
	RemindManager.Instance:Register(RemindName.HundredEquipAward, BindTool.Bind(self.GetHundredEquipAwardRemind, self))
end

function HundredEquipWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.HundredEquipView)
	RemindManager.Instance:UnRegister(RemindName.HundredEquipAward)
	HundredEquipWGData.Instance = nil
end

--百倍爆率 - 总信息
function HundredEquipWGData:SetAllInfo(protocol)
	self.level = protocol.level		--爆装等级
	self.rmb_buy_level = protocol.rmb_buy_level		--直购等级
	self.real_recharge_reward_flag = protocol.real_recharge_reward_flag	--真充奖励领取标识 01010101
	self.real_recharge_num = protocol.real_recharge_num		--真充额度
	self.target_reward_flag = protocol.target_reward_flag		--爆率达标奖励领取标识	
	self.is_open_flag = protocol.is_open_flag	--系统开启标识
	self.exp_per_times = protocol.exp_per_times	-- 累计活跃值(升级爆装等级用)
	self.task_list = protocol.task_list     --任务列表
end

--百倍爆率 - 真充额度更新
function HundredEquipWGData:RealRechargeNumUpdate(protocol)
	self.real_recharge_num = protocol.real_recharge_num		--真充额度
end

--百倍爆率 - 真充奖励领取状态更新
function HundredEquipWGData:RealRechargeRewardFlagUpdate(protocol)
	self.real_recharge_reward_flag = protocol.real_recharge_reward_flag	--真充奖励领取标识 01010101
end

--百倍爆率 - 直购等级更新
function HundredEquipWGData:RmbBuyLevelUpdate(protocol)
	self.rmb_buy_level = protocol.rmb_buy_level		--直购等级
end

--百倍爆率 - 真充boss状态更新
function HundredEquipWGData:RealRechargeBossStatusUpdate(protocol)
	self.boss_state = protocol.boss_state		---//boss状态 0100101 0:不存在 1:存活
end

--百倍爆率 - 爆率达标奖励
function HundredEquipWGData:TargetRewardFlagUpdate(protocol)
	self.target_reward_flag = protocol.target_reward_flag		--领取状态 010010101 0:未领取 1:已领取
end

--百倍爆率 - 活跃值更新
function HundredEquipWGData:ExpPerUpdate(protocol)
	self.exp_per_times = protocol.exp_per_times

	-- if self.task_info[protocol.seq] then
	--     self.task_info[protocol.seq].type = protocol.type --任务类型
	--     self.task_info[protocol.seq].state = protocol.state --任务状态
	--     self.task_info[protocol.seq].process = protocol.process --进度
	-- end
end

--百倍爆率 - 单个任务更新
function HundredEquipWGData:TaskUpdate(protocol)
	if self.task_list[protocol.task_type] then
	    self.task_list[protocol.task_type].complete_times = protocol.task_list.complete_times --今日完成次数
	    self.task_list[protocol.task_type].process = protocol.task_list.process 				--任务进度
	end
end

--百倍爆率 - 获取任务列表.
function HundredEquipWGData:GetTaskList()
	return self.task_list
end

--百倍爆率 - 获取记录的活跃值
function HundredEquipWGData:GetExpPerTimes()
	return self.exp_per_times
end


--获取爆装等级
function HundredEquipWGData:GetLevelValue()
	return self.level
end

--获取直购等级
function HundredEquipWGData:GetRmbBuyLevel()
	return self.rmb_buy_level
end

--获取爆装等级配置
function HundredEquipWGData:GetLevelCfgByLevel(level)
	return self.level_cfg[level]
end

--获取爆装等级是否满级
function HundredEquipWGData:GetLevelIsMaxLevel()
	return self.level >= #self.level_cfg
end

--获取爆装等级配置
function HundredEquipWGData:GetLevelCfg()
	return self.level_cfg
end

--获取缓存的爆率
function HundredEquipWGData:GetDropTimesListCfg(level)
	return self.drop_times_list_data[level]
end

--获取额外爆率道具展示缓存
function HundredEquipWGData:GetAddedDropTimesListShowCfg()
	return self.added_drop_times_list_show_data
end

--获取结算额外爆率道具展示缓存
function HundredEquipWGData:GetAddedDropTimesListShowCfg2(level)
	return self.added_drop_times_list_show_data2[level]
end

--获得真冲的值
function HundredEquipWGData:GetRealRechargeNum()
	return self.real_recharge_num
end

--每日真冲任务奖励
function HundredEquipWGData:GetRechargeTaskList()
	local list_data = {}
	for k_1, v_1 in pairs(self.recharge_task_cfg) do
		local state = self.real_recharge_reward_flag[v_1.seq] or 0
		local data = {}
		data.cfg = v_1
		data.state = state
		table.insert(list_data, data)
	end
	table.sort(list_data, function (a, b)
		return a.cfg.seq < b.cfg.seq
	end)
	return list_data
end

--每日真冲任务奖励
function HundredEquipWGData:GetRechargeTaskCfgBySeq(seq)
	return self.recharge_task_cfg[seq]
end

--获取直购的对应配置
function HundredEquipWGData:GetRmbBuyCfg(level)
	return self.rmb_buy_cfg[level]
end

--获取直购是否满级
function HundredEquipWGData:GetRmbBuyIsMaxLevel()
	return self.rmb_buy_level >= #self.rmb_buy_cfg
end

--获得爆率达标的奖励
function HundredEquipWGData:GetDropTimesTargetCfg(seq)
	return self.drop_times_target_cfg[seq]
end

function HundredEquipWGData:GetHundredEquipRefreshCfg(scene_id)
	return self.refresh_cfg[scene_id]
end

--获取对应字段
function HundredEquipWGData:GetOtherCfgByName(key)
	return self.other_cfg[key]
end

function HundredEquipWGData:GetBossListBySceneId(scene_id)
	local boss_data = self:GetHundredEquipRefreshCfg(scene_id)
	local lisy_data = {}
	if not IsEmptyTable(boss_data) then
		for k_1, v_1 in pairs(boss_data) do
			local monster_cfg = BossWGData.Instance:GetMonsterCfgByid(v_1.monster_id)
			if monster_cfg then
				local data = {}
				local str_tab = string.split(v_1.pos, ",")
				data.x_pos = tonumber(str_tab[1])
				data.y_pos = tonumber(str_tab[2])
				data.boss_id = v_1.monster_id
				data.boss_name = monster_cfg.name
				data.boss_level = monster_cfg.level
				data.type = BossWGData.MonsterType.Boss
				data.state = self.boss_state[v_1.monster_seq] or 1
				table.insert(lisy_data, data)
			end
		end
	end

	return lisy_data
end

function HundredEquipWGData:GetRatioTaskListData()
	local list_data = {}

	local level_data = self.task_cfg
	if not level_data then
		return list_data
	end

	for k, v in pairs(level_data) do
		if v.open_level <= RoleWGData.Instance.role_vo.level and v.open_dayindex <= TimeWGCtrl.Instance:GetCurOpenServerDay() then
			local data = {}
			data.cfg = v
			local task_info = self.task_list[k] or {}
			data.complete_times = task_info.complete_times or 0	--今日完成次数.
			data.state = task_info.complete_times >= v.complete_max_times and REWARD_STATE_TYPE.FINISH or REWARD_STATE_TYPE.UNDONE
			data.process = task_info.process or 0				--任务进度.
			table.insert(list_data, data)
		end
	end
	table.sort(list_data, function (a, b)
		if a.state == b.state then
			return a.cfg.sort < b.cfg.sort
		end

		return a.state < b.state
	end)

	return list_data
end

--获取对应的副本进入配置
function HundredEquipWGData:GetEnterMapCfgByType(type)
	local type_cfgs = self.enter_cfg[type]
	if type_cfgs then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		for k_1, v_1 in pairs(type_cfgs) do
			if role_level >= v_1.min_player_level and role_level <= v_1.max_player_level then
				return v_1
			end
		end
	end
end

-- 百倍爆装-排行榜
function HundredEquipWGData:UpdateRankInfos(protocal)
	self.my_rank_info.my_rank = protocal.my_rank
	self.my_rank_info.my_wave = protocal.my_wave

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	self.rank_info_list = {}

	local index = 0
	for _, value in ipairs(protocal.rank_item_list) do
		if value.rank ~= 0 then -- 没有排名信息不处理
			self.rank_info_list[index] = value
			index = index + 1
		end
	end
	SortTools.SortAsc(self.rank_info_list, "rank")
end

-- 获取自己的排名信息
function HundredEquipWGData:GetMyRankInfo()
	return self.my_rank_info
end

-- 获取排行榜列表
function HundredEquipWGData:GetRankInfoList()
	return self.rank_info_list
end

function HundredEquipWGData:GetHundredEquipViewRemind()
	if not FunOpen.Instance:GetFunIsOpened(GuideModuleName.HundredEquipView) then
		return 0
	end

	if self:RealRechargeRed() then
		return 1
	end

	if self:GetHundredEquipAwardRemind() == 1 then
		return 1
	end

	return 0
end

-- 排名预览奖励
function HundredEquipWGData:GetPreAwards()
	local cfg = ConfigManager.Instance:GetAutoConfig("hundredfold_drop_auto")
	return cfg and cfg.rank_reward
end

function HundredEquipWGData:GetHundredEquipAwardRemind()
	if not FunOpen.Instance:GetFunIsOpened(GuideModuleName.HundredEquipView) then
		return 0
	end

	if self:TitleTargetRewardRed() then
		return 1
	end

	if self:GetTaskLevelUpRed() then
		return 1
	end

	return 0
end

function HundredEquipWGData:RealRechargeRed()
	if not FunOpen.Instance:GetFunIsOpened(FunName.HundredEquipRatio) then
		return false
	end

	local list_data = self:GetRechargeTaskList()
	for k_1, v_1 in pairs(list_data) do --真冲奖励
		if v_1.state == 0 and self.real_recharge_num >= v_1.cfg.recharge_num then -- 真冲奖励可领取
			return true
		end

		if self:RatioGoToMapRed(v_1.cfg.map_type) then
			return true
		end
	end

	return false
end

--是否可以挑战副本红点
function HundredEquipWGData:RatioGoToMapRed(map_type)
	local enter_map_cfg = self:GetEnterMapCfgByType(map_type)
	if enter_map_cfg then
		local item_tab = enter_map_cfg.reward
		local has_count = ItemWGData.Instance:GetItemNumInBagById(item_tab.item_id)
		if has_count >= item_tab.num then
			return true
		end
	end

	return false
end

function HundredEquipWGData:GetTargetRewardReceive(seq)
	return self.target_reward_flag[seq] == 1
end

--判断爆率达标奖励红点显示
function HundredEquipWGData:TitleTargetRewardRed()
	local drop_time_add_show = 1
	if self.rmb_buy_level > 0 then
		local level_cfg = self:GetRmbBuyCfg(self.rmb_buy_level)
		drop_time_add_show = (1 + level_cfg.drop_time_add_show / 100)
	end

	if self.target_reward_flag[0] == 1 then
		return false
	end

	local drop_times = self:GetDropTimesListCfg(self.level)
	local max_value = 0
	for k_1, v_1 in pairs(drop_times) do
		if max_value < v_1 then
			max_value = v_1
		end
	end

	--副本最大爆率*直购倍率.
	local now_value = max_value * drop_time_add_show
	--爆率+守护特权爆率.
	local shtq_add_value = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSHTQHundredfoldDropPro()
	now_value = now_value + shtq_add_value
	local target_cfg = self:GetDropTimesTargetCfg(0) --只有一个
	return now_value >= target_cfg.drop_times_show
end

--任务等级可提升红点
function HundredEquipWGData:GetTaskLevelUpRed()
    local next_cfg = self:GetLevelCfgByLevel(self.level + 1)
	if not next_cfg then
		return false
	end

    local now_num = self.exp_per_times
    local target_num = next_cfg.exp_per_times_limit or 0
	if now_num < target_num then return false end

	return true
end

function HundredEquipWGData:GetWaveCfg(wave)
	return self.wave_cfg[wave]
end

function HundredEquipWGData:SetFuBenCurNum(cur_wave_num)
	self.fuben_cur_wave_num = cur_wave_num
end

function HundredEquipWGData:GetFuBenCurNum()
	return self.fuben_cur_wave_num
end

--获取副本当前波次应该展示的奖励.
function HundredEquipWGData:GetFuBenRewardList()
	local reward_list = {}

	for i = 1, #self.wave_cfg do
		if i <= self.fuben_cur_wave_num then
			local wave_cfg = self:GetWaveCfg(i)
			local reward_item = wave_cfg.reward_item

			for k1, v1 in pairs(reward_item) do
				local can_insert = true
				for k2, v2 in pairs(reward_list) do
					--堆叠相同道具.
					if v2.item_id == v1.item_id then
						v2.num = v2.num + v1.num
						can_insert = false
						break
					end
				end

				if can_insert then
					local item = { item_id = v1.item_id, num = v1.num, is_bind = v1.is_bind }
					table.insert(reward_list, item)
				end
			end
		end
	end

	return reward_list
end

-- 获取副本场景内的排行榜列表（伪排名）.
function HundredEquipWGData:GetFuBenRankInfoList()
	local rank_list = {}
	local my_rank_info = self:GetMyRankInfo()
	local my_rank = my_rank_info.my_rank
	local my_wave = my_rank_info.my_wave
	local is_updata = false

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local rank_wave_num = self.fuben_cur_wave_num <= 0 and self.fuben_cur_wave_num or self.fuben_cur_wave_num - 1

	local index = 1
	local have_my_rank = false	--是否存在玩家自己的历史排名.
	for _, value in pairs(self.rank_info_list) do
		if value.rank ~= 0 then -- 没有排名信息不处理
			rank_list[index] = {}
			rank_list[index].uid = value.uid
			rank_list[index].name = value.name
			local hight_wave = value.wave
			--自己的波数比历史波数大，则更新.
			if value.uid == role_id and rank_wave_num > value.wave then
				hight_wave = rank_wave_num
				if rank_wave_num > my_wave then
					my_wave = rank_wave_num
					is_updata = true
				end
			end
			rank_list[index].wave = hight_wave
			index = index + 1
		end

		if value.uid == role_id then
			have_my_rank = true
		end
	end

	--如果不存在玩家的历史排名，则把自己塞进去，做伪排名.
	if not have_my_rank and rank_wave_num > 0 then
		local role_vo = RoleWGData.Instance:GetRoleVo()
		local rank_info = {}
		rank_info.uid = role_id
		rank_info.name = role_vo.name
		rank_info.wave = rank_wave_num
		table.insert(rank_list, rank_info)

		my_wave = rank_wave_num
		is_updata = true
	end

	SortTools.SortDesc(rank_list, "wave")

	if is_updata then
		for i, v in ipairs(rank_list) do
			if v.uid == role_id then
				my_rank = i
				break
			end
		end
	end

	return rank_list, my_rank, my_wave
end
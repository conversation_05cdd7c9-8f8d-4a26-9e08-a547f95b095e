ControlBeastsCultivateWGData = ControlBeastsCultivateWGData or  BaseClass()
function ControlBeastsCultivateWGData:__init()
	if ControlBeastsCultivateWGData.Instance ~= nil then
		<PERSON>rro<PERSON><PERSON><PERSON>("[ControlBeastsCultivateWGData] attempt to create singleton twice!")
		return
	end
	ControlBeastsCultivateWGData.Instance = self

	self:InitCfg()
	self:InitInfo()

	RemindManager.Instance:Register(RemindName.BeastsAlchemyStrengthen, BindTool.Bind(self.ShowAlchemyStrengthenRemind, self)) 			-- 总红点
	RemindManager.Instance:Register(RemindName.BeastsAlchemySuccinct, BindTool.Bind(self.GetAlchemySuccinctRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsAlchemyInherit, BindTool.Bind(self.GetAlchemyInheritRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsAlchemyBreak, BindTool.Bind(self.GetAlchemyBreakRemind, self))
end

function ControlBeastsCultivateWGData:__delete()
	ControlBeastsCultivateWGData.Instance = nil

	self:DeleteCfg()
	self:DeleteInfo()

	RemindManager.Instance:UnRegister(RemindName.BeastsAlchemyStrengthen)
	RemindManager.Instance:UnRegister(RemindName.BeastsAlchemySuccinct)
	RemindManager.Instance:UnRegister(RemindName.BeastsAlchemyInherit)
	RemindManager.Instance:UnRegister(RemindName.BeastsAlchemyBreak)
end

------------------------------------------------------------- 配置信息 --------------------------------------------------------
-- 初始化配置表
function ControlBeastsCultivateWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("beast_equip_auto")
	if cfg then
		self.base_cfg = cfg.other[1]
		self.slot_cfg = ListToMap(cfg.slot, "fight_slot", "hole")
		self.slot_uplevel_cfg = ListToMap(cfg.slot_uplevel, "hole", "level")
		self.equip_cfg = cfg.equip
		self.color_words_nums_pool_cfg = ListToMap(cfg.color_words_nums_pool, "color", "hole")
		self.words_cfg = cfg.words
		self.words_pool_cfg = ListToMapList(cfg.words, "pool_id")
		self.word_refresh_cfg = cfg.word_refresh_cost
		self.slot_uplevel_item_cfg = cfg.slot_uplevel_item_cost
		self.slot_uplevel_item_id_cfg = ListToMap(cfg.slot_uplevel_item_cost, "item_id")
		self.suit_cfg = ListToMapList(cfg.suit, "suit_type")
		self.suit_num_cfg = ListToMap(cfg.suit, "suit_type", "num")
		self.skill_cfg = cfg.skill_desc
	end
end
-- 清理垃圾
function ControlBeastsCultivateWGData:DeleteCfg()
	self.base_cfg = nil
	self.slot_cfg = nil
	self.slot_uplevel_cfg = nil
	self.equip_cfg = nil
	self.color_words_nums_pool_cfg = nil
	self.words_pool_cfg = nil
	self.words_cfg = nil
	self.word_refresh_cfg = nil
	self.word_refresh_item_cfg = nil
	self.suit_cfg = nil
	self.suit_num_cfg = nil
	self.skill_cfg = nil
end

-- 初始化数据
function ControlBeastsCultivateWGData:InitInfo()
	if self.color_words_nums_pool_cfg then
		self.color_word_pool = {}

		for color, v in ipairs(self.color_words_nums_pool_cfg) do
			if not self.color_word_pool[color] then
				self.color_word_pool[color] = {}
			end

			for hole, data in pairs(v) do
				if not self.color_word_pool[color][hole] then
					self.color_word_pool[color][hole] = {}
				end

				if data and data.words_num_pool then
					local pool_str_list = Split(data.words_num_pool, ",")
					for index, pool_str in ipairs(pool_str_list) do
						local value = tonumber(pool_str) or 0
						if value ~= 0 then
							table.insert(self.color_word_pool[color][hole], index)
						end
					end
				end
			end
		end
	end
end

-- 清理垃圾
function ControlBeastsCultivateWGData:DeleteInfo()
	self.color_word_pool = nil
end

------------------------------------------------------
---------------------红点相关-------------------------
-- 是否能强化
function ControlBeastsCultivateWGData:ShowAlchemyStrengthenRemind()
	if self:GetAlchemyBeastEquipIsCanCultivate(true, false) then
		return 1
	end

	return 0
end

-- 是否能洗练
function ControlBeastsCultivateWGData:GetAlchemySuccinctRemind()
	if self:GetAlchemyBeastEquipIsCanCultivate(false, true) then
		return 1
	end

	return 0
end

-- 是否能传承
function ControlBeastsCultivateWGData:GetAlchemyInheritRemind()
	return 0
end

-- 是否能分解
function ControlBeastsCultivateWGData:GetAlchemyBreakRemind()
	return 0
end
------------------------------------------------------
---------------------配置数据-------------------------
-- 获取基础配置
function ControlBeastsCultivateWGData:GetBaseCfg()
	return self.base_cfg
end

-- 获取槽位配置
function ControlBeastsCultivateWGData:GetSlotCfg(slot, hole)
	local empty = {}
	return ((self.slot_cfg or empty)[slot] or empty)[hole]
end

-- 获取槽位升级配置
function ControlBeastsCultivateWGData:GetSlotUpLvCfg(hole, lv)
	local empty = {}
	return ((self.slot_uplevel_cfg or empty)[hole] or empty)[lv]
end

-- 获取装备配置
function ControlBeastsCultivateWGData:GetEquipCfg(item_id)
	local empty = {}
	return (self.equip_cfg or empty)[item_id]
end

-- 获取装备配置
function ControlBeastsCultivateWGData:GetAllEquipCfg()
	return self.equip_cfg
end

function ControlBeastsCultivateWGData:IsBeastAlchemyEquipItem(item_id)
	local empty = {}
	return (self.equip_cfg or empty)[item_id] ~= nil
end

-- 获取装备洗练词条配置
-- function ControlBeastsCultivateWGData:GetWordsPoolCfg(slot, hole)
-- 	local empty = {}
-- 	return ((self.words_pool_cfg or empty)[slot] or empty)[hole]
-- end

-- 获取词条配置
function ControlBeastsCultivateWGData:GetWordCfg(seq)
	local empty = {}
	return (self.words_cfg or empty)[seq]
end

-- 获取词条洗练消耗配置
function ControlBeastsCultivateWGData:GetWordRefreshListCfg(seq)
	local empty = {}
	return (self.word_refresh_cfg or empty)[seq]
end

-- 获取词条洗练消耗配置
function ControlBeastsCultivateWGData:GetWordRefreshItemCfg(item_id)
	local empty = {}
	return (self.word_refresh_item_cfg or empty)[item_id]
end

-- 获取是否是词条刷新物体
function ControlBeastsCultivateWGData:IsWordRefreshItem(item_id)
	return self:GetWordRefreshItemCfg(item_id) == nil
end

-- 获取装备套装配置
function ControlBeastsCultivateWGData:GetSuitListCfg(suit_type)
	local empty = {}
	return (self.suit_cfg or empty)[suit_type]
end

-- 获取装备套装配置
function ControlBeastsCultivateWGData:GetSuitCfgByTypeNum(suit_type, num)
	local empty = {}
	return ((self.suit_num_cfg or empty)[suit_type] or empty)[num]
end

-- 获取装备技能配置
function ControlBeastsCultivateWGData:GetSkillDescBySkillId(skill_id)
	local empty = {}
	return (self.skill_cfg or empty)[skill_id]
end

-- 获取当前槽位升级的道具列表
function ControlBeastsCultivateWGData:GetSlotUplevelItemListCfg()
	local empty = {}
	return self.slot_uplevel_item_cfg
end

-- 是否是槽位升级的道具
function ControlBeastsCultivateWGData:IsSlotUplevelItem(item_id)
	local empty = {}
	return (self.slot_uplevel_item_id_cfg or empty)[item_id] ~= nil
end

-- 是否是洗练的道具
function ControlBeastsCultivateWGData:IsEquipWordsRefreshItem(var_item_id)
	local base_cfg = self:GetBaseCfg()
	local words_refresh_base_cost_item = base_cfg and base_cfg.words_refresh_base_cost_item

	if words_refresh_base_cost_item.item_id == var_item_id then
		return true
	end

	if self.word_refresh_cfg then
		for k, v in pairs(self.word_refresh_cfg) do
			if v.cost_item_list then
				for m, n in pairs(v.cost_item_list) do
					if n.item_id and n.item_id == var_item_id then
						return true
					end
				end
			end
		end
	end
	
	return false
end

-- 是否是传承的道具
function ControlBeastsCultivateWGData:IsEquipInheritanceItem(var_item_id)
	local base_cfg = self:GetBaseCfg()
	local inheritance_item = base_cfg and base_cfg.inheritance_item

	if inheritance_item.item_id == var_item_id then
		return true
	end

	return false
end

-- 获取一个库的所有词条
function ControlBeastsCultivateWGData:GetPoolWordsListByPoolId(pool_id)
	local empty = {}
	return (self.words_pool_cfg or empty)[pool_id] or empty
end

-- 获取一个装备的所有库
function ControlBeastsCultivateWGData:GetPoolListByColorHole(color, hole)
	local empty = {}
	return ((self.color_word_pool or empty)[color] or empty)[hole] or {}
end

------------------------------------------------------
---------------------协议数据-------------------------
-- 幻兽装备总信息
function ControlBeastsCultivateWGData:SetBeastEquipInfo(protocol)
	self.fight_slot_list = protocol.fight_slot_list
end

-- 幻兽装备单个槽位信息
function ControlBeastsCultivateWGData:UpdateBeastEquipInfo(protocol)
	if not self.fight_slot_list then
		self.fight_slot_list = {}
	end

	if not self.fight_slot_list[protocol.fight_slot] then
		self.fight_slot_list[protocol.fight_slot] = {}
	end

	self.fight_slot_list[protocol.fight_slot][protocol.slot] = protocol.slot_equip
end

-- 驭兽装备 - 背包信息
function ControlBeastsCultivateWGData:SetBeastEquipMsgBag(protocol)
	self.grid_list = protocol.grid_list
end

-- 驭兽装备 - 背包信息变化
function ControlBeastsCultivateWGData:UpdateBeastEquipMsgBagGrid(protocol)
	if not self.grid_list then
		self.grid_list = {}
	end

	local old_equip_data = self.grid_list[protocol.grid_item.index]
	local old_equip_item_id = old_equip_data and old_equip_data.item_id or COMMON_CONSTS.NUMBER_ZERO
	if protocol.grid_item.item_id ~= COMMON_CONSTS.NUMBER_ZERO and protocol.grid_item.item_id ~= old_equip_item_id then
		local item_cfg = ItemWGData.Instance:GetItemConfig(protocol.grid_item.item_id)
		if item_cfg then
			-- 新获得提示
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.SysRemind.AddItem, item_name, 1)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
		end
	end

	self.grid_list[protocol.grid_item.index] = protocol.grid_item
end

-- 获取一个出战位的装备列表数据
function ControlBeastsCultivateWGData:GetFightBeastEquipList(fight_slot)
	local empty = {}
	return (self.fight_slot_list or empty)[fight_slot]
end

-- 获取一个出战位的某个装备
function ControlBeastsCultivateWGData:GetFightBeastEquipBySlot(fight_slot, slot)
	local empty = {}
	return ((self.fight_slot_list or empty)[fight_slot] or empty)[slot]
end

-- 获取背包数据
function ControlBeastsCultivateWGData:GetFightBeastEquipBag()
	return self.grid_list
end

-- 从背包中获取一个装备
function ControlBeastsCultivateWGData:GetFightBeastEquipByIndex(bag_index)
	local empty = {}
	return (self.grid_list or empty)[bag_index]
end
---------------------------------------------------------
---------------------模块函数----------------------------
-- 获取当前孔位的总等级
function ControlBeastsCultivateWGData:GetFightBeastAllEquipSlotLv(fight_slot)
	local list = self:GetFightBeastEquipList(fight_slot)
	local level = 0

	if list then
		if list[COMMON_CONSTS.NUMBER_ZERO] then
			level = level + list[COMMON_CONSTS.NUMBER_ZERO].level
		end

		for i, v in ipairs(list) do
			level = level + v.level
		end
	end

	return level
end

-- 获取当前战斗孔位是否有装备
function ControlBeastsCultivateWGData:GetFightBeastAllEquipNotHaveItem(fight_slot)
	local slot_index = 0
	local list = self:GetFightBeastEquipList(fight_slot)


	if list then
		if list[COMMON_CONSTS.NUMBER_ZERO] then
			local equip_item_id = (list[COMMON_CONSTS.NUMBER_ZERO].equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO

			if equip_item_id ~= COMMON_CONSTS.NUMBER_ZERO then
				return false
			end
		end

		for i, v in ipairs(list) do
			local equip_item_id = (v.equip_info or {}).item_id or COMMON_CONSTS.NUMBER_ZERO

			if equip_item_id ~= COMMON_CONSTS.NUMBER_ZERO then
				return false
			end
		end
	end

	return true
end

-- 获取一个孔位是否解锁
function ControlBeastsCultivateWGData:GetFightBeastEquipSlotIsLock(fight_slot, slot)
	local now_all_lv = self:GetFightBeastAllEquipSlotLv(fight_slot)
	local cfg = self:GetSlotCfg(fight_slot, slot)
    local total_hole_level_limit = cfg and cfg.total_hole_level_limit or COMMON_CONSTS.NUMBER_ONE
	local fight_slot_data = ControlBeastsWGData.Instance:GetHoleDataById(fight_slot + COMMON_CONSTS.NUMBER_ONE)
    local fight_slot_lock = fight_slot_data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE or fight_slot_data.beasts_bag_id == -1
    local slot_limit_lock = now_all_lv < total_hole_level_limit
    local is_slot_lock = fight_slot_lock or slot_limit_lock
	return is_slot_lock, total_hole_level_limit, now_all_lv
end

-- 获取当前套装个数
function ControlBeastsCultivateWGData:GetFightBeastAllEquipSuitNum(fight_slot, aim_suit_type)
	local list = self:GetFightBeastEquipList(fight_slot)
	local suit_num = 0

	local function get_is_suit_part(equip_info, aim_suit_type)
		if (not equip_info) or equip_info.item_id == 0 then
			return false
		end

		local cfg = self:GetEquipCfg(equip_info.item_id)
		local suit_type = cfg and cfg.suit_type or -1

		return aim_suit_type == suit_type
	end

	if list then
		if list[COMMON_CONSTS.NUMBER_ZERO] and list[COMMON_CONSTS.NUMBER_ZERO].equip_info then
			if get_is_suit_part(list[COMMON_CONSTS.NUMBER_ZERO].equip_info, aim_suit_type) then
				suit_num = suit_num + 1
			end
		end

		for i, v in ipairs(list) do
			if get_is_suit_part(v.equip_info, aim_suit_type) then
				suit_num = suit_num + 1
			end
		end
	end

	return suit_num
end

-- 获取装备属性
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipAttrList(equip_info, slot_index, slot_lv)
	local equip_item_id = equip_info and equip_info.item_id or COMMON_CONSTS.NUMBER_ZERO
	local cfg = self:GetEquipCfg(equip_item_id)
	local slot_cfg = nil
	local solt_next_cfg = nil

	if slot_index ~= nil and slot_lv ~= nil then
		slot_cfg = self:GetSlotUpLvCfg(slot_index, slot_lv)
		solt_next_cfg = self:GetSlotUpLvCfg(slot_index, slot_lv + COMMON_CONSTS.NUMBER_ONE)
	end
	
	return self:SpiltAlchemyBeastEquipAttrList(cfg, slot_cfg, solt_next_cfg)
end

-- 拆分组合属性列表
function ControlBeastsCultivateWGData:SpiltAlchemyBeastEquipAttrList(cfg, slot_cfg, solt_next_cfg, is_not_sub)
	local attr_list = {}

	-- 基础属性
	if cfg and not IsEmptyTable(cfg) then
		for i = 1, 6 do
			local key = cfg["attr_id" .. i]
			if key then
				if not attr_list[key] then
					attr_list[key] = {}
					attr_list[key].attr_str = key
				end
	
				attr_list[key].attr_value = cfg["attr_value" .. i]
			end
		end
	end

	-- 槽位强化属性
	if slot_cfg and not IsEmptyTable(slot_cfg) then
		for i = 1, 6 do
			local key = slot_cfg["attr_id" .. i]
			if key then
				local curr_value = slot_cfg["attr_value" .. i]

				if not attr_list[key] then
					attr_list[key] = {}
					attr_list[key].attr_str = key
					attr_list[key].attr_value = curr_value
				else
					attr_list[key].attr_value = attr_list[key].attr_value + curr_value
				end

				if solt_next_cfg and solt_next_cfg["attr_id" .. i] then
					attr_list[key].hide_arrow = true
					if is_not_sub then
						attr_list[key].add_value = solt_next_cfg["attr_value" .. i]
					else
						attr_list[key].add_value = solt_next_cfg["attr_value" .. i] - curr_value
					end
				end
			end
		end
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

-- 获取当前装备的套装评分
function ControlBeastsCultivateWGData:GetFightBeastEquipSuitScore(aim_suit_type, number)
	local list = self:GetSuitListCfg(aim_suit_type)
	local score = 0

	if list then
		for i, v in ipairs(list) do
			if number >= v.num then
				score = score + v.suit_score
			end
		end
	end

	return score
end

-- 获取当前是否已经装备
function ControlBeastsCultivateWGData:IsHasBeastEquipByFightSlot(fight_slot, slot)
	local equip_slot_data = self:GetFightBeastEquipBySlot(fight_slot, slot)
	local equip_info = equip_slot_data and equip_slot_data.equip_info

	if equip_info and (equip_info.item_id ~= 0) then
		return true
	end

	return false
end

-- 获取当前是否已经装备
function ControlBeastsCultivateWGData:AssembleBeastEquipByFightSlot(fight_slot, slot, slot_lv)
	local equip_slot_data = self:GetFightBeastEquipBySlot(fight_slot, slot)
	local equip_data = equip_slot_data and equip_slot_data.equip_info
	local item_data = nil

	if equip_data and (equip_data.item_id ~= 0) then
	    item_data = {
			item_id = equip_data.item_id, 
			equip_info = equip_data, 
			is_bag_equip = false, 
			fight_slot = fight_slot, 
			equip_slot = slot,
			equip_slot_lv = slot_lv,
		}
	end

	return item_data
end

-- 获取一个装备的评分
---@param equip_data 装备数据
---@param is_bag_equip 是否是背包装备
---@param fight_slot 战斗孔位
---@param slot 镶嵌孔位
---@param slot_lv 孔位等级
---@param is_succinct 是否为精炼
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipScore(equip_data, is_bag_equip, fight_slot, slot, slot_lv, is_succinct)
	if not equip_data then
		return 0
	end

	local ward_score = self:GetAlchemyBeastEquipWordScore(equip_data, is_succinct)
	-- 计算装备评分
	local cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(equip_data.item_id)
	local equip_score = cfg and cfg.initial_score or 0
	local aim_suit_type = cfg and cfg.suit_type or -1
	local suit_score = 0
	local solt_lv_score = 0

	if (not is_bag_equip) and fight_slot ~= nil then
		--计算套装评分
		local num = self:GetFightBeastAllEquipSuitNum(fight_slot, aim_suit_type)
		suit_score = self:GetFightBeastEquipSuitScore(aim_suit_type, num)
	end

	if (not is_bag_equip) and slot ~= nil and slot_lv ~= nil then
		--计算强化评分
		local slot_cfg = self:GetSlotUpLvCfg(slot, slot_lv)
		solt_lv_score = slot_cfg and slot_cfg.level_score or 0
	end

	return math.floor(ward_score + equip_score + suit_score + solt_lv_score)
end

-- 获取一个装备的词条评分
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipWordScore(equip_data, is_succinct)
	if not equip_data then
		return 0
	end

	local ward_score = 0
	local words_list = equip_data.words_list or {}

	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
		local ward_data = words_list[j]
		if ward_data then
			local final_seq = is_succinct and ward_data.can_replace_words or ward_data.words_seq
			local random_value = final_seq % 100
			local real_seq = math.floor(final_seq / 100)

			local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)
			if cfg then
				if cfg.words_type ~= 2 then
					-- 词条评分
					ward_score = ward_score + ((cfg.entry_score or 0) * (random_value / 100))
				else
					ward_score = ward_score + (cfg.entry_score or 0)
				end
			end
		end
	end

	return ward_score
end

-- 获取内丹是否可以镶嵌
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanWear()
	local battle_main_hole_data = ControlBeastsWGData.Instance:GetHoleMainData()
	local battle_assist_hole_data = ControlBeastsWGData.Instance:GetHoleAssistData()
	local all_beast_list = {}
	local not_have_beast_list = {}

	for k, v in pairs(battle_main_hole_data) do
		if v.hole_id and v.hole_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			if self:GetAlchemyBeastEquipIsCanWearForHole(v.hole_id) then
				return true
			end
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.hole_id and v.hole_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			if self:GetAlchemyBeastEquipIsCanWearForHole(v.hole_id) then
				return true
			end
		end
	end

	return false
end

-- 获取内丹是否可以镶嵌
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanWear()
	local battle_main_hole_data = ControlBeastsWGData.Instance:GetHoleMainData()
	local battle_assist_hole_data = ControlBeastsWGData.Instance:GetHoleAssistData()
	local all_beast_list = {}
	local not_have_beast_list = {}

	for k, v in pairs(battle_main_hole_data) do
		if v.hole_id and v.hole_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			if self:GetAlchemyBeastEquipIsCanWearForHole(v.hole_id) then
				return true
			end
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.hole_id and v.hole_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			if self:GetAlchemyBeastEquipIsCanWearForHole(v.hole_id) then
				return true
			end
		end
	end

	return false
end

-- 判断一个战斗孔位是否可以镶嵌
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanWearForHole(hole_id)
	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_SLOT_SEQ - 1 do
		local is_lock = self:GetFightBeastEquipSlotIsLock(hole_id, j)

		if not is_lock then
			local now_equip_data = self:GetFightBeastEquipBySlot(hole_id, j)
			local now_score = self:GetAlchemyBeastEquipScore(now_equip_data and now_equip_data.equip_info)
			if self:GetAlchemyBeastEquipIsCanWearForGrid(j, now_score) then
				return true
			end 
		end
	end

	return false
end

-- 获取当前装备是否可以镶嵌更好的装备
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanWearForGrid(slot_id, score)
	local list = self:GetFightBeastEquipBag() or {}
    for k, v in pairs(list) do
        if v.item_id ~= 0 then
			local cfg = self:GetEquipCfg(v.item_id)
			local hole = cfg and cfg.hole or -1

			if hole == slot_id then
				local equip_score = self:GetAlchemyBeastEquipScore(v)
				if equip_score > score then
					return true
				end
			end
        end
    end

	return false
end

-- 是否可以修炼
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanCultivate(is_check_strengthen, is_check_succinct)
	local battle_main_hole_data = ControlBeastsWGData.Instance:GetHoleMainData()
	local battle_assist_hole_data = ControlBeastsWGData.Instance:GetHoleAssistData()
	local all_beast_list = {}
	local not_have_beast_list = {}

	for k, v in pairs(battle_main_hole_data) do
		if v.hole_id and v.hole_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			if self:GetAlchemyBeastEquipIsCanCultivateForHole(v.hole_id, is_check_strengthen, is_check_succinct) then
				return true
			end
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.hole_id and v.hole_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			if self:GetAlchemyBeastEquipIsCanCultivateForHole(v.hole_id, is_check_strengthen, is_check_succinct) then
				return true
			end
		end
	end

	return false
end

-- 判断一个战斗孔位是否可以修炼
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanCultivateForHole(hole_id, is_check_strengthen, is_check_succinct)
	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_SLOT_SEQ - 1 do
		local is_lock = self:GetFightBeastEquipSlotIsLock(hole_id, j)

		if not is_lock then
			local now_equip_data = self:GetFightBeastEquipBySlot(hole_id, j)
			if is_check_strengthen and self:GetAlchemyBeastEquipIsCanStrengthen(j, now_equip_data) then
				return true
			end

			if is_check_succinct and self:GetAlchemyBeastEquipIsCanSuccinctSave(now_equip_data.equip_info) then
				return true
			end
		end
	end

	return false
end

-- 判断某个孔位是否可以强化
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanStrengthen(slot_index, now_equip_data)
	if not now_equip_data then
		return false
	end

	local next_lv = now_equip_data.level + 1
    local lv_cfg = self:GetSlotUpLvCfg(slot_index, now_equip_data.level)
    local lv_next_cfg = self:GetSlotUpLvCfg(slot_index, next_lv)
    local is_full_lv = lv_next_cfg == nil

	if is_full_lv then
		return false
	end

	if now_equip_data.equip_info == nil or now_equip_data.equip_info.item_id == COMMON_CONSTS.NUMBER_ZERO then
		return false
	end

	local need_exp = lv_cfg and lv_cfg.need_exp or 0
	local stuff_item_list = ControlBeastsCultivateWGData.Instance:GetSlotUplevelItemListCfg()
	local now_have_exp = 0
	for i, v in ipairs(stuff_item_list) do
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		now_have_exp = now_have_exp + item_num * v.exp
	end

	return now_have_exp >= need_exp
end

-- 判断某个孔位是否存在洗练未操作
function ControlBeastsCultivateWGData:GetAlchemyBeastEquipIsCanSuccinctSave(now_equip_data)
	local now_word_score = self:GetAlchemyBeastEquipWordScore(now_equip_data, false)
	local new_word_score = self:GetAlchemyBeastEquipWordScore(now_equip_data, true)
	return now_word_score < new_word_score
end

-- 获取当前背包的当前孔位的最好的装备
function ControlBeastsCultivateWGData:GetBestAlchemyBeastEquipForGrid(slot_id, score)
	local list = self:GetFightBeastEquipBag() or {}
	local best_equip = nil
	local best_score = nil

    for k, v in pairs(list) do
        if v.item_id ~= 0 then
			local cfg = self:GetEquipCfg(v.item_id)
			local hole = cfg and cfg.hole or -1

			if hole == slot_id then
				local equip_score = self:GetAlchemyBeastEquipScore(v)
				if equip_score > score then
					if best_score == nil  then
						best_score = equip_score
						best_equip = v
					else
						if best_score < equip_score then
							best_score = equip_score
							best_equip = v
						end
					end
				end
			end
        end
    end

	return best_equip
end

-- 获取一个装备全部孔位信息
function ControlBeastsCultivateWGData:GetFightBeastCamberedEquipList(fight_slot)
	local cambered_equip_list = {}

	local assemble_fun = function(equip, fight_slot, slot_index)
		local data = {}
		data.server_data = equip
		data.slot_index = slot_index
		data.fight_slot = fight_slot
		data.is_final_lock = self:GetFightBeastEquipSlotIsLock(fight_slot, slot_index)

		local is_can_operate = false
		if not data.is_final_lock then
			local now_score = self:GetAlchemyBeastEquipScore(equip.equip_info)
			local can_wear = self:GetAlchemyBeastEquipIsCanWearForGrid(slot_index, now_score)
			local is_can_strengthern = self:GetAlchemyBeastEquipIsCanStrengthen(slot_index, equip)
			local is_can_succinctsave = self:GetAlchemyBeastEquipIsCanSuccinctSave(equip.equip_info)
			is_can_operate = can_wear or is_can_strengthern or is_can_succinctsave
		end

		data.is_remind = is_can_operate
		table.insert(cambered_equip_list, data)

		if data.is_remind then
			return true
		else
			return false
		end
	end

	local list = self:GetFightBeastEquipList(fight_slot)
	local jump_index = nil
    if list then
		if list[COMMON_CONSTS.NUMBER_ZERO] then
			local is_red = assemble_fun(list[COMMON_CONSTS.NUMBER_ZERO], fight_slot, COMMON_CONSTS.NUMBER_ZERO)
			if is_red and jump_index == nil then
				jump_index = COMMON_CONSTS.NUMBER_ONE
			end
		end

		for i, v in ipairs(list) do
			local is_red = assemble_fun(v, fight_slot, i)
			if is_red and jump_index == nil then
				jump_index = i + COMMON_CONSTS.NUMBER_ONE
			end
		end
	end

	return cambered_equip_list, jump_index
end

-- 获取一个装备的所有可生成的词条信息
function ControlBeastsCultivateWGData:GetOneEquipWordPreviewList(item_id, hole)
	local word_pre_list = {}
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)

	if not item_cfg then
		return word_pre_list
	end

	local color = item_cfg.color or 1
	local pool_id_list = self:GetPoolListByColorHole(color, hole)
	for _, v in ipairs(pool_id_list) do
		local word_list = self:GetPoolWordsListByPoolId(v)

		for _, word_data in ipairs(word_list) do
			table.insert(word_pre_list, word_data)
		end
	end

	return word_pre_list, color
end
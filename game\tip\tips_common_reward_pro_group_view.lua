--通用概率展示面板，带分组
TipsCommonRewardProGroupView = TipsCommonRewardProGroupView or BaseClass(SafeBaseView)

function TipsCommonRewardProGroupView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_draw_pro_group_panel")
    self:SetMaskBg(true, true)
end

-- 设置属性值
function TipsCommonRewardProGroupView:SetData(data)
	self.data = data
    self:Open()
end

function TipsCommonRewardProGroupView:ReleaseCallBack()
    if self.probability_all_list then
        self.probability_all_list:DeleteMe()
        self.probability_all_list = nil
    end

    if self.group_list then
        self.group_list:DeleteMe()
        self.group_list = nil
    end
end

function TipsCommonRewardProGroupView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.ProbabilityTitle

    -- 分组列表
    if not self.group_list then
        self.group_list = AsyncListView.New(CommonRewardGroupRender, self.node_list.group_list) 
        self.group_list:SetStartZeroIndex(false)
        self.group_list:SetSelectCallBack(BindTool.Bind(self.OnClickGradeItem, self))
    end

    -- 品质列表
    if not self.probability_all_list then
        self.probability_all_list = AsyncListView.New(CommonRewardGroupQualityRender, self.node_list.probability_all_list) 
        self.probability_all_list:SetStartZeroIndex(false)
        self.probability_all_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize, self))
        -- self.group_list:SetSelectCallBack(BindTool.Bind(self.OnClickGradeItem, self))
    end
end

function TipsCommonRewardProGroupView:OnFlush()
    if not self.data then
        return
    end
    local group_count = #self.data.group_list
    self.node_list.group_list:SetActive(group_count > 1 )
    if group_count > 1 then
        self.node_list.probability_all_list.rect.sizeDelta = Vector2(870, 350)
    else
        self.node_list.probability_all_list.rect.sizeDelta = Vector2(870, 406)
    end
    
    self.group_list:SetDataList(self.data.group_list)

    if self.data.target_grade then
        self.group_list:JumpToIndex(self.data.target_grade)
    else
        self.group_list:JumpToIndex(1)
    end
end

function TipsCommonRewardProGroupView:OnClickGradeItem(cell)
    if (not cell)  or not cell.data then
        return
    end

    self.cur_group = cell.index
    self.probability_all_list:SetDataList(cell.data.quality_list)
    self.probability_all_list:JumpToIndex(1)
end

function TipsCommonRewardProGroupView:ChangeCellSize(data_index)
	if self.cur_group and self.data.group_list[self.cur_group] then
        if self.data.group_list[self.cur_group].quality_list[data_index + 1] then
            local probability_list = self.data.group_list[self.cur_group].quality_list[data_index + 1].probability_list
            local count = math.ceil(#probability_list / 3)
            local space_value = count > 0 and (count - 1) * 15 or 0
            return count * 100 + 40 + space_value
        end
	end

    return 0
end

----------------------------------------------------------------------------------
CommonRewardProGroupItemRender = CommonRewardProGroupItemRender or BaseClass(BaseRender)

function CommonRewardProGroupItemRender:LoadCallBack()
    if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list["item_root"])
	end
end

function CommonRewardProGroupItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil

    end 
end

function CommonRewardProGroupItemRender:OnFlush()
    if not self.data then
        return
    end
    
    self.item_cell:SetData({item_id = self.data.item_id})
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.text_name.text.text = ToColorStr(item_name, color) 
    self.node_list.text_probability.text.text = string.format(Language.TianShenLingHe.ProbabilityStr, self.data.random_count)
end
-------------------------------------------------------------------------------
CommonRewardGroupRender = CommonRewardGroupRender or BaseClass(BaseRender)

function CommonRewardGroupRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.text_normal.text.text = self.data.group_name
    self.node_list.text_hl.text.text = self.data.group_name
end

function CommonRewardGroupRender:OnSelectChange(is_select)
    self.node_list.normal:SetActive(not is_select)
    self.node_list.hl:SetActive(is_select)
end

-------------------------------------------------------------------------------
CommonRewardGroupQualityRender = CommonRewardGroupQualityRender or BaseClass(BaseRender)

function CommonRewardGroupQualityRender:LoadCallBack()
    if self.probability_list == nil then
		    -- 普通概率列表
            self.probability_list = AsyncBaseGrid.New()
            self.probability_list:CreateCells({col = 3, change_cells_num = 1, list_view = self.node_list.probability_list,
                                                        assetBundle = "uis/view/common_panel_prefab",
                                                        assetName = "reawrd_probability_render",
                                                        itemRender = CommonRewardProGroupItemRender, })
            self.probability_list:SetStartZeroIndex(false)
	end
end
function CommonRewardGroupQualityRender:__delete()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end 
end

function CommonRewardGroupQualityRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.text_title.text.text = self.data.title
    if self.data.probability_list and #self.data.probability_list > 0 then

        local count = #self.data.probability_list
        self.probability_list:SetDataList(self.data.probability_list)

        local cell_count =  math.ceil(count / 3)
        local space_value = cell_count > 0 and (cell_count - 1) * 15 or 0
        self.node_list.probability_list.rect.sizeDelta = Vector2(870, cell_count * 100 + space_value)
        self.view.layout_element.minHeight = cell_count * 100 + 40 + space_value
    else
        self.node_list.probability_list:SetActive(false)
    end

end


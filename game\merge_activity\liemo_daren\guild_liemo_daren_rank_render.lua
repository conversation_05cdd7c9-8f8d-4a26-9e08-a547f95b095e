--猎魔达人_仙盟猎魔榜
GuildLieMoDaRenRankRender = GuildLieMoDaRenRankRender or BaseClass(BaseRender)

function GuildLieMoDaRenRankRender:LoadCallBack()

end

function GuildLieMoDaRenRankRender:__delete()

end

function GuildLieMoDaRenRankRender:OnFlush()
    if not self.data then
        return
    end

    local bg_indx = self.index > 3 and 4 or self.index
    local bundle, asset = ResPath.GetNoPackPNG("a3_hfhd_lmzq_zt"..bg_indx)
    self.node_list["bg"].image:LoadSprite(bundle, asset, function()
        -- self.node_list["bg"].image:SetNativeSize()
    end)

    if self.index <= 3 then
        --local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset, function()
            self.node_list["rank_img"].image:SetNativeSize()
        end)
    else
        self.node_list["rank_text"].text.text = self.index
    end
    self.node_list["rank_img"]:SetActive(self.index <= 3)
    self.node_list["rank_text"]:SetActive(self.index > 3)

    --虚位以待逻辑
    if self.data.is_empty_value then
        self.node_list.guild_name.text.text = Language.LieMoDaRen.XuWeiYiDai
        self.node_list.score.text.text = ""
        return
    end

    self.node_list.guild_name.text.text = self.data.guild_name
    self.node_list.score.text.text = self.data.rank_value
end

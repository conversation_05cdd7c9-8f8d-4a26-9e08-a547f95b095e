
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_wg_data")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_wg_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_break_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_inherit_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_succinct_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_strengthen_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_alchemy_attr_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_succinct_pre_view")
require("game/control_beasts/control_beasts_inner_alchemy/control_beasts_cultivate_inherit_pre_view")

ControlBeastsCultivateWGCtrl = ControlBeastsCultivateWGCtrl or BaseClass(BaseWGCtrl)
function ControlBeastsCultivateWGCtrl:__init()
	if ControlBeastsCultivateWGCtrl.Instance ~= nil then
		ErrorLog("[ControlBeastsCultivateWGCtrl] attempt to create singleton twice!")
		return
	end

	self.beasts_cultivate_view = ControlBeastsCultivateWGView.New(GuideModuleName.ControlBeastsCultivateWGView)
	self.beasts_cultivate_data = ControlBeastsCultivateWGData.New()
	self.control_beasts_alchemy_attr_view = ControlBeastsAlchemyAttrView.New()
	self.beasts_cultivate_succinct_pre_view = ControlBeastsSuccinctPreView.New()
	self.beasts_cultivate_inherit_pre_view = ControlBeastsInheritPreView.New()
	self:RegisterAllProtocols()
	ControlBeastsCultivateWGCtrl.Instance = self	
end

function ControlBeastsCultivateWGCtrl:__delete()
	ControlBeastsCultivateWGCtrl.Instance = nil

	if self.beasts_cultivate_view then
		self.beasts_cultivate_view:DeleteMe()
		self.beasts_cultivate_view = nil
	end

	if self.beasts_cultivate_data then
		self.beasts_cultivate_data:DeleteMe()
		self.beasts_cultivate_data = nil
	end

	if self.control_beasts_alchemy_attr_view then
		self.control_beasts_alchemy_attr_view:DeleteMe()
		self.control_beasts_alchemy_attr_view = nil
	end
end

-- 物品变化
function ControlBeastsCultivateWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.beasts_cultivate_data:IsSlotUplevelItem(change_item_id) then
		self:FlushCurShowView()
		RemindManager.Instance:Fire(RemindName.BeastsAlchemyStrengthen)
		RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
	elseif self.beasts_cultivate_data:IsEquipWordsRefreshItem(change_item_id) then
		self:FlushCurShowView()
		RemindManager.Instance:Fire(RemindName.BeastsAlchemySuccinct)
		RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
	elseif self.beasts_cultivate_data:IsEquipInheritanceItem(change_item_id) then	
		self:FlushCurShowView()
		RemindManager.Instance:Fire(RemindName.BeastsAlchemyInherit)
	end
end

-- 注册协议
function ControlBeastsCultivateWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSBeastEquipClientOperateReq)
	self:RegisterProtocol(CSBeastEquipDecomposeOperateReq)
	self:RegisterProtocol(SCBeastEquipAllInfo, "OnSCBeastEquipAllInfo")
	self:RegisterProtocol(SCBeastEquipSingleInfo, "OnSCBeastEquipSingleInfo")
	self:RegisterProtocol(SCBeastEquipMsgBag, "OnSCBeastEquipMsgBag")
	self:RegisterProtocol(SCBeastEquipMsgBagGrid, "OnSCBeastEquipMsgBagGrid")
end

-- 幻兽内丹操作请求
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateReq(operate_type, param_1, param_2, param_3, param_4)
    local protocol = ProtocolPool.Instance:GetProtocol(CSBeastEquipClientOperateReq)
 	protocol.operate_type = operate_type or 0
 	protocol.param_1 = param_1 or -1
 	protocol.param_2 = param_2 or -1
 	protocol.param_3 = param_3 or -1
	protocol.param_4 = param_4 or -1
 	protocol:EncodeAndSend()
end

-- 内丹镶嵌 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperatePut(fight_slot, slot, grid_index)
	-- print_error("内丹镶嵌", fight_slot, slot, grid_index)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_EQUIP_PUT, fight_slot, slot, grid_index)
end

-- 内丹卸下 p1:出战位 p2:内丹槽位
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateUnPut(fight_slot, slot)
	-- print_error("内丹卸下", fight_slot, slot)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_EQUIP_UN_PUT, fight_slot, slot)
end

-- 内丹槽位升级 p1:出战位 p2:内丹槽位
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateUpLevel(fight_slot, slot)
	-- print_error("内丹槽位升级", fight_slot, slot)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_EQUIP_UP_LEVEL, fight_slot, slot)
end

-- 内丹洗练 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index p4:锁定的词条flag(二进制低位到高位)
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateRefresh(fight_slot, slot, grid_index, lock_flag)
	-- print_error("内丹洗练", fight_slot, slot, grid_index, lock_flag)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_EQUIP_REFRESH, fight_slot, slot, grid_index, lock_flag)
end

-- 词条替换 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateReplace(fight_slot, slot, grid_index)
	-- print_error("内丹替换", fight_slot, slot, grid_index)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_EQUIP_REPLACE, fight_slot, slot, grid_index)
end

-- 内丹传承 p1:出战位 p2:内丹槽位 p3:接受传承幻兽装备背包grid_index p4:给予传承的幻兽装备背包
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateInheritance(fight_slot, slot, grid_index, stuff_grid_id)
	-- print_error("内丹传承", fight_slot, slot, grid_index, stuff_grid_id)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_EQUIP_INHERITANCE, fight_slot, slot, grid_index, stuff_grid_id)
end

-- 内丹分享 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index 
function ControlBeastsCultivateWGCtrl:SendBeastEquipClientOperateShare(fight_slot, slot, grid_index)
	-- print_error("内丹分享", fight_slot, slot, grid_index)
	self:SendBeastEquipClientOperateReq(BEAST_EQUIP_OPERATE_TYPE.BEAST_EQUIP_OPERATE_TYPE_SHARE, fight_slot, slot, grid_index)
end

-- 幻兽内丹分解请求
function ControlBeastsCultivateWGCtrl:CSBeastEquipDecomposeOperateReq(decompose_list)
    local protocol = ProtocolPool.Instance:GetProtocol(CSBeastEquipDecomposeOperateReq)
 	protocol.count = #decompose_list
 	protocol.decompose_list = decompose_list
 	protocol:EncodeAndSend()
end

-- 幻兽装备总信息
function ControlBeastsCultivateWGCtrl:OnSCBeastEquipAllInfo(protocol)
	-- print_error("幻兽装备总信息", protocol)
	self.beasts_cultivate_data:SetBeastEquipInfo(protocol)
	ControlBeastsWGCtrl.Instance:FlushCurShowView()
	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
	RemindManager.Instance:Fire(RemindName.BeastsAlchemyStrengthen)
	RemindManager.Instance:Fire(RemindName.BeastsAlchemySuccinct)
	RemindManager.Instance:Fire(RemindName.BeastsAlchemyInherit)
end

-- 幻兽装备单个槽位信息
function ControlBeastsCultivateWGCtrl:OnSCBeastEquipSingleInfo(protocol)
	-- print_error("幻兽装备单个槽位信息", protocol)
	self.beasts_cultivate_data:UpdateBeastEquipInfo(protocol)
	ControlBeastsWGCtrl.Instance:FlushCurShowView()
	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
end

-- 驭兽装备 - 背包信息
function ControlBeastsCultivateWGCtrl:OnSCBeastEquipMsgBag(protocol)
	-- print_error("驭兽装备 - 背包信息", protocol)
	self.beasts_cultivate_data:SetBeastEquipMsgBag(protocol)
	ControlBeastsWGCtrl.Instance:FlushCurShowView()
	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
end

-- 驭兽装备 - 背包信息变化
function ControlBeastsCultivateWGCtrl:OnSCBeastEquipMsgBagGrid(protocol)
	-- print_error("驭兽装备 - 背包信息变化", protocol)
	self.beasts_cultivate_data:UpdateBeastEquipMsgBagGrid(protocol)
	ControlBeastsWGCtrl.Instance:FlushCurShowView()
	self:FlushCurShowView()
	RemindManager.Instance:Fire(RemindName.BeastsInnerAlchemy)
end

-- 驭兽内丹，操作回调
function ControlBeastsCultivateWGCtrl:OnSCBeastEquipOperateCallBack(operate_type, result)
	if result == 1 then
		if operate_type == MODULE_OPERATE_TYPE.OP_BEAST_EQUIP_WORDS_REFRESH then			-- 洗练成功
			if self.beasts_cultivate_view:IsOpen() then
				self.beasts_cultivate_view:CultivateOperateFinalEffect(UIEffectName.hsnd_clcg)
			end
		elseif operate_type == MODULE_OPERATE_TYPE.OP_BEAST_EQUIP_WORDS_INHERITANCE then	-- 传承成功
			if self.beasts_cultivate_view:IsOpen() then
				self.beasts_cultivate_view:CultivateOperateFinalEffect(UIEffectName.hsnd_cccg)
			end
		end
	end
end

-- 打开修炼面板
function ControlBeastsCultivateWGCtrl:OpenCultivateView(aim_hole_id, slot_index)
	self.beasts_cultivate_view:SetCurrSelectAlchemy(aim_hole_id, slot_index)
	if not self.beasts_cultivate_view:IsOpen() then
		self.beasts_cultivate_view:Open()
	else
		self.beasts_cultivate_view:Flush()
	end
end

function ControlBeastsCultivateWGCtrl:FlushCurShowView(param_t, key)
	-- print_error("刷新当前界面")
	if self.beasts_cultivate_view:IsOpen() then
		self.beasts_cultivate_view:FlushCurShowView(param_t, key)
	end
end

function ControlBeastsCultivateWGCtrl:OpenBeastsAlchemyAttrView(fight_slot)
	self.control_beasts_alchemy_attr_view:SetNowFightSlot(fight_slot)
	if not self.control_beasts_alchemy_attr_view:IsOpen() then
		self.control_beasts_alchemy_attr_view:Open()
	else
		self.control_beasts_alchemy_attr_view:Flush()
	end
end

function ControlBeastsCultivateWGCtrl:OpenCultivateSuccinctPreView(equip_item_id, beast_slot_index)
	self.beasts_cultivate_succinct_pre_view:SetNowPreItemId(equip_item_id, beast_slot_index)
	if not self.beasts_cultivate_succinct_pre_view:IsOpen() then
		self.beasts_cultivate_succinct_pre_view:Open()
	else
		self.beasts_cultivate_succinct_pre_view:Flush()
	end
end

function ControlBeastsCultivateWGCtrl:OpenCultivateInheritPreView(pre_left_data, pre_right_data)
	self.beasts_cultivate_inherit_pre_view:SetInheritPreData(pre_left_data, pre_right_data)
	if not self.beasts_cultivate_inherit_pre_view:IsOpen() then
		self.beasts_cultivate_inherit_pre_view:Open()
	else
		self.beasts_cultivate_inherit_pre_view:Flush()
	end
end
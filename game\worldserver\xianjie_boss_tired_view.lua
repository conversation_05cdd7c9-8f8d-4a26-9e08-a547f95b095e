--------------------------------------------------
-- 仙界boss怒气值
--------------------------------------------------
XianJieBossTiredView = XianJieBossTiredView or BaseClass(SafeBaseView)

function XianJieBossTiredView:__init()
	self.is_modal = false
	self.is_any_click_close = false
    self.is_safe_area_adapter = true
    self.view_name = "XianJieBossTiredView"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_xianjie_tired")
    self.is_open_drive = false
    
    self.view_layer = UiLayer.MainUILow
end

function XianJieBossTiredView:__delete()
end

function XianJieBossTiredView:ReleaseCallBack()
    if self.delay_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end
end

function XianJieBossTiredView:CloseCallBack()
    CountDownManager.Instance:RemoveCountDown("xianjie_count_down")
end

function XianJieBossTiredView:LoadCallBack()
    self.is_open_drive = false
    XUI.AddClickEventListener(self.node_list.btn_des,BindTool.Bind1(self.BrowsePlayInfo, self))
end

-- 玩法介绍
function XianJieBossTiredView:BrowsePlayInfo()
	local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.XianJieBoss.PlayTitle)
    local scene_type = Scene.Instance:GetSceneType()
	local str = FuBenWGData.GetFbSceneConfig(scene_type)
	role_tip:SetContent(str.fb_desc)
end

function XianJieBossTiredView:SetAinClick(is_on)

end

function XianJieBossTiredView:ShowIndexCallBack()
    self.scene_type = Scene.Instance:GetSceneType()
    -- self:AddDelayTime()
end

function XianJieBossTiredView:OnFlush()
    local num, end_time = XianJieBossWGData.Instance:GetXianJieTiredValue()
    num = num < 100 and num or 100
    -- [0.06, 0.94]
    local new_min_value, new_max_value = 0.06, 0.94
    local slider_value = new_min_value + ((new_max_value - new_min_value) / 1) * (num / 100)
    self.node_list["prog9_angry_value"].slider.value = slider_value
    self.node_list["ph_angry_value"].text.text = string.format(Language.XianJieBoss.AngerVlaue, num, 100)

    local exit_time = XianJieBossWGData.Instance:GetMonsterChallengeEndTime()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local delay = exit_time - 15 - cur_time
    --print_error("打印信息exit_time",exit_time, cur_time, end_time)
    local need_show_tired = num >= 100 and end_time > cur_time

    local is_tired
    local is_need_countdown = false

    if delay <= 0 and need_show_tired then
        is_tired = end_time < exit_time
        is_need_countdown = true
    elseif delay <= 0 then
        is_tired = false
        is_need_countdown = true
    elseif need_show_tired then
        is_tired = true
        is_need_countdown = true
    end

    self.node_list["layout_bubble"]:SetActive(is_need_countdown)
    if is_need_countdown and not CountDownManager.Instance:HasCountDown("xianjie_count_down") then
        local cur_end_time = is_tired and end_time or exit_time
        if cur_end_time + cur_time > 0 then
            self:UpdateCountDownTime(is_tired, cur_time, cur_end_time)
            CountDownManager.Instance:AddCountDown("xianjie_count_down",
                        BindTool.Bind(self.UpdateCountDownTime, self, is_tired),
                        BindTool.Bind(self.CompleteCountDownTime, self),
                        cur_end_time, nil, 0.5)
        end
    end
end


function XianJieBossTiredView:AddDelayTime()
    local exit_time = XianJieBossWGData.Instance:GetMonsterChallengeEndTime()
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    local delay = exit_time - 15 - cur_time
    if delay > 0 then
        if not self.delay_timer then
            self.delay_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.Flush, self), delay)
        end
    else
        self:Flush()
    end
end

function XianJieBossTiredView:UpdateCountDownTime(is_tired, elapse_time, total_time)
    local last_time = math.floor(total_time - elapse_time)
    if is_tired then
        self.node_list["lbl_angry_word"].text.text = string.format(Language.XianJieBoss.TiredDes, last_time)
    else
        self.node_list["lbl_angry_word"].text.text = Language.XianJieBoss.ExitDes
    end

    --self.node_list["ph_time_count"].text.text = last_time

    if last_time <= 5.5 and not self.is_open_drive then
        self.is_open_drive = true
        BossWGCtrl.Instance:OpenDriveView(self.scene_type)
    end
end

function XianJieBossTiredView:CompleteCountDownTime()
    -- FuBenWGCtrl.Instance:SendLeaveFB()
	self:Close()
end
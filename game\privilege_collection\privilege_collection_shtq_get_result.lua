PrivilegeCollectionSHTQGetResultView = PrivilegeCollectionSHTQGetResultView or BaseClass(SafeBaseView)

function PrivilegeCollectionSHTQGetResultView:__init()
    self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Half
    self.view_name = "PrivilegeCollectionSHTQGetResultView"
	self:SetMaskBg(true, true, nil, BindTool.Bind(self.OnClickGetBtn, self))

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_shtq_get_result_view")
end

function PrivilegeCollectionSHTQGetResultView:LoadCallBack()
	self.reward_list = AsyncListView.New(PCSHTQGetResultItemCellRender, self.node_list["reward_list"])
	self.reward_list:SetStartZeroIndex(true)

	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind1(self.OnClickGetBtn, self))
end

function PrivilegeCollectionSHTQGetResultView:ReleaseCallBack()
	if nil ~= self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function PrivilegeCollectionSHTQGetResultView:OnFlush()
	local show_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(self.data.seq)
	if not show_cfg then
		return
    end

	self.reward_list:SetDataList(show_cfg.reward_item)

	self.node_list.exp_text.text.text = Language.PrivilegeCollection.SHTQResultExpStr

	self.node_list.exp_num.text.text = CommonDataManager.ConverExp(self.data.save_exp)

	local last_lv_str = string.format(Language.PrivilegeCollection.SHTQResultLv, self.data.last_role_level)
	self.node_list.lv_text.text.text = string.format(Language.PrivilegeCollection.SHTQResultLvStr, last_lv_str)
	self.node_list.lv_num.text.text = string.format(Language.PrivilegeCollection.SHTQResultLv, self.data.last_role_level + self.data.can_up_level)

	local last_rate_str = string.format(Language.PrivilegeCollection.SHTQSpAttrNextStr, self.data.last_rate * 100)
	self.node_list.rate_text.text.text = string.format(Language.PrivilegeCollection.SHTQResultRateStr, last_rate_str)
	self.node_list.rate_num.text.text = string.format(Language.PrivilegeCollection.SHTQSpAttrNextStr, show_cfg.up_num * 100)
end

function PrivilegeCollectionSHTQGetResultView:SetData(data)
	self.data = data

	if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function PrivilegeCollectionSHTQGetResultView:OnClickGetBtn()
	self:Close()
	local exp_data = {}
	exp_data.last_role_level = self.data.last_role_level
	exp_data.save_exp = self.data.save_exp
	PrivilegeCollectionWGCtrl.Instance:OpenSHTQExpTips(exp_data)
end

---------------------------------------PCSHTQGetResultItemCellRender---------------------------------------
PCSHTQGetResultItemCellRender = PCSHTQGetResultItemCellRender or BaseClass(BaseRender)
function PCSHTQGetResultItemCellRender:__init()

end

function PCSHTQGetResultItemCellRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function PCSHTQGetResultItemCellRender:LoadCallBack()
	self.cell = ItemCell.New(self.node_list["item_cell"])
end

function PCSHTQGetResultItemCellRender:OnFlush()
	if not self.data then return end

	self.cell:SetData({item_id = self.data.item_id, num = self.data.num, is_bind = self.data.is_bind})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	self.node_list.name.text.text = name
end
BaseViewEffect = BaseViewEffect or BaseClass()

function BaseViewEffect:__init()
	self.is_need_play_tween = false
	self.open_tween = UITween.ShowFadeUp
	self.close_tween = UITween.HideFadeUp
	self.open_tweener = nil
	self.close_tweener = nil
	self.root_node_transform = nil
	self.open_source_view_root = nil
	self.is_screen_shot_opening = false
	self.is_set_ui_render_type_to_base = false
end

function BaseViewEffect:__delete()
end

function BaseViewEffect:Clear()
	self.is_screen_shot_opening = false
	self.root_node_transform = nil
end

function BaseViewEffect:SetGameObjRootTransform(root_node_transform)
	self.root_node_transform = root_node_transform
end

function BaseViewEffect:SetIsPlayTween(is_need_play_tween, open_tween, close_tween, open_source_view)
	self.is_need_play_tween = is_need_play_tween
	self.open_tween = open_tween
	self.close_tween = close_tween
	self.open_source_view_root = open_source_view
end

function BaseViewEffect:TryPlayTweenOpen()
	self:TryStopTweenClose()

	if nil ~= self.open_tween and self.open_tweener == nil and self.is_need_play_tween then
		local tween, update_func, complete_func = self.open_tween(self, self.open_source_view_root)
		self.open_tweener = {tween = tween, update_func = update_func, complete_func = complete_func}
		if nil ~= tween then
			tween:OnUpdate(function ()
				if nil ~= update_func then
					update_func()
				end
			end)
			tween:OnComplete(function ()
				if nil ~= complete_func then
					complete_func()
				end
				self.open_tweener = nil
			end)
		end
	end
end

function BaseViewEffect:TryPlayTweenClose(play_complete_callback)
	self:TryStopTweenOpen()
	if nil ~= self.close_tween and self.close_tweener == nil and self.is_need_play_tween then
		local tween, update_func, complete_func = self.close_tween(self, self.open_source_view_root)
		self.close_tweener = {tween = tween, update_func = update_func, complete_func = complete_func}
		if nil ~= tween then
			tween:OnUpdate(function ()
				if nil ~= update_func then
					update_func()
				end
			end)
			tween:OnComplete(function ()
				if nil ~= complete_func then
					complete_func()
				end
				self.close_tweener = nil

				if nil ~= play_complete_callback then
					play_complete_callback()
				end
			end)
		else
			-- 当 tween 为 nil 时，立即执行完成回调，确保界面能正常关闭
			self.close_tweener = nil
			if nil ~= play_complete_callback then
				play_complete_callback()
			end
		end
	else
		if nil ~= play_complete_callback then
			play_complete_callback()
		end
	end
end

function BaseViewEffect:TryStopTweenOpen()
	if self.open_tweener then
		self.open_tweener.tween:Kill()
		if self.open_tweener.complete_func then
			self.open_tweener.complete_func()
		end
		self.open_tweener = nil
	end
end

function BaseViewEffect:TryStopTweenClose()
	if self.close_tweener then
		self.close_tweener.tween:Kill()
		if self.close_tweener.complete_func then
			self.close_tweener.complete_func()
		end
		self.close_tweener = nil
	end
end

----[[ UI to Base
function BaseViewEffect:TryResetUICameraRenderType()
	if not IsNil(UICamera) then
		if self.is_set_ui_render_type_to_base then
			self.is_set_ui_render_type_to_base = false
			SafeBaseView.ResetUICameraRenderType()
		end
	end
end

function BaseViewEffect:TrySetUICameraRenderTypeToBase()
	if not IsNil(UICamera) then
		if not self.is_set_ui_render_type_to_base then
			self.is_set_ui_render_type_to_base = true
			SafeBaseView.SetUICameraRenderTypeToBase()
		end
	end
end
--]]
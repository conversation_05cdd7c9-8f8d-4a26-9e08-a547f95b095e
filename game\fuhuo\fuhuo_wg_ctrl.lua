require("game/fuhuo/fuhuo_view")
require("game/fuhuo/fuhuo_nobtn_view")
require("game/fuhuo/fuhuo_equip_fb_view")
require("game/boss/bosspanel/be_killed_view")
require("game/boss/bosspanel/shenyuan_boss_fuhuo_view")
require("game/eternal_night/eternal_night_fuhuo_view")
require("game/worlds_no1/worlds_no1_fuhuo_view")
require("game/cross_flag_grabbing_battlefield/cross_flag_grabbing_battlefield_fuhuo_view")

--require("game/boss/bosspanel/be_killed_vip_view")
--require("game/boss/bosspanel/fuhuo_killed_out")
-- 复活
FuhuoWGCtrl = FuhuoWGCtrl or BaseClass(BaseWGCtrl)
function FuhuoWGCtrl:__init()
	if FuhuoWGCtrl.Instance ~= nil then
		ErrorLog("[FuhuoWGCtrl] attempt to create singleton twice!")
		return
	end
	FuhuoWGCtrl.Instance = self
	self.old_guaiji_type = GUAI_JI_TYPE.NOT
	self.view = FuhuoView.New(GuideModuleName.FuhuoView)
	self.nobtn_view = FuhuoNoBtnView.New(GuideModuleName.FuhuoNoBtnView)
	self.equp_fb_view = FuhuoEquipFbView.New(GuideModuleName.FuhuoEquipFbView)
    self.killed_in_boss_view = BeKilledView.New(GuideModuleName.BeKilledView)
    self.shenyuan_view = ShenYuanBossFuhuoView.New()
	self.eternal_night_fuhuo_view = EternalNightFuHuoView.New(GuideModuleName.EternalNightFuHuoView)
	self.worlds_no1_fuhuo_view = WorldsNO1FuhuoView.New(GuideModuleName.WorldsNO1FuhuoView)
	self.cross_flag_grabbing_battlefield_fuhuo_view = CrossFlagGrabbingBattlefieldFuHuoView.New()
	--self.killed_in_vip_view = BeKilledInVIPView.New(GuideModuleName.BeKilledInVIPView)
	--self.killed_out_view = BeKilledOutView.New(GuideModuleName.BeKilledOutView)
	self.last_is_auto_revive = false

	self:RegisterAllEvents()
end

function FuhuoWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
	if nil ~= self.nobtn_view then
		self.nobtn_view:DeleteMe()
		self.nobtn_view = nil
	end
	
	if nil ~= self.worlds_no1_fuhuo_view then
		self.worlds_no1_fuhuo_view:DeleteMe()
		self.worlds_no1_fuhuo_view = nil
	end

	if nil ~= self.equp_fb_view then
		self.equp_fb_view:DeleteMe()
		self.equp_fb_view = nil
	end

	if nil ~= self.killed_in_boss_view then
		self.killed_in_boss_view:DeleteMe()
		self.killed_in_boss_view = nil
    end
    
	if nil ~= self.shenyuan_view then
		self.shenyuan_view:DeleteMe()
		self.shenyuan_view = nil
	end
   
	if nil ~= self.eternal_night_fuhuo_view then
		self.eternal_night_fuhuo_view:DeleteMe()
		self.eternal_night_fuhuo_view = nil
    end

	--if nil ~= self.killed_in_vip_view then
	--	self.killed_in_vip_view:DeleteMe()
	--	self.killed_in_vip_view = nil
	--end

	--if self.killed_out_view ~= nil then
	--	self.killed_out_view:DeleteMe()
	--	self.killed_out_view = nil
	--end

	if nil ~= self.cross_flag_grabbing_battlefield_fuhuo_view then
		self.cross_flag_grabbing_battlefield_fuhuo_view:DeleteMe()
		self.cross_flag_grabbing_battlefield_fuhuo_view = nil
	end

	self.realive_cost_all_gold = nil
	FuhuoWGCtrl.Instance = nil
end

function FuhuoWGCtrl:RegisterAllEvents()
	RoleWGData.Instance:NotifyAttrChange(BindTool.Bind1(self.RoleDataChangeCallback, self), {"hp"})
	self:BindGlobalEvent(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind1(self.OnLoadingComplete, self))
end

function FuhuoWGCtrl:SetBeKilledFuhuoCallback(common_callback,here_callback)
	if self.killed_in_boss_view then
		self.killed_in_boss_view:SetFuhuoCallback(common_callback,here_callback)
	end
end

function FuhuoWGCtrl:SetFuhuoCallback(common_callback,here_callback)
	if self.view then
		self.view:SetFuhuoCallback(common_callback,here_callback)
	end
end

function FuhuoWGCtrl:OnLoadingComplete()
	if GameVoManager.Instance:GetMainRoleVo().hp <= 0 then
		self:Open()
	end
end
function FuhuoWGCtrl:SetOpenParam(death_count_in_day,killer_objid)
	local view = self:GetFuhuoView()
	if view then
		-- view:SetOpenParam(death_count_in_day,killer_objid)
	end
end

-- 自动复活不用弹框
function FuhuoWGCtrl:AutoRevive(fuhuo_view)
	self.last_is_auto_revive = false
	if fuhuo_view == self.equp_fb_view or fuhuo_view == self.eternal_night_fuhuo_view then
		return false
	end

	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if 0 == fb_scene_cfg.is_auto_revive or 0 == fb_scene_cfg.is_resurgence then
		return false
	end

	local shield_hearsay = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_REVIVE)
	if not shield_hearsay then
		return false
	end

	local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	local free_count = RechargeWGData.Instance:GetFreeFuHuoCount()
	local num = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE) -- 有复活石使用复活石复活，没有仙玉复活

	if is_active and free_count > 0 then -- vip投资免费复活
		self.last_is_auto_revive = true
		FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.TOZI_FREE)
	elseif num > 0 then
		self.last_is_auto_revive = true
        GuajiWGCtrl.Instance:ClearAtkCache()
        FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_STUFF, 0, -1)
    else
        local main_role_vo = RoleWGData.Instance:GetRoleInfo()
        local cost = self:GetFuhuoGold()
        if cost > main_role_vo.bind_gold + main_role_vo.gold then return false end
        self.last_is_auto_revive = true
        GuajiWGCtrl.Instance:ClearAtkCache()
        FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
	end

	return true
end

function FuhuoWGCtrl:Open(death_count_in_day,killer_objid)
    local view = self:GetFuhuoView()
	if self:AutoRevive(view) then
		return
	end
	if  killer_objid == -1 or killer_objid == nil then
		death_count_in_day = 0
		killer_objid = 0
	end

	if killer_objid ~= 0 then
		local kill_obj = Scene.Instance:GetObj(killer_objid)
		if kill_obj ~= nil and kill_obj.vo ~= nil then
			self:SetKillerName(kill_obj.vo and kill_obj.vo.name or "")
		end
	end

	local scene_type = Scene.Instance:GetSceneType()
	if view then
		local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
		if fb_scene_cfg.is_resurgence == 1 then
            view:Open()
            if scene_type == SceneType.WorldBoss and BossWGData.Instance:GetWorldBossRoleRealiveInfo().realive_count == 5 then
                view:Flush(0, "daojishi", {time = BossWGData.Instance:GetWorldBossRoleFuhuoTime()})
            else
                if fb_scene_cfg and fb_scene_cfg.free_fuhuo_time then
                    view:Flush(0, "daojishi", {time = fb_scene_cfg.free_fuhuo_time})
                end
            end
		else
			if scene_type == SceneType.CROSS_ULTIMATE_BATTLE then
				UltimateBattlefieldWGCtrl.Instance:OpenFuhuoView()
			end
		end
	elseif scene_type == SceneType.VIP_BOSS then
		FuBenWGCtrl.Instance:SetOutFbTime(TimeWGCtrl.Instance:GetServerTime() + 3.5)
	end
end

function FuhuoWGCtrl:SetLiuJieSecondView(re_time)
	self.equp_fb_view:Open()
	self.equp_fb_view:Flush(0,"daojishi",{time = re_time})
end

-- 设置击杀者名字
function FuhuoWGCtrl:SetKillerName(killer_name,killer_level,is_role, plat_type, server_id, uid, type, param)
	if Scene.Instance:GetSceneType() == SceneType.CROSS_ULTIMATE_BATTLE then
		UltimateBattlefieldWGCtrl.Instance:SetKillerName(killer_name, killer_level, is_role, plat_type, server_id, uid)
	else
        local view = self:GetFuhuoView()
        if view ~= nil then
            view:SetKillerName(killer_name,killer_level,is_role, plat_type, server_id, uid, type, param)
        end
	end
end

-- 设置击杀者等级
function FuhuoWGCtrl:SetKillerLevel(killer_level)
	-- local view = self:GetFuhuoView()
	-- if view == self.killed_in_boss_view then
	-- 	view:SetKillerLevel(killer_level)
	-- end
end

function FuhuoWGCtrl:OnMainRoleRealive()
	if self.nobtn_view:IsOpen() then
		self.nobtn_view:FuhuoCallback()
	elseif self.view:IsOpen() then
		self.view:FuhuoCallback()
	elseif self.killed_in_boss_view:IsOpen() then
		self.killed_in_boss_view:FuhuoCallback()
	elseif self.shenyuan_view:IsOpen() then
		self.shenyuan_view:FuhuoCallback()
	elseif self.eternal_night_fuhuo_view:IsOpen() then
        self.eternal_night_fuhuo_view:FuhuoCallback()
	elseif self.cross_flag_grabbing_battlefield_fuhuo_view:IsOpen() then
		self.cross_flag_grabbing_battlefield_fuhuo_view:FuhuoCallback()
	end
	if self.equp_fb_view and self.equp_fb_view.is_auto then
		self.equp_fb_view:FuhuoCallback()
	end
end

function FuhuoWGCtrl:RoleDataChangeCallback(atr_name, new_value, old_value)
    if atr_name == "hp" then
        local scene_type = Scene.Instance:GetSceneType()
		if old_value == 0 and new_value > 0 then
			local use_type = self:GetViewUseFuHuoType()
			self:OnMainRoleRealive()
			self:CloseCurView(true, use_type)
			-- 重置变身进度CD
			GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_BIANSHEN,0,0)
		elseif new_value <= 0 
			and scene_type ~= SceneType.Fb_Welkin
			and scene_type ~= SceneType.Kf_PVP
			and scene_type ~= SceneType.DEMONS_FB
			-- and scene_type ~= SceneType.CROSS_TASK_CHAIN_BOSS
			and scene_type ~= SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID
			and scene_type ~= SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI
            and scene_type ~= SceneType.ZHUANZHI_FB
			and scene_type ~= SceneType.TEAM_COMMON_TOWER_FB_1
        then
			local view = self:GetFuhuoView()
			if view ~= nil and not view:IsOpen() then
				self:Open()
			end
		end
	end
end

function FuhuoWGCtrl:GetFuhuoView()
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type  == SceneType.GongChengZhan then
		return self.nobtn_view
	elseif scene_type == SceneType.WorldBoss or scene_type == SceneType.KF_BOSS or scene_type == SceneType.SG_BOSS or
		 scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.MJ_BOSS or scene_type == SceneType.SHENGYU_BOSS
         or scene_type ==  SceneType.MJ_KF_BOSS or scene_type ==  SceneType.GUIDE_BOSS or scene_type == SceneType.VIP_BOSS
         or scene_type == SceneType.XianJie_Boss then
		return self.killed_in_boss_view
	--elseif scene_type == SceneType.TEAM_EQUIP_FB or scene_type == SceneType.JIUSHEDAO_FB then
	--	return self.killed_in_vip_view
	elseif scene_type == SceneType.TEAM_EQUIP_FB
	 	-- or scene_type == SceneType.HIGH_TEAM_EQUIP_FB
		or scene_type == SceneType.ZHUSHENTA_FB
	 	-- or scene_type == SceneType.COPPER_FB
		or scene_type == SceneType.PET_FB or scene_type == SceneType.FakePetFb
	 	or scene_type == SceneType.LingHunGuangChang
	 	or scene_type == SceneType.CROSS_TASK_CHAIN_BOSS
	 	or scene_type == SceneType.Wujinjitan or scene_type == SceneType.PERSON_BOSS or scene_type == SceneType.TIAN_SHEN_FB
	 	or scene_type  == SceneType.FBCT_NEWPLAYERFB then
	 	-- or scene_type == SceneType.VIP_BOSS then
		return self.equp_fb_view
        -- return self.killed_out_view
	elseif scene_type == SceneType.FengShenBang or scene_type ==  SceneType.Kf_Honorhalls 
		-- or scene_type == SceneType.Shenyuan_boss
    	or scene_type == SceneType.Guild_Invite
    	or scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then--深渊Boss、封神榜、青云之巅,寻宝boss
		-- or scene_type == SceneType.YEZHANWANGCHENGFUBEN then
        return self.shenyuan_view
    elseif scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then  	--永夜之巅特殊复活面板
    	return self.eternal_night_fuhuo_view
    elseif scene_type == SceneType.TianShen3v3 then
        return ViewManager.Instance:GetView(GuideModuleName.TianShen3v3ReliveView)
    elseif scene_type == SceneType.WorldsNO1 then
    	return self.worlds_no1_fuhuo_view
	elseif scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
		return self.cross_flag_grabbing_battlefield_fuhuo_view
	else
		return self.view
	end
end

function FuhuoWGCtrl:FlushFuHuoView()
	local scene_type = Scene.Instance:GetSceneType()
	if self.view:IsOpen() and (scene_type == SceneType.WorldBoss or scene_type == SceneType.KF_BOSS) then
		self.view:FlushSafeView()
	end
end

function FuhuoWGCtrl:GetFuhuoGold()
	if self.realive_cost_all_gold == nil then
		local relivecfg = ConfigManager.Instance:GetAutoConfig("globalconfig_auto").other
		for k,v in ipairs(relivecfg) do
			if v.field_name == "realive_cost_all_gold" then
				self.realive_cost_all_gold = v.field_value
			end
		end
	end
	return self.realive_cost_all_gold or 0
end

function FuhuoWGCtrl:SetOldGuaJiType(guai_ji_type)
	self.old_guaiji_type = guai_ji_type
end

function FuhuoWGCtrl:GetViewUseFuHuoType()
	local view = self:GetFuhuoView()
	local use_type = FuHuoType.Common
	local flag = self.last_is_auto_revive

	if view ~= nil and view.GetUseFuHuoType ~= nil then
		use_type = view:GetUseFuHuoType()
	else
		print_error("复活面板要有GetUseFuHuoType接口", view ~= nil and view:GetViewName() or "nil")
	end

	use_type = (use_type == nil or use_type == FuHuoType.None) and FuHuoType.Common or use_type

	if view ~= nil and view:IsOpen() then
	else
		if flag then
			use_type = FuHuoType.Here
		end
	end

	return use_type
end

function FuhuoWGCtrl:CloseCurView(is_real_revive, use_type)
	local view = self:GetFuhuoView()
	local use_type = use_type or FuHuoType.Common

	if is_real_revive and self.fuhuo_must_callback ~= nil then
		self.fuhuo_must_callback(use_type)
	end
end

function FuhuoWGCtrl:GetFuhuoType()
	return self.view:GetFuhuoType()
end

function FuhuoWGCtrl:SetShenYuanFuHuoCallBack(common_callback,here_callback)
	if self.shenyuan_view then
		self.shenyuan_view:SetFuhuoCallback(common_callback,here_callback)
	end
end

--设置复活后 必会调用的回调 
--上面那些SetFuhuoCallback的回调  是基于复活界面会打开情况下的
--如果那些设置了自动复活的会跳过复活界面的打开导致复活的回调方法执行不了
function FuhuoWGCtrl:SetFuhuoMustCallback(must_callback)
	self.fuhuo_must_callback = must_callback
end

--上面设置FuhuoMustCallback 一定要记得清掉
function FuhuoWGCtrl:ClearFuhuoMustCallback()
	self.fuhuo_must_callback = nil
end

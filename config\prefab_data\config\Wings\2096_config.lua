return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "2096_rest",
				effectAsset = {
					BundleName = "model/wings/2096_prefab",
					AssetName = "2096_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "restanimation",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "",
						effectAsset = {},

					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}
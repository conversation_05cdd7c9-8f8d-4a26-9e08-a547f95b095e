function ControlBeastsCultivateWGView:LoadBreakViewCallBack()
    if nil == self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid = BeastBatchGrid.New(self)
        self.beasts_alchemy_bag_grid:SetStartZeroIndex(false)
        self.beasts_alchemy_bag_grid:SetIsShowTips(false)
        self.beasts_alchemy_bag_grid:SetNoSelectState(false)
        self.beasts_alchemy_bag_grid:SetIsMultiSelect(true)   

        local bundle = "uis/view/control_beasts_alchemy_ui_prefab"
        local asset = "beasts_alchemy_bag_item"
        self.beasts_alchemy_bag_grid:CreateCells({
            col = 3,
            cell_count = BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_BAG_NUM + 5, 
            list_view = self.node_list["beasts_alchemy_bag_grid"],
            assetBundle = bundle, 
            assetName = asset, 
            itemRender = BeastAlchemyBatchSelectRender
        })
        self.beasts_alchemy_bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectBeastAlchemyBagCellCallBack, self))
	end

    if not self.alchemy_break_list then
        self.alchemy_break_list = AsyncListView.New(ItemCell, self.node_list.alchemy_break_list)
    end

    -- 品质选择查看列表
	if not self.select_color_list then
		self.select_color_list = AsyncListView.New(BeastsDesposeColorSelectItemRender, self.node_list.select_color_list)
		self.select_color_list:SetSelectCallBack(BindTool.Bind(self.ColorSelectCallBack, self))
        self.select_color_list:SetStartZeroIndex(true)
	end

    -- 星级选择查看列表
    if not self.select_type_list then
        self.select_type_list = AsyncListView.New(BeastsDesposeStarSelectItemRender, self.node_list.select_type_list)
		self.select_type_list:SetSelectCallBack(BindTool.Bind(self.TypeSelectCallBack, self))
        self.select_type_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.alchemy_break_execute_btn, BindTool.Bind(self.OnClickAlchemyBreakBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_select_color, BindTool.Bind(self.ClickBtnSelectColor, self))
    XUI.AddClickEventListener(self.node_list.btn_select_type, BindTool.Bind(self.ClickBtnSelectType, self))
    XUI.AddClickEventListener(self.node_list.close_select_color_part, BindTool.Bind(self.ClickCloseColorList, self))
    XUI.AddClickEventListener(self.node_list.close_select_type_part, BindTool.Bind(self.ClickCloseTypeList, self))
    XUI.AddClickEventListener(self.node_list.alchemy_break_clear_btn, BindTool.Bind(self.OnClickAlchemyBreakClearBtn, self))
    XUI.AddClickEventListener(self.node_list.alchemy_break_quick_btn, BindTool.Bind(self.OnClickAlchemyBreakQuickBtn, self))
end

function ControlBeastsCultivateWGView:OpenBreakViewCallBack()
end

function ControlBeastsCultivateWGView:CloseBreakViewCallBack()
end

function ControlBeastsCultivateWGView:ShowBreakViewCallBack()
    self.color_select_index = nil
	self.type_select_index = nil
    self.color_status = nil
    self.type_status = nil
end

function ControlBeastsCultivateWGView:ReleaseBreakViewCallBack()
    if self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:DeleteMe()
        self.beasts_alchemy_bag_grid = nil
    end

    if self.alchemy_break_list then
        self.alchemy_break_list:DeleteMe()
        self.alchemy_break_list = nil
    end

    if self.select_color_list then
        self.select_color_list:DeleteMe()
        self.select_color_list = nil
    end


    if self.select_type_list then
        self.select_type_list:DeleteMe()
        self.select_type_list = nil
    end

    self.color_select_index = nil
	self.type_select_index = nil
    self.color_status = nil
    self.type_status = nil
    self.now_item_list = nil
end
-----------------------------------------------------------------------------
function ControlBeastsCultivateWGView:FlushBreakViewCallBack(param_t)
	-- for k,v in pairs(param_t) do
	-- 	if k == "all" then
    --     elseif k == "main" then
    --     elseif k == "assist" then
    --     end
    -- end
    if self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:CancleAllSelectCell()
    end

    self.color_select_index = self.color_select_index or 0
	self.type_select_index = self.type_select_index or 0
    self.color_status = self.color_status or false
    self.type_status = self.type_status or false

    self.select_color_list:SetDataList(Language.Cultivation.QualityName)
    self.select_type_list:SetDataList(Language.ContralBeastsAlchemy.DesposeTypeList)
    self:FushSelectStatus()
    self:FlushBeastsAlchemyBagGrid(true)
    self:ShowBreakViewChangeHole()
end

function ControlBeastsCultivateWGView:ShowBreakViewChangeHole()
    local selected_cells = self.beasts_alchemy_bag_grid:GetAllSelectCell()
    local is_have_select_equip = #selected_cells > 0

    self.node_list.alchemy_break_has_data:CustomSetActive(is_have_select_equip)
    self.node_list.alchemy_not_break_has_data:CustomSetActive(not is_have_select_equip)
    self.node_list.alchemy_break_operate_root:CustomSetActive(is_have_select_equip)
    self.node_list.alchemy_break_quick_btn:CustomSetActive(not is_have_select_equip)

    if is_have_select_equip then
        self:FlushBreakViewDecomposeList(selected_cells)
    end
end

function ControlBeastsCultivateWGView:FlushBreakViewDecomposeList(selected_cells)
    self.now_item_list = {}
    local reward_list = {}

    local add_item = function(item_data)
        if reward_list[item_data.item_id] then
            reward_list[item_data.item_id].num = reward_list[item_data.item_id].num + item_data.num
        else
            reward_list[item_data.item_id] = {}
            reward_list[item_data.item_id].item_id = item_data.item_id
            reward_list[item_data.item_id].num = item_data.num
            reward_list[item_data.item_id].is_bind = item_data.is_bind
        end
    end

    for i, v in ipairs(selected_cells) do
        if v.equip_info and v.equip_info.item_id ~= COMMON_CONSTS.NUMBER_ZERO then
            local cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(v.equip_info.item_id)
            if cfg and cfg.decompose_item_list then
                for m, n in pairs(cfg.decompose_item_list) do
                    if n and n.item_id then
                        add_item(n)
                    end
                end
            end
        end
    end

    for k, v in pairs(reward_list) do
        if v and v.item_id then
            table.insert(self.now_item_list, v)
        end
    end

    self.alchemy_break_list:SetDataList(self.now_item_list)
end

-- 品质选择查看列表
function ControlBeastsCultivateWGView:ColorSelectCallBack(color_item, cell_index, is_default, is_click)
	if (not color_item) or (not color_item.data) then
		return
	end

	if self.color_select_index == cell_index then
		return
	end

	self.color_select_index = cell_index
    self.color_status = false
    self:FushSelectStatus()
    self:FlushBeastsAlchemyBagGrid(true, self.color_select_index, self.type_select_index)
end

-- 星级选择查看列表
function ControlBeastsCultivateWGView:TypeSelectCallBack(star_item, cell_index, is_default, is_click)
	if (not star_item) or (not star_item.data) then
		return
	end

	if self.type_select_index == cell_index then
		return
	end

	self.type_select_index = cell_index
    self.type_status = false
    self:FushSelectStatus()
    self:FlushBeastsAlchemyBagGrid(true, self.color_select_index, self.type_select_index)
end

-- 刷新选中状态
function ControlBeastsCultivateWGView:FushSelectStatus()
    local cur_color_str = Language.Cultivation.QualityName[self.color_select_index]
    local cur_type_str = Language.ContralBeastsAlchemy.DesposeTypeList[self.type_select_index]
    self.node_list.cur_select_color_text.text.text = ToColorStr(cur_color_str, ITEM_COLOR[self.color_select_index]) 
    self.node_list.cur_select_type_text.text.text = cur_type_str

    self.node_list.select_color_down:CustomSetActive(self.color_status)
    self.node_list.select_color_up:CustomSetActive(not self.color_status)
    self.node_list.select_color_list_part:CustomSetActive(self.color_status)

    self.node_list.select_type_down:CustomSetActive(self.type_status)
    self.node_list.select_type_up:CustomSetActive(not self.type_status)
    self.node_list.select_type_list_part:CustomSetActive(self.type_status)
end
--------------------------------------------------------------------------------
-- 丹药点击分解
function ControlBeastsCultivateWGView:OnClickAlchemyBreakBtn()
    local selected_cells = self.beasts_alchemy_bag_grid:GetAllSelectCell()
    if #selected_cells <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeastsAlchemy.AlchemyInheritError3)
        return
    end

    local decompose_list = {}
    for i, v in ipairs(selected_cells) do
        if v.equip_info and v.equip_info.item_id ~= COMMON_CONSTS.NUMBER_ZERO then
            table.insert(decompose_list, v.equip_info.index)
        end
    end

    ControlBeastsCultivateWGCtrl.Instance:CSBeastEquipDecomposeOperateReq(decompose_list)
end

-- 品质选择按钮点击
function ControlBeastsCultivateWGView:ClickBtnSelectColor()
    self.color_status = true
    self:FushSelectStatus()
end

-- 星级选择按钮点击
function ControlBeastsCultivateWGView:ClickBtnSelectType()
    self.type_status = true
    self:FushSelectStatus()
end

-- 关闭品质选择框
function ControlBeastsCultivateWGView:ClickCloseColorList()
    self.color_status = false
    self:FushSelectStatus()
end

-- 关闭星级选择框
function ControlBeastsCultivateWGView:ClickCloseTypeList()
    self.type_status = false
    self:FushSelectStatus()
end

-- 丹药点击清理
function ControlBeastsCultivateWGView:OnClickAlchemyBreakClearBtn()
    if self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:CancleAllSelectCell()
    end

    self:FlushBreakViewCallBack()
end

-- 丹药点击快速添加
function ControlBeastsCultivateWGView:OnClickAlchemyBreakQuickBtn()
    if self.now_show_grid_list and self.beasts_alchemy_bag_grid then
        self.beasts_alchemy_bag_grid:SetAllSelectCell(#self.now_show_grid_list)
        self.beasts_alchemy_bag_grid:RefreshSelectCellState()
        self:ShowBreakViewChangeHole()
    end
end

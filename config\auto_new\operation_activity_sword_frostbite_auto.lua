-- Y-运营活动-一剑霜寒.xls
local item_table={
[1]={item_id=37249,num=1,is_bind=1},
[2]={item_id=26191,num=1,is_bind=1},
[3]={item_id=22099,num=100,is_bind=1},
[4]={item_id=22014,num=6280,is_bind=1},
[5]={item_id=26553,num=1,is_bind=1},
[6]={item_id=26522,num=1,is_bind=1},
[7]={item_id=26507,num=1,is_bind=1},
[8]={item_id=48071,num=1,is_bind=1},
[9]={item_id=48118,num=1,is_bind=1},
[10]={item_id=30427,num=10,is_bind=1},
[11]={item_id=26357,num=5,is_bind=1},
[12]={item_id=37241,num=1,is_bind=1},
[13]={item_id=26193,num=1,is_bind=1},
[14]={item_id=22099,num=200,is_bind=1},
[15]={item_id=22014,num=10000,is_bind=1},
[16]={item_id=26559,num=1,is_bind=1},
[17]={item_id=57805,num=1,is_bind=1},
[18]={item_id=26523,num=1,is_bind=1},
[19]={item_id=26508,num=1,is_bind=1},
[20]={item_id=48071,num=2,is_bind=1},
[21]={item_id=48118,num=2,is_bind=1},
[22]={item_id=29614,num=3,is_bind=1},
[23]={item_id=26357,num=10,is_bind=1},
[24]={item_id=37282,num=1,is_bind=1},
[25]={item_id=26194,num=1,is_bind=1},
[26]={item_id=22099,num=500,is_bind=1},
[27]={item_id=22014,num=20000,is_bind=1},
[28]={item_id=26568,num=1,is_bind=1},
[29]={item_id=26524,num=1,is_bind=1},
[30]={item_id=26509,num=1,is_bind=1},
[31]={item_id=48071,num=3,is_bind=1},
[32]={item_id=48118,num=3,is_bind=1},
[33]={item_id=29614,num=5,is_bind=1},
[34]={item_id=26357,num=20,is_bind=1},
[35]={item_id=59690,num=1,is_bind=1},
[36]={item_id=59690,num=2,is_bind=1},
[37]={item_id=59690,num=4,is_bind=1},
[38]={item_id=59690,num=10,is_bind=1},
[39]={item_id=59690,num=5,is_bind=1},
[40]={item_id=59690,num=20,is_bind=1},
[41]={item_id=57804,num=1,is_bind=1},
[42]={item_id=59690,num=3,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{}
},

open_day_meta_table_map={
},
draw_num={
{consume_num=1,},
{draw_num=1,consume_num=3,},
{draw_num=2,consume_num=5,},
{draw_num=3,},
{draw_num=4,},
{draw_num=5,},
{draw_num=6,},
{draw_num=7,consume_num=15,},
{draw_num=8,consume_num=20,},
{draw_num=9,consume_num=30,},
{draw_num=10,consume_num=40,},
{draw_num=11,consume_num=50,},
{round=2,},
{round=2,},
{draw_num=2,},
{round=2,},
{round=2,},
{draw_num=5,},
{draw_num=6,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=2,},
{round=3,consume_num=2,},
{round=3,consume_num=6,},
{draw_num=2,},
{round=3,draw_num=3,},
{draw_num=4,},
{round=3,draw_num=5,},
{draw_num=6,},
{round=3,draw_num=7,},
{round=3,consume_num=40,},
{round=3,consume_num=60,},
{round=3,consume_num=80,},
{round=3,consume_num=100,}
},

draw_num_meta_table_map={
[27]=28,	-- depth:1
[17]=5,	-- depth:1
[13]=1,	-- depth:1
[18]=17,	-- depth:2
[7]=8,	-- depth:1
[4]=3,	-- depth:1
[34]=10,	-- depth:1
[33]=9,	-- depth:1
[32]=10,	-- depth:1
[31]=32,	-- depth:2
[30]=9,	-- depth:1
[29]=30,	-- depth:2
[26]=2,	-- depth:1
[24]=12,	-- depth:1
[22]=10,	-- depth:1
[21]=9,	-- depth:1
[20]=8,	-- depth:1
[19]=20,	-- depth:2
[35]=11,	-- depth:1
[16]=4,	-- depth:2
[15]=16,	-- depth:3
[14]=2,	-- depth:1
[23]=11,	-- depth:1
[36]=12,	-- depth:1
},
reward_pool={
{reward_item={[0]=item_table[1]},reward_type=2,player_guarantee=12,},
{seq=1,reward_item={[0]=item_table[2]},},
{seq=2,reward_item={[0]=item_table[3]},},
{seq=3,reward_item={[0]=item_table[4]},},
{seq=4,reward_item={[0]=item_table[5]},},
{seq=5,},
{seq=6,reward_item={[0]=item_table[6]},},
{seq=7,reward_item={[0]=item_table[7]},},
{seq=8,reward_item={[0]=item_table[8]},},
{seq=9,reward_item={[0]=item_table[9]},},
{seq=10,reward_item={[0]=item_table[10]},},
{seq=11,reward_item={[0]=item_table[11]},},
{round=2,reward_item={[0]=item_table[12]},},
{round=2,reward_item={[0]=item_table[13]},},
{round=2,reward_item={[0]=item_table[14]},},
{round=2,reward_item={[0]=item_table[15]},},
{round=2,reward_item={[0]=item_table[16]},},
{seq=5,reward_item={[0]=item_table[17]},},
{round=2,reward_item={[0]=item_table[18]},},
{round=2,reward_item={[0]=item_table[19]},},
{round=2,reward_item={[0]=item_table[20]},},
{round=2,reward_item={[0]=item_table[21]},},
{round=2,reward_item={[0]=item_table[22]},},
{round=2,reward_item={[0]=item_table[23]},},
{round=3,reward_item={[0]=item_table[24]},reward_type=1,player_guarantee=12,},
{round=3,reward_item={[0]=item_table[25]},},
{round=3,reward_item={[0]=item_table[26]},},
{round=3,reward_item={[0]=item_table[27]},},
{round=3,reward_item={[0]=item_table[28]},},
{round=3,},
{round=3,reward_item={[0]=item_table[29]},},
{round=3,reward_item={[0]=item_table[30]},},
{round=3,reward_item={[0]=item_table[31]},},
{round=3,reward_item={[0]=item_table[32]},},
{round=3,reward_item={[0]=item_table[33]},},
{round=3,reward_item={[0]=item_table[34]},}
},

reward_pool_meta_table_map={
[30]=6,	-- depth:1
[27]=3,	-- depth:1
[28]=4,	-- depth:1
[29]=5,	-- depth:1
[32]=8,	-- depth:1
[33]=9,	-- depth:1
[34]=10,	-- depth:1
[26]=2,	-- depth:1
[31]=7,	-- depth:1
[24]=12,	-- depth:1
[18]=24,	-- depth:2
[22]=10,	-- depth:1
[21]=9,	-- depth:1
[20]=8,	-- depth:1
[19]=7,	-- depth:1
[35]=11,	-- depth:1
[17]=5,	-- depth:1
[16]=4,	-- depth:1
[15]=3,	-- depth:1
[14]=2,	-- depth:1
[23]=11,	-- depth:1
[36]=12,	-- depth:1
[13]=1,	-- depth:1
},
task={
{task_type=9,dart_num=1,panel="",reward_item={[0]=item_table[35]},},
{seq=1,task_type=1,param1=10,dart_num=1,task_desc="星光魂契抽奖%s/10次",panel="TreasureHunt#treasurehunt_fuwen",reward_item={[0]=item_table[35]},},
{seq=2,task_type=1,param1=50,dart_num=2,task_desc="星光魂契抽奖%s/50次",panel="TreasureHunt#treasurehunt_fuwen",reward_item={[0]=item_table[36]},},
{seq=3,task_type=1,param1=100,task_desc="星光魂契抽奖%s/100次",panel="TreasureHunt#treasurehunt_fuwen",},
{seq=4,task_type=2,param2=4,param3=1,dart_num=2,task_desc="穿戴1个橙品质单属性魂卡",panel="MingWenView",reward_item={[0]=item_table[36]},},
{seq=5,task_type=2,param2=5,param3=1,dart_num=4,task_desc="穿戴1个红品质单属性魂卡",panel="MingWenView",reward_item={[0]=item_table[37]},},
{seq=6,task_type=2,param2=6,param3=2,dart_num=10,task_desc="穿戴1个粉品质双属性魂卡",panel="MingWenView",reward_item={[0]=item_table[38]},},
{seq=7,task_type=7,task_desc="万魂幡兑换2次",panel="MingWenView#ming_wen_dui_huan",},
{seq=8,param1=6,task_desc="实付金额达到%s/6元",},
{seq=9,param1=30,dart_num=5,task_desc="实付金额达到%s/30元",reward_item={[0]=item_table[39]},},
{seq=10,param1=68,task_desc="实付金额达到%s/68元",},
{seq=11,param1=128,dart_num=10,task_desc="实付金额达到%s/128元",reward_item={[0]=item_table[38]},},
{seq=12,param1=388,dart_num=20,task_desc="实付金额达到%s/388元",reward_item={[0]=item_table[40]},}
},

task_meta_table_map={
[11]=10,	-- depth:1
},
model_show={
{}
},

model_show_meta_table_map={
},
other_default_table={open_level=100,money_type=2,draw_item_price=40,},

open_day_default_table={start_day=1,end_day=9999,grade=1,},

draw_num_default_table={grade=1,round=1,draw_num=0,draw_item_id=59690,consume_num=10,},

reward_pool_default_table={grade=1,round=1,seq=0,reward_item={[0]=item_table[41]},reward_type=3,player_guarantee=0,reward_num_limit=1,},

task_default_table={grade=1,seq=0,task_type=8,param1=1,param2=0,param3=0,param4=0,dart_num=3,task_desc="累计登陆%s/1天",panel="recharge#recharge_cz",reward_item={[0]=item_table[42]},},

model_show_default_table={grade=1,round=3,model_show_type=1,model_bundle_name="model/wings/2096_prefab",model_asset_name=2096,model_show_itemid=37282,model_pos="0.72|0.4|0",model_rot="0|0|23.5",model_scale=0.9,}

}


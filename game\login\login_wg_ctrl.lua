require("game/login/login_wg_data")
require("game/login/login_view")
require("game/login/login_select_server_view")
require("game/login/login_create_role_view")
require("game/login/login_select_role_view")
require("game/login/server_warning_tips")
require("game/login/login_create_role_diy_view")
require("game/login/login_show_role_appearance_view")
require("game/login/login_create_role_name_view")


-- require("game/login/reconnect_view")

-- 登录
LoginWGCtrl = LoginWGCtrl or BaseClass(BaseWGCtrl)

LOGIN_STATE_PLAT_LOGIN = 0
LOGIN_STATE_SERVER_LIST = 1
LOGIN_STATE_CREATE_ROLE = 2
LOGIN_STATE_LOADING = 3
AUTO_RECONNENT_COUNT = 5		--- 自动重连次数(超过这个次数没有自动重连上则不在自动重连，踢回登录界面)
LOGIN_VERIFY_KEY = "566713d23b8810efb313d6934cf77610"

GlobalLocalRoleId = nil
GlobalLocalRoleName = nil

local UnityApplication = UnityEngine.Application
local UnitySysInfo = UnityEngine.SystemInfo
local ReconnectHandle = require("game/login/reconnect_handle")


function LoginWGCtrl:__init()
	if LoginWGCtrl.Instance ~= nil then
		print_error("[LoginWGCtrl] attempt to create singleton twice!")
		return
	end
	LoginWGCtrl.Instance = self

	self.data = LoginWGData.New()
	self.view = LoginView.New(GuideModuleName.Login)
	self.select_server_view = LoginSelectServerView.New(GuideModuleName.select_server)
	self.server_warning = ServerWarningTips.New()
	self.create_diy_view = LoginCreateRoleDIYView.New()
	self.show_role_apprerance_view = LoginShowRoleAppearanceView.New(GuideModuleName.LoginShowRoleAppearanceView)
	self.create_select_name_view = LoginCreateRoleNameView.New()
	self:RegisterAllProtocols()
	self:RegisterAllEvents()
	-- 加载品质控制器
	QualityConfig.ClearInstance()
	local loader = AllocResAsyncLoader(self, "QualityConfig")
	loader:Load(
		"misc/quality",
		"QualityConfig",
		typeof(QualityConfig),
		function(config)
			 -- 低内存系统不开启实时阴影
			if IsLowMemSystem and QualityConfig ~= nil then
				QualityConfig.SetOverrideShadowQuality(0, 0)
			end

			if config ~= nil then
				print_log("Load the QualityConfig.")
			else
				print_error("Can not load the QualityConfig")
			end
		end)

	-- emoji配置
	local emoji_loader = AllocResAsyncLoader(self, "EmojiAsset")
	emoji_loader:Load(
		"uis/emoji",
		"emoji_tex",
		typeof(TMPro.TMP_SpriteAsset),
		function(asset)
			if nil == asset then
				print_error("Emoji Sprite Asset is nil !!!")
			else
				TMPro.TMP_Settings.instance.defaultSpriteAsset = asset
			end
        end)
	local text_loader = AllocResAsyncLoader(self, "EmojiText")
	text_loader:Load(
		"uis/emoji",
		"emoji",
		typeof(UnityEngine.TextAsset),
		function(text)
			if nil == text then
				print_error("EmojiText is nil !!!")
			else
				EmojiText.BuildEmojiLookUp(text.text)
			end
        end)

	-- 创建渠道匹配器.
	AgentAdapter.New()
	-- 加载鼠标点击特效
	LoginWGCtrl.CreateClickEffectCanvas(self)

	self.on_pause_timer_out = nil
	self.on_pause_timer_enter = nil
	self.main_role_in_special_action_data = nil
end

function LoginWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	self.select_server_view:DeleteMe()
	self.select_server_view = nil
	
	self.view:DeleteMe()
	self.view = nil

	self.server_warning:DeleteMe()
	self.server_warning = nil

	self.create_diy_view:DeleteMe()
	self.create_diy_view = nil

	self.show_role_apprerance_view:DeleteMe()
	self.show_role_apprerance_view = nil

	self.create_select_name_view:DeleteMe()
	self.create_select_name_view = nil

	LoginWGCtrl.Instance = nil

	if nil ~= AgentAdapter.Instance then
		AgentAdapter.Instance:DeleteMe()
		AgentAdapter.Instance = nil
	end

	if nil ~= self.busy_alert then
		self.busy_alert = nil
	end

	-- if nil ~= self.recon_view then
	-- 	self.recon_view:DeleteMe()
	-- 	self.recon_view = nil
	-- end

	self.on_pause_timer_out = nil
	self.on_pause_timer_enter = nil
	self.main_role_in_special_action_data = nil
end

-- 加载鼠标点击特效
function LoginWGCtrl.CreateClickEffectCanvas(root)
	local UIRoot = GameObject.Find("GameRoot/UILayer").transform
	local bundle, asset = "uis/view/click_effect_canvas_prefab", "ClickEffectCanvas"
	local async_loader = AllocAsyncLoader(root, "ClickEffectCanvas")
	if UIRoot then
		async_loader:SetParent(UIRoot)
	end

	async_loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		local canvas = obj:GetComponent(typeof(UnityEngine.Canvas))
		canvas.overrideSorting = true
		canvas.sortingOrder = 30000
		canvas.worldCamera = UICamera
		canvas.transform:SetLocalScale(1, 1, 1)
		local rect = canvas.transform:GetComponent(typeof(UnityEngine.RectTransform))
		rect.anchorMax = Vector2(1, 1)
		rect.anchorMin = Vector2(0, 0)
		rect.anchoredPosition3D = Vector3(0, 0, 0)
		rect.sizeDelta = Vector2(0, 0)
	end)
end

local update_count = 0
function LoginWGCtrl:Update(now_time, elapse_time)
	update_count = update_count + 1
	InitWGCtrl:SetPercent(math.min(0.99, 0.9 + update_count / 500))
end

function LoginWGCtrl:StartLogin(complete_callback)
	PushCtrl(self)
	self.view:SetLoadCallBack(complete_callback)
	self.view:PreloadScene(chuangjue_bundle, chuangjue_asset, function ()
		PopCtrl(self)
		update_count = 0
		InitWGCtrl:SetPercent(1)
		self.view:PreLoadCG(function()
			self.view:Open()
		end)
	end)
end

function LoginWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCLoginAck, "OnLoginAck")
	self:RegisterProtocol(SCAccountKeyError, "OnAccountKeyError")
	self:RegisterProtocol(SCChangeGS, "OnSCChangeGS")
	self:RegisterProtocol(SCRoleListAck, "OnRoleListAck")
	self:RegisterProtocol(SCMergeRoleListAck, "OnMergeRoleListAck")
	self:RegisterProtocol(SCCreateRoleAck, "OnCreateRoleAck")
	self:RegisterProtocol(SCUserEnterGSAck, "OnUserEnterGSAck")
	self:RegisterProtocol(SCProfNumInfo, "OnProfNumInfo")
	-- self:RegisterProtocol(SCLHeartBeat, "OnLHeartBeat")
	self:RegisterProtocol(SCServerBusy, "OnServerBusy")
	self:RegisterProtocol(SCDisconnectNotice, "OnDisconnectNotice")
	self:RegisterProtocol(SCCheckForbidInfo, "OnCheckForbidInfo")

	self:RegisterProtocol(CSLHeartBeat)
	self:RegisterProtocol(CSLoginReq)
	self:RegisterProtocol(CSRoleReq)
	self:RegisterProtocol(CSCreateRoleReq)
	self:RegisterProtocol(CSUserEnterGSReq)
	self:RegisterProtocol(CSHeartBeat)
	self:RegisterProtocol(CSDisconnectReq)
	self:RegisterProtocol(CSSpeedUpHello)
end

function LoginWGCtrl:RegisterAllEvents()
	self.connect_login_server_handle = self:BindGlobalEvent(LoginEventType.LOGIN_SERVER_CONNECTED, BindTool.Bind(self.OnConnectLoginServer, self))
	self.disconnect_login_server_handle = self:BindGlobalEvent(LoginEventType.LOGIN_SERVER_DISCONNECTED, BindTool.Bind(self.OnDisconnectLoginServer, self))

	self.connect_game_server_handle = self:BindGlobalEvent(LoginEventType.GAME_SERVER_CONNECTED, BindTool.Bind(self.OnConnectGameServer, self))
	self.disconnect_game_server_handle = self:BindGlobalEvent(LoginEventType.GAME_SERVER_DISCONNECTED, BindTool.Bind(self.OnDisconnectGameServer, self))
end

function LoginWGCtrl:UnRegisterLoginEvents()
	self:UnBind(self.connect_login_server_handle)
	self:UnBind(self.disconnect_login_server_handle)
	self:UnBind(self.connect_game_server_handle)
	self:UnBind(self.disconnect_game_server_handle)
	self:UnBind(self.connect_cross_server_handle)
	self:UnBind(self.disconnect_cross_server_handle)
end

function LoginWGCtrl:GetView()
	return self.view
end

function LoginWGCtrl:OpenSelectServerView()
	self.select_server_view:Open()
end

function LoginWGCtrl:OpenReconnect()
	ViewManager.Instance:Open(GuideModuleName.ReconnectView)
end

function LoginWGCtrl:OnServerBusy(protocol)
	if nil == self.busy_alert then
		self.busy_alert = Alert.New()
		self.busy_alert:SetOkString(Language.Common.Confirm)
		local dis_str = Language.Login.ServerBusy
		self.busy_alert:SetLableString(dis_str)
		-- self.busy_alert:SetIaAnyClickClose(true)
		-- self.busy_alert:SetModal(true)
		-- self.busy_alert.CloseCallBack = function() end
	end

	self.busy_alert:Open()
	self.busy_alert:UseOne()
end

function LoginWGCtrl:OnGamePause(is_pause)
	if is_pause then
		self.on_pause_timer_out = os.time()
	else
		self.on_pause_timer_enter = os.time()
	end
end

function LoginWGCtrl.SendLoginReq()
	local user_vo = GameVoManager.Instance:GetUserVo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSLoginReq)
	protocol.rand_1 = math.floor(math.random(1000000, ********))
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.rand_2 = math.floor(math.random(1000000, ********))
	protocol.plat_name = user_vo.account_user_id
	protocol.plat_server_id = user_vo.plat_server_id
	protocol.access_token = user_vo.access_token

	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function LoginWGCtrl:OnConnectLoginServer(is_suc)
	if is_suc then
		self.SendLoginReq()

		if nil == self.login_server_heartbeat_timer then
			self.login_server_heartbeat_timer = GlobalTimerQuest:AddRunQuest(function()
				self.SendLoginServerHeartBeat()
			end, 10)
		end
	else
		-- ReportManager:Step(Report.STEP_LOGIN_SERVER_CONNECTED_FAILED)
		TipWGCtrl.Instance:ShowDisconnected(DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR)
		InitWGCtrl:HideLoading()
		InitWGCtrl:DestroyLoadingView()
	end
end

function LoginWGCtrl:OnDisconnectLoginServer(custom_disconnect_reason, custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
	if nil ~= self.login_server_heartbeat_timer then
		GlobalTimerQuest:CancelQuest(self.login_server_heartbeat_timer)
		self.login_server_heartbeat_timer = nil
	end

	-- 非手动断线，显示断线提示	
	if real_disconnect_reason ~= GameNet.DisconnectReason.Manual then
		if TipWGCtrl.Instance ~= nil then
			TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
		end
	end
end

function LoginWGCtrl:OnConnectGameServer(is_suc)
	if is_suc then
		self.SendUserEnterGSReq()
	else
		TipWGCtrl.Instance:ShowSystemMsg("登陆游戏服务器失败.")
	end
end

-- 与游戏服务器断线
function LoginWGCtrl:OnDisconnectGameServer(custom_disconnect_reason, custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
	print_error("LoginWGCtrl:OnDisconnectGameServer", custom_disconnect_reason, custom_disconnect_notice_type, GameNet.DisconnectReasonStr[real_disconnect_reason], disconnect_detail)

	if SocietyWGCtrl.Instance then
		SocietyWGCtrl.Instance:ClearTeamInfo()
	end

	if ChatWGData.Instance then
		ChatWGData.Instance:RemoveAllChannel()
	end

	if FuBenWGData.Instance then
		FuBenWGData.Instance:DefFBState(false)
	end

	local can_auto_connect = false
	local show_disconnect_tip = true

	-- 非（主动断开 和 服务器强制断开），允许自动重连
	if real_disconnect_reason == GameNet.DisconnectReason.NotConnected
	or real_disconnect_reason == GameNet.DisconnectReason.SendError
	or real_disconnect_reason == GameNet.DisconnectReason.ReceiveError
	or real_disconnect_reason == GameNet.DisconnectReason.ReceiveStartError then
		can_auto_connect = true
	end

	-- 强制
	if not GameNet.Instance then
		can_auto_connect = false
	elseif custom_disconnect_reason == GameNet.DISCONNECT_REASON_FORBIDDEN then
		can_auto_connect = false
	end

	-- 若主动断线
	if real_disconnect_reason == GameNet.DisconnectReason.Manual then
		if custom_disconnect_notice_type and custom_disconnect_notice_type == DISCONNECT_NOTICE_TYPE.INVALID then
			show_disconnect_tip = false
		end
	end

	print_error("can_auto_connect->", can_auto_connect, "show_disconnect_tip->", show_disconnect_tip)
	if not can_auto_connect then
		if TipWGCtrl.Instance ~= nil and show_disconnect_tip then
			TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
		end
		return
	end

	-- 重连游戏服
	GameNet.Instance:AsyncConnectGameServer(5, function (is_succ)
		-- 未能连接游戏服失败
		if not is_succ then
			if TipWGCtrl.Instance ~= nil then
				TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
			end
		end
	end)
end

-- 发送国家协议
function LoginWGCtrl:SendRoleChooseCamp(camp_type,is_random)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSRoleChooseCamp)
	send_protocol.camp_type = camp_type
	send_protocol.is_random = is_random or 0
	send_protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function LoginWGCtrl:OnLoginAck(protocol)
	TimeWGCtrl.Instance:SetServerTime(protocol.server_time, protocol.pi_time)

	local error_code = protocol.result
	if 0 == error_code then
		-- ReportManager:Step(Report.STEP_ON_LOGIN_ACK)
		GameNet.Instance:SetGameServerInfo(protocol.gs_hostname, protocol.gs_port)
		print_log("LoginWGCtrl:OnLoginAck hostname:" .. protocol.gs_hostname .. "  prot:" .. protocol.gs_port)
		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:SetNowRole(protocol.role_id)
		user_vo.login_time = protocol.time
		user_vo.session_key = protocol.key
		user_vo.anti_wallow = protocol.anti_wallow
		user_vo.scene_id = protocol.scene_id
		user_vo.last_scene_id = protocol.last_scene_id

		GameNet.Instance:DisconnectLoginServer()
		GameNet.Instance:AsyncConnectGameServer(5)

		self:TrySendLoginFanLi2PHP()
	else
		TipWGCtrl.Instance:ShowSystemMsg(string.format("登陆认证失败: %d.", error_code))

		if LoginAckResult.LOGIN_LOGIN_FORBID == error_code then
			local str = Language.Login.CountBeBand
			local ok_func = function ()
				GlobalEventSystem:Fire(LoginEventType.LOGOUT)
			end
			TipWGCtrl.Instance:OpenConfirmAlertTips(str, ok_func)
		else
			local disconnect_notice_type = LOGIN_ACK_RESULT_TO_DISCONNECT_NOTICE_TYPE[error_code]
										or DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR
			TipWGCtrl.Instance:ShowDisconnected(disconnect_notice_type)
		end
	end
end

function LoginWGCtrl:OnAccountKeyError(protocol)
	if protocol.result == ACCOUNT_KEY_ERROR.MD5 then
		TipWGCtrl.Instance:ShowSystemMsg("MD5 Error !!!")
	end
end

-- 用户信息发生改变
function LoginWGCtrl:OnSCChangeGS(protocol)
	local user_vo = GameVoManager.Instance:GetUserVo()
	user_vo.scene_id = protocol.scene_id
	user_vo.scene_key = protocol.scene_key
	user_vo.last_scene_id = protocol.last_scene_id
	user_vo.session_key = protocol.key
	user_vo.login_time = protocol.time
end

-- php登录返利
function LoginWGCtrl:TrySendLoginFanLi2PHP()
	if self.is_send_login_fanli_2_php or not TimeWGCtrl.Instance:GetHasServerTimeFlag() then
		return
	end

	if GLOBAL_CONFIG.param_list.login_fanli_switch == 1 then
		ReportManager:SendLoginFanLi2PHP()
	end

	self.is_send_login_fanli_2_php = true
end

function LoginWGCtrl:OnRoleListAck(protocol)
	self.data:SetRoleListAck(protocol)

	local result = protocol.result
	-- ReportManager:Step(Report.STEP_ROLE_LIST_ACK)
	local user_vo = GameVoManager.Instance:GetUserVo()
	if LoginAckResult.LOGIN_RESULT_SUC == result and protocol.count > 0 then
		if IS_ON_CROSSSERVER then
			user_vo:SetNowRole(protocol.role_list[1].role_id)
			local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
			mainrole_vo.name = protocol.role_list[1].role_name
			self.SendRoleReq()
		else
			local curr_select_role_id = LoginWGData.Instance:GetCurrSelectRoleId()
			for k,v in pairs(protocol.role_list) do
				if v.role_id == curr_select_role_id then
					user_vo:SetNowRole(protocol.role_list[k].role_id)
					local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
					mainrole_vo.name = protocol.role_list[k].role_name
					self.SendRoleReq()
					return
				end
			end

			self.view:OnChangeToSelectRole()
		end

	elseif LoginAckResult.LOGIN_RESULT_NO_ROLE == result then
		local server_vo = LoginWGData.Instance:GetServerVoById(user_vo.plat_server_id)
		-- 停止注册
		if server_vo and server_vo.register_status == 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.ServerOpenTips4)
			self.view:WaitViewShowEndByStopRegister()
			GameNet.Instance:ResetLoginServer()
			return
		end

		self.view:OnChangeToCreate()
		if 0 == #protocol.role_list then
			CacheManager.Instance:ClearAllCache()
		end
	elseif LoginAckResult.LOGIN_LOGIN_FORBID == result then
		local str = Language.Login.CountBeBand
		local ok_func = function ()
			GlobalEventSystem:Fire(LoginEventType.LOGOUT)
		end
		TipWGCtrl.Instance:OpenConfirmAlertTips(str,ok_func)
	end
end

function LoginWGCtrl:OnMergeRoleListAck(protocol)
	print_log("Login::LoginWGCtrl:OnMergeRoleListAck")
	-- ReportManager:Step(Report.STEP_ROLE_LIST_MERGE_ACK)
	self.data:SetRoleListAck(protocol)
	if protocol.count == 0 then
		print_log("OnMergeRoleListAck has no count")
		self.view:OnChangeToCreate()
	elseif protocol.result == LoginAckResult.LOGIN_LOGIN_FORBID then
		local str = Language.Login.CountBeBand
		local ok_func = function ()
			GlobalEventSystem:Fire(LoginEventType.LOGOUT)
		end
		TipWGCtrl.Instance:OpenConfirmAlertTips(str,ok_func)
	else
		local last_server_list = LoginWGData.Instance:GetLastLoginServer() --最近登陆服务器
		local cur_select_server = tonumber(last_server_list[1])
		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:ClearRoleList()
		for i = 1, protocol.count do
			user_vo:AddRole(
				protocol.role_list[i].role_id,
				protocol.role_list[i].role_name,
				protocol.role_list[i].avatar,
				protocol.role_list[i].sex,
				protocol.role_list[i].prof,
				protocol.role_list[i].country,
				protocol.role_list[i].level,
				protocol.role_list[i].create_time,
				protocol.role_list[i].online_time,
				protocol.role_list[i].last_logout_time,
				protocol.role_list[i].capability,
				protocol.role_list[i].avatar_key_big,
				protocol.role_list[i].avatar_key_small
				)

		LoginWGData.Instance:SetServerListHead(cur_select_server, i, protocol.role_list[i].role_id,
			protocol.role_list[i].prof, protocol.role_list[i].sex, protocol.role_list[i].level, protocol.role_list[i].role_name)
		end
			if IS_ON_CROSSSERVER then
				user_vo:SetNowRole(protocol.role_list[1].role_id)
				local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
				mainrole_vo.name = protocol.role_list[1].role_name
				self.SendRoleReq()
			else
				local curr_select_role_id = LoginWGData.Instance:GetCurrSelectRoleId()
				for k,v in pairs(protocol.role_list) do
					if v.role_id == curr_select_role_id then
						user_vo:SetNowRole(protocol.role_list[k].role_id)
						local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
						mainrole_vo.name = protocol.role_list[k].role_name
						self.SendRoleReq()
						return
					end
				end

				self.view:OnChangeToSelectRole()
			end
		-- end
	end
end

function LoginWGCtrl:OnProfNumInfo(protocol)
	local sex = protocol.male_num <= protocol.female_num and GameEnum.MALE or GameEnum.FEMALE

	local min_prof, min_prof_num
	for k,v in ipairs(protocol.prof_num_list) do
		if not RoleWGData.Instance:GetIsShieldSexAndProf(sex, k) then
			if not min_prof_num or v < min_prof_num then
				min_prof_num = v
				min_prof = k
			end
		end
	end

	self.view:SetLowProf(min_prof)
	self.view:SetLowSex(sex)
end

function LoginWGCtrl.SendRoleReq()
	local user_vo = GameVoManager.Instance:GetUserVo()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleReq)
	protocol.rand_1 = math.floor(math.random(1000000, ********))
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.rand_2 = math.floor(math.random(1000000, ********))
	protocol.role_id = mainrole_vo.role_id
	protocol.plat_name = user_vo.account_user_id
	protocol.plat_server_id = user_vo.plat_server_id

	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function LoginWGCtrl.SendCreateRole(role_name, prof, sex, had_create_role_num, diy_appearance_info)
	local user_vo = GameVoManager.Instance:GetUserVo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCreateRoleReq)
	protocol.plat_name = user_vo.account_user_id
	protocol.role_name = role_name
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.plat_server_id = user_vo.plat_server_id
	protocol.avatar = 0
	protocol.sex = sex
	protocol.prof = prof
	protocol.had_create_role_num = had_create_role_num or 0
	protocol.plat_spid = tostring(GLOBAL_CONFIG.local_package_info.config.agent_id)
	protocol.pay_money = LoginWGData.Instance:GetUserMoney()
	protocol.default_body_res_id = diy_appearance_info.body_id or 0
	protocol.default_face_res_id = diy_appearance_info.face_id or 0
	protocol.default_hair_res_id = diy_appearance_info.hair_id or 0

	protocol.hair_color = diy_appearance_info.hair_color or {}
	protocol.eye_size = diy_appearance_info.eye_size or 0
	protocol.eye_position = diy_appearance_info.eye_position or 0
	protocol.eye_shadow_color = diy_appearance_info.eye_shadow_color or {}
	protocol.left_pupil_type = diy_appearance_info.left_pupil_type or 0
	protocol.left_pupil_size = diy_appearance_info.left_pupil_size or 0
	protocol.left_pupil_color = diy_appearance_info.left_pupil_color or {}
	protocol.right_pupil_type = diy_appearance_info.right_pupil_type or 0
	protocol.right_pupil_size = diy_appearance_info.right_pupil_size or 0
	protocol.right_pupil_color = diy_appearance_info.right_pupil_color or {}
	protocol.mouth_size = diy_appearance_info.mouth_size or 0
	protocol.mouth_position = diy_appearance_info.mouth_position or 0
	protocol.mouth_color = diy_appearance_info.mouth_color or {}
	protocol.face_decal_id = diy_appearance_info.face_decal_id or 0
	protocol.preset_seq = diy_appearance_info.preset_seq or 0
	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function LoginWGCtrl:OnCreateRoleAck(protocol)
	if 0 == protocol.result then
		Scene.Instance:OpenSceneLoading()

		print_log("LoginWGCtrl:OnCreateRoleAck", protocol.result)
		-- ReportManager:Step(Report.STEP_CREATE_ROLE_ACK)
		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:ClearRoleList()
		user_vo:AddRole(
			protocol.role_id,
			protocol.role_name,
			protocol.avatar,
			protocol.sex,
			protocol.prof, 0,
			protocol.level,
			protocol.create_time,
			protocol.online_time,
			protocol.last_logout_time,
			protocol.capability)

		user_vo:SetNowRole(protocol.role_id)

		local last_server_list = LoginWGData.Instance:GetLastLoginServer() --最近登陆服务器
		local cur_select_server = tonumber(last_server_list[1])
		LoginWGData.Instance:SetServerListHead(cur_select_server, nil, protocol.role_id,
			protocol.prof, protocol.sex, protocol.level, protocol.role_name)

		LoginWGData.Instance:SetCurrSelectRoleId(protocol.role_id)

		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		mainrole_vo.name = protocol.role_name

		if self.create_diy_view and self.create_diy_view:IsOpen() then
			self.create_diy_view:Close()
		end

		
		if self.create_select_name_view and self.create_select_name_view:IsOpen() then
			self.create_select_name_view:Close()
		end

		if self.show_role_apprerance_view and self.show_role_apprerance_view:IsOpen() then
			self.show_role_apprerance_view:Close()
		end

		local role_diy_appearance_view = RoleDiyAppearanceWGCtrl.Instance:GetShowView()
		if role_diy_appearance_view and role_diy_appearance_view:IsOpen() then
			role_diy_appearance_view:Close()
		end

		self.SendRoleReq()
		GlobalEventSystem:Fire(LoginEventType.CREATE_ROLE)

		-- 神起上报
		-- local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		-- ReportManager:ReportUrlToSQ(main_role_vo.server_id, protocol.role_name, protocol.role_id, protocol.level, protocol.create_time, "createRole")
	else
		print_log("LoginWGCtrl:OnCreateRoleAck", protocol.result)
		-- ReportManager:Step(Report.STEP_CREATE_ROLE_ACK_FAILED)

		local show_msg = "创建角色失败"
		if -1 == protocol.result then
			show_msg = "创角色已满，请更换账号或者移步新服"
		elseif -2 == protocol.result then
			show_msg = "该昵称已存在, 请修改昵称"
		elseif -3 == protocol.result then
			show_msg = "名字不合法"
		elseif -4 == protocol.result then
			show_msg = "服务器爆满，请移步新服吧"
		elseif -5 == protocol.result then
			show_msg = "角色昵称长度非法"
		end

		TipWGCtrl.Instance:ShowSystemMsg(show_msg)
	end
end

function LoginWGCtrl.SendLoginServerHeartBeat()
	local protocol = ProtocolPool.Instance:GetProtocol(CSLHeartBeat)
	if GameNet.Instance:IsLoginServerConnected() then --在登陆服的时候请求
		protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
	end
end

-- 登录服心跳返回
-- function LoginWGCtrl:OnLHeartBeat()
	--print_error("登录服心跳返回")
-- end

function LoginWGCtrl:OnUserEnterGSAck(protocol)
	self.data:SetUserPlatType(protocol.plat_type)
	local result_str = tostring(protocol.result)
	if 0 == protocol.result then
		result_str = result_str .. " 成功"
	elseif -1 == protocol.result then
		result_str = result_str .. " 角色已存在"
	elseif -2 == protocol.result then
		result_str = result_str .. " 没找到场景"
	end
	print_log("Login::LoginWGCtrl:OnUserEnterGSAck result:" .. tostring(protocol.result) .. ",result_str:" .. result_str)

	if 0 == protocol.result then
		-- ReportManager:Step(Report.STEP_ENTER_GS_ACK)
		-- -- 清空资源
		-- self.view:ClearScenes()
		-- 关闭网络提示
		-- GlobalTimerQuest:CancelQuest(self.show_disconnect_tips_timer)
		TipWGCtrl.Instance:CloseDisconnected()
		-- 发送进度游戏成功事件
		GlobalEventSystem:Fire(LoginEventType.ENTER_GAME_SERVER_SUCC)
	elseif -1 == protocol.result then
		-- ReportManager:Step(Report.STEP_ENTER_GS_ACK_FAILED)
		self.enter_gs_count = (self.enter_gs_count or 0) + 1
		if self.enter_gs_count >= 5 then
			self.enter_gs_count = 0
			---这里广播一个断线在再触发一次
			TipWGCtrl.Instance:ShowDisconnected()
		else
			self.enter_gs_timer = GlobalTimerQuest:AddDelayTimer(
				function() self.SendUserEnterGSReq() end, 0.5)
		end
	elseif -3 == protocol.result then
		-- 玩家在跨服，请求重连
		-- ReportManager:Step(Report.STEP_ENTER_GS_ACK_FAILED)
		self.enter_gs_count = (self.enter_gs_count or 0) + 1
		if self.enter_gs_count >= 5 then
			self.enter_gs_count = 0
		else
			self.enter_gs_timer = GlobalTimerQuest:AddDelayTimer(
				function() self.SendUserEnterGSReq() end, 3)
		end
	else
		-- ReportManager:Step(Report.STEP_ENTER_GS_ACK_FAILED)
		self.enter_gs_count = 0
		print_log("LoginWGCtrl:OnUserEnterGSAck", protocol.result)
	end

	if 0 == protocol.result and not IS_ON_CROSSSERVER then
		if not GameVoManager.Instance then
			return
		end

		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local data_list = LoginWGData.Instance:GetRoleListAck()
		local index = nil
		for k,v in pairs(data_list.role_list) do
			if mainrole_vo.role_id == v.role_id then
				index = k
			end
		end
		index = index or #data_list.role_list + 1
		local login_server_id = UserVo.GetServerId(mainrole_vo.role_id)
		PlayerPrefsUtil.SetString("SAVE_UP_LOGIN", index)

		--开启冒险的时候保存用户名
		local uservo = GameVoManager.Instance:GetUserVo()
		uservo.is_in_game_server = true
		PlayerPrefsUtil.SetString("login_account_name", uservo.account_user_id)
		PlayerPrefsUtil.SetInt("login_account_uid", mainrole_vo.role_id)
	end
end

function LoginWGCtrl.SendUserEnterGSReq()
	-- ReportManager:Step(Report.STEP_SEND_ENTER_GS)
	local user_vo = GameVoManager.Instance:GetUserVo()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

	local net_type = UnityApplication.internetReachability
	local system = UnitySysInfo.operatingSystem
	local device_model = UnitySysInfo.deviceModel
	local device_info = string.format("%s\t%s\t%s\t%s", DeviceTool.GetDeviceID(), net_type, system, device_model)

	local protocol = ProtocolPool.Instance:GetProtocol(CSUserEnterGSReq)
	protocol.scene_id = user_vo.scene_id
	protocol.scene_key = user_vo.scene_key 
	protocol.last_scene_id = user_vo.last_scene_id
	protocol.role_id = user_vo:GetOriginalRoleID()
	protocol.role_name = mainrole_vo.role_name
	protocol.time = user_vo.login_time
	protocol.is_login = 1
	protocol.server_id = mainrole_vo.server_id
	protocol.key = user_vo.session_key
	protocol.plat_name = user_vo.account_user_id
	protocol.is_micro_pc = 0
	protocol.plat_spid = tostring(GLOBAL_CONFIG.local_package_info.config.agent_id)
	protocol.device_info = device_info

	-- LoginWGCtrl.Instance:CheckMainRoleInSpecialStatus()
	protocol:EncodeAndSend(GameNet.Instance:GetGameServerNet())
	if GlobalLocalRoleId == nil then
		GlobalLocalRoleId = protocol.role_id
		GlobalLocalRoleName = mainrole_vo.role_name
	end
	-- print_log("Login::LoginWGCtrl.SendUserEnterGSReq name=" .. mainrole_vo.role_name.."server_id="..mainrole_vo.server_id)
end

----[[处理玩家处于特殊状态，重进场景问题导致特殊状态异常（如：断线重连）
function LoginWGCtrl:CheckMainRoleInSpecialStatus()
	local main_role = Scene.Instance and Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	if self.main_role_in_special_action_data then
		return
	end

	-- 旧状态是在跳跃中
	if main_role:IsJump() then
		local data = {
			is_jump = true,
			cur_jumping_type = main_role.cur_jumping_type,
			target_jumping_id = main_role.target_jumping_id,
			target_x = main_role.target_x,
			target_y = main_role.target_y,
		}

		self.main_role_in_special_action_data = data
	else
		self.main_role_in_special_action_data = nil
	end
end

function LoginWGCtrl:ClearMainRoleInSpecialAction()
	self.main_role_in_special_action_data = nil
end

function LoginWGCtrl:GetMainRoleInSpecialActionData()
	return self.main_role_in_special_action_data
end
--]]

function LoginWGCtrl:SendGameServerHeartBeat()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSHeartBeat)
	send_protocol:EncodeAndSend(GameNet.Instance:GetGameServerNet())
end

function LoginWGCtrl:ExitReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSDisconnectReq)
	protocol:EncodeAndSend(GameNet.Instance:GetGameServerNet())
end

function LoginWGCtrl:SendSpeedUpHello()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSSpeedUpHello)
	send_protocol:EncodeAndSend()
end

-- 断开当前服务器
function LoginWGCtrl.SendUserLogout()
	-- local protocol = ProtocolPool.Instance:GetProtocol(CSUserLogout)
	-- protocol:EncodeAndSend()
	local protocol = ProtocolPool.Instance:GetProtocol(CSDisconnectReq)
	protocol:EncodeAndSend(GameNet.Instance:GetGameServerNet())
end

function LoginWGCtrl:OnDisconnectNotice(protocol)
	GameNet.Instance.custom_disconnect_notice_type = protocol.reason
end

function LoginWGCtrl:OnCheckForbidInfo(protocol)
	-- 服务器封禁
	if protocol.forbid_status == 1 then
		GameNet.Instance.custom_disconnect_reason = GameNet.DISCONNECT_REASON_FORBIDDEN
		GameNet.Instance.custom_disconnect_notice_type = DISCONNECT_NOTICE_TYPE.LOGIN_FORBID
	end
end

-- 在加载页面出来后,再清空资源
function LoginWGCtrl:ClearScenes()
	if self.view then
		self.view:ClearScenes()
	end
end

function LoginWGCtrl:OnLoginOut()
	if self.create_diy_view and self.create_diy_view:IsOpen() then
		self.create_diy_view:ReturnToLoginCreateView()
	end

	local role_diy_appearance_view = RoleDiyAppearanceWGCtrl.Instance:GetShowView()
	if role_diy_appearance_view and role_diy_appearance_view:IsOpen() then
		role_diy_appearance_view:OnCloseView()
	end

	if self.create_select_name_view and self.create_select_name_view:IsOpen() then
		self.create_select_name_view:Close()
	end
	
	if self.show_role_apprerance_view and self.show_role_apprerance_view:IsOpen() then
		self.show_role_apprerance_view:OnCloseView()
	end

	
	if self.view:IsOpen() then
		-- self.view:SetIsOpenCreate(false)
		self.view:Flush(0, "back_login_view")
	end
end

function LoginWGCtrl:SelectServerViewToLoginView()
	if self.view:IsOpen() then
		self.view:Flush(0, "select_server_back")
	end
end

function LoginWGCtrl:OpenSeverWarningTips(data)
	self.server_warning:SetContent(data)
	self.server_warning:Open()
end

function LoginWGCtrl:ChangeLoginViewShow(is_show)
	if self.view then
		self.view:SetRendering(is_show)
	end
end



function LoginWGCtrl:OpenCreateDIYView(data)
	self.create_diy_view:SetDataAndOpen(data)
end

function LoginWGCtrl:OpenCreateSelectNameView(data)
	self.create_select_name_view:SetDataAndOpen(data)
end

function LoginWGCtrl:OpenShowRoleAppranceView(data)
	self.show_role_apprerance_view:SetShowDataAndOpen(data)
end


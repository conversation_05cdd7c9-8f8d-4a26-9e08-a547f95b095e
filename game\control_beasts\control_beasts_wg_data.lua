ControlBeastsWGData = ControlBeastsWGData or  BaseClass()
 
BEAST_BATTLE_ENUM = {
	MAIN_BATTLE_HOLE = 3,
	SUB_BATTLE_HOLE = 6,
}

BEASTS_HOLE_STATUS = 
{
	NOT_ACTIVE = 1,			-- 未激活
	ACTIVE = 2,				-- 已激活
}

BEASTS_COMPOSE_STATUS = 	--	用于合成
{
	NORMAL = 1,				-- 未选取
	SELECT = 2,				-- 已选取
}

BEASTS_BORN_STATUS =
{
	NONE = 0,				-- 空的
	CAN_BORN = 1, 			-- 可孵化
	UNBORN = 2, 			-- 未孵化
	BORN = 3, 				-- 已孵化
}

BAG_SHOW_TYPE = {
    NONE = 0,				-- 空
    FORWARD = 1,			-- 正放
    REVERSE = 2,			-- 倒放
}

BEAST_KING_CONDITION_TYPE = {
    BESTA_LEVEL = 1,
    BESTA_STAR = 2,
    BESTA_COLOR = 3,
    VIP = 4,
}

-- 增加几个特效(品质)
BEAST_EFFECT_COLOR = {
	[1] = "UI_HSicon_N", 
	[2] = "UI_HSicon_R", 
	[3] = "UI_HSicon_SR", 
	[4] = "UI_HSicon_SSR", 
	[5] = "UI_HSicon_UR", 
	[6] = "UI_HSicon_SP", 
	[7] = "UI_HSicon_UP", 
	[8] = "UI_HSicon_USP"
}

-- 增加几个特效(类型)
BEAST_EFFECT_TYPE = {
	[1] = "UI_HSicon_lan",
	[2] = "UI_HSicon_hong",
	[3] = "UI_HSicon_lv",
	[4] = "UI_HSicon_jin",
	[5] = "UI_HSicon_zi",
	[6] = "UI_HSicon_fen",
}

BOMB_BEAST_EFFECT_TYPE = {
	[1] = "UI_HS_yinjiget_lan",
	[2] = "UI_HS_yinjiget_hong",
	[3] = "UI_HS_yinjiget_lv",
	[4] = "UI_HS_yinjiget_jin",
	[5] = "UI_HS_yinjiget_zi",
	[6] = "UI_HS_yinjiget_fen",
}

BEAST_COMPOSE_SPEND_TYPE = 
{
	BEAST_SPECIAL = 0,
	BEAST_ELEMENT = 1,
	BEAST_STAR = 2,
	BEAST_CHIP = 3,
	BEAST_COLOR_SP = 6,
	BEAST_SP_ELEMENT = 100,
}

-- 圣魂类型
SPIRIT_TYPE = {
	Attr = 1,	-- 属性
	Multy = 2,	-- 百分比加成
	Skill = 3,	-- 技能
	Awake = 4,  -- 觉醒
}

function ControlBeastsWGData:__init()
	if ControlBeastsWGData.Instance ~= nil then
		ErrorLog("[ControlBeastsWGData] attempt to create singleton twice!")
		return
	end
	ControlBeastsWGData.Instance = self

	self:InitCfg()
	self:InitInfo()

	RemindManager.Instance:Register(RemindName.ControlBeasts, BindTool.Bind(self.ShowControlBeastsRemind, self)) 			-- 总红点
	RemindManager.Instance:Register(RemindName.BeastsBattle, BindTool.Bind(self.GetBeastsBattleRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsCulture, BindTool.Bind(self.GetBeastsCultureRemind, self))
	-- RemindManager.Instance:Register(RemindName.BeastsRefining, BindTool.Bind(self.GetBeastsRefiningRemind, self))
    -- RemindManager.Instance:Register(RemindName.BeastsStable, BindTool.Bind(self.GetBeastsStableRemind, self))
	-- RemindManager.Instance:Register(RemindName.BeastsContract, BindTool.Bind(self.GetBeastsContractRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsCompose, BindTool.Bind(self.GetBeastsComposeRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsHandBook, BindTool.Bind(self.GetBeastsHandBookRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsKing, BindTool.Bind(self.GetBeastsKingRemind, self))
	RemindManager.Instance:Register(RemindName.HolyBeastsContract, BindTool.Bind(self.GetHolyBeastContractRemind, self))
	RemindManager.Instance:Register(RemindName.HolyBeastsSpirit, BindTool.Bind(self.GetHolySpiritRemind, self))
	RemindManager.Instance:Register(RemindName.BeastsInnerAlchemy, BindTool.Bind(self.GetInnerAlchemyRemind, self))
end

function ControlBeastsWGData:__delete()
	ControlBeastsWGData.Instance = nil

	self:DeleteCfg()
	self:DeleteInfo()

	RemindManager.Instance:UnRegister(RemindName.ControlBeasts)
	RemindManager.Instance:UnRegister(RemindName.BeastsBattle)
	RemindManager.Instance:UnRegister(RemindName.BeastsCulture)
	-- RemindManager.Instance:UnRegister(RemindName.BeastsRefining)
    -- RemindManager.Instance:UnRegister(RemindName.BeastsStable)
	-- RemindManager.Instance:UnRegister(RemindName.BeastsContract)
	RemindManager.Instance:UnRegister(RemindName.BeastsCompose)
	RemindManager.Instance:UnRegister(RemindName.BeastsHandBook)
	RemindManager.Instance:UnRegister(RemindName.BeastsKing)
	RemindManager.Instance:UnRegister(RemindName.HolyBeastsContract)
	RemindManager.Instance:UnRegister(RemindName.HolyBeastsSpirit)
	RemindManager.Instance:UnRegister(RemindName.BeastsInnerAlchemy)
end

------------------------------------------------------------- 配置信息 --------------------------------------------------------
-- 初始化配置表
function ControlBeastsWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("beasts_cfg_auto")
	if cfg then
		self.battle_hole_cfg = cfg.hole
		self.base_cfg = cfg.other[1]
		self.hole_level_map_cfg = ListToMap(cfg.hole_level, "hole_id", "hole_level")
		self.beasts_map_cfg = cfg.beasts
		self.beasts_chip_map_cfg = cfg.bests_ship
		self.beasts_type_star_cfg = ListToMapByDisorder(cfg.beasts, "beast_type", "beast_star")
		self.beast_level_map_cfg = cfg.beast_level
		self.beast_best_attr_map_cfg = ListToMap(cfg.beast_best_attr, "beast_id", "beast_level")
		self.beast_flair_score_map_cfg = cfg.beast_flair_score
		self.beast_flair_map_cfg = cfg.beast_flair
		self.skill_pool_map_cfg = cfg.skill_pool
		self.flair_change_map_cfg = cfg.flair_change
		self.beast_tree_map_cfg = cfg.beast_tree
		self.beasts_group_map_cfg = cfg.beasts_group
		self.beast_king_map_cfg = cfg.beast_king
		self.star_circle_attr_pool = cfg.star_circle_attr_pool
		self.star_circle_refresh_map_pool = ListToMapList(cfg.star_circle_refresh_pool, "beast_king_level")
		self.star_circle_level = cfg.star_circle_level
		self.beast_level_limit = cfg.beast_level_limit
		self.beast_star_level_limit = ListToMap(cfg.beast_level_limit, "star_level")
		self.preview_beast_cfg = cfg.preview_beast[1]
		self.star_circle_attr_preview_cfg = cfg.star_circle_attr_preview
		self.star_circle_attr_id_color_cfg = ListToMap(cfg.star_circle_attr_pool, "array_attr_id", "array_attr_color")
		self.beast_break_cfg = cfg.beast_break
		self.beast_flair_item_cfg = cfg.flair_item
		self.refine_cfg = ListToMap(cfg.refine, "seq", "index")
		self.refine_weight_cfg = ListToMap(cfg.refine_weight, "seq", "star_level")
		-- self.refine_weight_item1_cfg = ListToMap(cfg.refine_weight, "up_cost_item_id")
		self.refine_weight_item2_cfg = ListToMap(cfg.refine_weight, "cost_item_id")
		self.beast_skill_group_cfg = cfg.beast_skill_group
		self.beast_star_skill_group_cfg = cfg.beast_star_skill_group
		self.be_skill_cfg = cfg.be_skill
		self.fetters_attr_cfg = ListToMapList(cfg.fetters_attr, "fetters_type")
		self.fetters_attr_cfg2 = ListToMap(cfg.fetters_attr, "fetters_type", "type_num")
		self.draw_model_show_cfg = cfg.draw_model_show
		self.compose_card_cfg = cfg.compose_card
		self.handbook_reward_cfg = ListToMap(cfg.handbook_reward, "beast_type", "beast_star")
		self.handbook_desc_cfg = cfg.handbook_des

		self.skin_cfg = ListToMap(cfg.skin, "skin_seq")
		self.skin_cfg_id = ListToMap(cfg.skin, "skin_item_id")
		self.skin_type_cfg = ListToMapList(cfg.skin, "beast_type")
		self.skin_level_cfg = ListToMap(cfg.skin_level, "skin_seq", "level")

		self.holy_beast_cfg = cfg.holy_beast -- 圣兽配置
		self.holy_beast_type_cfg = ListToMap(cfg.holy_beast, "beast_type") -- 圣兽配置
		self.holy_spirit_cfg = ListToMap(cfg.holy_spirit, "seq", "index") -- 圣魂配置
		self.holy_spirit_page_cfg = ListToMapList(cfg.holy_spirit, "seq", "page") -- 圣魂配置（分页）
		self.holy_spirit_level_cfg = ListToMap(cfg.holy_spirit_level, "seq", "index", "level") -- 圣魂等级配置
		self.holy_spirit_skill_cfg = ListToMap(cfg.holy_spirit_skill, "beast_type", "skill_id") -- 链接技能配置
		self.holy_link_per_cfg = ListToMap(cfg.holy_link_per, "beast_color") -- 链接加成配置
		self.holy_spirit_page_name_cfg = ListToMap(cfg.holy_spirit_page_name, "beast_type") -- 圣魂分页名称

		self.holy_beast_item_cfg = ListToMap(cfg.holy_beast, "call_cost_item_id")
		self.holy_beast_spirit_item_cfg = ListToMap(cfg.holy_spirit_level, "cost_item_id0")
		self.holy_beast_spirit_item_cfg2 = ListToMap(cfg.holy_spirit_level, "cost_item_id1")
		self.pellet_cfg = cfg.pellet
		self.pellet_item_cfg = ListToMap(cfg.pellet, "cost_item_id")
	end

	local cfg1 = ConfigManager.Instance:GetAutoConfig("element_reaction_auto")
	if cfg1 then
		self.element_effect_cfg = cfg1.effect
		self.element_effect_level_cfg = ListToMap(cfg1.effect, "element1", "element2", "level")
	end
end
-- 清理垃圾
function ControlBeastsWGData:DeleteCfg()
	self.battle_hole_cfg = nil
	self.base_cfg = nil
	self.hole_level_map_cfg = nil
	self.beasts_map_cfg = nil
	self.beast_level_map_cfg = nil
	self.beast_flair_score_map_cfg = nil
	self.beast_flair_map_cfg = nil
	self.skill_pool_map_cfg = nil
	self.flair_change_map_cfg = nil
	self.beast_tree_map_cfg = nil
	self.beasts_group_map_cfg = nil
	self.beast_king_map_cfg = nil
	self.star_circle_attr_pool = nil
	self.star_circle_level = nil
	self.beast_level_limit = nil
	self.preview_beast_cfg = nil
	self.beast_break_cfg = nil
	self.beast_flair_item_cfg = nil
	self.star_circle_attr_id_color_cfg = nil
	self.refine_cfg = nil
	self.refine_weight_cfg = nil
	self.beast_skill_group_cfg = nil
	self.be_skill_cfg = nil
	-- self.refine_weight_item1_cfg = nil
	self.refine_weight_item2_cfg = nil
	self.fetters_attr_cfg = nil
	self.fetters_attr_cfg2 = nil
end

-- 初始化数据
function ControlBeastsWGData:InitInfo()
	self.battle_main_hole_data = {}
	self.battle_assist_hole_data = {}
	if self.battle_hole_cfg then
		for hole_id, hole_data in pairs(self.battle_hole_cfg) do
			local client_hole_id = hole_id + 1	--这里加一方便客户端取列表
			if client_hole_id < 4 then
				self.battle_main_hole_data[client_hole_id] = ControlBeastsWGData:CreateHoleData(hole_data, true) 		--主战位
			else
				self.battle_assist_hole_data[client_hole_id - 3] = ControlBeastsWGData:CreateHoleData(hole_data, false) 	--辅战位
			end
		end
	end

	-- 重新拆分数据，配置表全是字符串，封装客户端好查询数据
	self.beast_flair_score_list = {}
	if self.beast_flair_score_map_cfg then
		for beast_star, beast_flair_data in ipairs(self.beast_flair_score_map_cfg) do
			self.beast_flair_score_list[beast_star] = {}

			if beast_flair_data and beast_flair_data.beast_flair_interval then
				local flair_str_list = Split(beast_flair_data.beast_flair_interval, ",")
				for index, interval_str in ipairs(flair_str_list) do
					local value = tonumber(interval_str) or 0
					self.beast_flair_score_list[beast_star][index] = value
				end
			end
		end
	end

	-- 重新拆分数据，配置表全是字符串，封装客户端好查询数据
	self.beast_flair_change_list = {}
	if self.flair_change_map_cfg then
		for beast_star, beast_star_flair_per_data in ipairs(self.flair_change_map_cfg) do
			self.beast_flair_change_list[beast_star] = {}
			self.beast_flair_change_list[beast_star].beast_star_flair_per = {}
			--self.beast_flair_change_list[beast_star].flair_limit = {}

			if beast_star_flair_per_data and beast_star_flair_per_data.beast_star_flair_per then
				local flair_per_list = Split(beast_star_flair_per_data.beast_star_flair_per, "|")
				for index, flair_per_str in ipairs(flair_per_list) do
					local value = tonumber(flair_per_str) or 0
					self.beast_flair_change_list[beast_star].beast_star_flair_per[index] = value
				end
			end
		end
	end

	-- 重新拆分数据，配置表全是字符串，封装客户端好查询数据
	self.beasts_group_list = {}
	if self.beasts_group_map_cfg then
		for beast_group, beasts_group_data in ipairs(self.beasts_group_map_cfg) do
			self.beasts_group_list[beast_group] = {}
			self.beasts_group_list[beast_group].beast_group_id = beast_group
			self.beasts_group_list[beast_group].group_skill_id = beasts_group_data.group_skill_id
			self.beasts_group_list[beast_group].group_skill_name_icon = beasts_group_data.group_skill_name_icon
			self.beasts_group_list[beast_group].group_show_beast_id = beasts_group_data.beast_id
			

			if beasts_group_data and beasts_group_data.group_conditions then
				local flair_per_list = Split(beasts_group_data.group_conditions, ",")
				local conditions = {}
				conditions.need_num = tonumber(flair_per_list[1]) or 0
				conditions.need_type = tonumber(flair_per_list[2]) or 0
				self.beasts_group_list[beast_group].group_conditions = conditions
			end
		end
	end

	-- 重新拆分数据，配置表全是字符串，封装客户端好查询数据
	self.beast_king_list = {}
	if self.beast_king_map_cfg then
		for beast_king_level, beast_king_data in ipairs(self.beast_king_map_cfg) do
			self.beast_king_list[beast_king_level] = {}
			self.beast_king_list[beast_king_level].beast_king_data = beast_king_data
			self.beast_king_list[beast_king_level].king_conditions = {}

			if beast_king_data and beast_king_data.king_conditions and beast_king_data.conditions_params then
				if beast_king_data.king_conditions == 0 or beast_king_data.conditions_params == 0 then
					self.beast_king_list[beast_king_level].king_conditions = 0
				else
					local king_conditions_list = Split(beast_king_data.king_conditions, "|")
					local conditions_params_list = Split(beast_king_data.conditions_params, "|")
					if #king_conditions_list == #conditions_params_list then
						for index, conditions_data in ipairs(king_conditions_list) do
							local conditions = {}
							conditions.type = tonumber(conditions_data) or 0
							if conditions_params_list[index] then
								local params_list = Split(conditions_params_list[index], ",")
								conditions.params_1 = tonumber(params_list[1]) or 0
								conditions.params_2 = tonumber(params_list[2]) or 0
							end
	
							table.insert(self.beast_king_list[beast_king_level].king_conditions, conditions)
						end
					end
				end
			end
		end
	end

	self.beast_map_list = {}
	self.star_up_item_chip_list = {}
	if self.beasts_map_cfg then
		for beast_id, beast_data in pairs(self.beasts_map_cfg) do
			self.beast_map_list[beast_id] = {}
			self.beast_map_list[beast_id].beast_data = beast_data
			self.beast_map_list[beast_id].beast_flair_preview = {}
			self.beast_map_list[beast_id].beast_best_attr_star = {}
			self.beast_map_list[beast_id].starup_beast_ids = {}
			self.beast_map_list[beast_id].beast_preview = {}
			
			if beast_data and beast_data.beast_flair_preview then
				local beast_flair_list = Split(beast_data.beast_flair_preview, "|")
				for index, beast_flair_data in ipairs(beast_flair_list) do
					local preview = {}
					local preview_list = Split(beast_flair_data, ",")
					preview.min = tonumber(preview_list[1]) or 0
					preview.max = tonumber(preview_list[2]) or 0
					
					if index == #beast_flair_list then
						preview.min = preview.min / 10000
						preview.max = preview.max / 10000
					end

					self.beast_map_list[beast_id].beast_flair_preview[index] = preview
				end
			end

			if beast_data and beast_data.beast_best_attr_star then
				local best_attr_str_list = Split(beast_data.beast_best_attr_star, ",")
				for index, beast_flair_str in ipairs(best_attr_str_list) do
					self.beast_map_list[beast_id].beast_best_attr_star[index] = tonumber(beast_flair_str) or 0 
				end
			end

			if beast_data and beast_data.starup_beast_ids then
				if beast_data.starup_beast_ids == 0 then
					self.beast_map_list[beast_id].starup_beast_ids = beast_data.starup_beast_ids
				else
					local starup_beast_group_str_list = Split(beast_data.starup_cost_other, "|")
					local starup_beast_list_data = {}
	
					for i, starup_beast_str_list in ipairs(starup_beast_group_str_list) do
						local starup_beast_list = Split(starup_beast_str_list, ",")
						local param_1 = tonumber(starup_beast_list[1]) or 0 
						local param_2 = tonumber(starup_beast_list[2]) or 0 
						local param_3 = tonumber(starup_beast_list[3]) or 0 
	
						local starup_beast_data = {}
						starup_beast_data.num = param_3
	
						if param_1 > 1000 then			-- 特殊幻兽
							starup_beast_data.beast_id = param_1
						elseif param_1 == 0 then		-- 使用碎片
							starup_beast_data.chip_id = param_2
							self.star_up_item_chip_list[param_2] = param_2
						elseif param_1 == -1 then		-- 只限制星级
							starup_beast_data.star = param_2
						elseif param_1 >= 1 and param_1 < 1000  then	-- 限制类型加星级
							starup_beast_data.element = param_1
							starup_beast_data.star = param_2
						end
		
						starup_beast_list_data[i] = starup_beast_data
					end
	
					self.beast_map_list[beast_id].starup_beast_ids = starup_beast_list_data
				end
			end

			if beast_data and beast_data.beast_preview then
				local beast_preview_list = Split(beast_data.beast_preview, "|")
				for index, beast_preview_data in ipairs(beast_preview_list) do
					local preview = {}
					local preview_list = Split(beast_preview_data, ",")
					preview.beast_star = tonumber(preview_list[1]) or 0
					preview.beast_id = tonumber(preview_list[2]) or 0

					self.beast_map_list[beast_id].beast_preview[index] = preview
				end
			end
		end
	end

	self.holy_spirit_page_name_list = {}
	if self.holy_spirit_page_name_cfg then
		for k, v in pairs(self.holy_spirit_page_name_cfg) do
			local page_name_list = Split(v.page_name, "|")
			self.holy_spirit_page_name_list[k] = page_name_list
		end
	end

	-- 初始化背包数据(三个背包)(发给服务器的话需要减一个位置)
	-- 未孵化背包
	-- 未孵化背包(排序)
	self.beasts_egg_sort_list = {}
	self.beasts_egg_list = {}
	for i = 1, BEAST_DEFINE.BEAST_PACK_COUNT_MAX do
		self.beasts_egg_list[i] = self:CreateBeastData()
	end
	-- 未孵化个数，给到孵化判断(增减量)
	self.beasts_egg_num = 0

	-- 已孵化背包
	self.beasts_list = {}
	-- 已孵化背包(排序)
	self.beasts_sort_list = {}
	for i = 1, BEAST_DEFINE.BEAST_BORN_COUNT_MAX do
		self.beasts_list[i] = self:CreateIncubateBeastData()
	end

	-- 圣兽列表
	self.holy_beasts_list = {}
	for i, v in ipairs(self.holy_beast_cfg) do
		local data = self:CreateHolyBeastData(v)
		self.holy_beasts_list[v.beast_type] = data
	end

	-- 孵化槽背包
	self.breeding_slot_list = {}
	for i = 1, BEAST_DEFINE.BEAST_BREEDING_COUNT_MAX do
		self.breeding_slot_list[i] = self:CreateBreedingSlotData()
	end

	-- 星阵背包
	self.beast_star_circle = {}
	for i = 1, BEAST_DEFINE.BEAST_FIGHTING_COUNT_MAX do
		self.beast_star_circle[i] = self:CreateCircleData()
	end

	self.related_beasts = {}
	self.preview_skill = {}
	if self.preview_beast_cfg and self.preview_beast_cfg.related_beasts then
		local related_beasts_list = Split(self.preview_beast_cfg.related_beasts, ",")
		for _, related_beasts_data in ipairs(related_beasts_list) do
			local beast_id = tonumber(related_beasts_data) or 0
			if beast_id ~= 0 then
				self.related_beasts[beast_id] = true
			end
		end

		local preview_skill_list = Split(self.preview_beast_cfg.preview_skill, ",")
		for i = 1, 9 do
			local skill_id = tonumber(preview_skill_list[i]) or -1
			if skill_id ~= -1 then
				table.insert(self.preview_skill, skill_id)
			else
				table.insert(self.preview_skill, -1)
			end
		end
	end

	self.beast_flair_item = {}
	if self.beast_flair_item_cfg then
		for _, item_data in pairs(self.beast_flair_item_cfg) do
			local flair_item_per = item_data.flair_item_per
			local flair_item_per_list = Split(item_data.flair_item_per, "|")
			local per_list = {}

			for i, per_str in ipairs(flair_item_per_list) do
				local per_num = tonumber(per_str) or 0
				per_list[i] = per_num / 100	--万分比，这里取百分号除以100
			end

			if item_data and item_data.min_star and item_data.max_star then
				for i = item_data.min_star, item_data.max_star do
					self.beast_flair_item[i] = {}
					self.beast_flair_item[i].item_id = item_data.flair_item_id or 0
					self.beast_flair_item[i].per_num = per_list[i] or 0
				end
			end
		end
	end

	self.preview_beast = self:CreatePreviewBeast()
	self.get_way_table = self:GetBaseGetWay()
	self.element_obj_id = 0
	self.element_param1 = 0
	self.element_param2 = 0
	self.element_level = 0

	self.selected_holy_beast_data = nil
end

-- 初始化一个出战位
function ControlBeastsWGData:CreateHoleData(cfg_data, is_main_battle)
	local hole_data = {}
	hole_data.hole_id = cfg_data.hole_id
	hole_data.role_level = cfg_data.role_level
	hole_data.fb_tower_level = cfg_data.fb_tower_level
	hole_data.vip_limit = cfg_data.vip_limit
	hole_data.zhuanzhi = cfg_data.zhuanzhi
	hole_data.can_battle = false					-- 是否可上阵红点
	hole_data.beasts_bag_id = -1						-- 灵兽背包id
	hole_data.hole_level = 0
	hole_data.is_main_battle = is_main_battle
	hole_data.state = cfg_data.is_need_unlock == 1 and BEASTS_HOLE_STATUS.NOT_ACTIVE or BEASTS_HOLE_STATUS.ACTIVE	-- 孔位状态
	hole_data.red = false
	
	hole_data.attr_types = cfg_data.attr_types
	hole_data.attr_list = {}
	if hole_data.attr_types ~= 0 then
		local str_list = Split(hole_data.attr_types, "|")
		for index, str in ipairs(str_list) do
			local attr_str = tonumber(str) or 0
			table.insert(hole_data.attr_list, attr_str)
		end
	end

	return hole_data
end

-- 初始化一个灵兽(未孵化)
function ControlBeastsWGData:CreateBeastData()
	local beast_data = {}
	beast_data.bag_id = 0					-- 背包id
	beast_data.item_id = 0					-- 物品id
	beast_data.server_data = nil
	beast_data.is_can_compose = false			-- 可合成
	beast_data.is_have_beast = false
	beast_data.is_egg = true
	beast_data.compose_status = BEASTS_COMPOSE_STATUS.NORMAL
	
	return beast_data
end

-- 初始化一个灵兽(已孵化)
function ControlBeastsWGData:CreateIncubateBeastData()
	local incubate_data = {}
	incubate_data.bag_id = 0							-- 背包id
	incubate_data.item_id = 0							-- 物品id
	incubate_data.server_data = nil
	incubate_data.is_can_upgrade = false				-- 可升级
	incubate_data.is_can_change_flair = false			-- 可提升资质
	incubate_data.is_can_compose = false				-- 可合成
	incubate_data.is_can_learn_skill = false			-- 可学技能
	incubate_data.is_have_beast = false
	incubate_data.is_holy_beast = false					-- 是否圣兽
	incubate_data.beast_type = false					-- 幻兽类型
	incubate_data.link_beast_data = incubate_data		-- 链接对象 无链接则为自身
	incubate_data.cap_value = 0							-- 战斗力
	incubate_data.flair_score = 0						-- 资质值 四个资质 相加 * 成长值
	incubate_data.is_egg = false
	incubate_data.is_preview = false					-- 是否是展示物
	incubate_data.pop_param = 0							-- 弹出类型
	incubate_data.compose_status = BEASTS_COMPOSE_STATUS.NORMAL

	return incubate_data
end

-- 初始化一个圣兽数据
function ControlBeastsWGData:CreateHolyBeastData(cfg)
	local holy_data = {}
	holy_data.cfg = cfg
	holy_data.beast_type = cfg.beast_type
	holy_data.bag_id = 0							-- 背包id
	holy_data.item_id = 0							-- 物品id
	holy_data.beast_data = nil						-- 普通幻兽数据
	holy_data.is_unlock = false						-- 是否解锁
	holy_data.is_contracted = false					-- 是否已链接
	holy_data.link_bag_id = -1						-- 链接对象背包id
	return holy_data
end

-- 初始化一个孵化槽
function ControlBeastsWGData:CreateBreedingSlotData()
	local breeding_slot = {}
	breeding_slot.bag_id = 0							-- 背包id
	breeding_slot.server_data = nil
	breeding_slot.status = BEASTS_BORN_STATUS.NONE		-- 可孵化
	breeding_slot.can_quick = false						-- 可快速孵化

	return breeding_slot
end

-- 初始化一个星阵
function ControlBeastsWGData:CreateCircleData()
	local circle_data = {}
	circle_data.attr_list = nil							-- 属性列表
	circle_data.interval_times = 0						-- 轮回数
	circle_data.is_unlock = 0							-- 是否解锁
	circle_data.is_need_promote = 0						-- 是否需要突破到下一轮回才能继续升星
	circle_data.is_can_circle = false					-- 可炼根

	return circle_data
end

-- 初始化一个预览御兽
function ControlBeastsWGData:CreatePreviewBeast()
	if (not self.preview_beast_cfg) or (not self.preview_beast_cfg.preview_beast) then
		return nil
	end

	local beast_data = self:CreateIncubateBeastData()
	beast_data.is_preview = true
	local server_data = {}
	server_data.beast_id = self.preview_beast_cfg.preview_beast
	server_data.exp = 0
	server_data.beast_level = 1
	server_data.stand_by_slot = -1
	server_data.skill_ids = self.preview_skill
	server_data.flair_values = {}

	local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(self.preview_beast_cfg.preview_beast)
	if map_data and map_data.beast_flair_preview then
		for index = 1, BEAST_DEFINE.BEAST_FLAIR_COUNT do
			local preview_data = map_data.beast_flair_preview[index]
			if preview_data then
				if index == BEAST_DEFINE.BEAST_FLAIR_COUNT then
					server_data.effort_value = preview_data.max or 0
					server_data.effort_value = server_data.effort_value * 10000
				else
					table.insert(server_data.flair_values, preview_data.max or 0)
				end
			end
		end
	end

	beast_data.server_data = server_data
	return beast_data
end

-- 清理垃圾
function ControlBeastsWGData:DeleteInfo()
	self.battle_main_hole_data = nil
	self.battle_assist_hole_data = nil
	self.beast_flair_score_list = nil
	self.beast_flair_change_list = nil
	self.beasts_group_list = nil
	self.beast_king_list = nil
	self.beast_map_list = nil
	-- 未孵化背包
	self.beasts_egg_list = nil
	-- 未孵化背包(排序)
	self.beasts_egg_sort_list = nil
	-- 已孵化背包
	self.beasts_list = nil
	-- 已孵化背包(排序)
	self.beasts_sort_list = nil
	-- 孵化槽背包
	self.breeding_slot_list = nil
	self.beasts_egg_num = 0
	-- 快速合成列表
	self.quick_compse_list = nil
	-- 基础信息
	self.beast_base_info = nil
	-- 星阵背包
	self.beast_star_circle = nil
	-- 提前展示龙
	self.related_beasts = nil
	self.preview_skill = nil
	self.preview_beast = nil
	-- 获取途径
	self.get_way_table = nil
	self.beast_king_level = nil
	-- 资质提升道具
	self.beast_flair_item = nil
	-- 选中的幻兽数据缓存
	self.open_skin_beasts_data = nil
	-- 圣兽数据
	self.holy_beasts_list = nil
	self.selected_holy_beast_data = nil
	-- 属性数据对象
	self.attribute_cache = nil
end

-- 总红点
function ControlBeastsWGData:ShowControlBeastsRemind()
	if self:GetBeastsBattleRemind() > 0 then
		return 1
	end

	if self:GetBeastsCultureRemind() > 0 then
		return 1
	end

	-- if self:GetBeastsRefiningRemind() > 0 then
	-- 	return 1
	-- end

	-- if self:GetBeastsStableRemind() > 0 then
	-- 	return 1
	-- end

	if self:GetBeastsComposeRemind() > 0 then
		return 1
	end

	if self:GetBeastsKingRemind() > 0 then
		return 1
	end
	-- if self:GetBeastsContractRemind() > 0 then
	-- 	return 1
	-- end

	return 0
end

-- 出战红点
function ControlBeastsWGData:GetBeastsBattleRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.BeastsBattle)
	if not is_open then
		return 0
	end

	if self:GetBeastsMainBattleRemind() then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_BATTLE, 1, TabIndex.beasts_battle)
		return 1
	end

	if self:GetBeastsSubBattleRemind() then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_BATTLE, 1, TabIndex.beasts_battle)
		return 1
	end

	if self:GetBeastsKingRemind() > 0 then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_BATTLE, 1, TabIndex.beasts_battle)
		return 1
	end
	
	self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_BATTLE, 0, TabIndex.beasts_battle)
	return 0
end

-- 出战主战位红点
function ControlBeastsWGData:GetBeastsMainBattleRemind()
	if self.battle_main_hole_data then
		for _, main_hole_data in ipairs(self.battle_main_hole_data) do
			if main_hole_data.red then
				return true
			end
		end
	end

	return false
end

-- 出战辅战位红点
function ControlBeastsWGData:GetBeastsSubBattleRemind()
	if self.battle_assist_hole_data then
		for _, assist_hole_data in ipairs(self.battle_assist_hole_data) do
			if assist_hole_data.red then
				return true
			end
		end
	end

	return false
end

-- 培养红点
function ControlBeastsWGData:GetBeastsCultureRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.BeastsCulture)
	if not is_open then
		return 0
	end

	if not self.beasts_list then
		return 0
	end

	local is_have_beast = false

	for _, beasts_data in ipairs(self.beasts_list) do
		if beasts_data.is_can_upgrade then
			self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_CULTURE, 1, TabIndex.beasts_culture)
			return 1
		end

		if beasts_data.is_can_change_flair then
			self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_CULTURE, 1, TabIndex.beasts_culture)
			return 1
		end

		if self:GetBeastsSkinRed(beasts_data) then
			return 1
		end

		if self:GetHolyBeastCanContract(beasts_data) then
			return 1
		end
		
		if self:GetHolyBeastSpiritRed(beasts_data.beast_type) then
			return 1
		end

		is_have_beast = beasts_data.is_have_beast or is_have_beast
	end

	if (not is_have_beast) then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_CULTURE, 0, TabIndex.beasts_culture)
		return 0
	end

	if ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRed() then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_CULTURE, 1, TabIndex.beasts_culture)
		return 1
	end

	if self:GetBeastsPelletRed() > 0  then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_CULTURE, 1, TabIndex.beasts_culture)
		return 1
	end

	self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_CULTURE, 0, TabIndex.beasts_culture)
	return 0
end

-- 培养升级红点
function ControlBeastsWGData:GetBeastsCultureUpgradeRemind()
	if not self.beasts_list then
		return 0
	end

	for _, beasts_data in ipairs(self.beasts_list) do
		if beasts_data.is_can_upgrade then
			return 1
		end
	end

	return 0
end

-- 培养转换资质红点
function ControlBeastsWGData:GetBeastsCultureChangeFlairRemind()
	if not self.beasts_list then
		return 0
	end

	for _, beasts_data in ipairs(self.beasts_list) do
		if beasts_data.is_can_change_flair then
			return 1
		end
	end

	return 0
end

-- 培养学技红点
function ControlBeastsWGData:GetBeastsCultureLearnSkillRemind()
	if not self.beasts_list then
		return 0
	end

	-- for _, beasts_data in ipairs(self.beasts_list) do
	-- 	if beasts_data.is_can_learn_skill then
	-- 		return 1
	-- 	end
	-- end

	return 0
end

-- 炼根红点
function ControlBeastsWGData:GetBeastsRefiningRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.BeastsRefining)
	if not is_open then
		return 0
	end

	if not self.beast_star_circle then
		return 0
	end

	for _, beasts_data in ipairs(self.beast_star_circle) do
		if beasts_data.is_unlock == 1 and beasts_data.is_can_circle then
			return 1
		end
	end

	return 0
end

-- 闲厩红点
function ControlBeastsWGData:GetBeastsStableRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.BeastsStable)
	if not is_open then
		return 0
	end

	if not self.breeding_slot_list then
		return 0
	end

	for _, beasts_data in ipairs(self.breeding_slot_list) do
		if beasts_data.status == BEASTS_BORN_STATUS.CAN_BORN then
			return 1
		elseif beasts_data.status == BEASTS_BORN_STATUS.UNBORN and beasts_data.can_quick then
			return 1
		elseif beasts_data.status == BEASTS_BORN_STATUS.BORN then
			return 1
		end
	end

	return 0
end

-- 合成红点
function ControlBeastsWGData:GetBeastsComposeRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.BeastsCompose)
	if not is_open then
		return 0
	end

	if not self.beasts_list then
		return 0
	end

	for _, beasts_data in ipairs(self.beasts_list) do
		if beasts_data.is_can_compose then
			return 1
		end
	end

	-- for _, beasts_egg_data in ipairs(self.beasts_egg_list) do
	-- 	if beasts_egg_data.is_can_compose then
	-- 		return 1
	-- 	end
	-- end

	return 0
end

-- 兽王
function ControlBeastsWGData:GetBeastsKingRemind()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.BeastsKing)
	if not is_open then
		return 0
	end

	if self:GetBeastBaseKingRed() then
		return 1
	end
	
	return 0
end

-- 抽奖
function ControlBeastsWGData:GetBeastsContractRemind()
	if ControlBeastsContractWGData.Instance:GetCurBeastDrawModeRed() then
		return 1
	end

	return 0
end

-- 内丹红点
function ControlBeastsWGData:GetInnerAlchemyRemind()
	local is_can_wear = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipIsCanWear()
	if is_can_wear then
		return 1
	end

	local is_can_equip_strengthen = ControlBeastsCultivateWGData.Instance:ShowAlchemyStrengthenRemind()
	if is_can_equip_strengthen ~= 0 then
		return 1
	end

	local is_can_equip_succinct = ControlBeastsCultivateWGData.Instance:GetAlchemySuccinctRemind()
	if is_can_equip_strengthen ~= 0 then
		return 1
	end

	return 0
end

-- 御兽数据排序（上阵 > 品质 > 战力 > 类型 > 星级 > 资质）
function ControlBeastsWGData:SortTableData(aim_table, is_egg)
	table.sort(aim_table, function (a, b)
		local server_data_A = a.server_data
		local server_data_B = b.server_data
		if (not server_data_A) or (not server_data_B) then
			return false
		end

		local compare_A = server_data_A.stand_by_slot
		local compare_B = server_data_B.stand_by_slot
		if compare_A ~= compare_B and (compare_A ~= -1 or compare_B ~= -1) then
			if compare_A == -1 then
				return false
			end

			if compare_B == -1 then
				return true
			end

			return compare_A < compare_B
		end

		local beast_cfg_A = self:GetBeastCfgById(server_data_A.beast_id)
		local beast_cfg_B = self:GetBeastCfgById(server_data_B.beast_id)
		if (not beast_cfg_A) or (not beast_cfg_B) then
			return false
		end

		compare_A = beast_cfg_A.beast_color
		compare_B = beast_cfg_B.beast_color
		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		-- compare_A = a.cap_value
		-- compare_B = b.cap_value
		-- if compare_A ~= compare_B then
		-- 	return compare_A > compare_B
		-- end

		compare_A = beast_cfg_A.beast_type
		compare_B = beast_cfg_B.beast_type
		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		compare_A = beast_cfg_A.beast_star
		compare_B = beast_cfg_B.beast_star

		-- if is_egg then
		-- 	return compare_A > compare_B
		-- end

		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		-- compare_A = a.flair_score
		-- compare_B = b.flair_score

		return compare_A > compare_B
	end)
end

-- 御兽数据排序2
function ControlBeastsWGData:SortTableData2(aim_table)
	table.sort(aim_table, function (a, b)
		local beast_data_A = a.beast_data
		local beast_data_B = b.beast_data
		if (not beast_data_A) or (not beast_data_B) then
			return false
		end
		
		local server_data_A = beast_data_A.server_data
		local server_data_B = beast_data_B.server_data
		if (not server_data_A) or (not server_data_B) then
			return false
		end

		local beast_cfg_A = self:GetBeastCfgById(server_data_A.beast_id)
		local beast_cfg_B = self:GetBeastCfgById(server_data_B.beast_id)
		if (not beast_cfg_A) or (not beast_cfg_B) then
			return false
		end

		local compare_A = beast_data_A.cap_value
		local compare_B = beast_data_B.cap_value
		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		local compare_A = beast_cfg_A.beast_color
		local compare_B = beast_cfg_B.beast_color
		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		compare_A = beast_cfg_A.beast_type
		compare_B = beast_cfg_B.beast_type
		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		compare_A = beast_cfg_A.beast_star
		compare_B = beast_cfg_B.beast_star
		if compare_A ~= compare_B then
			return compare_A > compare_B
		end

		compare_A = beast_data_A.flair_score
		compare_B = beast_data_B.flair_score
		return compare_A > compare_B
	end)
end

-- 御兽变强更新
function ControlBeastsWGData:SetBeastStrengthen(tip_type, value, tab_index)
	if value == 1 then
		MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.ControlBeastsView, tab_index)
            return true
        end)
	else
		MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
	end
end
----------------------------------------直接获取配置表信息开始---------------------------------------------
---获取基础表
function ControlBeastsWGData:GetBaseCfg()
	return self.base_cfg
end

---获取基础幻兽表
function ControlBeastsWGData:GetBeastsMapCfg()
	return self.beasts_map_cfg
end

function ControlBeastsWGData:IsBeastsItem(item_id)
	return nil ~= self.beasts_map_cfg[item_id]
end

function ControlBeastsWGData:GetBeastsChipIdToBeastsId(item_id)
	return (self.beasts_chip_map_cfg[item_id] or {}).beast_id
end

---获取获取途径
function ControlBeastsWGData:GetBaseGetWay()
	local get_way_table = {}

	if not self.base_cfg then
		return nil
	end

	local get_way_list = Split(self.base_cfg.get_way, ",")
	for _, get_way_str in ipairs(get_way_list) do
		local value = tonumber(get_way_str) or 0
		local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way[value]
		table.insert(get_way_table, get_way_cfg)
	end

	return get_way_table
end

function ControlBeastsWGData:GetBaseGetWayTable()
	return self.get_way_table
end

-- 获取孔位配置信息根据ID
function ControlBeastsWGData:GetHoleCfgById(hole_id)
	local empty = {}
	return (self.battle_hole_cfg or empty)[hole_id] or nil
end

-- 获取孔位升级信息根据ID
function ControlBeastsWGData:GetHoleLevelCfgById(hole_id, level)
	local empty = {}
	return ((self.hole_level_map_cfg or empty)[hole_id] or empty)[level] or nil
end

-- 获取灵兽配置信息根据ID
function ControlBeastsWGData:GetBeastCfgById(beast_id)
	local empty = {}
	return ((self.beast_map_list or empty)[beast_id] or empty).beast_data or nil
end

-- 获取升星灵兽配置
function ControlBeastsWGData:GetBeastStarupCfgById(beast_id)
	local empty = {}
	return ((self.beast_map_list or empty)[beast_id] or empty).starup_beast_ids or nil
end

-- 获取灵兽配置信息根据ID
function ControlBeastsWGData:GetBeastDatMapaById(beast_id)
	local empty = {}
	return (self.beast_map_list or empty)[beast_id] or nil
end

-- 获取灵兽的兽王配置根据等级
function ControlBeastsWGData:GetBeastKingDataBylevel(beast_king_level)
	local empty = {}
	return (self.beast_king_list or empty)[beast_king_level] or nil
end

-- 获取灵兽升级信息根据等级（所有灵兽使用一套经验等级配置）
function ControlBeastsWGData:GetBeastLevelCfgByLevel(beast_level)
	local empty = {}
	return (self.beast_level_map_cfg or empty)[beast_level] or nil
end

-- 获取灵兽至臻属性信息(所有属性)
function ControlBeastsWGData:GetBeastBestAttrDataListById(beast_id)
	local empty = {}
	return (self.beast_best_attr_map_cfg or empty)[beast_id] or nil
end

-- 获取灵兽至臻属性信息(单个最佳匹配属性)返回当前数据和下一级数据
function ControlBeastsWGData:GetNowBeastAttrBestCfgByLevel(beast_id, beast_level)
	local best_data_list = self:GetBeastBestAttrDataListById(beast_id)
	if not best_data_list then
		return nil, nil
	end

	local cur_data = best_data_list[beast_level]
	local next_data = best_data_list[beast_level + 1]
	if cur_data then		---匹配到了当前级直接返回一下
		return cur_data, next_data
	end

	--没有匹配到当前级则需要排查到当前低于等级的属性但是下一级还是当前等级加一，没有则返回空
	local cur_max_level = 0
	for _, best_data in pairs(best_data_list) do
		if best_data and best_data.beast_level then
			if best_data.beast_level <= beast_level and cur_max_level <= best_data.beast_level then
				cur_max_level = best_data.beast_level 
			end
		end
	end

	cur_data = best_data_list[cur_max_level]
	return cur_data, next_data
end

-- 获取资质加成属性数据
function ControlBeastsWGData:GetFlairAttrDataById(flair_id)
	local empty = {}
	return (self.beast_flair_map_cfg or empty)[flair_id] or nil
end

-- 获取技能相关配置
function ControlBeastsWGData:GetSkillDataBySkillId(skill_id)
	local empty = {}
	return (self.skill_pool_map_cfg or empty)[skill_id] or nil
end

-- 获取灵兽的模型资源id
function ControlBeastsWGData:GetBeastModelResId(beast_id, skin_seq)
	if skin_seq and skin_seq >= 0 then
		return self:GetBeastModelSkinResId(skin_seq)
	end

	local beast_cfg = self:GetBeastCfgById(beast_id)
	if beast_cfg then
		return beast_cfg.beast_model
	end
	return 0
end

-- 获取灵兽的皮肤模型资源id
function ControlBeastsWGData:GetBeastModelSkinResId(skin_seq)
	local skin_cfg = self:GetBeastsSkinCfgBySeq(skin_seq)
	if skin_cfg then
		return skin_cfg.beast_skin_model
	end
	return 0
end

-- 获取灵兽的皮肤模型资源id
function ControlBeastsWGData:GetBeastModelSkinResIdByItemId(item_id)
	return (self.skin_cfg_id[item_id] or {}).beast_skin_model or 0
end

-- 获取是否是需要弹出新获得的幻兽
function ControlBeastsWGData:GetIsShowNewBeast(beast_id)
	local beast_cfg = self:GetBeastCfgById(beast_id)
	local base_cfg = self:GetBaseCfg()
	
	if beast_cfg and base_cfg then
		return beast_cfg.beast_color >= base_cfg.need_getnew_color
	end

	return 0
end

-- 获取属性列表
function ControlBeastsWGData:GetBeastLevelData(data)
	if data and data.bag_id and data.is_beast then
		local beast_data = self:GetBeastDataById(data.bag_id)
		if beast_data and beast_data.server_data then
			return self:GetBeastLevelCfgByLevel(beast_data.server_data.beast_level)
		end
	end

	return self:GetBeastLevelCfgByLevel(1)
end

-- 组合基础的属性列表
function ControlBeastsWGData:GetBeastLevelAndBaseData(beast_id, level_cfg)
	local best_beas_cfg = self:GetBeastCfgById(beast_id)
	local attr_cfg = {}
	if best_beas_cfg and level_cfg then
		for i = 1, 6 do
			local key = string.format("attr_id%d", i)
			local value_key = string.format("attr_value%d", i)
			attr_cfg[key] = best_beas_cfg[key] or 0
			attr_cfg[value_key] = best_beas_cfg[value_key] or 0

			if level_cfg[value_key] and level_cfg[key] then
				attr_cfg[value_key] = attr_cfg[value_key] + level_cfg[value_key]
			end
		end
	end

	return attr_cfg
end


-- 获取臻品属性列表
function ControlBeastsWGData:GetBeastBestLevelData(data)
	if data.is_egg then
		return self:GetNowBeastAttrBestCfgByLevel(data.item_id, 1)
	else
		local beast_data = self:GetBeastDataById(data.bag_id)
		if beast_data and beast_data.server_data then
			return self:GetNowBeastAttrBestCfgByLevel(beast_data.server_data.beast_id, beast_data.server_data.beast_level)
		end
		return nil
	end
end

-- 获取一个已孵化的灵兽战力
function ControlBeastsWGData:GetBeastBestCapability(data)
	if data.bag_id then
		local beast_data = self:GetBeastDataById(data.bag_id)
		if beast_data and beast_data.server_data then
			local cap, _ = ControlBeastsWGData.Instance:GetBeastsCapValue(beast_data.server_data)
			return tonumber(cap) or 0 
		end
	end

	return 0
end

-- 获取一个已孵化的灵兽资质评分等级
function ControlBeastsWGData:GetBeastBestFlairScoreIndex(data, is_stable)
	local score_index = 0
	local flair_score = 0

	local beast_data = nil

	if is_stable then
		beast_data = data
	else
		beast_data = self:GetBeastDataById(data.bag_id)
	end

	if beast_data and beast_data.server_data then
		local server_data = beast_data.server_data
		if server_data.flair_values then
			for index, flair_value in ipairs(server_data.flair_values) do
				flair_score = flair_score + flair_value
			end
		end

		flair_score = flair_score * (server_data.effort_value / 10000)
		local beast_cfg = self:GetBeastCfgById(server_data.beast_id)
		if beast_cfg then
			score_index = self:GetFlairScoreByScore(beast_cfg.beast_star, flair_score) or 0
		end
	end

	return score_index, flair_score
end

-- 获取臻品属性解锁条件
function ControlBeastsWGData:GetBeastBestUnlockStar(data, index)
	if data.is_egg then
		return ""
	else
		local beast_data = self:GetBeastDataById(data.bag_id)
		if beast_data and beast_data.server_data then
			local beast_data_map = self:GetBeastDatMapaById(beast_data.server_data.beast_id)
			local beast_star = 0
			if beast_data_map and beast_data_map.beast_data then
				beast_star = beast_data_map.beast_data.beast_star or beast_star
			end

			local need_star = 0
			if beast_data_map and beast_data_map.beast_best_attr_star then
				need_star = beast_data_map.beast_best_attr_star[index] or 0
			end
			
			if beast_star >= need_star then
				return ""
			else
				return string.format(Language.F2Tip.BeastAttrEgg, need_star)
			end
		end

		return ""
	end
end

-- 获取属性库里面的属性配置通过属性id
function ControlBeastsWGData:GetStarCircleAttrCfgById(attr_id)
	local empty = {}
	return (self.star_circle_attr_pool or empty)[attr_id] or nil
end

-- 获取属性库里面的属性配置通过属性id
function ControlBeastsWGData:GetStarCirclereRreshAttrCfgList()
	local beast_base_info = self:GetBeastBaseInfo()

	if not beast_base_info then
		return nil
	end

	return (self.star_circle_refresh_map_pool or {})[beast_base_info.beast_king_level] or nil
end

-- 获取属性库里面的属性配置通过属性id
function ControlBeastsWGData:GetStarCirclerePreviewAttrCfgList()
	local beast_king_level = self:GetBeastBaseInfoKingLevel()

	if not beast_king_level then
		return nil
	end

	if not self.star_circle_attr_preview_cfg then
		return nil
	end
	
	local show_table = {}
	for _, preview_data in pairs(self.star_circle_attr_preview_cfg) do
		if preview_data and preview_data.beast_king_level then
			local data = {}
			data.beast_king_level = preview_data.beast_king_level
			data.attr_name = preview_data.attr_name
			data.is_unlock = beast_king_level >= preview_data.beast_king_level
			table.insert(show_table, data)
		end
	end

	return show_table
end

-- 获取星阵轮回配置通过星阵轮回
function ControlBeastsWGData:GetStarCircleLevelCfgById(star_level)
	local empty = {}
	return (self.star_circle_level or empty)[star_level] or nil
end

-- 获取分解配置根据驭兽id
function ControlBeastsWGData:GetBeastDesposeMessageCfgById(beast_id)
	local empty = {}
	return (self.beast_break_cfg or empty)[beast_id] or nil
end

-- 获取洗练配置根据seq
function ControlBeastsWGData:GetBeastRefineCfgBySeqIndex(seq, index)
	local empty = {}
	return((self.refine_cfg or empty)[seq] or empty)[index]
end

-- 获取洗练配置根据seq
function ControlBeastsWGData:GetBeastRefineCfgListBySeq(seq)
	local empty = {}
	return(self.refine_cfg or empty)[seq] 
end

-- 获取洗练权重配置根据seq
function ControlBeastsWGData:GetBeastRefineWeightCfgBySeq(seq, level)
	local empty = {}
	return((self.refine_weight_cfg or empty)[seq] or empty)[level]
end

-- 获取技能组配置根据seq
function ControlBeastsWGData:GetBeastSkillGroupCfgBySeq(seq)
	local empty = {}
	return (self.beast_skill_group_cfg or empty)[seq]
end

-- 获取技能组配置根据seq
function ControlBeastsWGData:GetBeastStarSkillGroupCfgBySeq(seq)
	local empty = {}
	return (self.beast_star_skill_group_cfg or empty)[seq]
end

-- 获取技能组配置根据seq
function ControlBeastsWGData:GetBeastBeSkillCfgBySeq(seq)
	local empty = {}
	return (self.be_skill_cfg or empty)[seq]
end

-- 获取共鸣属性根据类型
function ControlBeastsWGData:GetFetterCfgByType(fetters_type)
	local empty = {}
	return (self.fetters_attr_cfg or empty)[fetters_type]
end

-- 获取共鸣属性根据类型
function ControlBeastsWGData:GetFetterCfgByTypeNumber(fetters_type, num)
	local empty = {}
	return ((self.fetters_attr_cfg2 or empty)[fetters_type] or empty)[num]
end

-- 获取图鉴奖励
function ControlBeastsWGData:GetHandBookRewardCfgByTypeStar(beast_type, beast_star)
	local empty = {}
	return ((self.handbook_reward_cfg or empty)[beast_type] or empty)[beast_star]
end

-- 获取图鉴描述
function ControlBeastsWGData:GetHandBookDescCfgByType(beast_type)
	local empty = {}
	return (self.handbook_desc_cfg or empty)[beast_type]
end

-- 获取属性丹配置
function ControlBeastsWGData:GetBeastsPelletCfg()
	return self.pellet_cfg
end

-- 获取属性丹配置
function ControlBeastsWGData:GetBeastsPelletCfgBySeq(seq)
	local empty = {}
	return (self.pellet_cfg or empty)[seq]
end
----------------------------------------直接获取配置表信息结束--------------------------------------------

----------------------------------------获取客户端封装配置表信息开始---------------------------------------
-- 获取孔位信息根据ID(客户端数据, 服务器从0开始的)
-- 这里请注意去表中的数据使用hole_id
-- 取缓存数据需要用列表client_hole_id
function ControlBeastsWGData:GetHoleDataById(client_hole_id)
	local empty = {}			
	local list = self.battle_main_hole_data
	if client_hole_id > 3 then
		client_hole_id = client_hole_id - 3		-- 下标剪掉三个对应去掉主战位三个位置偏差
		list = self.battle_assist_hole_data
	end

	return (list or empty)[client_hole_id] or nil
end

-- 获取主战位孔位信息
function ControlBeastsWGData:GetHoleMainData()
	return self.battle_main_hole_data
end

-- 获取主战位孔位信息
function ControlBeastsWGData:GetHoleMainDataByHoleId(client_hole_id)
	local empty = {}
	return (self.battle_main_hole_data or empty)[client_hole_id] or nil
end

-- 获取辅战位孔位信息
function ControlBeastsWGData:GetHoleAssistData()
	return self.battle_assist_hole_data
end

-- 获取主战位孔位信息
function ControlBeastsWGData:GetHoleAssistDataByHoleId(client_hole_id)
	local empty = {}
	return (self.battle_assist_hole_data or empty)[client_hole_id] or nil
end

-- 获取出战的驭兽总等级
function ControlBeastsWGData:GetBattleBeastsAllLevel()
	local all_level = 0
	local battle_main_hole_data = self:GetHoleMainData()
	local battle_assist_hole_data = self:GetHoleAssistData()

	for k, v in pairs(battle_main_hole_data) do
		if v.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(v.beasts_bag_id)
			if beast_data and beast_data.server_data then
				all_level = all_level + beast_data.server_data.beast_level
			end
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(v.beasts_bag_id)
			if beast_data and beast_data.server_data then
				all_level = all_level + beast_data.server_data.beast_level
			end
		end
	end

	return all_level
end

-- 获取已孵化的驭兽数据
function ControlBeastsWGData:GetBeastDataById(bag_id)
	local empty = {}
	local client_hole_id = bag_id				-- 先加一取到客户端数据id
	return (self.beasts_list or empty)[client_hole_id] or nil
end

-- 获取已孵化的驭兽列表
function ControlBeastsWGData:GetBeastsList()
	return self.beasts_list
end

-- 获取已孵化的驭兽可上阵列表
function ControlBeastsWGData:GetBattleBeastsList(is_change, stand_by_slot, self_bag_id)
	if not self.beasts_list then
		return nil
	end

	local now_battle_type_list = {}
	local battle_beast_data = {}
	local now_battle_hole_list = {}
	local self_battle_type = nil
	local self_battle_cap = 0
	local is_add_self = false
	local battle_main_hole_data = self:GetHoleMainData()
	local battle_assist_hole_data = self:GetHoleAssistData()

	local self_beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self_bag_id)
	if self_beast_data and self_beast_data.server_data then
		local cfg = self:GetBeastCfgById(self_beast_data.server_data.beast_id)
		self_battle_type = cfg and cfg.beast_type or 0
		self_battle_cap = self_beast_data.cap_value
	end

	local add_to_battle_id_func = function(beast_type)
		now_battle_type_list[beast_type] = true
	end

	local add_battle_beast_func = function(beast_data)
		local new_data = {}
		new_data.beast_data = beast_data
		new_data.is_change = is_change
		new_data.is_egg = false
		new_data.stand_by_slot = stand_by_slot
		new_data.cap_value = beast_data.cap_value--self:GetBeastsCapValue(beast_data.server_data)

		table.insert(battle_beast_data, new_data)
	end

	for k, v in pairs(battle_main_hole_data) do
		if v.beasts_bag_id ~= -1 then
			local beast_data = self:GetBeastDataById(v.beasts_bag_id)
			if beast_data and beast_data.server_data then
				local cfg = self:GetBeastCfgById(beast_data.server_data.beast_id)
				add_to_battle_id_func(cfg and cfg.beast_type or 0)
			end
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.beasts_bag_id ~= -1 then
			local beast_data = self:GetBeastDataById(v.beasts_bag_id)
			if beast_data and beast_data.server_data then
				local cfg = self:GetBeastCfgById(beast_data.server_data.beast_id)
				add_to_battle_id_func(cfg and cfg.beast_type or 0)
			end
		end
	end

	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data 
			and beast_data.is_have_beast 
			and beast_data.server_data 
			and self_bag_id ~= beast_data.bag_id 
			and beast_data.server_data.stand_by_slot == -1 
			and ((beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1) or
				(not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index == -1))
		then --已上阵的也能上阵
			local cfg = self:GetBeastCfgById(beast_data.server_data.beast_id)
			local beast_type = cfg and cfg.beast_type or 0

			if beast_data.cap_value > self_battle_cap then
				is_add_self = true
			end

			if not now_battle_type_list[beast_type] then
				add_battle_beast_func(beast_data)
			else
				if is_change and beast_type == self_battle_type then
					add_battle_beast_func(beast_data)
				end
			end
		end
	end

	if is_add_self and self_beast_data and self_beast_data.server_data then
		add_battle_beast_func(self_beast_data)
	end

	self:SortTableData2(battle_beast_data)
	return battle_beast_data
end

-- 获取当前已孵化的驭兽的最高等级
function ControlBeastsWGData:GetBeastsMaxLevel()
	if not self.beasts_list then
		return nil
	end

	local beast_best_level = 0

	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data and beast_data.is_have_beast and beast_data.server_data then
			if beast_data.server_data.beast_level > beast_best_level then
				beast_best_level = beast_data.server_data.beast_level
			end
		end
	end

	return beast_best_level
end

-- 获取未孵化可被吞噬的驭兽(is_all 全部选取)
function ControlBeastsWGData:GetCanSelectBeastsEggList(aim_beast_id, is_special, is_all)
	if not self.beasts_egg_list then
		return nil
	end

	local battle_beast_data = {}

	for _, beasts_egg_data in ipairs(self.beasts_egg_list) do
		if beasts_egg_data and beasts_egg_data.is_have_beast and beasts_egg_data.server_data then
			if is_all then
				table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beasts_egg_data, true, beasts_egg_data.server_data.beast_id))
			else
				if is_special then
					if self:CheckEnoughSpecialCondition(aim_beast_id, beasts_egg_data.server_data.beast_id) then
						table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beasts_egg_data, true, beasts_egg_data.server_data.beast_id))
					end
				else
					if self:CheckEnoughSameStarCondition2(aim_beast_id, beasts_egg_data.server_data.beast_id)  then
						table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beasts_egg_data, true, beasts_egg_data.server_data.beast_id))
					end
				end
			end
		end
	end

	return battle_beast_data
end

--可被吞噬的驭兽数据
function ControlBeastsWGData:CreateCanSelectBeastsData(beast_data, is_egg, item_id)
	local new_data = {}
	new_data.beast_data = beast_data
	new_data.is_egg = is_egg
	new_data.item_id = item_id
	return new_data
end

-- 获取孵化可被吞噬的驭兽(is_all 全部选取)
function ControlBeastsWGData:GetCanSelectBeastsList(aim_beast_id, is_special, is_element, element, star, is_all, select_list)
	if not self.beasts_list then
		return nil
	end

	local battle_beast_data = {}

	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data and beast_data.is_have_beast and beast_data.server_data and beast_data.server_data.stand_by_slot == -1 and beast_data.server_data.holy_spirit_link_index == -1
		and (not ((beast_data.server_data.beast_level > 1 or beast_data.server_data.exp > 0) or beast_data.server_data.refine_times > 0)) then
			if is_all then
				table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beast_data, false, beast_data.server_data.beast_id))
			else
				if is_special then
					if self:CheckEnoughSpecialCondition(aim_beast_id, beast_data.server_data.beast_id) and (not select_list[beast_data.bag_id]) then
						table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beast_data, false, beast_data.server_data.beast_id))
					end
				elseif is_element then
					if self:CheckEnoughSameElementCondition(aim_beast_id, element, star, beast_data.server_data.beast_id) and (not select_list[beast_data.bag_id]) then
						table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beast_data, false, beast_data.server_data.beast_id))
					end
				else
					if self:CheckEnoughSameStarCondition(aim_beast_id, star, beast_data.server_data.beast_id) and (not select_list[beast_data.bag_id]) then
						table.insert(battle_beast_data, self:CreateCanSelectBeastsData(beast_data, false, beast_data.server_data.beast_id))
					end
				end
			end
		end
	end

	return battle_beast_data
end

-- 获取已孵化的驭兽可培养列表(合成不拿提前展示龙)
function ControlBeastsWGData:GetCultureBeastsList(is_compose, is_sort, compose_index, culture_index)
	if not self.beasts_list then
		return nil
	end

	local battle_beast_data = {}
	local is_have_preview_beast = false

	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data and beast_data.is_have_beast and beast_data.server_data then
			beast_data.need_show_red = false			-- 格子展示红点（不同页签不同条件，临时字段）

			if culture_index then
				beast_data.need_show_red = beast_data.is_can_upgrade or beast_data.is_can_change_flair or 
											self:GetBeastsSkinRed(beast_data) or
											self:GetHolyBeastCanContract(beast_data) or
											self:GetHolyBeastSpiritRed(beast_data.beast_type)
			elseif compose_index then
				beast_data.need_show_red = beast_data.is_can_compose 
			end
			table.insert(battle_beast_data, beast_data)

			if not is_have_preview_beast then
				is_have_preview_beast = self:CheckHavePreviewBeast(beast_data.server_data.beast_id)
			end
		end
	end

	if is_sort then
		self:SortTableData(battle_beast_data)
	end

	local function move_element_to_index(t, old_index, new_index)
		if new_index > #t then
			for i = #t, new_index - 1 do
				table.insert(t, self:CreateIncubateBeastData())
			end
		end
		if old_index <= #t then
			local value = t[old_index]
			table.remove(t, old_index)
			table.insert(t, new_index, value)
		end
	end
	-- 圣兽展示排序
	local swap_list = {}
	for i = 1, #battle_beast_data do
		local beast_data = battle_beast_data[i]
		if beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1 then
			local old_index = nil
			local new_index = nil
			for j, v in ipairs(battle_beast_data) do
				if v.bag_id == beast_data.server_data.holy_spirit_link_index + 1 then
					old_index = j
					new_index = j > i and i + 2 or i + 1
					break
				end
			end
			if new_index and new_index ~= old_index then
				move_element_to_index(battle_beast_data, old_index, new_index)
			end
		end
	end

	-- if (not is_compose) and (not is_have_preview_beast) and #battle_beast_data <= 0 then
	-- 	local preview_beast = self.preview_beast
	-- 	if preview_beast then
	-- 		table.insert(battle_beast_data, 1, preview_beast)
	-- 	end
	-- end


	return battle_beast_data
end

-- 获取已孵化的驭兽可培养列表
-- is_reset  	是否为重置列表（培养过的可以重置）
-- is_despose  	是否为分解列表（需要限制未培养过的品质颜色，sr及以下的才可以分解 color<=3）
-- is_back  	是否为回退列表（需要限制未培养过的已升过星红色的幻兽 星级 >=6 星）
local DESPOSE_COLOR = 3
local BACK_STAR = 5
function ControlBeastsWGData:GetCultureBeastsList2(is_reset, is_despose, is_back, is_sort)
	if not self.beasts_list then
		return nil
	end

	local battle_beast_data = {}

	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data and beast_data.is_have_beast and beast_data.server_data then --and beast_data.server_data.stand_by_slot == -1 
			beast_data.need_show_red = false			-- 格子展示红点（不同页签不同条件，临时字段）
			local server_data = beast_data.server_data
			-- 计算资质评级
			local beast_cfg = self:GetBeastCfgById(server_data.beast_id)
			local cfg = self:GetBeastDesposeMessageCfgById(server_data.beast_id)
			local beast_color = beast_cfg and beast_cfg.beast_color or 0
			local beast_star = beast_cfg and beast_cfg.beast_star or 0

			-- 非圣兽且未缔结
			local flag = not beast_data.is_holy_beast and server_data.holy_spirit_link_index == -1 and beast_data.server_data.stand_by_slot == -1 
			-- 重置列表
			if is_reset and beast_data.server_data.stand_by_slot == -1 and flag then
				if server_data.beast_level > 1 or server_data.exp > 0 then
					table.insert(battle_beast_data, beast_data)
				end
			elseif is_despose and beast_data.server_data.stand_by_slot == -1 and flag and (cfg ~= nil and cfg.break_exp ~= nil and (not IsEmptyTable(cfg.break_exp))) then
				if not ((server_data.beast_level > 1 or server_data.exp > 0) or server_data.refine_times > 0) and beast_color ~= 0 and beast_color <= DESPOSE_COLOR then
					table.insert(battle_beast_data, beast_data)
				end	
			elseif is_back and flag and (cfg ~= nil and ((cfg.back_item ~= nil and (not IsEmptyTable(cfg.back_item))) or (cfg.back_item ~= nil and cfg.back_item ~= 0))) then 
				if not (server_data.beast_level > 1 or server_data.exp > 0) and beast_star ~= 0 and beast_star >= BACK_STAR then
					table.insert(battle_beast_data, beast_data)
				end	
			end
		end
	end

	if is_sort then
		self:SortTableData(battle_beast_data)
	end

	return battle_beast_data
end

-- 获取预览的那个数据
function ControlBeastsWGData:GetPreviewBeast()
	return self.preview_beast
end

-- 检测是否包含提前展示龙
function ControlBeastsWGData:CheckHavePreviewBeast(beast_id)
	if not self.related_beasts then
		return false
	end

	if self.related_beasts[beast_id] then
		return true
	end

	return false
end

-- 获取可培养的灵兽个数
function ControlBeastsWGData:GetCultureBeastsListCount()
	local battle_beast_data = self:GetCultureBeastsList(true)
	return #battle_beast_data
end

-- 获取一个星阵通过id
function ControlBeastsWGData:GetStarCircleDataById(bag_id)
	local empty = {}
	local client_hole_id = bag_id				-- 先加一取到客户端数据id
	return (self.beast_star_circle or empty)[client_hole_id] or nil
end

-- 获取一个星阵属性数据通过id
function ControlBeastsWGData:GetStarCircleAttrDataById(bag_id, attr_index)
	local empty = {}
	local client_hole_id = bag_id				-- 先加一取到客户端数据id
	return (((self.beast_star_circle or empty)[client_hole_id] or empty).attr_list or empty)[attr_index] or nil
end

-- 获取所有的星阵
function ControlBeastsWGData:GetStarCircleData()
	return self.beast_star_circle
end

-- 获取所有的星阵轮数总和
function ControlBeastsWGData:GetStarCircleTotleIntervalTimes()
	if not self.beast_star_circle then
		return 0
	end

	local totle_interval_times = 0
	for _, fefining_data in ipairs(self.beast_star_circle) do
		totle_interval_times = totle_interval_times + fefining_data.interval_times
	end

	return totle_interval_times
end

-- 获取星阵对应的最大灵兽等级
function ControlBeastsWGData:GetStarCircleBeastMaxLevel(totle_interval_times)
	if not totle_interval_times then
		totle_interval_times = self:GetStarCircleTotleIntervalTimes()
	end

	if not self.beast_level_limit then
		return 99
	end

	for _, level_limit_data in pairs(self.beast_level_limit) do
		if level_limit_data and level_limit_data.total_array_level_start then
			if totle_interval_times >= level_limit_data.total_array_level_start and totle_interval_times <= level_limit_data.total_array_level_end then
				return level_limit_data.beast_max_level
			end
		end
	end

	return 99
end

-- 获取星阵对应的最大灵兽等级
function ControlBeastsWGData:GetStarCircleBeastBreakLevel(totle_interval_times)
	if not totle_interval_times then
		totle_interval_times = self:GetStarCircleTotleIntervalTimes()
	end

	if not self.beast_level_limit then
		return 99, nil
	end

	for _, level_limit_data in pairs(self.beast_level_limit) do
		if level_limit_data and level_limit_data.total_array_level_start then
			if totle_interval_times >= level_limit_data.total_array_level_start and totle_interval_times <= level_limit_data.total_array_level_end then
				return totle_interval_times, level_limit_data.total_array_level_end + 1
			end
		end
	end

	return 99, nil
end

function ControlBeastsWGData:GetStarCircleBeastLevelCfg()
	return self.beast_level_limit or {}
end

function ControlBeastsWGData:GetBeastLevelLimitCfgByStar(start_level)
	return (self.beast_star_level_limit or {})[start_level]
end

function ControlBeastsWGData:GetElementEffectCfg()
	return self.element_effect_cfg or {}
end

-- 获取未孵化的驭兽数据
function ControlBeastsWGData:GetBeastEggDataById(bag_id)
	local empty = {}
	local client_hole_id = bag_id				-- 先加一取到客户端数据id
	return (self.beasts_egg_list or empty)[client_hole_id] or nil
end

-- 获取已孵化的驭兽列表
function ControlBeastsWGData:GetBeastsEggList()
	return self.beasts_egg_list
end

-- 获取未孵化背包是否有可孵化的幻兽
function ControlBeastsWGData:GetBeastsEggListIsHaveBeast()
	for _, beast_egg_data in ipairs(self.beasts_egg_list) do
		if beast_egg_data and beast_egg_data.is_have_beast then
			return true
		end
	end
	return false
end

-- 获取资质评级信息根据星级(客户端数据)
function ControlBeastsWGData:GetFlairDataByStar(beast_star)
	local empty = {}
	return (self.beast_flair_score_list or empty)[beast_star] or nil
end

function ControlBeastsWGData:GetFlairScoreByServerData(server_data)
	if (not server_data) or (not server_data.flair_values) then
		return 0, 0
	end

	local flair_score = 0
	local daim_index = 0

	for _, flair_value in ipairs(server_data.flair_values) do
		flair_score = flair_score + flair_value
	end
	
	flair_score = math.floor(flair_score * (server_data.effort_value / 10000))
	-- 计算资质评级
	local beast_cfg = self:GetBeastCfgById(server_data.beast_id)

	if beast_cfg then
		daim_index = self:GetFlairScoreByScore(beast_cfg.beast_star, flair_score)
	end

	return flair_score, daim_index
end


-- 获取一个等级资质的评分
function ControlBeastsWGData:GetFlairScoreByScore(beast_star, flair_score)
	local flair_score_list = self:GetFlairDataByStar(beast_star)

	if not flair_score_list then
		return 1
	end

	local daim_index = 1
	for index, score in ipairs(flair_score_list) do
		if flair_score >= score then
			daim_index = index
		else
			break
		end
	end

	---融合可能出现超过区间取了最后一个值，这里要返回到可取范围
	if daim_index > #flair_score_list - 1 then
		daim_index = #flair_score_list - 1
	end

	return daim_index
end

-- 获取当前灵兽被吞噬的成功率(当前灵兽星级， 消耗目标星级)
function ControlBeastsWGData:GetBeastStarFlairPerByStar(beast_star, spend_star)
	local empty = {}
	return (((self.beast_flair_change_list or empty)[beast_star] or empty).beast_star_flair_per or {})[spend_star] or nil
end

-- 获取当前灵兽使用道具吞噬的成功率
function ControlBeastsWGData:GetBeastStarItemFlairPerByStar(beast_star)
	local empty = {}
	return (self.beast_flair_item or empty)[beast_star]
end

-- 获取组合技能数据
function ControlBeastsWGData:GetGroupSkillDataByGroup(beast_group)
	local empty = {}
	return (self.beasts_group_list or empty)[beast_group] or nil
end

-- 获取全部的组合
function ControlBeastsWGData:GetAllSkillGroupTable()
	return self.beasts_group_list
end

-- 获取组合技能数据
function ControlBeastsWGData:GetGroupSkillDataByGroupSkillID(skill_id)
	if not self.beasts_group_map_cfg then
		return nil
	end

	for _, beasts_group_data in ipairs(self.beasts_group_map_cfg) do
		if beasts_group_data and beasts_group_data.group_skill_id == skill_id then
			return beasts_group_data
		end
	end

	return nil
end

-- 获取兽王数据数据根据等级
function ControlBeastsWGData:GetBeastDataByKingLevel(beast_king_level)
	local empty = {}
	return (self.beast_king_list or empty)[beast_king_level] or nil
end

-- 获取一个孵化池
function ControlBeastsWGData:GetBreedingSlotDataById(bag_id)
	local empty = {}
	local client_hole_id = bag_id				-- 先加一取到客户端数据id
	return (self.breeding_slot_list or empty)[client_hole_id] or nil
end

-- 获取所有的孵化池
function ControlBeastsWGData:GetBreedingSlotData()
	return self.breeding_slot_list
end

-- 获取当前背包的所有技能书
function ControlBeastsWGData:GetAllSkillBookDataList()
	if not self.skill_pool_map_cfg then
		return nil
	end

	local skill_bag_table = {}

	for _, skill_data in pairs(self.skill_pool_map_cfg) do
		if skill_data and skill_data.skill_id then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(skill_data.skill_item)
			if item_num > 0 then
				local bag_data = {}
				bag_data.item_id = skill_data.skill_item
				bag_data.item_num = item_num
				bag_data.skill_id = skill_data.skill_id
				bag_data.skill_rate = skill_data.skill_rate
				table.insert(skill_bag_table, bag_data)
			end
		end
	end

	table.sort(skill_bag_table, function (a, b)
		return a.skill_rate < b.skill_rate
	end)

	return skill_bag_table
end

--检测是否是技能书道具id
function ControlBeastsWGData:CheckisBookItemId(item_id)
	if not self.skill_pool_map_cfg then
		return false
	end

	for _, skill_data in pairs(self.skill_pool_map_cfg) do
		if skill_data and skill_data.skill_item == item_id then
			return true
		end
	end

	return false
end

--检测是否是一键孵化道具id
function ControlBeastsWGData:CheckisQuickIncubateItemId(item_id)
	local base_cfg = self:GetBaseCfg()
	if not base_cfg then
		return false
	end

	return base_cfg.incubate_item.item_id == item_id
end

--检测是否是升级星阵道具id
function ControlBeastsWGData:CheckisCircleILeveltemId(item_id)
    local star_circle_level_cfg = ControlBeastsWGData.Instance:GetStarCircleLevelCfgById(0)
    if star_circle_level_cfg and star_circle_level_cfg.item then
        return star_circle_level_cfg.item.item_id == item_id
    end
end

--检测是否是升级星阵道具id
function ControlBeastsWGData:CheckisBeastChangeFlairItemId(item_id)
	-- if self.refine_weight_item1_cfg[item_id] ~= nil then
	-- 	return true
	-- end

	if self.refine_weight_item2_cfg[item_id] ~= nil then
		return true
	end

	return false
end

--检测是否是合成的道具id
function ControlBeastsWGData:CheckisBeastStarUpItemId(item_id)
	if self.star_up_item_chip_list and self.star_up_item_chip_list[item_id] ~= nil then
		return true
	end

	return false
end
----------------------------------------获取客户端封装配置表信息结束---------------------------------------
-- 所有出战位信息
function ControlBeastsWGData:SetStandBySlotInfo(server_data)
	if server_data and server_data.stand_by_slot_list then
		for client_index, stand_by_slot_info in ipairs(server_data.stand_by_slot_list) do
			self:SetSingleStandBySlotInfo(client_index, stand_by_slot_info, true)
		end
	end
end

-- 单个出战位
function ControlBeastsWGData:SetSingleStandBySlotInfo(hole_id, server_data, is_client)
	local client_id = hole_id
	if not is_client then					-- 不是客户端下标需要加一
		client_id = client_id + 1
	end

	local client_bag_id = -1
	-- 和服务器差一个位置，这里一定加一个1
	if server_data.bag_index ~= -1 then
		client_bag_id = server_data.bag_index + 1
	end
	 
	local hole_data = self:GetHoleDataById(client_id)
	if hole_data and server_data then
		hole_data.hole_level = server_data.slot_level or 0 
		hole_data.beasts_bag_id = client_bag_id or -1 
		hole_data.is_main_battle = client_id <= 3
		hole_data.state = server_data.is_unlock == 1 and BEASTS_HOLE_STATUS.ACTIVE or BEASTS_HOLE_STATUS.NOT_ACTIVE 	-- 孔位状态
		hole_data.can_battle = server_data.is_unlock == 1 and (hole_data.beasts_bag_id < 0)
		hole_data.red = self:GetSingleRed(hole_data)
	end
end

-- 刷新所有出战位的红点信息
function ControlBeastsWGData:RefreshAllHoleSingleRed()
	if self.battle_main_hole_data then
		for _, main_hole_data in ipairs(self.battle_main_hole_data) do
			if main_hole_data then
				main_hole_data.red = self:GetSingleRed(main_hole_data)
			end
		end
	end

	if self.battle_assist_hole_data then
		for _, assist_hole_data in ipairs(self.battle_assist_hole_data) do
			if assist_hole_data then
				assist_hole_data.red = self:GetSingleRed(assist_hole_data)
			end
		end
	end
end

-- 获取一个孔位的解锁信息
function ControlBeastsWGData:GetLastHoleLockStatusByHoleId(hole_id)
	-- 获取的是上一个数据
	if hole_id == 3 then
		return true
	end

	local hole_data = self:GetHoleDataById(hole_id)
	if hole_data == nil then
		return true
	end

	return hole_data.state == BEASTS_HOLE_STATUS.ACTIVE
end

-- 获取出战位红点信息
function ControlBeastsWGData:GetSingleRed(hole_data)
	if hole_data.state == BEASTS_HOLE_STATUS.NOT_ACTIVE then
		-- 检测解锁条件是否达到
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
		local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
		local cur_vip_lv = VipWGData.Instance:GetVipLevel()
		local last_unlock = self:GetLastHoleLockStatusByHoleId(hole_data.hole_id)
		return role_level >= hole_data.role_level and pass_level >= hole_data.fb_tower_level and prof_level >= hole_data.zhuanzhi and cur_vip_lv >= hole_data.vip_limit and last_unlock
	end

	if hole_data.beasts_bag_id == -1 then
		for _, beast_data in ipairs(self.beasts_list) do
			if beast_data and beast_data.server_data and beast_data.is_have_beast and beast_data.server_data.stand_by_slot == -1
				and ((beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1) or
				(not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index == -1)) then
				return true
			end
		end
	else
		local now_battle_type_list = {}
		local add_to_battle_id_func = function(beast_type)
			now_battle_type_list[beast_type] = true
		end

		local battle_main_hole_data = self:GetHoleMainData()
		local battle_assist_hole_data = self:GetHoleAssistData()
		for k, v in pairs(battle_main_hole_data) do
			if v.beasts_bag_id ~= -1 then
				local beast_data = self:GetBeastDataById(v.beasts_bag_id)
				if beast_data and beast_data.server_data then
	
					local cfg = self:GetBeastCfgById(beast_data.server_data.beast_id)
					add_to_battle_id_func(cfg and cfg.beast_type or 0)
				end
			end
		end
	
		for k, v in pairs(battle_assist_hole_data) do
			if v.beasts_bag_id ~= -1 then
				local beast_data = self:GetBeastDataById(v.beasts_bag_id)
				if beast_data and beast_data.server_data then
	
					local cfg = self:GetBeastCfgById(beast_data.server_data.beast_id)
					add_to_battle_id_func(cfg and cfg.beast_type or 0)
				end
			end
		end

		local beast_client_data = self:GetBeastDataById(hole_data.beasts_bag_id)
		if beast_client_data and beast_client_data.is_have_beast then
			for _, beast_data in ipairs(self.beasts_list) do
				if beast_data and beast_data.server_data and beast_data.is_have_beast and beast_data.server_data.stand_by_slot == -1 
					and beast_data.cap_value > beast_client_data.cap_value
					and ((beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1) or
					(not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index == -1)) then
					local cfg = self:GetBeastCfgById(beast_data.server_data.beast_id)
					local beast_type = cfg and cfg.beast_type or 0
					if not now_battle_type_list[beast_type] then
						return true
					end
				end
			end
		end
	end


	return false
end

-- 所有已孵化信息
function ControlBeastsWGData:SetAllBeastBagInfo(server_data)
	if server_data and server_data.beast_bag then
		for bag_id, beast_data in ipairs(server_data.beast_bag) do
			self:SetSingleBeastBagInfo(bag_id, beast_data, true)
		end
	end
end

-- 检测是否需要刷新升星
function ControlBeastsWGData:CheckNeedRefreshCompose(now_server_data, new_server_data, now_beast_id, new_beast_id)
	if now_beast_id == -1 and new_beast_id ~= -1 then
		ControlBeastsWGCtrl.Instance:OnBeastsItemDataChange(new_beast_id, GameEnum.DATALIST_CHANGE_REASON_ADD)
		return true
	end

	if now_beast_id ~= -1 and new_beast_id == -1 then
		return true
	end

	local now_lv = now_server_data and now_server_data.beast_level or 0
	local new_lv = new_server_data and new_server_data.beast_level or 0
	local now_exp = now_server_data and now_server_data.exp or 0
	local new_exp = new_server_data and new_server_data.exp or 0
	local now_refine_times = now_server_data and now_server_data.refine_times or 0
	local new_refine_times = new_server_data and new_server_data.refine_times or 0
	local now_link_index = now_server_data and now_server_data.holy_spirit_link_index or -1
	local new_link_index = new_server_data and new_server_data.holy_spirit_link_index or -1

	if (now_lv == 0 and new_lv ~= 0) or (now_exp == 0 and new_exp ~= 0) or (now_refine_times == 0 and new_refine_times ~= 0) or (now_link_index ~= new_link_index) then
		return true
	end

	return false
end

-- 刷新一下单个的已孵化信息
function ControlBeastsWGData:SetSingleBeastBagInfo(bag_id, beast_data, is_client)
	local client_bag_id = bag_id
	if not is_client then					-- 不是客户端下标需要加一
		client_bag_id = client_bag_id + 1
	end

	local beast_client_data = self:GetBeastDataById(client_bag_id)
	-- local skill_book_list_data = self:GetAllSkillBookDataList()
	local need_refresh_compose = false

	if beast_client_data and beast_data then
		need_refresh_compose = self:CheckNeedRefreshCompose(beast_client_data.server_data, beast_data, beast_client_data.item_id, beast_data.beast_id)
		beast_client_data.bag_id = client_bag_id				-- 背包id
		beast_client_data.item_id = beast_data.beast_id			-- 物品id
		beast_client_data.server_data = beast_data
		beast_client_data.link_beast_data = beast_client_data
		beast_client_data.is_have_beast = beast_data.beast_id > 0
		beast_client_data.beast_type = self:GetBeastTypeByBeastId(beast_data.beast_id)
		beast_client_data.is_holy_beast = self:IsHolyBeastType(beast_client_data.beast_type)

		-- beast_client_data.is_can_learn_skill = self:RefreshBeastLearnSkill(skill_book_list_data, beast_client_data)	-- 打书暂时屏蔽
		-- beast_client_data.is_can_compose = self:GetSingleBeastComposeList(client_bag_id, beast_data.beast_id)
		-- beast_client_data.flair_score = self:SetSingleBeastBagInfoFlairScore(beast_client_data.server_data)

		-- 圣兽相关处理start
		if beast_data.holy_spirit_link_index ~= -1 then
			local link_beast_data = self:GetBeastDataById(beast_data.holy_spirit_link_index + 1)
			if link_beast_data and link_beast_data.is_have_beast and (link_beast_data.server_data.holy_spirit_link_index + 1) == client_bag_id then -- 未互相连接不做处理
				if beast_client_data.is_holy_beast then
					local beast_star = (self:GetBeastCfgById(link_beast_data.item_id) or {}).beast_star or 0
					local beast_id = self:GetBeastIdByTypeAndStar(beast_client_data.beast_type, beast_star)
					beast_client_data.item_id = beast_id
					beast_client_data.server_data.beast_id = beast_id
					beast_client_data.server_data.exp = link_beast_data.server_data.exp
					beast_client_data.server_data.beast_level = link_beast_data.server_data.beast_level
					beast_client_data.link_beast_data = link_beast_data
					link_beast_data.link_beast_data = beast_client_data

					beast_client_data.is_can_upgrade = self:RefreshBeastUpGrade(link_beast_data)
					beast_client_data.is_can_change_flair = self:RefreshBeastChangeFlair(beast_client_data)
					link_beast_data.is_can_upgrade = false
					link_beast_data.is_can_change_flair = false
				else
					local beast_star = (self:GetBeastCfgById(beast_data.beast_id) or {}).beast_star or 0
					local beast_id = self:GetBeastIdByTypeAndStar(link_beast_data.beast_type, beast_star)
					link_beast_data.item_id = beast_id
					link_beast_data.server_data.beast_id = beast_id
					link_beast_data.server_data.exp = beast_data.exp
					link_beast_data.server_data.beast_level = beast_data.beast_level
					link_beast_data.link_beast_data = beast_client_data
					beast_client_data.link_beast_data = link_beast_data

					link_beast_data.is_can_upgrade = self:RefreshBeastUpGrade(beast_client_data)
					link_beast_data.is_can_change_flair = self:RefreshBeastChangeFlair(link_beast_data)
					beast_client_data.is_can_upgrade = false
					beast_client_data.is_can_change_flair = false
				end
			end
		else
			if beast_client_data.is_holy_beast then
				beast_client_data.is_can_upgrade = false
				beast_client_data.is_can_change_flair = false
			else
				beast_client_data.is_can_upgrade = self:RefreshBeastUpGrade(beast_client_data)
				beast_client_data.is_can_change_flair = self:RefreshBeastChangeFlair(beast_client_data)
			end
		end
		self:SetSingleHolyBeastInfo(beast_client_data)
		-- 圣兽处理end

		local color = ControlBeastsWGData.Instance:GetBeastColorByBeastId(beast_data.beast_id)
		if color >= 3 then	-- 没有达到品质的需要归0
			local cap, attribute = self:GetBeastsCapValue(beast_data)
			beast_client_data.cap_value = cap
		else
			beast_client_data.cap_value = 0
		end
	end

	return need_refresh_compose
end

-- 刷新单个圣兽信息
function ControlBeastsWGData:SetSingleHolyBeastInfo(beast_data)
	for k, v in pairs(self.holy_beasts_list) do
		if v.bag_id == beast_data.bag_id and v.beast_type ~= beast_data.beast_type then
			v.bag_id = 0
			v.item_id = 0
			v.beast_data = nil
			v.is_unlock = false
			v.is_contracted = false
			v.link_bag_id = -1
		end

		if v.beast_type == beast_data.beast_type then
			v.bag_id = beast_data.bag_id
			v.item_id = beast_data.item_id
			v.beast_data = beast_data
			v.is_unlock = true
			v.is_contracted = beast_data.server_data.holy_spirit_link_index ~= -1
			v.link_bag_id = v.is_contracted and (beast_data.server_data.holy_spirit_link_index + 1) or -1
		end
	end
end

-- 设置单个已孵化资质值
function ControlBeastsWGData:SetSingleBeastBagInfoFlairScore(server_data)
	if (not server_data) or (not server_data.flair_values) then
		return 0
	end

	local flair_score = 0

	for _, flair_value in ipairs(server_data.flair_values) do
		flair_score = flair_score + flair_value
	end

	flair_score = flair_score * (server_data.effort_value / 10000)
	return flair_score
end

-- 刷新一下单个驭兽可升级红点
function ControlBeastsWGData:RefreshBeastUpGrade(beast_client_data)
	if not self.beast_base_info then
		return false
	end

	if (not beast_client_data) or (not beast_client_data.server_data) or beast_client_data.server_data.stand_by_slot == -1 then
		return false
	end

	local server_data = beast_client_data.server_data
    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
    local beast_star = beast_cfg and beast_cfg.beast_star or 0
    local next_beast_star = beast_star + 1
    local level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(beast_star)
    local next_level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(next_beast_star)
    local is_full_level = server_data.beast_level == level_limit_cfg.beast_max_level

	local level_cfg = self:GetBeastLevelCfgByLevel(server_data.beast_level)
	if level_cfg then
		if self.beast_base_info.total_exp >= level_cfg.need_exp - server_data.exp and (not is_full_level) then
			return true
		end
	end

	return false
end

-- 刷新一下所有驭兽可升级红点
function ControlBeastsWGData:RefreshAllBeastUpGrade()
	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data then
			if (beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index == -1) or 
					(beast_data.is_have_beast and not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1) then
				beast_data.is_can_upgrade = false
			else
				beast_data.is_can_upgrade = self:RefreshBeastUpGrade(beast_data.link_beast_data)
			end
		end
	end
end

-- 刷新一下单个驭兽可替换资质红点
function ControlBeastsWGData:RefreshBeastChangeFlair(beast_client_data)
	if not self.beast_base_info then
		return false
	end

	if (not beast_client_data) or (not beast_client_data.server_data) then
		return false
	end

	-- 未上阵的不给洗髓红点
	if beast_client_data.server_data.stand_by_slot == -1 then
		return false
	end	 

    local server_data = beast_client_data.server_data
	local best_base_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
	local refine_seq = best_base_cfg and best_base_cfg.refine_seq or 0
    local refine_maxlevel = best_base_cfg and best_base_cfg.refine_maxlevel or 0
    -- 洗练等级操作改为全部按照星级来计算
    local refine_level = best_base_cfg and best_base_cfg.beast_star or 1 --server_data.refine_level or 0
    local weight_cfg = ControlBeastsWGData.Instance:GetBeastRefineWeightCfgBySeq(refine_seq, refine_level)
    local now_max = weight_cfg and weight_cfg.rand_right or 0
    local all_pre_value = 0
    local all_now_value = 0
    local all_max_value = 0

    if server_data.refine_attr_list then
        for index, refine_attr_data in ipairs(server_data.refine_attr_list) do
			all_max_value = all_max_value + now_max
			all_pre_value = all_pre_value + refine_attr_data.pre_rand_value
			all_now_value = all_now_value + refine_attr_data.rand_value
        end
    end

	if all_pre_value > 0 then
		return true
	end

	local consume_item_id = weight_cfg and weight_cfg.cost_item_id or 0
    local need_item_count = weight_cfg and weight_cfg.cost_item_num or 0
	local is_rand_full = all_now_value == all_max_value                     -- 洗练最大值

	if (not is_rand_full) and consume_item_id ~= 0 and need_item_count ~= 0 then												-- 可洗练
		local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
		if item_num >= need_item_count then
			return true
		end
	end

	if is_rand_full and refine_level < refine_maxlevel then					-- 可突破
		consume_item_id = weight_cfg and weight_cfg.up_cost_item_id or 0
        need_item_count = weight_cfg and weight_cfg.up_cost_item_num or 0
		if consume_item_id ~= 0 and need_item_count ~= 0 then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
			if item_num >= need_item_count then
				return true
			end
		end
	end

	return false
end

-- 刷新一下所有驭兽可提升资质红点
function ControlBeastsWGData:RefreshAllBeastChangeFlair()
	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data then
			if (beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index == -1) or 
			(beast_data.is_have_beast and not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1) then
				beast_data.is_can_change_flair = false
			else
				beast_data.is_can_change_flair = self:RefreshBeastChangeFlair(beast_data)
			end
		end
	end
end

-- 刷新一下单个驭兽可打书红点
function ControlBeastsWGData:RefreshBeastLearnSkill(skill_book_list_data, beast_client_data)
	if (not beast_client_data) or (not beast_client_data.server_data) or (not beast_client_data.is_have_beast) then
		return false
	end

	local server_data = beast_client_data.server_data
	if server_data.skill_ids then
		for _, value in ipairs(server_data.skill_ids) do
			if value == -1 and #skill_book_list_data > 0 then
				return true
			end
		end
	end

	return false
end

-- 刷新一下全部驭兽可打书红点
function ControlBeastsWGData:RefreshAllBeastLearnSkill()
	local skill_book_list_data = self:GetAllSkillBookDataList()

	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data then
			beast_data.is_can_learn_skill = self:RefreshBeastLearnSkill(skill_book_list_data, beast_data)
		end
	end
end

-- 所有已孵化池信息
function ControlBeastsWGData:SetAllBreedingInfo(server_data)
	if server_data and server_data.breeding_slot_list then
		for bag_id, breeding_data in ipairs(server_data.breeding_slot_list) do
			self:SetSingleBreedingInfo(bag_id, breeding_data, true)
		end
	end
end

-- 刷新一下单个的孵化池
function ControlBeastsWGData:SetSingleBreedingInfo(bag_id, breeding_data, is_client)
	local client_bag_id = bag_id
	if not is_client then					-- 不是客户端下标需要加一
		client_bag_id = client_bag_id + 1
	end

	local breeding_client_data = self:GetBreedingSlotDataById(client_bag_id)
	if breeding_client_data and breeding_data then
		breeding_client_data.bag_id = client_bag_id				-- 背包id
		breeding_client_data.server_data = breeding_data
		breeding_client_data.is_have_beast = breeding_data.beast_id > 0

		if breeding_client_data.is_have_beast then
			local cur_time_stamp = TimeWGCtrl.Instance:GetServerTime()
			local last_time = breeding_data.finish_timestamp - cur_time_stamp
			breeding_client_data.status = last_time > 0 and BEASTS_BORN_STATUS.UNBORN or BEASTS_BORN_STATUS.BORN		-- 可孵化
		else
			breeding_client_data.status = self:RefreshSingleBreedingInfo()
		end

		breeding_client_data.can_quick = self:RefreshSingleQuickBreedingInfo()
	end
end

-- 刷新可孵化红点信息
function ControlBeastsWGData:RefreshSingleBreedingInfo()
	for _, beast_egg_data in ipairs(self.beasts_egg_list) do
		if beast_egg_data and beast_egg_data.is_have_beast then
			return BEASTS_BORN_STATUS.CAN_BORN
		end
	end
	return BEASTS_BORN_STATUS.NONE
end

-- 刷新快速孵化红点信息
function ControlBeastsWGData:RefreshSingleQuickBreedingInfo()
	local base_cfg = self:GetBaseCfg()
	if not base_cfg then
		return false
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.incubate_item.item_id)
	return item_num >=  base_cfg.incubate_item.num
end

-- 刷新孵化槽红点信息
function ControlBeastsWGData:RefreshAllQuickBreedingInfo()
	for _, breeding_slot_data in ipairs(self.breeding_slot_list) do
		if breeding_slot_data then
			self:SetSingleBreedingInfoForSelf(breeding_slot_data.bag_id)
		end
	end
end

-- 刷新一下单个的孵化池
function ControlBeastsWGData:SetSingleBreedingInfoForSelf(client_bag_id)
	local breeding_client_data = self:GetBreedingSlotDataById(client_bag_id)
	if breeding_client_data then
		if breeding_client_data.is_have_beast and breeding_client_data.server_data then
			local server_data = breeding_client_data.server_data
			local cur_time_stamp = TimeWGCtrl.Instance:GetServerTime()
			local last_time = server_data.finish_timestamp - cur_time_stamp
			breeding_client_data.status = last_time > 0 and BEASTS_BORN_STATUS.UNBORN or BEASTS_BORN_STATUS.BORN		-- 可孵化
		else
			breeding_client_data.status = self:RefreshSingleBreedingInfo()
		end

		breeding_client_data.can_quick = self:RefreshSingleQuickBreedingInfo()
	end
end

-- 设置所有的驭兽(未孵化)
function ControlBeastsWGData:SetAllBeastEggBagInfo(server_data)
	if server_data and server_data.egg_bag then
		for bag_id, beast_egg_data in ipairs(server_data.egg_bag) do
			self:RefreshSingleBeastEggData(bag_id, beast_egg_data, true, true)
		end
	end
end

-- 刷新一下单个的驭兽(未孵化)
-- is_all 表示整理不需要弹获得物品
function ControlBeastsWGData:RefreshSingleBeastEggData(bag_id, beast_egg_data, is_client, is_all)
	local client_bag_id = bag_id
	if not is_client then					-- 不是客户端下标需要加一
		client_bag_id = client_bag_id + 1
	end

	local beast_egg_client_data = self:GetBeastEggDataById(client_bag_id)
	if beast_egg_client_data and beast_egg_data then

		local change_reason = beast_egg_client_data.item_id == -1 and GameEnum.DATALIST_CHANGE_REASON_ADD or GameEnum.DATALIST_CHANGE_REASON_UPDATE
		if beast_egg_data.beast_id ~= -1 and change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD and (not is_all) then
			ControlBeastsWGCtrl.Instance:OnBeastsItemDataChange(beast_egg_data.beast_id, change_reason)
		end

		beast_egg_client_data.bag_id = client_bag_id
		beast_egg_client_data.server_data = beast_egg_data
		beast_egg_client_data.item_id = beast_egg_data.beast_id			-- 物品id
		beast_egg_client_data.is_have_beast = beast_egg_data.beast_id > 0
		beast_egg_client_data.is_can_compose = self:GetSingleBeastEggComposeList(client_bag_id, beast_egg_data.beast_id)
	end
end

-- 设置所有的星阵
function ControlBeastsWGData:SetAllStarCircleInfo(server_data)
	if server_data and server_data.circle_list then
		for bag_id, circle_data in ipairs(server_data.circle_list) do
			self:RefreshSingleStarCircleData(bag_id, circle_data, true)
		end
	end
end

-- 刷新一下单个的星阵
function ControlBeastsWGData:RefreshSingleStarCircleData(bag_id, circle_data, is_client)
	local client_bag_id = bag_id
	if not is_client then					-- 不是客户端下标需要加一
		client_bag_id = client_bag_id + 1
	end

	local beast_circle_data = self:GetStarCircleDataById(client_bag_id)
	if beast_circle_data and circle_data then
		beast_circle_data.attr_list = circle_data.attr_list
		beast_circle_data.interval_times = circle_data.interval_times or 0		-- 轮回数
		beast_circle_data.is_unlock = circle_data.is_unlock	or 0				-- 是否解锁
		beast_circle_data.is_need_promote = circle_data.is_need_promote or 0 	-- 是否需要突破到下一轮回才能继续升星
		beast_circle_data.is_can_circle = self:RefreshSingleStarCircleRed(beast_circle_data)
	end
end

-- 刷新单一星阵红点
function ControlBeastsWGData:RefreshSingleStarCircleRed(beast_circle_data)
	local star_circle_level_cfg = self:GetStarCircleLevelCfgById(beast_circle_data.interval_times)
	if not star_circle_level_cfg then
		return false
	end

	if not beast_circle_data.attr_list then
		return false
	end

	local is_full = true
	for _, attr_data in ipairs(beast_circle_data.attr_list) do
        if attr_data and attr_data.star_num < 10 then
            is_full = false
			break
        end
    end

	if is_full then
		local star_circle_level_cfg_next = self:GetStarCircleLevelCfgById(beast_circle_data.interval_times + 1)
		if not star_circle_level_cfg_next then
			return false
		end
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(star_circle_level_cfg.item.item_id)
	return item_num >= star_circle_level_cfg.item.num or is_full
end

-- 刷新全部星阵红点
function ControlBeastsWGData:RefreshAllStarCircleRed()
	for _, beast_circle_data in ipairs(self.beast_star_circle) do
		if beast_circle_data then
			beast_circle_data.is_can_circle = self:RefreshSingleStarCircleRed(beast_circle_data)
		end
	end
end

-- 设置基本信息
function ControlBeastsWGData:SetBeastBaseInfo(server_data)
	self.beast_base_info = server_data
	self.beast_king_level = server_data.beast_king_level
	self:RefreshAllBeastUpGrade()
end

-- 获取所有基本信息
function ControlBeastsWGData:GetBeastBaseInfo()
	return self.beast_base_info
end

-- 获取所有基本信息
function ControlBeastsWGData:GetBeastBaseInfoKingLevel()
	return self.beast_king_level
end

-- 获取兽王红点
function ControlBeastsWGData:GetBeastBaseKingRed()
	if (not self.beast_base_info) or (not self.beast_base_info.beast_king_level) then
		return false
	end

	local cfg_data = self:GetBeastKingDataBylevel(self.beast_base_info.beast_king_level)
	if not cfg_data then
		return false
	end

	local next_cfg_data = self:GetBeastKingDataBylevel(self.beast_base_info.beast_king_level + 1)
	if not next_cfg_data then
		return false
	end

    local is_can_unlock = true
	if type(cfg_data.king_conditions) == "number" then
		return false
	elseif cfg_data.king_conditions then
		for _, condition_data in ipairs(cfg_data.king_conditions) do
			if condition_data.type == BEAST_KING_CONDITION_TYPE.VIP then
				local cur_num = VipWGData.Instance:GetVipLevel()
				is_can_unlock = cur_num >= condition_data.params_1 and is_can_unlock
			else
				local cur_num = self:GetCurBeastListNum(condition_data.type, condition_data.params_2)
				is_can_unlock = cur_num >= condition_data.params_1 and is_can_unlock
			end
		end
	end

    return is_can_unlock
end

-- 获取当前的拥有的经验
function ControlBeastsWGData:GetBeastBaseInfoExp()
	return self.beast_base_info and self.beast_base_info.total_exp or 0
end

-- 获取当前的拥有的经验
function ControlBeastsWGData:GetBeastBaseKingLevel()
	return self.beast_base_info and self.beast_base_info.beast_king_level or 0
end

-- 获取一个灵兽的属性，可选返回下一级属性(属性值等于 基础值 * 资质的千分比 * 成长值)
function ControlBeastsWGData:GetBeastLevelAttrList(beast_id, beast_level, need_next, flair_values, effort_value)
	local base_attr_cfg = self:GetBeastLevelCfgByLevel(beast_level)
	local best_beas_cfg = self:GetBeastCfgById(beast_id)
	local base_next_attr_cfg = nil

	local exp_base_value = {}
	exp_base_value.attr_str = 1000
	exp_base_value.attr_value = base_attr_cfg and base_attr_cfg.beast_level_exp_base_value or 0
	local aim_value = exp_base_value.attr_value
	local curr_level = beast_level

	while aim_value == exp_base_value.attr_value do
		curr_level = curr_level + 1
		local next_attr_cfg = self:GetBeastLevelCfgByLevel(curr_level)
		if not next_attr_cfg then
			aim_value = 0
			curr_level = 0
			break
		end

		aim_value = next_attr_cfg.beast_level_exp_base_value
	end
	exp_base_value.add_value = aim_value
	exp_base_value.aim_level = curr_level

	if need_next then
		base_next_attr_cfg = self:GetBeastLevelCfgByLevel(beast_level + 1)
	end

	local return_attr_list = self:SpiltBeastLevelAttrList(base_attr_cfg, base_next_attr_cfg, flair_values, effort_value, best_beas_cfg)
	local return_table = {}

	for _, attr_data in pairs(return_attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table, exp_base_value
end

-- 拆分组合属性列表
function ControlBeastsWGData:SpiltBeastLevelAttrList(cfg, next_cfg, flair_values, effort_value, best_beas_cfg)
	local attr_list = {}
	for i = 1, 6 do
		local key = cfg["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

			local real_flair_value = self:GetRealFlairValue(key, flair_values)
			local real_effort_value = effort_value / 10000-- real_flair_value * real_effort_value *
			local real_attr_value = cfg["attr_value" .. i]
			attr_list[key].attr_value = math.floor(real_attr_value) 

			if best_beas_cfg and best_beas_cfg["attr_id" .. i] then
				attr_list[key].attr_value = attr_list[key].attr_value + best_beas_cfg["attr_value" .. i]
				attr_list[key].add_value = best_beas_cfg["attr_value" .. i]
			end

			if next_cfg and next_cfg["attr_id" .. i] then
				real_attr_value = real_flair_value * real_effort_value * next_cfg["attr_value" .. i] - attr_list[key].attr_value
				local temp_add_value = attr_list[key].add_value or 0
				attr_list[key].add_value = temp_add_value --+ math.floor(real_attr_value)
			end
		end
	end

	return attr_list
end

-- 根据属性获取对应的资质比
function ControlBeastsWGData:GetRealFlairValue(attr_str, flair_values)
	if not self.beast_flair_map_cfg then
		return 1
	end

	local real_flair_index = -1

	for flair_index, flair_data in pairs(self.beast_flair_map_cfg) do
		if flair_data then
			if flair_data.flair_attr_id1 == attr_str or flair_data.flair_attr_id2 == attr_str then
				real_flair_index = flair_index
				break
			end
		end
	end

	-- 千分比，没找到直接返回1000
	local real_flair_value = flair_values[real_flair_index + 1] or 1000
	return real_flair_value / 1000
end

-- 检查臻品解锁星级
function ControlBeastsWGData:CheckBeastBestUnlockStar(beast_id, index)
	local beast_data_map = self:GetBeastDatMapaById(beast_id)
	local beast_star = 0
	if beast_data_map and beast_data_map.beast_data then
		beast_star = beast_data_map.beast_data.beast_star or beast_star
	end

	local need_star = 0
	if beast_data_map and beast_data_map.beast_best_attr_star then
		need_star = beast_data_map.beast_best_attr_star[index] or 0
	end

	if beast_star >= need_star then
		return true, 0
	else
		return false, need_star
	end
end

-- 获取至臻属性
function ControlBeastsWGData:GetBeastLevelBestAttrList(beast_id, beast_level)
	local attr_list = {}
	local cfg, next_cfg = self:GetNowBeastAttrBestCfgByLevel(beast_id, beast_level)

	for i = 1, 2 do
		local is_unlock, need_star = self:CheckBeastBestUnlockStar(beast_id, i)
		local key = nil
		if (not is_unlock) or (not cfg) then
			key = 1000 + i
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end
			attr_list[key].attr_value = 1
			attr_list[key].add_value = 0
			attr_list[key].is_unlock = is_unlock
			attr_list[key].need_star = need_star
		else
			local key = cfg["attr_id" .. i]
			if key then
				if not attr_list[key] then
					attr_list[key] = {}
					attr_list[key].attr_str = key
				end
	
				attr_list[key].attr_value = cfg["attr_value" .. i]
	
				if next_cfg then
					attr_list[key].add_value = next_cfg["attr_value" .. i] - attr_list[key].attr_value
				end
	
				attr_list[key].is_unlock = is_unlock
				attr_list[key].need_star = need_star
			end
		end
	end

	local return_table = {}

	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

-- 获取一个灵兽战斗力(可选上阵id, 上阵level)(2024.12.11 去掉至臻属性)
function ControlBeastsWGData:GetBeastsCapValue(beast_data, base_attr, bast_base_attr, hole_id, hole_level, is_return_not_cap)
	if not beast_data then
		return 0, nil
	end

	if beast_data.beast_id == -1 then
		return 0, nil
	end

	if not base_attr then
		base_attr = self:GetBeastLevelAttrListNew(beast_data.beast_id, beast_data.beast_level, false, beast_data.refine_attr_list, true, true)
	end

	-- if not bast_base_attr then
	-- 	bast_base_attr = self:GetBeastLevelBestAttrList(beast_data.beast_id, beast_data.beast_level)
	-- end

	if hole_id and hole_level then
		base_attr = self:PickAttrFromHole(base_attr, hole_id, hole_level)
		-- bast_base_attr = self:PickAttrFromHole(bast_base_attr, hole_id, hole_level)
	end

	if is_return_not_cap then
		return base_attr --, bast_base_attr
	end

	if not self.attribute_cache then
		self.attribute_cache = AttributePool.CreateAttribute()
	else
		AttributePool.ResetAttribute(self.attribute_cache)
	end

	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if self.attribute_cache[attr_str] then
			self.attribute_cache[attr_str] = self.attribute_cache[attr_str] + value
		end
	end

	-- 加入基础属性
    for index, attr_cell in ipairs(base_attr) do
		if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
			local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
			add_tab(attr_str, attr_cell.attr_value)
		end
    end

	-- 加入至臻属性
    -- for index, attr_cell in ipairs(bast_base_attr) do
	-- 	if attr_cell.is_unlock and attr_cell.attr_str < 1000 and attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
	-- 		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
	-- 		add_tab(attr_str, attr_cell.attr_value)
	-- 	end
    -- end

	-- 计算技能属性战力

	local cap = AttributeMgr.GetCapability(self.attribute_cache)

	-- 圣魂技能固定战力
	local spirit_skill_cap = 0
	if self:IsHolyBeast(beast_data.beast_id) then
		local beast_type = self:GetBeastTypeByBeastId(beast_data.beast_id)
		spirit_skill_cap = self:GetHolySpiritSkillCap(beast_type)
	end

	local total_cap = cap + spirit_skill_cap
	return total_cap, self.attribute_cache
end

-- 获取一个属性列表的战斗力
function ControlBeastsWGData:GetCapValueByAttrList(attr_list)
	if not attr_list then
		return 0
	end

	local attribute = AttributePool.AllocAttribute()
	local function add_tab(attr_str, value)
		if attr_str == nil or value == nil then
			return
		end
		if attribute[attr_str] then
			attribute[attr_str] = attribute[attr_str] + value
		end
	end
	
	-- 加入属性
    for index, attr_cell in ipairs(attr_list) do
		local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
		add_tab(attr_str, attr_cell.attr_value)
    end

	local cap = AttributeMgr.GetCapability(attribute)
	return cap, attribute
end

-- 摘选出当前的上阵加成
function ControlBeastsWGData:PickAttrFromHole(attr_list, hole_id, hole_level)
	local hole_data = ControlBeastsWGData.Instance:GetHoleDataById(hole_id + 1)

	if (not hole_data) or (not hole_data.attr_types) then
		return attr_list
	end

	if hole_data.attr_types == 0 then
		return attr_list
	end

	local cfg_data = ControlBeastsWGData.Instance:GetHoleLevelCfgById(hole_id, hole_level)
	local per = 1
	if cfg_data and cfg_data.hole_addition then
		per = cfg_data.hole_addition / 10000
	end

	local hole_addition = {}

	for _, attr_str in ipairs(hole_data.attr_list) do
		hole_addition[attr_str] = true
	end

	for _, attr_data in ipairs(attr_list) do
		if attr_data and attr_data.attr_str then
			local attr_str = attr_data.attr_str
			if hole_addition[attr_str] then
				attr_data.attr_value = math.floor(per * attr_data.attr_value)
			else
				attr_data.attr_value = 0
			end
		end
	end

	return attr_list
end

-- 获取当前灵兽列表各个类型个数
function ControlBeastsWGData:GetCurBeastListNum(beast_king_condition_type, aim_num)
	local cur_num = 0
	local battle_main_hole_data = self:GetHoleMainData()
	local battle_assist_hole_data = self:GetHoleAssistData()

	for k, v in pairs(battle_main_hole_data) do
		if v.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(v.beasts_bag_id)
			if beast_data and beast_data.server_data then
				local server_data = beast_data.server_data
				local beast_cfg = self:GetBeastCfgById(server_data.beast_id)

				if beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_LEVEL and server_data.beast_level >= aim_num then
					cur_num = cur_num + 1
				elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_STAR and beast_cfg and beast_cfg.beast_star >= aim_num then
					cur_num = cur_num + 1
				elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_COLOR and beast_cfg and beast_cfg.beast_color >= aim_num then
					cur_num = cur_num + 1
				end
			end
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(v.beasts_bag_id)
			if beast_data and beast_data.server_data then
				local server_data = beast_data.server_data
				local beast_cfg = self:GetBeastCfgById(server_data.beast_id)

				if beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_LEVEL and server_data.beast_level >= aim_num then
					cur_num = cur_num + 1
				elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_STAR and beast_cfg and beast_cfg.beast_star >= aim_num then
					cur_num = cur_num + 1
				elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_COLOR and beast_cfg and beast_cfg.beast_color >= aim_num then
					cur_num = cur_num + 1
				end
			end
		end
	end

	return cur_num
end

-- 获取兽王属性列表
function ControlBeastsWGData:GetLevelKingAttrList(cfg)
	local attr_list = {}
	for i = 1, 6 do
		local key = cfg["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

			attr_list[key].attr_value = cfg["attr_value" .. i]
		end
	end

	local return_table = {}

	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

------------------------------------------------------------------------------
------------------------------------合成逻辑-----------------------------------
function ControlBeastsWGData:SetBeastDataSelect(bag_id, is_egg, compose_status)
	local list = is_egg and self.beasts_egg_list or self.beasts_list
	if list and list[bag_id] then
		list[bag_id].compose_status = compose_status
	end
end

-- 封装一下选出来的数据
function ControlBeastsWGData:CreateSelectBeastData(data, is_egg)
	local info = {}
	info.is_egg = is_egg
	info.bag_id = data.bag_id
	info.beast_id = data.server_data.beast_id
	info.server_data = data.server_data
	return info
end

-- 检测是否满足条件
function ControlBeastsWGData:CheckComposeSatisfyFun(aim_beast_id, starup_beast_ids, beast_id, check_table)
	for i, data in ipairs(starup_beast_ids) do
		if data.beast_id ~= nil then	-- 特殊id
			if check_table.special_num == nil then check_table.special_num = 0 end

			if self:CheckEnoughSpecialCondition(data.beast_id, beast_id) and check_table.special_num < data.num then
				check_table.special_num = check_table.special_num + 1
				return true, BEAST_COMPOSE_SPEND_TYPE.BEAST_SPECIAL, nil, nil
			end
		end

		if data.element ~= nil then
			if check_table.element == nil then check_table.element = {} end
			if check_table.element[data.element] == nil then check_table.element[data.element] = {} end
			if check_table.element[data.element][data.star] == nil then check_table.element[data.element][data.star] = 0 end

			if self:CheckEnoughSameElementCondition(aim_beast_id, data.element, data.star, beast_id) and check_table.element[data.element][data.star] < data.num then
				check_table.element[data.element][data.star] = check_table.element[data.element][data.star] + 1
				return true, BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT, data.element, data.star
			end
		elseif data.star ~= nil then
			if check_table.element == nil then check_table.element = {} end
			if check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT] == nil then check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT] = {} end
			if check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] == nil then check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] = 0 end

			if self:CheckEnoughSameStarCondition(aim_beast_id, data.star, beast_id) and check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] < data.num then
				check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] = check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] + 1
				return true, BEAST_COMPOSE_SPEND_TYPE.BEAST_STAR, BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT, data.star
			end
		end
	end

	return false
end

-- 检测是否满足条件(卡片)
function ControlBeastsWGData:CheckComposeSatisfyCardFun(aim_beast_id, starup_beast_ids, check_table, add_one_beast_data_fun)
	for i, data in ipairs(starup_beast_ids) do
		if data.element ~= nil then
			if check_table.element == nil then check_table.element = {} end
			if check_table.element[data.element] == nil then check_table.element[data.element] = {} end
			if check_table.element[data.element][data.star] == nil then check_table.element[data.element][data.star] = 0 end

			local use_card_data = ControlBeastsWGData.Instance:GetBeastsComposeUseCard(data.star)
			local use_card_id = use_card_data and use_card_data.item_id or 0
			local use_card_num = ItemWGData.Instance:GetItemNumInBagById(use_card_id)
			local add_list_num = 0

			if use_card_num >= data.num then
				check_table.element[data.element][data.star] = data.num 
				add_list_num = data.num 
			else
				if use_card_num > 0 then
					check_table.element[data.element][data.star] = use_card_num
					add_list_num = use_card_num
				end
			end

			if add_list_num ~= 0 then
				for i = 1, add_list_num do
					local card_data = {}
					card_data.item_id = use_card_id
					card_data.is_card = true
					add_one_beast_data_fun(true, BEAST_COMPOSE_SPEND_TYPE.BEAST_ELEMENT, data.element, data.star, card_data, true)
				end
			end
		end
	end
end

-- 检测合成碎片是否满足
function ControlBeastsWGData:CheckComposeSatisfyChipFun(starup_beast_ids)
	for i, data in ipairs(starup_beast_ids) do
		if data.chip_id ~= nil then
			return self:CheckEnoughChipsCondition(data.chip_id, data.num)
		end
	end

	return true
end

-- 刷新一下单个驭兽是否可合成是否添加列表
function ControlBeastsWGData:CheckBeastCompose(aim_bag_id, aim_egg_bag_id, aim_beast_id, is_egg, is_use_card)
	local aim_cfg = self:GetBeastCfgById(aim_beast_id)
	local starup_beast_ids = self:GetBeastStarupCfgById(aim_beast_id)

	if (not aim_cfg) or (not starup_beast_ids) or IsEmptyTable(starup_beast_ids) or starup_beast_ids == 0 then
		return false, nil
	end

	local check_table = {}
	local return_data = {}
	return_data.special_list = {}
	return_data.same_list = {} --3.22修改成符合条件的不是本身的幻兽
	local sort_table = #self.beasts_sort_list > 0 and self.beasts_sort_list or self.beasts_list

	-- 检测是否满足条件
	local final_check_satisfy_fun = function(check_table)
		for i, data in ipairs(starup_beast_ids) do
			if data.beast_id ~= nil and data.num ~= check_table.special_num then
				return false
			elseif data.element ~= nil then
				if check_table.element == nil or 
				check_table.element[data.element] == nil or 
				check_table.element[data.element][data.star] == nil or
				data.num ~= check_table.element[data.element][data.star] then
					return false
				end
			elseif data.star ~= nil then
				if check_table.element == nil or 
				check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT] == nil or 
				check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] == nil or
				data.num ~= check_table.element[BEAST_COMPOSE_SPEND_TYPE.BEAST_SP_ELEMENT][data.star] then
					return false
				end
			end
		end

		return true
	end

	local add_one_beast_data_fun = function(is_satisfy, index, element, star, beast_data, is_card)
		if is_satisfy then
			if index == BEAST_COMPOSE_SPEND_TYPE.BEAST_SPECIAL then
				if is_card then
					table.insert(return_data.special_list, beast_data)
				else
					table.insert(return_data.special_list, self:CreateSelectBeastData(beast_data, false))
				end
			else
				if return_data.same_list[index] == nil then
					return_data.same_list[index] = {}
				end

				if return_data.same_list[index][element] == nil then
					return_data.same_list[index][element] = {}
				end

				if return_data.same_list[index][element][star] == nil then
					return_data.same_list[index][element][star] = {}
				end

				if is_card then
					table.insert(return_data.same_list[index][element][star], beast_data)
				else
					table.insert(return_data.same_list[index][element][star], self:CreateSelectBeastData(beast_data, false))
				end
			end
		end
	end

	if is_use_card then
		self:CheckComposeSatisfyCardFun(aim_beast_id, starup_beast_ids, check_table, add_one_beast_data_fun)
	end

	if not is_egg then	-- 先检测兽(改为逆序，先吃垃圾的狗粮卡)
		--for _, beast_data in ipairs(self.beasts_list) do
		for i = #sort_table, 1, -1 do
			local beast_data = sort_table[i]
			if beast_data.server_data and (beast_data.server_data.stand_by_slot < 0) 
			and beast_data.is_have_beast
			and beast_data.server_data.holy_spirit_link_index == -1
			and beast_data.compose_status == BEASTS_COMPOSE_STATUS.NORMAL 
			and aim_cfg.starup_beast_id ~= 0
			and beast_data.bag_id ~= aim_bag_id
			and (not ((beast_data.server_data.beast_level > 1 or beast_data.server_data.exp > 0) or beast_data.server_data.refine_times > 0)) then	--去除出战的和已经被选的和自身
				local is_satisfy, index, element, star = self:CheckComposeSatisfyFun(aim_beast_id, starup_beast_ids, beast_data.server_data.beast_id, check_table)
				add_one_beast_data_fun(is_satisfy, index, element, star, beast_data)
			end
		end

		if final_check_satisfy_fun(check_table) and self:CheckComposeSatisfyChipFun(starup_beast_ids) then
			return true, return_data
		end
	end

	-- local sort_egg_table = #self.beasts_egg_sort_list > 0 and self.beasts_egg_sort_list or self.beasts_egg_list
	-- -- 兽蛋检测(改为逆序，先吃垃圾的狗粮卡)
	-- -- for _, beasts_egg_data in ipairs(self.beasts_egg_list) do
	-- for i = #sort_egg_table, 1, -1 do
	-- 	local beasts_egg_data = sort_egg_table[i]
	-- 	if beasts_egg_data.server_data 
	-- 	and beasts_egg_data.is_have_beast
	-- 	and beasts_egg_data.compose_status == BEASTS_COMPOSE_STATUS.NORMAL
	-- 	and aim_cfg.starup_beast_id ~= 0
	-- 	and beasts_egg_data.bag_id ~= aim_egg_bag_id then	--去除出战的和已经被选的和自身
	-- 		local is_satisfy, index = check_satisfy_fun(beasts_egg_data.server_data.beast_id)

	-- 		if is_satisfy then
	-- 			if index == 0 then
	-- 				table.insert(return_data.special_list, self:CreateSelectBeastData(beasts_egg_data, true))
	-- 			else
	-- 				if return_data.same_list[index] == nil then
	-- 					return_data.same_list[index] = {}
	-- 				end

	-- 				table.insert(return_data.same_list[index], self:CreateSelectBeastData(beasts_egg_data, true))
	-- 			end
	-- 		end

	-- 		if final_check_satisfy_fun and self:CheckComposeSatisfyChipFun(starup_beast_ids) then
	-- 			return true, return_data
	-- 		end
	-- 	end
	-- end

	return false, return_data
end

-- 判断是否满足条件(特殊幻兽)
function ControlBeastsWGData:CheckEnoughSpecialCondition(aim_beast_id, beast_id)
	if (not beast_id) or beast_id == 0 then
		return false
	end

	return aim_beast_id == beast_id
end

-- 判断是否满足条件(星级)
function ControlBeastsWGData:CheckEnoughSameStarCondition(aim_beast_id, star, beast_id)
	local stuff_cfg = self:GetBeastCfgById(beast_id)
	local aim_cfg = self:GetBeastCfgById(aim_beast_id)
	if (not stuff_cfg) or (not aim_cfg) then
		return false
	end

	return star == stuff_cfg.beast_star and stuff_cfg.beast_color <= aim_cfg.beast_color
end

-- 判断是否满足条件(类型加星级)
function ControlBeastsWGData:CheckEnoughSameStarCondition2(aim_beast_id, beast_id)
	local aim_cfg = self:GetBeastCfgById(aim_beast_id)
	local stuff_cfg = self:GetBeastCfgById(beast_id)
	if (not aim_cfg) or (not stuff_cfg) then
		return false
	end

	return aim_cfg.beast_star == stuff_cfg.beast_star and aim_cfg.beast_element == stuff_cfg.beast_element
end

-- 判断是否满足条件(纯星级)
function ControlBeastsWGData:CheckEnoughSameStarCondition3(aim_beast_id, beast_id)
	local aim_cfg = self:GetBeastCfgById(aim_beast_id)
	local stuff_cfg = self:GetBeastCfgById(beast_id)
	if (not aim_cfg) or (not stuff_cfg) then
		return false
	end

	return aim_cfg.beast_star == stuff_cfg.beast_star
end

-- 判断是否满足条件(元素)
function ControlBeastsWGData:CheckEnoughSameElementCondition(aim_beast_id, element, star, beast_id)
	local stuff_cfg = self:GetBeastCfgById(beast_id)
	local aim_cfg = self:GetBeastCfgById(aim_beast_id)
	if (not stuff_cfg) or (not aim_cfg) then
		return false
	end

	if stuff_cfg.beast_color >= BEAST_COMPOSE_SPEND_TYPE.BEAST_COLOR_SP then
		return false
	end

	return stuff_cfg.beast_element == element and stuff_cfg.beast_star == star and stuff_cfg.beast_color < aim_cfg.beast_color
end

-- 判断是否满足条件碎片
function ControlBeastsWGData:CheckEnoughChipsCondition(chips, num)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(chips)
	return item_num >= num
end

-- 刷新一下单个驭兽是否可合成红点状态
function ControlBeastsWGData:RefreshAllBeastComposeListRedStatus()
	for _, temp_data in ipairs(self.beasts_list) do
		if temp_data and temp_data.server_data then
			local data = temp_data.server_data
			if (temp_data.is_holy_beast and data.holy_spirit_link_index == -1) or 
					(not temp_data.is_holy_beast and data.holy_spirit_link_index ~= -1) then
				temp_data.is_can_compose = false
			else
				local is_can_compose, _ = self:GetSingleBeastComposeList(temp_data.link_beast_data.bag_id, temp_data.link_beast_data.server_data.beast_id)
				temp_data.is_can_compose = is_can_compose
			end
		end
	end

	-- for _, temp_data in ipairs(self.beasts_egg_list) do
	-- 	if temp_data and temp_data.server_data then
	-- 		local data = temp_data.server_data
	-- 		local is_can_compose, _ = self:GetSingleBeastEggComposeList(temp_data.bag_id, data.beast_id)
	-- 		temp_data.is_can_compose = is_can_compose
	-- 	end
	-- end
end

-- 刷新一下单个驭兽是否可合成是否添加列表
function ControlBeastsWGData:RefreshAllBeastComposeList()
	self.quick_compse_list = {}
	self.beasts_sort_list = {}
	self.beasts_egg_sort_list = {}

	for _, temp_data in ipairs(self.beasts_list) do
		if temp_data.is_have_beast then
			table.insert(self.beasts_sort_list, temp_data)
		end
	end
	self:SortTableData(self.beasts_sort_list)

	-- 去掉蛋的合成
	-- for _, temp_data in ipairs(self.beasts_egg_list) do
	-- 	if temp_data.is_have_beast then
	-- 		table.insert(self.beasts_egg_sort_list, temp_data)
	-- 	end
	-- end
	-- self:SortTableData(self.beasts_egg_sort_list, true)

	for _, temp_data in ipairs(self.beasts_sort_list) do
		if temp_data and temp_data.server_data and temp_data.compose_status == BEASTS_COMPOSE_STATUS.NORMAL then
			local target_beast_data = temp_data.link_beast_data -- 使用链接对象
			local data = target_beast_data.server_data
			local is_can_compose, beast_compose_list
			if (temp_data.is_holy_beast and temp_data.server_data.holy_spirit_link_index == -1) or (not temp_data.is_holy_beast and temp_data.server_data.holy_spirit_link_index ~= -1) then
				is_can_compose, beast_compose_list = false, nil
			else
				is_can_compose, beast_compose_list = self:GetSingleBeastComposeList(target_beast_data.bag_id, data.beast_id)
			end
			temp_data.is_can_compose = is_can_compose
		
			if is_can_compose and beast_compose_list then
				local batch_data = self:CreateComposeBatchData(data.beast_id, target_beast_data.bag_id, beast_compose_list, false)
				table.insert(self.quick_compse_list, batch_data)
				self:SetBeastDataSelect(target_beast_data.bag_id, false, BEASTS_COMPOSE_STATUS.SELECT)
				self:ChangeBeastComposeStatus(beast_compose_list, false)
			end
		end
	end

	-- for _, temp_data in ipairs(self.beasts_egg_sort_list) do
	-- 	if temp_data and temp_data.server_data and temp_data.compose_status == BEASTS_COMPOSE_STATUS.NORMAL then
	-- 		local data = temp_data.server_data
	-- 		local is_can_compose, beast_compose_list = self:GetSingleBeastEggComposeList(temp_data.bag_id, data.beast_id)
	-- 		temp_data.is_can_compose = is_can_compose
		
	-- 		if is_can_compose and beast_compose_list then
	-- 			local batch_data = self:CreateComposeBatchData(data.beast_id, temp_data.bag_id, beast_compose_list, true)
	-- 			table.insert(self.quick_compse_list, batch_data)
	-- 			self:SetBeastDataSelect(temp_data.bag_id, true, BEASTS_COMPOSE_STATUS.SELECT)
	-- 			self:ChangeBeastComposeStatus(beast_compose_list, false)
	-- 		end
	-- 	end
	-- end
end

-- 获取一键批量
function ControlBeastsWGData:GetAllBeastComposeList()
	self:RefreshAllBeastComposeList()	-- 获取列表
	self:ResetAllBeastComposeStatus()	-- 取消选择
	return self.quick_compse_list
end

function ControlBeastsWGData:CreateComposeBatchData(beast_id, bag_id, list, is_egg)
	local batch_data = {}
	batch_data.beast_id = beast_id
	batch_data.bag_id = bag_id
	batch_data.is_egg = is_egg
	batch_data.beast_compose_list = list
	batch_data.select = true
	return batch_data
end

-- 重置选取状态（确保批次已被选出来了）
function ControlBeastsWGData:ResetAllBeastComposeStatus()
	for _, temp_data in ipairs(self.beasts_list) do
		if temp_data and temp_data.server_data then
			self:SetBeastDataSelect(temp_data.bag_id, false, BEASTS_COMPOSE_STATUS.NORMAL)
		end
	end

	for _, temp_data in ipairs(self.beasts_egg_list) do
		if temp_data and temp_data.server_data then
			self:SetBeastDataSelect(temp_data.bag_id, true, BEASTS_COMPOSE_STATUS.NORMAL)
		end
	end

	self.beasts_sort_list = {}
	self.beasts_egg_sort_list = {}
end

-- 改变兽的合成状态
function ControlBeastsWGData:ChangeBeastComposeStatus(list, is_reset)
	if not list then
		return
	end

	local aim_status = is_reset and BEASTS_COMPOSE_STATUS.NORMAL or BEASTS_COMPOSE_STATUS.SELECT
	if list and list.special_list then
		for _, info in ipairs(list.special_list) do
			self:SetBeastDataSelect(info.bag_id, info.is_egg, aim_status)
		end
	end

	if list and list.same_list then
		for _, same_data in pairs(list.same_list) do
			for element, star_data in pairs(same_data) do
				for satr, star_list in pairs(star_data) do
					for _, beasts_data in pairs(star_list) do
						self:SetBeastDataSelect(beasts_data.bag_id, beasts_data.is_egg, aim_status)
					end
				end
			end
		end
	end
end

-- 刷新一下单个驭兽是否可合成是否添加列表
function ControlBeastsWGData:GetSingleBeastComposeList(aim_bag_id, aim_beast_id, is_use_card)
	local is_can_compose, return_table = self:CheckBeastCompose(aim_bag_id, -1, aim_beast_id, false, is_use_card)
	return is_can_compose, return_table
end

-- 刷新一下单个驭兽是否可合成是否添加列表
function ControlBeastsWGData:GetSingleBeastEggComposeList(aim_egg_bag_id, aim_beast_id, is_use_card)
	local is_can_compose, return_table = self:CheckBeastCompose(-1, aim_egg_bag_id, aim_beast_id, true, is_use_card)
	return is_can_compose, return_table
end

-- 获取批次合成列表
function ControlBeastsWGData:GetBatchComposeList()
	return self.quick_compse_list
end


-- 设置一键合成的选中状态, 没有传入背包id和蛋参数则表示全部操作选中未选中
function ControlBeastsWGData:SetBatchComposeListSelect(is_select, bag_id, is_egg)
	if bag_id and is_egg then
		for _, compose_data in ipairs(self.quick_compse_list) do
			if compose_data.bag_id == bag_id and compose_data.is_egg == is_egg then
				compose_data.select = is_select
				return
			end
		end
	else
		for _, compose_data in ipairs(self.quick_compse_list) do
			compose_data.select = is_select
		end
	end
end

-- 获取消耗的列表拆分成可消耗的展示的列表
function ControlBeastsWGData:GetSpiltSingleBeastComposeList(aim_beast_id, aim_bag_id, compose_list)
	local special_data = {}
	special_data.aim_beast_id = aim_beast_id
	special_data.aim_bag_id = aim_bag_id
	special_data.list = compose_list and compose_list.special_list or {}
	special_data.num = compose_list and #special_data.list or 0

	local same_data = {}
	same_data.aim_beast_id = aim_beast_id
	same_data.aim_bag_id = aim_bag_id
	same_data.list = compose_list and compose_list.same_list or {}
	same_data.num = compose_list and #same_data.list or 0

	local return_table = {}
	table.insert(return_table, special_data)
	table.insert(return_table, same_data)
	return return_table
end

function ControlBeastsWGData:CreateComposeBeastData(beast_data)
	local data = {}
	data.is_egg = beast_data.is_egg
	data.bag_id = beast_data.bag_id
	return data
end

-- 封装发送给服务器的数据
function ControlBeastsWGData:CreateComposeData(main_data, compose_list, starup_beast_ids)
	local data = {}
	data.aim_beast = self:CreateComposeBeastData(main_data)
	data.compose_list = {}
	if compose_list and compose_list.special_list then
		for _, special_data in ipairs(compose_list.special_list) do
			table.insert(data.compose_list, special_data)
		end
	end

	if compose_list and compose_list.same_list then
		for _, same_data in pairs(compose_list.same_list) do
			for element, star_data in pairs(same_data) do
				for satr, star_list in pairs(star_data) do
					for _, beasts_data in pairs(star_list) do
						table.insert(data.compose_list, beasts_data)
					end
				end
			end
		end
	end

	-- 加入碎片
	if starup_beast_ids then
		for i, starup_beast_data in ipairs(starup_beast_ids) do
			if starup_beast_data.chip_id ~= nil then
				local temp_data = {}
				temp_data.is_chip = true
				temp_data.item_id = starup_beast_data.chip_id
				temp_data.item_num = starup_beast_data.num

				table.insert(data.compose_list, temp_data)
			end
		end
	end

	return data
end
------------------------------------合成逻辑end--------------------------------
-- 获取全部满足激活的组合技能
function ControlBeastsWGData:GetCurBattleGroupTypeNumberList()
	-- 获取当前上阵所有类型的个数
	local all_type_table = {}

	local get_type_number = function (beast_id)
		local beast_cfg = self:GetBeastCfgById(beast_id)
		if beast_cfg then
			return beast_cfg.beast_type
		end
		
		return nil
	end

	local group_type_table = function (beasts_bag_id)
		local beast_data = self:GetBeastDataById(beasts_bag_id)
		if beast_data and beast_data.server_data.beast_id then
			local beast_type = get_type_number(beast_data.server_data.beast_id)
			if beast_type then
				if all_type_table[beast_type] then
					all_type_table[beast_type] = all_type_table[beast_type] + 1
				else
					all_type_table[beast_type] = 1
				end
			end
		end
	end

	for _, main_hole in ipairs(self.battle_main_hole_data) do
		if main_hole.beasts_bag_id ~= -1 then
			group_type_table(main_hole.beasts_bag_id)
		end
	end

	for _, assist_hole in ipairs(self.battle_assist_hole_data) do
		if assist_hole.beasts_bag_id ~= -1 then
			group_type_table(assist_hole.beasts_bag_id)
		end
	end

	return all_type_table
end

-- 获取当前的组合技能列表
function ControlBeastsWGData:GetCurBattleGroupSkillList()
	local all_type_table = self:GetCurBattleGroupTypeNumberList()
	local retur_table = {}
	
	if all_type_table and self.beasts_group_list then
		for _, group_data in ipairs(self.beasts_group_list) do
			if group_data and group_data.group_conditions then
				local group_conditions = group_data.group_conditions
				local need_type = group_conditions.need_type
				local need_num = group_conditions.need_num

				local temp_table = {}
				temp_table.group_skill_id = group_data.group_skill_id
				temp_table.beast_group_id = group_data.beast_group_id
				temp_table.group_skill_name_icon = group_data.group_skill_name_icon
				temp_table.need_type = need_type
				temp_table.need_num = need_num
				temp_table.cur_num = all_type_table[need_type] or 0
				temp_table.is_unlock = temp_table.cur_num >= temp_table.need_num
				table.insert(retur_table, temp_table)
			end
		end
	end

	return retur_table
end

-- 获取当前激活的组合技能列表
function ControlBeastsWGData:GetCurBattleGroupSkillActiveList()
	local all_type_table = self:GetCurBattleGroupTypeNumberList()
	local retur_table = {}
	
	if all_type_table and self.beasts_group_list then
		for _, group_data in ipairs(self.beasts_group_list) do
			if group_data and group_data.group_conditions then
				local group_conditions = group_data.group_conditions
				local need_type = group_conditions.need_type
				local need_num = group_conditions.need_num
				local cur_num = all_type_table[need_type] or 0
				if cur_num >= need_num then
					local temp_table = {}
					temp_table.group_skill_id = group_data.group_skill_id
					temp_table.need_type = need_type
					temp_table.need_num = need_num
					temp_table.cur_num = cur_num
					temp_table.is_unlock = temp_table.cur_num >= temp_table.need_num
					table.insert(retur_table, temp_table)
				end
			end
		end
	end

	return retur_table
end

-- 获取当前组技能的相关御兽
function ControlBeastsWGData:GetCurBattleGroupSkillBeastIds(beast_group_id)
	local beast_group_cfg = self:GetGroupSkillDataByGroup(beast_group_id)
	local retur_table = {}
	local beast_id = 0 
	local need_num = 3

	if beast_group_cfg and beast_group_cfg.group_conditions and self.beasts_map_cfg then
		beast_id = beast_group_cfg.group_show_beast_id
		need_num = beast_group_cfg.group_conditions.need_num
	end

	if beast_id ~= 0 then
		for i = 1, need_num do
			table.insert(retur_table, beast_id)
		end
	end
	
	return retur_table
end

-- 获取当前组技能的相关御兽
function ControlBeastsWGData:GetCurBeastElementByBeastId(beast_id)
	local beast_cfg = self:GetBeastCfgById(beast_id)
	if beast_cfg then
		return beast_cfg.beast_element or 0
	end

	return 0
end

-- 获取御兽重置需要的元宝数
function ControlBeastsWGData:GetConsumeGoldNumByLevel(beast_level)
	local lv_cfg = self:GetBeastLevelCfgByLevel(beast_level)
	if not lv_cfg then
		return 0
	end

	return lv_cfg.consume_gold_num or 0
end

-- 获取当前的解锁状态和数据
function ControlBeastsWGData:GetHoleStatusData(hole_id)
	local cfg_data = self:GetHoleCfgById(hole_id)
	if not cfg_data then
		return false, nil
	end

	local is_can_unlock = true
	local return_data = {}

	local assemble_data = function(str, now, aim)
		local info = {}
		info.str = str
		info.now = now
		info.aim = aim

		return now >= aim, info
	end

	if cfg_data.role_level and cfg_data.role_level ~= 0 then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		is_can_unlock, return_data = assemble_data(Language.ContralBeasts.HoleTips3, role_level, cfg_data.role_level)

		if not is_can_unlock then
			return is_can_unlock, return_data
		end
	end

	if cfg_data.fb_tower_level and cfg_data.fb_tower_level ~= 0 then
		local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
		is_can_unlock, return_data = assemble_data(Language.ContralBeasts.HoleTips4, pass_level, cfg_data.fb_tower_level)

		if not is_can_unlock then
			return is_can_unlock, return_data
		end
	end

	if cfg_data.zhuanzhi and cfg_data.zhuanzhi ~= 0 then
		local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
		is_can_unlock, return_data = assemble_data(Language.ContralBeasts.HoleTips4_1, prof_level, cfg_data.zhuanzhi)

		if not is_can_unlock then
			return is_can_unlock, return_data
		end
	end

	if cfg_data.vip_limit and cfg_data.vip_limit ~= 0 then
		local cur_vip_lv = VipWGData.Instance:GetVipLevel()
		is_can_unlock, return_data = assemble_data(Language.ContralBeasts.HoleTips5, cur_vip_lv, cfg_data.vip_limit)

		if not is_can_unlock then
			return is_can_unlock, return_data
		end
	end

	return is_can_unlock, nil
end

-----------------------------驭兽战斗相关-----------------------
-- 驭兽技能信息
function ControlBeastsWGData:SetSkillInfo(protocol)
	local client_index = protocol.index + 1
	self:SetBeastSingleFightInfo(client_index, protocol)
end

-- 驭兽战斗信息
function ControlBeastsWGData:SetBeastFightInfo(protocol)
	if protocol.beast_fight_info then
		local last_list = nil

		local index = 0
		local change_table = {}
		local change_bag_id_table = {}
		if self.beast_fight_info then
			last_list = TableCopy(self.beast_fight_info)

			for i, last_data in ipairs(last_list) do
				if last_data.bag_id ~= -1 then
					index = index + 1
					change_bag_id_table[last_data.bag_id] = {}
					change_bag_id_table[last_data.bag_id].bag_id = last_data.bag_id
				end

				if last_data.beast_id then
					change_table[last_data.beast_id] = {}
					change_table[last_data.beast_id].beast_id = last_data.beast_id
				end
			end
		end
		
		local new_index = 0
		for i, message in ipairs(protocol.beast_fight_info) do
			if message.bag_id ~= -1 then
				new_index = new_index + 1
				change_bag_id_table[message.bag_id] = {}
				change_bag_id_table[message.bag_id].bag_id = message.bag_id
			end

			if message.beast_id then
				change_table[message.beast_id] = {}
				change_table[message.beast_id].beast_id = message.beast_id
			end

			self:SetBeastSingleFightInfo(i, message, last_list)
		end

		local max_count = 0
		for k, v in pairs(change_table) do
			if v and v.beast_id then
				max_count = max_count + 1
			end
		end

		local max_bag_count = 0
		for k, v in pairs(change_bag_id_table) do
			if v and v.bag_id then
				max_bag_count = max_bag_count + 1
			end
		end

		if index ~= new_index or max_count > new_index or max_bag_count > new_index then
			for i, fight_info in ipairs(self.beast_fight_info) do
				fight_info.old_battle_index = fight_info.battle_index
			end
		end
	end
end

-- 设置驭兽技能信息(当个数发生变化)
function ControlBeastsWGData:SetBeastSingleFightInfo(client_index, message, last_list)
	if not self.beast_fight_info then
		self.beast_fight_info = {}
	end

	if not self.beast_fight_info[client_index] then
		self.beast_fight_info[client_index] = {}
	end

	local server_index = client_index - 1
	local get_old_battle_index = function(list, bag_id, server_index)
		for i, last_data in ipairs(last_list) do
			if last_data.bag_id == bag_id and last_data.bag_id ~= -1 and bag_id ~= -1 then
				return last_data.battle_index
			end
		end

		return server_index
	end

	if last_list == nil then
		self.beast_fight_info[client_index].old_battle_index = server_index
	else
		self.beast_fight_info[client_index].old_battle_index = get_old_battle_index(last_list, message.bag_id, server_index)
	end

	self.beast_fight_info[client_index].bag_id = message.bag_id				--驭兽背包id
	self.beast_fight_info[client_index].battle_index = server_index
	self.beast_fight_info[client_index].skill_id = message.skill_id
	self.beast_fight_info[client_index].cd_finish_time = message.cd_finish_time
	self.beast_fight_info[client_index].cd_change_time = message.cd_change_time

	if message.beast_id ~= nil and message.beast_id ~= 0 then
		self.beast_fight_info[client_index].beast_id = message.beast_id
	end
end

-- 获取上一次的在列表的位置
function ControlBeastsWGData:GetBeastFightInfoLastIndex()
	
end

-- 获取当前的出战的驭兽技能信息
function ControlBeastsWGData:GetSkillInfoByIndex(client_index)
	return (self.beast_fight_info or {})[client_index]
end 

-- 获取当前的驭兽技能列表和历史出战当前出战
function ControlBeastsWGData:GetSkillInfoList()
	return self.beast_fight_info
end 

-- 展示技能
function ControlBeastsWGData:ShowBeastSkill(beast_id, is_nor, need_spirit)
    local map_data = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
    if map_data then
        local skill_id = map_data.skill_id
        
        if is_nor then
            skill_id = map_data.normal_skill_id
        end
        --去技能数据类查
        local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
        local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(skill_id)
        if client_cfg and beast_cfg then
			local spirit_data
			if need_spirit and not is_nor then
				spirit_data = self:GetHolySpiritSkillData(map_data.beast_type)
			end
            local show_data = {
				skill_box_type = SKILL_BOX_TYPE.CONTROL_BEASTS,
                icon = client_cfg.icon_resource,
                top_text = beast_cfg.skill_name,
                body_text = client_cfg.description,
				capability = beast_cfg.capability_inc,
                x = 0,
                y = 0,
                set_pos2 = true,
				hide_next = IsEmptyTable(spirit_data),
				is_active_skill = not is_nor,
				skill_level = is_nor and map_data.normal_skill_level or map_data.skill_level,
				passive_str = is_nor and Language.Common.PuGong or nil,
				spirit_data = spirit_data
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
        end
    end
end

-- 展示技能
function ControlBeastsWGData:ShowSpBeastSkill(beast_id)
    local map_data = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
    if map_data then
		local be_skill_id = map_data.be_skill_id or 0
		local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(be_skill_id)
		if be_skill_cfg then
			local show_data = {
                icon = be_skill_cfg.skill_icon,
                top_text = be_skill_cfg.skill_name,
                body_text = be_skill_cfg.skill_des,
                x = 0,
                y = 0,
                set_pos2 = true,
				hide_next = true,
				is_active_skill = false,
				skill_level = map_data.be_skill_level or 0,
				passive_str = Language.Common.ZhuanShu
            }
            NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
		end
    end
end

-- 通过属性id和属性颜色获取最高属性数据
function ControlBeastsWGData:GetCircleAttrDataByIdColor(attr_id, color)
	return ((self.star_circle_attr_id_color_cfg or {})[attr_id] or {})[color]
end

-- 获取当前能到达多少级(给予目标等级则表示是否能达到目标等级)
function ControlBeastsWGData:GetCanArrivedLevel(server_data, aim_lv)
	if not server_data then
		return 0, 0
	end

	local lv = server_data.beast_level					-- 当前等级
	local arrived_lv_need_exp = 0
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
	local beast_star = beast_cfg and beast_cfg.beast_star or 0
	local level_limit_cfg = ControlBeastsWGData.Instance:GetBeastLevelLimitCfgByStar(beast_star)
	local star_max_level = level_limit_cfg and level_limit_cfg.beast_max_level or 0
	if star_max_level > aim_lv then
		star_max_level = aim_lv
	end

	if lv >= star_max_level then
		return 0, 0
	end

	local exp = server_data.exp							-- 当前经验
	local cur_have_exp = self:GetBeastBaseInfoExp()		-- 当前可使用经验
	while lv < star_max_level and cur_have_exp > 0 do	-- while 循环计算等级
		local level_cfg = ControlBeastsWGData.Instance:GetBeastLevelCfgByLevel(lv)
		if not level_cfg then
			break
		end
		
		local need_exp = level_cfg.need_exp - exp
		if cur_have_exp < need_exp then
			break
		end

		arrived_lv_need_exp = arrived_lv_need_exp + need_exp
		cur_have_exp = cur_have_exp - need_exp
		lv = lv + 1
		exp = 0
	end

	return lv, arrived_lv_need_exp
end
-----------------------------驭兽战斗-----------------------

-----------------------------驭兽资质（新）-----------------------
function ControlBeastsWGData:GetBeastLevelAttrListNew(beast_id, beast_level, need_next, refine_attr_list, is_need_refine, is_need_holy)
	local base_attr_cfg = self:GetBeastLevelCfgByLevel(beast_level)
	local best_beas_cfg = self:GetBeastCfgById(beast_id)
	local base_next_attr_cfg = nil

	if need_next then
		base_next_attr_cfg = self:GetBeastLevelCfgByLevel(beast_level + 1)
	end

	local return_attr_list = self:SpiltBeastLevelAttrListNew(base_attr_cfg, base_next_attr_cfg, best_beas_cfg)
	if is_need_refine then
		local refine_seq = (best_beas_cfg or {}).refine_seq or 0

		if refine_attr_list and refine_seq ~= -1 then
			for i, v in ipairs(refine_attr_list) do
				local index = i - 1
				local refind_cfg = self:GetBeastRefineCfgBySeqIndex(refine_seq, index)
				local key = refind_cfg.attr_id or 0

				if return_attr_list and return_attr_list[key] then
					local refine_value = v.attr_value + v.attr_value * (v.rand_value / 10000)
					refine_value = math.floor(refine_value)
					return_attr_list[key].attr_value = return_attr_list[key].attr_value + refine_value
				end
			end
		end
	end

	--圣魂属性加成
	if is_need_holy and self:IsHolyBeast(beast_id) then
		local beast_type = self:GetBeastTypeByBeastId(beast_id)
		local spirit_attr_list = self:GetHolySpiritTotalAttrList2(beast_type)
		self:MergeBeastAttrList(return_attr_list, spirit_attr_list)
	end

	local return_table = {}
	for _, attr_data in pairs(return_attr_list) do
		if attr_data and attr_data.attr_str and attr_data.attr_str ~= 0 then
			table.insert(return_table, attr_data)
		end
	end

	-- 圣兽链接加成
	if is_need_holy and self:IsHolyBeast(beast_id) then
		local beast_type = self:GetBeastTypeByBeastId(beast_id)
		local holy_beast_data = self:GetHolyBeastData(beast_type)
		if holy_beast_data and holy_beast_data.is_contracted then
			local link_beast_id = holy_beast_data.beast_data.link_beast_data.server_data.beast_id
			local aim_cfg = self:GetBeastCfgById(link_beast_id)
			local beast_color = aim_cfg and aim_cfg.beast_color or 0
			local contract_rate = self:GetHolyBeastContractRate(beast_color)
			self:MulAttributeRemovePer(return_table, contract_rate / 10000 + 1)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

-- 拆分组合属性列表
function ControlBeastsWGData:SpiltBeastLevelAttrListNew(cfg, next_cfg, best_beas_cfg)
	local attr_list = {}

	if not IsEmptyTable(cfg) then
		for i = 1, 6 do
			local key = cfg["attr_id" .. i]
			if key then
				if not attr_list[key] then
					attr_list[key] = {}
					attr_list[key].attr_str = key
				end
	
				attr_list[key].attr_value = cfg["attr_value" .. i]
	
				if best_beas_cfg and best_beas_cfg["attr_id" .. i] then
					attr_list[key].attr_value = attr_list[key].attr_value + best_beas_cfg["attr_value" .. i]
				end
	
				if next_cfg and next_cfg["attr_id" .. i] then
					local next_value = best_beas_cfg["attr_value" .. i] + next_cfg["attr_value" .. i]
					local temp_add_value = next_value - attr_list[key].attr_value
					attr_list[key].add_value = temp_add_value
				end
			end
		end
	end

	return attr_list
end

-- 获取当前幻兽类型所有的御兽id列表(有序列表)
function ControlBeastsWGData:GetBeastsTypeList(beast_id)
	local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(beast_id)
	local min_beast = ((beast_map_data or {}).beast_preview or {})[1]
	local show_list = {}

	if min_beast ~= nil then
		-- 找到当前的所有的可升星的目标
		local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(min_beast.beast_id)
		local starup_beast_id = aim_cfg.starup_beast_id or 0

		while starup_beast_id > 0 do
			table.insert(show_list, aim_cfg)
			aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(starup_beast_id)

			if aim_cfg ==nil then
				break
			end

			starup_beast_id = aim_cfg.starup_beast_id or 0
		end
	end

	return show_list
end

-- 获取当前幻兽类型所有的御兽id列表
function ControlBeastsWGData:GetBeastsTypeIdList(beast_id)
	local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(beast_id)
	local min_beast = ((beast_map_data or {}).beast_preview or {})[1]
	local show_list = {}

	if min_beast ~= nil then
		-- 找到当前的所有的可升星的目标
		local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(min_beast.beast_id)
		local starup_beast_id = aim_cfg.starup_beast_id or 0

		if starup_beast_id > 0 then
			while starup_beast_id > 0 do
				show_list[aim_cfg.beast_id] = true
				aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(starup_beast_id)
	
				if aim_cfg ==nil then
					break
				end
	
				starup_beast_id = aim_cfg.starup_beast_id or 0
			end

			-- 加入最后一个目标
			show_list[aim_cfg.beast_id] = true
		else
			show_list[aim_cfg.beast_id] = true
		end
	end

	return show_list
end

-- 获取模型展示配置[废弃]
function ControlBeastsWGData:GetBeastsDrawShowPosSca(beast_id)
	local empty = {}
	return (self.draw_model_show_cfg or empty)[beast_id]
end

-- 获取Spine展示配置
function ControlBeastsWGData:GetBeastsDrawSpineShowCfg(spine_id)
	local empty = {}
	return (self.draw_model_show_cfg or empty)[spine_id]
end

-- 获取当前升星的万能道具
function ControlBeastsWGData:GetBeastsComposeUseCard(beast_star)
	local empty = {}
	return (self.compose_card_cfg or empty)[beast_star]
end

-- 设置条件数体
function ControlBeastsWGData:SetBeastKingConditionData(cfg_data)
    local condition_table = {}

    if not cfg_data then
        return condition_table, false
    end

    local king_conditions = cfg_data.king_conditions
    local is_can_unlock = true

	if type(king_conditions) == "number" then
        return condition_table, false
    else
        for _, condition_data in ipairs(king_conditions) do
            if condition_data.type == BEAST_KING_CONDITION_TYPE.VIP then
                local cur_num = VipWGData.Instance:GetVipLevel()
                table.insert(condition_table, self:MontageKingConditionStr(cur_num, condition_data.params_1 or 0, condition_data.params_2 or 0, condition_data.type))
            else
                local cur_num = self:GetCurBeastListNum(condition_data.type, condition_data.params_2)
                is_can_unlock = cur_num >= condition_data.params_1 and is_can_unlock
                table.insert(condition_table, self:MontageKingConditionStr(cur_num, condition_data.params_1 or 0, condition_data.params_2 or 0, condition_data.type))
            end
        end
    end

    return condition_table, is_can_unlock
end

-- 拼接条件文本字符串
function ControlBeastsWGData:MontageKingConditionStr(cur, max, aim, beast_king_condition_type)
	local temp_data = {}
	temp_data.is_enough = cur >= max
	local color = temp_data.is_enough and COLOR3B.D_GREEN or COLOR3B.RED
    local progress_str = ToColorStr(string.format("（%d/%d）", cur, max), color)
 
    local str = nil
    if beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_LEVEL then
        str = string.format(Language.ContralBeasts.KingTips1, max, aim, progress_str)
    elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_STAR then
        str = string.format(Language.ContralBeasts.KingTips2, max, aim, progress_str)
    elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.BESTA_COLOR then
        str = string.format(Language.ContralBeasts.KingTips3, max, Language.ContralBeasts.BeastColorNmae[aim], progress_str)
    elseif beast_king_condition_type == BEAST_KING_CONDITION_TYPE.VIP then
        str = string.format(Language.ContralBeasts.KingTips4, max, progress_str)
    end

    if str ~= nil then
        temp_data.desc = str
    end

	return temp_data
end

function ControlBeastsWGData:CheckCanBeastDraw(need_count)
	local now_count = 0
	for _, beasts_data in ipairs(self.beasts_list) do
		if beasts_data.is_have_beast then
			now_count = now_count + 1
		end
	end

	return BEAST_DEFINE.BEAST_BORN_COUNT_MAX - now_count >= need_count
end
-----------------------------驭兽资质（新）-----------------------

--------------------------- 元素反应
function ControlBeastsWGData:SetElementData(protocol)
	self.element_obj_id = protocol.obj_id
	self.element_param1 = math.floor(protocol.element_param1 / 1000)
	self.element_param2 = math.floor(protocol.element_param2 / 1000)
	local element_level1 = protocol.element_param1 % 1000
	local element_level2 = protocol.element_param2 % 1000
	self.element_level = element_level1 > element_level2 and element_level2 or element_level1
end

function ControlBeastsWGData:GetElementObjID()
	return self.element_obj_id
end

function ControlBeastsWGData:GetAllElementType()
	return self.element_param1, self.element_param2
end

function ControlBeastsWGData:GetElementComboCfg()
	if ((self.element_effect_level_cfg[self.element_param1] or {})[self.element_param2] or {})[self.element_level] then
		return ((self.element_effect_level_cfg[self.element_param1] or {})[self.element_param2] or {})[self.element_level]
	else
		return ((self.element_effect_level_cfg[self.element_param2] or {})[self.element_param1] or {})[self.element_level]
	end
end

--------------------------- 元素反应

--------------------------- 幻兽图鉴
--幻兽图鉴信息
function ControlBeastsWGData:SetBeastHoodbookInfo(protocol)
	self.beast_hand_book_info = protocol.item_list
end

--幻兽图鉴信息(单个更新)
function ControlBeastsWGData:UpdateBeastHoodbookInfo(protocol)
	if not self.beast_hand_book_info then
		self.beast_hand_book_info = {}
	end

	self.beast_hand_book_info[protocol.beast_type] = protocol.item_data
end

-- 获取当前种类的幻兽数据
function ControlBeastsWGData:GetBeastHoodbookInfo(beast_type)
	local empty = {}
	return (self.beast_hand_book_info or empty)[beast_type]
end

-- 幻兽图鉴页签红点信息
function ControlBeastsWGData:GetBeastsHandBookRemind()
	if not self.beast_hand_book_info then
		return 0
	end

	if self:GetBeastsHandBookRewardRemind() then
		self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_BOOK_REWARD, 1, TabIndex.beasts_book)
		return 1
	end

	if ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRed() then
		return 1
	end

	self:SetBeastStrengthen(MAINUI_TIP_TYPE.BEASTS_BOOK_REWARD, 0, TabIndex.beasts_book)
	return 0
end

-- 获取当前图鉴是否有
function ControlBeastsWGData:GetBeastsHandBookRewardRemind()
	for i, v in ipairs(self.beast_hand_book_info) do
		if self:ChenkHaveRewardCanGet(i) and self:GetHandBookRewardCfgByTypeStar(i, v.max_star) ~= nil then
			return true
		end
	end

	return false
end

-- 获取当前图鉴是否有可领取奖励
function ControlBeastsWGData:ChenkHaveRewardCanGet(beast_type)
	if (not self.beast_hand_book_info) or (not self.beast_hand_book_info[beast_type]) then
		return false
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data = self.beast_hand_book_info[beast_type]
	local cfg = self:GetBeastCfgByTypeAndStar(beast_type, data.max_star)
	if cfg == nil or open_day < cfg.show_day then
		return false
	end

	return data.reward_star < data.max_star
end

-- 获取当前图鉴是否有可领取奖励
function ControlBeastsWGData:ChenkBeastHandBookIsActive(beast_type)
	if (not self.beast_hand_book_info) or (not self.beast_hand_book_info[beast_type]) then
		return false
	end

	local data = self.beast_hand_book_info[beast_type]
	return data.max_star > 0
end

-- 获取已孵化的种族驭兽是否已拥有，已拥有则不可合成
function ControlBeastsWGData:CheckBeastTypeIsHaveInBag(var_beast_type)
	if not self.beasts_list then
		return false
	end


	for _, beast_data in ipairs(self.beasts_list) do
		if beast_data and beast_data.is_have_beast and beast_data.server_data then
			if beast_data.beast_type == var_beast_type then
				return true
			end
		end
	end

	return false
end
-- 获取当前图鉴是否已领取奖励
function ControlBeastsWGData:ChenkHaveRewardCanGeted(beast_type)
	if (not self.beast_hand_book_info) or (not self.beast_hand_book_info[beast_type]) then
		return false
	end

	local data = self.beast_hand_book_info[beast_type]
	return data.reward_star ~= -1 and data.reward_star == data.max_star
end

-- 获取当前展示的列表
function ControlBeastsWGData:GetNowGetRewardList(beast_type)
	local aim_table = self:GetBeastsMapCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local return_table = {}
	local quick_reward_table = {}
	local is_add_table = {}

	for k, beast_data in pairs(aim_table) do
		local beast_map_data = self:GetBeastDatMapaById(beast_data.beast_id)
		if beast_map_data and beast_map_data.beast_preview then
			local min_start_beast = beast_map_data.beast_preview[1]
			local min_star = min_start_beast and min_start_beast.beast_star or 0

			--and 
			if (beast_data.beast_star == min_star and beast_data.beast_color < 8
			and open_day >= beast_data.show_day) or (beast_type == beast_data.beast_type) then
				if not is_add_table[beast_data.beast_type] then
					is_add_table[beast_data.beast_type] = true
					table.insert(return_table, beast_data)

					if self:ChenkHaveRewardCanGet(beast_data.beast_type)
					and self:GetHandBookRewardCfgByTypeStar(beast_data.beast_type, beast_data.beast_star) ~= nil then
						table.insert(quick_reward_table, beast_data)
					end
				end
			end
		end
	end

	table.sort(return_table, SortTools.KeyUpperSorter("beast_color"))
	return return_table, quick_reward_table
end
--------------------------- 幻兽图鉴

--------------------------- 幻兽皮肤start

-- 设置皮肤服务器数据
function ControlBeastsWGData:SetBeastsSkinData(protocol)
	self.skin_info_list = protocol.skin_level_list
end

-- 获取皮肤等级
function ControlBeastsWGData:GetBeastsSkinData(skin_seq)
	return (self.skin_info_list or {})[skin_seq] or 0
end

-- 皮肤是否激活
function ControlBeastsWGData:BeastSkinIsActiveById(item_id)
	local cfg = self.skin_cfg_id[item_id]
	if cfg then
		local skin_level = self:GetBeastsSkinData(cfg.skin_seq)
		return skin_level > 0
	end
	return false
end

-- 获取皮肤配置byseq
function ControlBeastsWGData:GetBeastsSkinCfgBySeq(skin_seq)
	return self.skin_cfg[skin_seq]
end

-- 获取皮肤配置by item_id
function ControlBeastsWGData:GetBeastsSkinCfgByItemId(item_id)
	return self.skin_cfg_id[item_id]
end

-- 获取幻兽种类根据id
function ControlBeastsWGData:GetBeastTypeByBeastId(beast_id)
	local beasts_cfg = self:GetBeastCfgById(beast_id)
	return (beasts_cfg or {}).beast_type or 0
end

-- 获取幻兽种类根据id
function ControlBeastsWGData:GetBeastColorByBeastId(beast_id)
	local beasts_cfg = self:GetBeastCfgById(beast_id)
	return (beasts_cfg or {}).beast_color or 0
end

-- 获取皮肤seq对应的幻兽种类
function ControlBeastsWGData:GetBeastTypeBySkinSeq(skin_seq)
	return (self.skin_cfg[skin_seq] or {}).beast_type or 0
end

-- 获取某个幻兽的所有皮肤配置 by beast_id
function ControlBeastsWGData:GetSkinCfgListByBeastId(beast_id)
	local beast_type = self:GetBeastTypeByBeastId(beast_id)
	return self.skin_type_cfg[beast_type] or {}
end

-- 获取某个幻兽的所有皮肤配置 by beasts_type
function ControlBeastsWGData:GetSkinListByBeastsType(beasts_type)
	return self.skin_type_cfg[beasts_type] or {}
end

-- 获取皮肤升级配置
function ControlBeastsWGData:GetSkinLevelCfg(skin_seq, level)
	return (self.skin_level_cfg[skin_seq] or {})[level]
end

-- 获取皮肤属性
function ControlBeastsWGData:GetSkinAttrList(skin_seq, level)
	if level <= 0 then level = 1 end
	local skin_cfg = self:GetSkinLevelCfg(skin_seq, level)
	local attr_list = EquipWGData.GetSortAttrListByTypeCfg(skin_cfg, "attr_id", "attr_value", 1, 7)
	return attr_list
end

-- 获取预览幻兽配置
function ControlBeastsWGData:GetBeatsPreviewCfgByBeastType(beast_type)
	local beast_cfg_list = self:GetBeastsMapCfg()
	for k, v in pairs(beast_cfg_list) do
		if v.beast_type == beast_type then
			local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(v.beast_id)
			if beast_map_data and beast_map_data.beast_preview then
				local min_start_beast = beast_map_data.beast_preview[1]
				local min_star = min_start_beast and min_start_beast.beast_star or 0
				if v.beast_star == min_star then
					return v
				end
			end
		end
	end
	return nil
end

-- 获取背包中目标类型幻兽
function ControlBeastsWGData:FindTargetBeastData(beast_type)
	for i, v in ipairs(self.beasts_list) do
		if v.is_have_beast then
			local beast_cfg = (self.beasts_map_cfg or {})[v.server_data.beast_id]
			if beast_cfg.beast_type == beast_type then
				return v
			end
		end
	end
	return nil
end

-- 幻兽皮肤红点
function ControlBeastsWGData:GetBeastsSkinRed(beast_data)
	if not beast_data or not beast_data.is_have_beast then
		return false
	end

	local skin_cfg = self:GetSkinCfgListByBeastId(beast_data.server_data.beast_id)
	for i, v in ipairs(skin_cfg) do
		if self:GetSingleBeastSkinRed(v.skin_seq) then
			return true
		end
	end

	return false
end

-- 幻兽皮肤红点
function ControlBeastsWGData:GetSingleBeastSkinRed(skin_seq)
	if skin_seq < 0 then
		return false
	end
	local skin_level = self:GetBeastsSkinData(skin_seq)
	if skin_level == 0 then -- 仅检查是否可激活，目前不可升级
		local skin_level_cfg = self:GetSkinLevelCfg(skin_seq, skin_level)
		if skin_level_cfg then
			local had_num = ItemWGData.Instance:GetItemNumInBagById(skin_level_cfg.cost_item_id)
			local need_num = skin_level_cfg.cost_item_num
			if had_num >= need_num then
				return true
			end
		end
	end
	return false
end

--------------------------- 幻兽皮肤end


--------------------------- 创世圣兽start ---------------------------

function ControlBeastsWGData:GetSelectedHolyBeastData()
	return self.selected_holy_beast_data
end

function ControlBeastsWGData:SetSelectedHolyBeastData(holy_beast_data)
	self.selected_holy_beast_data = holy_beast_data
end

--检测是否是圣兽解锁道具
function ControlBeastsWGData:CheckIsHolyBeastUnlockItemId(item_id)
	if self.holy_beast_item_cfg[item_id] ~= nil then
		return true
	end
	return false
end

-- 圣兽配置by cost item
function ControlBeastsWGData:GetHolyBeastCfgByUnlockItemId(item_id)
	return self.holy_beast_item_cfg[item_id]
end

--检测是否是圣魂升级道具
function ControlBeastsWGData:CheckIsHolySpiritUpLevelItemId(item_id)
	if self.holy_beast_spirit_item_cfg[item_id] ~= nil then
		return true
	elseif self.holy_beast_spirit_item_cfg2[item_id] ~= nil then
		return true
	else
		return false
	end
end

-- 获取颜色对应链接加成
function ControlBeastsWGData:GetHolyBeastContractRate(beast_color)
	return (self.holy_link_per_cfg[beast_color] or {}).add_per or 0
end

--------契书--------

-- 获取圣兽数据
function ControlBeastsWGData:GetHolyBeastData(beast_type)
	return (self.holy_beasts_list or {})[beast_type]
end

-- 获取所有圣兽数据
function ControlBeastsWGData:GetHolyBeastDataList()
	local data_list = {}
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k, v in pairs(self.holy_beasts_list) do
		local had_num = ItemWGData.Instance:GetItemNumInBagById(v.cfg.call_cost_item_id)
		if open_day >= v.cfg.open_day or had_num > 0 or v.is_unlock then
			v.sort = (v.is_unlock and 0 or 10000) + v.beast_type
			table.insert(data_list, v)
		end
	end
	table.sort(data_list, SortTools.KeyLowerSorter("sort"))
	return data_list
end

-- 是否是圣兽
function ControlBeastsWGData:IsHolyBeast(beast_id)
	local beast_type = self:GetBeastTypeByBeastId(beast_id)
	return self.holy_beast_type_cfg[beast_type] ~= nil
end

-- 是否是圣兽
function ControlBeastsWGData:IsHolyBeastType(beast_type)
	return self.holy_beast_type_cfg[beast_type] ~= nil
end

-- 创世圣兽配置列表
function ControlBeastsWGData:GetHolyBeastsCfgList()
	return self.holy_beast_cfg or {}
end

-- 单个圣兽红点
function ControlBeastsWGData:GetSingleHolyBeastRed(holy_beast_data)
	if self:GetHolyBeastsCanUnlock(holy_beast_data.beast_type) then
		return true
	end
	if self:GetHolyBeastCanContract(holy_beast_data.beast_data) then
		return true
	end
	if self:GetHolyBeastSpiritRed(holy_beast_data.beast_type) then -- 圣魂
		return true
	end
	return false
end

-- 通过幻兽类型和星级获取幻兽id
function ControlBeastsWGData:GetBeastIdByTypeAndStar(beast_type, star)
	local empty = {}
	return ((self.beasts_type_star_cfg[beast_type] or empty)[star] or empty).beast_id or 0
end

-- 通过幻兽类型和星级获取幻兽配置
function ControlBeastsWGData:GetBeastCfgByTypeAndStar(beast_type, star)
	local empty = {}
	return (self.beasts_type_star_cfg[beast_type] or empty)[star]
end

-- 契书红点
function ControlBeastsWGData:GetHolyBeastContractRemind()
	local is_opened = FunOpen.Instance:GetFunIsOpened(FunName.HolyBeastsView)
	if not is_opened then
		return 0
	end

	for i, v in ipairs(self.holy_beast_cfg) do
		if self:GetHolyBeastsCanUnlock(v.beast_type) then
			return 1
		end
	end

	for k, v in pairs(self.holy_beasts_list) do
		if self:GetHolyBeastCanContract(v.beast_data) then
			return 1
		end
	end

	return 0
end

-- 是否可解锁
function ControlBeastsWGData:GetHolyBeastsCanUnlock(beast_type)
	local cur_beast_data = self.holy_beasts_list[beast_type]
	if not cur_beast_data or cur_beast_data.is_unlock then
		return false
	end
	local had_num = ItemWGData.Instance:GetItemNumInBagById(cur_beast_data.cfg.call_cost_item_id)
	local need_num = cur_beast_data.cfg.call_cost_item_num
	return had_num >= need_num
end

-- 圣兽是否可缔结
function ControlBeastsWGData:GetHolyBeastCanContract(beast_data)
	if not beast_data or not beast_data.is_holy_beast or beast_data.server_data.holy_spirit_link_index ~= -1 then
		return false
	end

	local beast_type = self:GetBeastTypeByBeastId(beast_data.server_data.beast_id)
	local holy_cfg = self.holy_beast_type_cfg[beast_type] or {}
	local base_cfg = self:GetBeastCfgById(holy_cfg.beast_id)
	if not base_cfg then
		return false
	end
	local min_star = base_cfg.beast_star  -- 初始星级

	-- 查找满足条件的幻兽
	for i, v in ipairs(self.beasts_list) do
		if v.is_have_beast and not v.is_holy_beast and v.server_data.holy_spirit_link_index == -1 then
			local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(v.server_data.beast_id)
			if beast_cfg and beast_cfg.beast_star >= min_star then
				return true
			end
		end
	end
	return false
end

-- 获取可缔结列表
function ControlBeastsWGData:GetCanContractList(holy_beast_type)
	local data_list = {}
	local holy_beast_cfg = self.holy_beast_type_cfg[holy_beast_type]
	if not holy_beast_cfg then return data_list end
	local cfg = self:GetBeastCfgById(holy_beast_cfg.beast_id) -- 初始星级圣兽
	if not cfg then return data_list end

	-- 可缔结幻兽星级不能低于初始圣兽星级
	local min_star = cfg.beast_star
	for i, v in ipairs(self.beasts_list) do
		if v.is_have_beast and not v.is_holy_beast and v.server_data.holy_spirit_link_index == -1 then
			local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(v.server_data.beast_id)
			if beast_cfg and beast_cfg.beast_star >= min_star then 
				table.insert(data_list, v)
			end
		end
	end

	table.sort(data_list, SortTools.KeyUpperSorter("cap_value"))
	return data_list
end

function ControlBeastsWGData:MulAttributeRemovePer(attr_list, num)
	for i = 1, #attr_list do
		if not EquipmentWGData.Instance:GetAttrIsPer(attr_list[i].attr_str) then
			attr_list[i].attr_value = math.floor(attr_list[i].attr_value * num)
			if attr_list[i].add_value then
				attr_list[i].add_value = math.floor(attr_list[i].add_value * num)
			end
		end
	end
	return attr_list
end

--------圣魂--------

-- 获取圣魂配置列表
function ControlBeastsWGData:GetHolySpiritCfgList(beast_type)
	local holy_cfg = self.holy_beast_type_cfg[beast_type]
	if holy_cfg then
		return self.holy_spirit_cfg[holy_cfg.holy_seq] or {}
	end
	return {}
end

-- 获取单个圣魂配置
function ControlBeastsWGData:GetHolySpiritCfg(beast_type, index)
	local holy_cfg = self.holy_beast_type_cfg[beast_type]
	if not holy_cfg then
		return nil
	end
	return (self.holy_spirit_cfg[holy_cfg.holy_seq] or {})[index]
end

-- 获取圣兽所有圣魂配置(分页)
function ControlBeastsWGData:GetHolySpiritAllPageCfg(beast_type)
	local holy_seq = (self.holy_beast_type_cfg[beast_type] or {}).holy_seq or -1
	return self.holy_spirit_page_cfg[holy_seq] or {}
end

-- 获取某页圣魂配置
function ControlBeastsWGData:GetHolySpiritCfgByPage(beast_type, page)
	local spirit_cfg = self:GetHolySpiritAllPageCfg(beast_type)
	return spirit_cfg[page] or {}
end

-- 获取圣魂分页数据
function ControlBeastsWGData:GetHolySpiritPageList(beast_type)
	local page_list = {}
	local name_list = self.holy_spirit_page_name_list[beast_type]
	if not name_list then
		return page_list
	end
	for i, v in ipairs(name_list) do
		local data = {}
		data.page = i - 1
		data.page_name = v
		data.beast_type = beast_type
		table.insert(page_list, data)
	end
	return page_list
end

-- 获取某个圣魂是否可升级
function ControlBeastsWGData:GetSpiritCanUpGrade(beast_type, index)
	local spirit_cfg = self:GetHolySpiritCfg(beast_type, index)
	if not spirit_cfg then return false end

	local holy_beast_data = self.holy_beasts_list[beast_type]
	if not holy_beast_data or not holy_beast_data.is_unlock or not holy_beast_data.is_contracted then return false end

	local level = holy_beast_data.beast_data.server_data.holy_spirit_level_list[index]
	if not level then return false end

	local cur_level_cfg = self:GetHolySpiritLevelCfg(beast_type, index, level)
	local next_level_cfg = self:GetHolySpiritLevelCfg(beast_type, index, level + 1)
	if not cur_level_cfg or not next_level_cfg then return false end

	local pre_index_list = Split(spirit_cfg.pre_index, "|")
	local pre_param_list = Split(spirit_cfg.pre_param, "|")

	for i = 1, #pre_index_list do
		local pre_index = tonumber(pre_index_list[i])
		local pre_param = tonumber(pre_param_list[i])

		if pre_index == -1 then --判断总等级
			local total_level = self:GetTotalLevelByPage(beast_type, spirit_cfg.page)
			if total_level < pre_param then
				return false
			end
		else -- 判断父节点
			local parent_level = holy_beast_data.beast_data.server_data.holy_spirit_level_list[pre_index]
			if not parent_level or parent_level < pre_param then
				return false
			end
		end
	end

	-- 判断道具是否足够
	local cost_item_id, cost_item_num
	for i = 0, 1 do
		cost_item_id = cur_level_cfg["cost_item_id" .. i]
		cost_item_num = cur_level_cfg["cost_item_num" .. i]
		if cost_item_id > 0 then
			if not self:CheckItemIsEnough(cost_item_id, cost_item_num) then
				return false
			end
		end
	end
	return true
end

-- 获取某页圣魂总等级
function ControlBeastsWGData:GetTotalLevelByPage(beast_type, page)
	local holy_beast_data = self.holy_beasts_list[beast_type]
	if not holy_beast_data or not holy_beast_data.is_unlock then return false end

	local spirit_cfg_list = self:GetHolySpiritCfgByPage(beast_type, page)
	local level = 0
	for i, v in ipairs(spirit_cfg_list) do
		level = level + (holy_beast_data.beast_data.server_data.holy_spirit_level_list[v.index] or 0)
	end
	return level
end

-- 获取某页圣魂是否开启
function ControlBeastsWGData:GetSpiritPageIsUnlock(beast_type, page)
	local holy_beast_data = self.holy_beasts_list[beast_type]
	if not holy_beast_data or not holy_beast_data.is_unlock then return false end

	local spirit_cfg_list = self:GetHolySpiritCfgByPage(beast_type, page)
	local spirit_cfg = (spirit_cfg_list or {})[1]
	if not spirit_cfg then return false end

	local pre_index_list = Split(spirit_cfg.pre_index, "|")
	local pre_param_list = Split(spirit_cfg.pre_param, "|")

	for i = 1, #pre_index_list do
		local pre_index = tonumber(pre_index_list[i])
		local pre_param = tonumber(pre_param_list[i])

		if pre_index == -1 then --判断总等级
			local total_level = self:GetTotalLevelByPage(beast_type, spirit_cfg.page)
			if total_level < pre_param then
				return false
			end
		else -- 判断父节点
			local parent_level = holy_beast_data.beast_data.server_data.holy_spirit_level_list[pre_index]
			if not parent_level or parent_level < pre_param then
				return false
			end
		end
	end
	return true
end

-- 页签红点
function ControlBeastsWGData:GetHolySpiritRemind()
	local is_fun_opened = FunOpen.Instance:GetFunIsOpenedByTabName("holy_beasts_spirit")
	if not is_fun_opened then
		return 0
	end

	for i, v in ipairs(self.holy_beast_cfg) do
		if self:GetHolyBeastSpiritRed(v.beast_type) then
			return 1
		end
	end
	return 0
end

-- 圣兽圣魂红点
function ControlBeastsWGData:GetHolyBeastSpiritRed(beast_type)
	local is_fun_opened = FunOpen.Instance:GetFunIsOpenedByTabName("holy_beasts_spirit")
	if not is_fun_opened then
		return false
	end
	
	if not self:IsHolyBeastType(beast_type) then
		return false
	end

	local beast_data = self:GetHolyBeastData(beast_type)
	if not beast_data or not beast_data.is_contracted then --未缔结无圣魂红点
		return false
	end

	local spirit_page_cfg = self:GetHolySpiritAllPageCfg(beast_type)
	for k, v in pairs(spirit_page_cfg) do
		if self:GetHolySpiritPageRed(beast_type, k) then
			return true
		end
	end
	return false
end

-- 获取圣魂分页红点
function ControlBeastsWGData:GetHolySpiritPageRed(beast_type, page)
	local beast_data = self:GetHolyBeastData(beast_type)
	if not beast_data or not beast_data.is_contracted then --未缔结无圣魂红点
		return false
	end

	if not self:GetSpiritPageIsUnlock(beast_type, page) then
		return false
	end
	local cfg_list = self:GetHolySpiritCfgByPage(beast_type, page)
	for i, v in ipairs(cfg_list) do
		if self:GetSpiritCanUpGrade(beast_type, v.index) then
			return true
		end
	end
	return false
end

-- 某个道具是否足够
function ControlBeastsWGData:CheckItemIsEnough(item_id, need_num)
	local had_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	return had_num >= need_num
end

-- 获取圣魂等级配置
function ControlBeastsWGData:GetHolySpiritLevelCfgList(beast_type, index)
	local holy_cfg = self.holy_beast_type_cfg[beast_type]
	if not holy_cfg then
		return nil
	end
	return (self.holy_spirit_level_cfg[holy_cfg.holy_seq] or {})[index] or {}
end

-- 获取单条圣魂等级配置
function ControlBeastsWGData:GetHolySpiritLevelCfg(beast_type, index, level)
	local holy_cfg = self.holy_beast_type_cfg[beast_type]
	if not holy_cfg then
		return nil
	end
	return ((self.holy_spirit_level_cfg[holy_cfg.holy_seq] or {})[index] or {})[level]
end

-- 获取圣魂等级
function ControlBeastsWGData:GetHolySpiritLevel(beast_type, index)
	local holy_beast_data = self.holy_beasts_list[beast_type]
	if holy_beast_data and holy_beast_data.is_unlock then
		return holy_beast_data.beast_data.server_data.holy_spirit_level_list[index] or 0
	end
	return 0
end

-- 获取圣魂技能配置
function ControlBeastsWGData:GetHolySpiritSkillCfg(beast_type, skill_id)
	return (self.holy_spirit_skill_cfg[beast_type] or {})[skill_id]
end

-- 获取圣魂技能加成
function ControlBeastsWGData:GetHolySpiritSkillData(beast_type)
	local spirit_skill_list = {}
	if not self:IsHolyBeastType(beast_type) then
		return spirit_skill_list
	end
	local spirit_cfg_list = self:GetHolySpiritAllPageCfg(beast_type)

	for page, cfg_list in pairs(spirit_cfg_list) do
		local skill_desc_str
		for i, v in ipairs(cfg_list) do
			if v.type == SPIRIT_TYPE.Skill then
				local level = self:GetHolySpiritLevel(beast_type, v.index)
				if level > 0 then
					local skill_level_cfg = self:GetHolySpiritLevelCfg(beast_type, v.index, level)
					local skill_cfg = self:GetHolySpiritSkillCfg(beast_type, skill_level_cfg.param0)
					if not skill_desc_str then
						skill_desc_str = skill_cfg.skill_desc
					else
						skill_desc_str = skill_desc_str .. "\n" .. skill_cfg.skill_desc
					end
				end
			end
		end
		if skill_desc_str then
			local page_name = self.holy_spirit_page_name_list[beast_type][page + 1] or ""
			local skill_title_str = string.format(Language.ContralBeasts.SpiritSkillStr, page_name)
			table.insert(spirit_skill_list, { skill_title = skill_title_str, skill_des = skill_desc_str })
		end
	end
	return spirit_skill_list
end

-- 获取圣魂总属性
function ControlBeastsWGData:GetHolySpiritTotalAttrList(beast_type)
	local holy_beast_data = self.holy_beasts_list[beast_type]
	if not holy_beast_data or not holy_beast_data.is_unlock then
		return {}
	end

	local total_attr_list = AttributePool.AllocAttribute()
	local attr_per_ratio = 0
	local spirit_cfg_list = self:GetHolySpiritCfgList(beast_type)
	for k, v in pairs(spirit_cfg_list) do
		local level = self:GetHolySpiritLevel(beast_type, v.index)
		if level > 0 then
			local spirit_level_cfg = self:GetHolySpiritLevelCfg(beast_type, v.index, level)
			if v.type == SPIRIT_TYPE.Attr then
				local attr_list = EquipWGData.GetSortAttrListByTypeCfg(spirit_level_cfg, "attr_id", "attr_value", 0, 3)
				attr_list = EquipWGData.GetConvertAttrList(attr_list)
				total_attr_list = AttributeMgr.AddAttributeAttr(total_attr_list, attr_list)
			elseif v.type == SPIRIT_TYPE.Multy or v.type == SPIRIT_TYPE.Awake  then
				attr_per_ratio = attr_per_ratio + spirit_level_cfg.param0
			end
		end
	end

	total_attr_list = AttributeMgr.MulAttribute(total_attr_list, (attr_per_ratio / 10000 + 1))
	return total_attr_list
end

-- 合并属性到attr_list1 { 101 = { attr_str = 101 , attr_value = 0 }} 类型
function ControlBeastsWGData:MergeBeastAttrList(attr_list1, attr_list2)
	for key, attr_data in pairs(attr_list2) do
		if not attr_list1[key] then
			attr_list1[key] = {}
			attr_list1[key].attr_str = key
			attr_list1[key].attr_value = attr_data.attr_value
		else
			attr_list1[key].attr_value = attr_list1[key].attr_value + attr_data.attr_value
		end
	end
	return attr_list1
end

-- 获取圣魂总属性 { attr_str = 101 , attr_value = 0 }
function ControlBeastsWGData:GetHolySpiritTotalAttrList2(beast_type)
	local holy_beast_data = self.holy_beasts_list[beast_type]
	if not holy_beast_data or not holy_beast_data.is_unlock then
		return {}
	end

	local attr_list = {}

	local add_attr = function(cfg)
		for i = 0, 3 do
			local key = cfg["attr_id" .. i]
			if key and key > 0 then
				if not attr_list[key] then
					attr_list[key] = {}
					attr_list[key].attr_str = key
					attr_list[key].attr_value = cfg["attr_value" .. i]
				else
					attr_list[key].attr_value = attr_list[key].attr_value + cfg["attr_value" .. i]
				end
			end
		end
	end

	local attr_per_ratio = 0 --加成万分比
	local spirit_cfg_list = self:GetHolySpiritCfgList(beast_type)
	for k, v in pairs(spirit_cfg_list) do
		local level = self:GetHolySpiritLevel(beast_type, v.index)
		if level > 0 then
			local spirit_level_cfg = self:GetHolySpiritLevelCfg(beast_type, v.index, level)
			if spirit_level_cfg then
				if v.type == SPIRIT_TYPE.Attr then
					add_attr(spirit_level_cfg)
				elseif v.type == SPIRIT_TYPE.Multy or v.type == SPIRIT_TYPE.Awake  then
					attr_per_ratio = attr_per_ratio + spirit_level_cfg.param0
				end
			end
		end
	end

	for k, v in pairs(attr_list) do
		v.attr_value = v.attr_value * (attr_per_ratio / 10000 + 1)
	end

	return attr_list
end

-- 获取圣魂技能固定战力
function ControlBeastsWGData:GetHolySpiritSkillCap(beast_type)
	local holy_beast_data = self.holy_beasts_list[beast_type]
	if not holy_beast_data or not holy_beast_data.is_unlock then
		return 0
	end
	local skill_total_cap = 0
	local spirit_cfg_list = self:GetHolySpiritCfgList(beast_type)
	for k, v in pairs(spirit_cfg_list) do
		local level = self:GetHolySpiritLevel(beast_type, v.index)
		if level > 0 then
			local spirit_level_cfg = self:GetHolySpiritLevelCfg(beast_type, v.index, level)
			if spirit_level_cfg then
				if v.type == SPIRIT_TYPE.Skill then
					skill_total_cap = skill_total_cap + spirit_level_cfg.capability_inc
				end
			end
		end
	end
	return skill_total_cap
end

-- 获取圣魂总战力
function ControlBeastsWGData:GetHolySpiritTotalCap(beast_type)
	local attr_list = self:GetHolySpiritTotalAttrList(beast_type)
	local cap = AttributeMgr.GetCapability(attr_list)
	local skill_total_cap = self:GetHolySpiritSkillCap(beast_type)
	local total_cap = cap + skill_total_cap
	return total_cap
end

-- 检测圣兽提前开启条件
function ControlBeastsWGData:CheckHolyBeastsIsCanOpen()
	for i, v in ipairs(self.holy_beast_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.call_cost_item_id)
		if num > 0 then
			return true
		end
		local holy_beast_data = self:GetHolyBeastData(v.beast_type)
		if holy_beast_data.is_unlock then
			return true
		end
	end
	return false
end

--------------------------- 创世圣兽end ---------------------------
--------------------------- 幻兽属性丹start -----------------------
-- 属性丹信息
function ControlBeastsWGData:SetBeastsPelletInfo(protocol)
	self.pellet_level_list = protocol.pellet_level_list
end

-- 获取对应属性丹等级
function ControlBeastsWGData:GetBeastsPelletLvBySeq(seq)
	return (self.pellet_level_list or {})[seq] or 0
end

-- 获取对应属性丹配置
function ControlBeastsWGData:IsBeastsPelletCfgByItem(item_id)
	return (self.pellet_item_cfg or {})[item_id]
end

-- 获取对应属性丹等级
function ControlBeastsWGData:IsBeastsPelletItem(item_id)
	local data = self:IsBeastsPelletCfgByItem(item_id)
	return data ~= nil
end

-- 获取属性丹红点信息
function ControlBeastsWGData:GetBeastsPelletRed()
    local cfg = self:GetBeastsPelletCfg()

	if not cfg then
		return 0
	end

    for i = 0, 2 do
		local cfg_data = cfg[i]

		if cfg_data and self:GetBeastsPelletRedBySeq(cfg_data.seq) then
			return 1
		end
	end

	return 0
end

-- 获取属性丹红点信息
function ControlBeastsWGData:GetBeastsPelletRedBySeq(seq)
	local cfg_data = self:GetBeastsPelletCfgBySeq(seq)

	if not cfg_data then
		return false
	end

    local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg_data.cost_item_id)
	local lv = ControlBeastsWGData.Instance:GetBeastsPelletLvBySeq(seq)
	local level_limit = cfg_data.level_limit or 0
	local last_lv = level_limit - lv
	local last_use_num = math.min(last_lv, item_num)
	
	return item_num > 0 and last_lv > 0, last_use_num
end

-- 获取幻兽属性丹全部属性
function ControlBeastsWGData:GetSXDAttrTipsData()
	local cfg = self:GetBeastsPelletCfg()
	if not cfg then
		return 0
	end

	local total_add_per = 0
	local attr_list = {}

	-- 拿到总加成
    for i = 0, 2 do
		local cfg_data = cfg[i]
		if cfg_data then
			local lv = self:GetBeastsPelletLvBySeq(cfg_data.seq)
			local lv_all_add_per = cfg_data.all_add_per / 10000 * lv
			if lv_all_add_per ~= 0 then
				total_add_per = total_add_per + lv_all_add_per
			end
		end
	end

	-- 加入所有属性
	if total_add_per ~= 0 then
		local data_attr_per = {}
		local per_index = 20000
		data_attr_per.attr_str = per_index
		data_attr_per.attr_value = total_add_per * 10000
		data_attr_per.is_per = true
		data_attr_per.reset_name = Language.ContralBeasts.CultureSXDTypeAll
		attr_list[per_index] = data_attr_per
	end

	-- 拿到到所有属性
	for i = 0, 2 do
		local cfg_data = cfg[i]
		if cfg_data then
			self:GetSXDAttrListBySeq(attr_list, cfg_data, total_add_per + 1)
		end
	end

	local return_table = {}
	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	local tips_data = {
		title_text = Language.ContralBeasts.CultureText14,
		attr_data = return_table,
		prefix_text = "+",
	}

	return tips_data
end

-- 获取一个属性
function ControlBeastsWGData:GetSXDAttrListBySeq(cur_table, cfg, total_add_per)
	if not cfg then
		return
	end

	local lv = self:GetBeastsPelletLvBySeq(cfg.seq)
	local real_add_per = 1 + ((cfg.add_per / 10000) * lv)

	if cfg.add_per ~= nil and cfg.add_per ~= 0 and cfg.add_per * lv ~= 0 then
		local data_attr_per = {}
		local per_index = 10000 + cfg.seq
		data_attr_per.attr_str = per_index
		data_attr_per.attr_value = cfg.add_per * lv
		data_attr_per.is_per = true
		data_attr_per.reset_name = Language.ContralBeasts.CultureSXDType[cfg.seq]
		cur_table[per_index] = data_attr_per
	end

	for i = 0, 3 do
		local key = cfg["attr_id" .. i]
		local value = math.floor(cfg["attr_value" .. i] * lv * real_add_per * total_add_per)

		if key and key > 0 and value > 0 then
			if not cur_table[key] then
				cur_table[key] = {}
				cur_table[key].attr_str = key
				cur_table[key].attr_value = value
			else
				cur_table[key].attr_value = cur_table[key].attr_value + value
			end
		end
	end
end
--------------------------- 幻兽属性丹end -----------------------

--------------------------- 幻兽内丹start -----------------------
-- 获取所有的幻兽出战位
function ControlBeastsWGData:GetAllBattleBattleList()
	local battle_main_hole_data = self:GetHoleMainData()
	local battle_assist_hole_data = self:GetHoleAssistData()
	local all_beast_list = {}
	local not_have_beast_list = {}

	for k, v in pairs(battle_main_hole_data) do
		if v.beasts_bag_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			table.insert(all_beast_list, v)
		else
			table.insert(not_have_beast_list, v)
		end
	end

	for k, v in pairs(battle_assist_hole_data) do
		if v.beasts_bag_id ~= -1 and v.state == BEASTS_HOLE_STATUS.ACTIVE then
			table.insert(all_beast_list, v)
		else
			table.insert(not_have_beast_list, v)
		end
	end

	for i, v in ipairs(not_have_beast_list) do
		table.insert(all_beast_list, v)
	end

	return all_beast_list
end
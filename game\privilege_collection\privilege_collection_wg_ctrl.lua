require("game/privilege_collection/privilege_collection_wg_data")
require("game/privilege_collection/privilege_collection_view")
require("game/privilege_collection/privilege_collection_zztq_view")
require("game/privilege_collection/privilege_collection_nstq_view")
require("game/privilege_collection/privilege_collection_zjtq_view")
require("game/privilege_collection/privilege_collection_sqcy_view")
require("game/privilege_collection/privilege_collection_shtq_view")
require("game/privilege_collection/privilege_collection_sqcy_tip")
require("game/privilege_collection/privilege_collection_recharge_package_alert")
require("game/privilege_collection/privilege_collection_shtq_get_result")
require("game/privilege_collection/privilege_collection_shtq_exp_tips")

PrivilegeCollectionWGCtrl = PrivilegeCollectionWGCtrl or BaseClass(BaseWGCtrl)

function PrivilegeCollectionWGCtrl:__init()
	if PrivilegeCollectionWGCtrl.Instance then
		ErrorLog("[PrivilegeCollectionWGCtrl] attempt to create singleton twice!")
		return
	end

	PrivilegeCollectionWGCtrl.Instance = self
    self.view = PrivilegeCollectionView.New(GuideModuleName.PrivilegeCollectionView)
    self.data = PrivilegeCollectionWGData.New()
	self.sqcy_tip = PrivilegeCollectionSQCYTip.New()
	self.shtq_get_result = PrivilegeCollectionSHTQGetResultView.New()
	self.shtq_exp_tips = PrivilegeCollectionSHTQExpTips.New()

	if not self.pass_day_event then
		self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayChange, self))
	end

	self:RegisterProtocol(CSGuardPrivilegeOperate)
	self:RegisterProtocol(SCHolyWeaponAllInfo, "OnSCHolyWeaponAllInfo")
	self:RegisterProtocol(SCHolyWeaponUpdateInfo, "OnSCHolyWeaponUpdateInfo")
	self:RegisterProtocol(SCHolyWeaponSkillProcessUpdateInfo, "OnHolyWeaponSkillProcessUpdateInfo")
	self:RegisterProtocol(SCGuardPrivilegeInfo, "OnSCGuardPrivilegeInfo")
end

function PrivilegeCollectionWGCtrl:__delete()
	PrivilegeCollectionWGCtrl.Instance = nil

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

	if self.sqcy_tip then
		self.sqcy_tip:DeleteMe()
		self.sqcy_tip = nil
	end

	if self.shtq_get_result then
		self.shtq_get_result:DeleteMe()
		self.shtq_get_result = nil
	end

	if self.shtq_exp_tips then
		self.shtq_exp_tips:DeleteMe()
		self.shtq_exp_tips = nil
	end

	if self.pass_day_event then
		GlobalEventSystem:UnBind(self.pass_day_event)
		self.pass_day_event = nil
	end
end

function PrivilegeCollectionWGCtrl:OpenSHTQExpTips(data)
	if self.shtq_exp_tips then
		self.shtq_exp_tips:SetData(data)
	end
end

function PrivilegeCollectionWGCtrl:SetViewJumpFlag()
	if self.view:IsOpen() and self.view.SetJumpFlag then
		self.view:SetJumpFlag()
	end
end

function PrivilegeCollectionWGCtrl:OpenSHTQResultTips(data)
	if self.shtq_get_result then
		self.shtq_get_result:SetData(data)
	end
end

function PrivilegeCollectionWGCtrl:SendOperateRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCangJinShangPuClientReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

-- ====================================== 圣器残页 start ==============================================================

function PrivilegeCollectionWGCtrl:OnSCHolyWeaponAllInfo(protocol)
	-- print_error("------------OnSCHolyWeaponAllInfo--------- ", protocol)
	self.data:SetHolyWeaponList(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.pri_col_sqcy)
	end
	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_SQCY)
end

function PrivilegeCollectionWGCtrl:OnSCHolyWeaponUpdateInfo(protocol)
	-- print_error("------------OnSCHolyWeaponUpdateInfo--------- ", protocol)
	self.data:SetHolyWeaponInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.pri_col_sqcy)
	end
	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_SQCY)
end

function PrivilegeCollectionWGCtrl:OnHolyWeaponSkillProcessUpdateInfo(protocol)
	-- print_error("------------OnHolyWeaponSkillProcessUpdateInfo--------- ", protocol)
	self.data:SetHolyWeaponSkillProcess(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.pri_col_sqcy)
	end
	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_SQCY)
end

function PrivilegeCollectionWGCtrl:OpenSQCYTip(data)
	if self.sqcy_tip then
		self.sqcy_tip:SetDataAndOpen(data)
	end
end
-- ====================================== 圣器残页 end ==============================================================

function PrivilegeCollectionWGCtrl:OnDayChange()
	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_SQCY)
end

------------------------------------- 守护特权 start -------------------------------------
--操作协议.
function PrivilegeCollectionWGCtrl:SendGuardPrivilegeOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuardPrivilegeOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function PrivilegeCollectionWGCtrl:OnSCGuardPrivilegeInfo(protocol)
	-- print_error("------------OnSCGuardPrivilegeInfo--------- ", protocol)
	local is_first = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeIsFirstSend()		--判断首次下发协议不显示恭喜获得.
	local last_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
	local last_role_level = RoleWGData.Instance:GetRoleLevel()
	local last_save_exp = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSaveExp()

	self.data:SetGuardPrivilegeList(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.pri_col_shtq)
	end
	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_SHTQ)

	if not is_first then
		local cur_seq = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeSeq()
		--购买后展示恭喜获得.
		if cur_seq ~= last_seq then
			local can_up_level, can_up_baifenbi = PrivilegeCollectionWGData.Instance:SaveExpCanUpLevel(cur_seq)
			local up_num = 0
			local show_cfg = PrivilegeCollectionWGData.Instance:GetGuardPrivilegeCfgBySeq(last_seq)
			if show_cfg then
				up_num = show_cfg.up_num
			end

			local result_data = {}
			result_data.seq = cur_seq
			result_data.save_exp = last_save_exp
			result_data.last_role_level = last_role_level
			result_data.last_rate = up_num
			result_data.can_up_level = can_up_level
			self:OpenSHTQResultTips(result_data)
			if self.view:IsOpen() then
				self.view:Close()
			end

			MainuiWGCtrl.Instance:FlushView(0, "flush_hundred_num")
		end
	end
end
------------------------------------- 守护特权 end -------------------------------------
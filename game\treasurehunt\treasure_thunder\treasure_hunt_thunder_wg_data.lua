TreasureHuntThunderWGData = TreasureHuntThunderWGData or BaseClass()

function TreasureHuntThunderWGData:__init()
	if TreasureHuntThunderWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[TreasureHuntThunderWGData] attempt to create singleton twice!")
		return
	end

	TreasureHuntThunderWGData.Instance = self

    self:InitCfg()

	self.level = 1
	self.draw_times = 0
	self.score = 0
	self.count_reward_flag = {}
	self.shop_item_list = {}
	self.person_record_list = {}
	self.world_record_list = {}
	self.storge_item_list = {}

	self.cache_draw_btn_index = 0

	RemindManager.Instance:Register(RemindName.TreasureHunt_Thunder, BindTool.Bind(self.ShowThunderDrawRemind, self))
end

function TreasureHuntThunderWGData:__delete()
	TreasureHuntThunderWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.TreasureHunt_Thunder)
end

function TreasureHuntThunderWGData:InitCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("thunder_draw_auto")

	self.draw_num_cfg = ListToMap(cfg.draw_num, "seq")
	self.reward_pool_cfg = ListToMap(cfg.reward_pool, "level")
	self.exchange_cfg = ListToMap(cfg.exchange, "seq")
    self.item_random_desc_cfg = ListToMapList(cfg.item_random_desc, "level")
	self.count_reward_cfg = ListToMap(cfg.count_reward, "level", "seq")
	self.show_reward_cfg = ListToMap(cfg.show_reward, "level")
	self.other_cfg = cfg.other[1]
end

function TreasureHuntThunderWGData:SetAllThunderDrawInfo(protocol)
	self.level = protocol.level
    self.draw_times = protocol.draw_times
    self.score = protocol.score
    self.count_reward_flag = protocol.count_reward_flag
    self.shop_item_list = protocol.shop_item_list
end

function TreasureHuntThunderWGData:SetUpdateThunderDrawInfo(protocol)
	local change_data = protocol.change_data
	self.level = change_data.level
	self.draw_times = change_data.draw_times
	self.score = change_data.score
	self.count_reward_flag = change_data.count_reward_flag
end

function TreasureHuntThunderWGData:SetUpdateThunderDrawShop(protocol)
	local change_data = protocol.change_data
	if self.shop_item_list[change_data.seq] then
		self.shop_item_list[change_data.seq] = change_data.buy_times
	end
end

function TreasureHuntThunderWGData:SetThunderDrawPersonalRecord(protocol)
	self.person_record_list = protocol.record_list
end

function TreasureHuntThunderWGData:SetThunderDrawWorldRecord(protocol)
	self.world_record_list = protocol.record_list
end

function TreasureHuntThunderWGData:SetThunderDrawStorgeInfo(protocol)
	self.storge_item_list = protocol.item_list
end

--概率列表
function TreasureHuntThunderWGData:GetGaiLvInfoList(level)
	return self.item_random_desc_cfg[self.level] or {}
end

--展示奖励信息
function TreasureHuntThunderWGData:GetShowRewardInfo()
	return self.show_reward_cfg[self.level]
end

function TreasureHuntThunderWGData:GetOtherInfo()
	return self.other_cfg
end

function TreasureHuntThunderWGData:GetAllDrawNumCfg()
	return self.draw_num_cfg
end

--获取抽奖的选项
function TreasureHuntThunderWGData:CacheOrGetDrawIndex(btn_index)
	if btn_index then
		self.cache_draw_btn_index = btn_index
	end

	return self.cache_draw_btn_index
end

--奖励结果
function TreasureHuntThunderWGData:SetResultData(protocol)
	local big_reward_count = protocol.big_reward_count
    self.reward_list = {}

    local list_count = MsgAdapter.ReadInt()
    for i = 1, list_count do
		local data = {}
		data.item_id = MsgAdapter.ReadUShort()
		data.reversh = MsgAdapter.ReadChar()
		data.is_bind = MsgAdapter.ReadChar()
		data.num = MsgAdapter.ReadInt()
        self.reward_list[i] = data
	end
end

--获取奖励对应的配置
function TreasureHuntThunderWGData:CalDrawRewardList(protocol)
    local draw_reward_list = {}
    local big_reward_list = {}
	local big_reward_count = protocol.big_reward_count

	for i, v in pairs(protocol.reward_list) do
		if i <= big_reward_count then
			table.insert(big_reward_list, v.item_id)
		else
			table.insert(draw_reward_list, v)
		end
	end

	return draw_reward_list, big_reward_list
end

--仓库数据
function TreasureHuntThunderWGData:GetStorageItemList()
    return self.storge_item_list
end

-- 记录
function TreasureHuntThunderWGData:GetRecordInfoByIndex(index)
	if index == 2 then
		return self.person_record_list
	else
		return self.world_record_list
	end
end

function TreasureHuntThunderWGData:GetCurLevel()
	return self.level
end

function TreasureHuntThunderWGData:GetCurDrawTimes()
	return self.draw_times
end

function TreasureHuntThunderWGData:GetCurScore()
	return self.score
end

function TreasureHuntThunderWGData:GetRewardPoolcfgByLevel(level)
	return self.reward_pool_cfg[level]
end

function TreasureHuntThunderWGData:GetExchangeRewardList()
	local exchange_list = {}
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k, v in pairs(self.exchange_cfg) do
		if open_day >= v.open_day and self.level >= v.limit_level then
			local data = {}
			data.cfg = v
			data.seq = v.seq
			data.buy_times = self.shop_item_list[v.seq] or 0
			data.buy_sort = 0
			if v.limit_type == 0 or v.limit_num > data.buy_times then
				data.buy_sort = 0
			else
				data.buy_sort = 1
			end
			table.insert(exchange_list, data)
		end
	end

	if not IsEmptyTable(exchange_list) then
		table.sort(exchange_list, SortTools.KeyLowerSorters("buy_sort", "seq"))
	end

	return exchange_list
end

function TreasureHuntThunderWGData:GetCountRewardCfgByLevel(level)
	return self.count_reward_cfg[level]
end

function TreasureHuntThunderWGData:GetCountRewardFlagBySeq(seq)
	return self.count_reward_flag[seq] or 0
end


--概率展示
function TreasureHuntThunderWGData:GetDrawProbabilityInfo()
	if IsEmptyTable(self.probability_list) then
		self.probability_list = {}
		-- 跳转目标档位
		self.probability_list.target_grade = self.level
		-- 档位列表
		self.probability_list.group_list = {}

		-- 遍历配置
		for k, draw_prop_list in ipairs(self.item_random_desc_cfg) do
			local data = {}
			-- 类型列表
			data.quality_list = {}
			-- 档位名字
			data.group_name = string.format(Language.TreasureHunt.ThunderDrawGroupName, k)
			-- 概率排序
			table.sort(draw_prop_list, SortTools.KeyLowerSorter("random_count"))
			-- 遍历配置，
			for _, v in ipairs(draw_prop_list) do
				-- 判断是否有对应的品质列表table，没有就创建
				if IsEmptyTable(data.quality_list[v.show_type]) then
					data.quality_list[v.show_type] = {
						title = Language.TreasureHunt.ThunderDrawProbabilityTitle[v.show_type],
						probability_list = {}
					}
				end
				-- 把数据加入到对应类型的列表
				table.insert(data.quality_list[v.show_type].probability_list, v)

			end
			table.insert(self.probability_list.group_list, data)
		end

	else
		self.probability_list.target_grade = self.level
	end

	return self.probability_list
end

function TreasureHuntThunderWGData:ShowThunderDrawRemind()
	local draw_num_cfg = self:GetAllDrawNumCfg()
    local other_cfg = self:GetOtherInfo()
	local has_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.draw_item_id)

	-- 抽奖道具足够
	if has_num >= draw_num_cfg[0].consume_num then
		return 1
	end

	--仓库有道具
	if not IsEmptyTable(self.storge_item_list) then
		return 1 
	end

	--次数奖励
	local count_reward_list = self:GetCountRewardCfgByLevel(self.level)
	if not IsEmptyTable(count_reward_list) then
		for i, v in pairs(count_reward_list) do
			local is_get = self:GetCountRewardFlagBySeq(v.seq) == 1
			if not is_get and self.draw_times >= v.draw_times then
				return 1
			end
		end
	end

	return 0
end

